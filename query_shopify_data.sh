#!/bin/bash

# Database connection parameters
DB_HOST="ecomquant.cluster-cigrbvfmsdyb.us-east-1.rds.amazonaws.com"
DB_USER="postgres"
DB_PASSWORD="VgJTIMYz30XlpqgVjHs3"
DB_NAME="adlibs"
STORE_ID="420057a5-1c6d-4e13-a08e-e156ab065d96"

echo "Querying Shopify data for store ID: $STORE_ID"
echo "=================================================="

# Create JSON output file
OUTPUT_FILE="shopify_data_${STORE_ID}.json"

# Query Shopify Orders
echo ""
echo "=== QUERYING SHOPIFY ORDERS (LIMIT 100) ==="
echo "Executing query: SELECT * FROM shopify_orders WHERE linked_store_id = '$STORE_ID' LIMIT 100"
echo "Timestamp: $(date '+%Y-%m-%d %H:%M:%S')"
ORDERS_JSON=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -t -c "SET search_path TO chargeback; SELECT COALESCE(json_agg(row_to_json(t)), '[]'::json) FROM (SELECT * FROM shopify_orders WHERE linked_store_id = '$STORE_ID' LIMIT 100) t;" | sed 's/^[ \t]*//;s/[ \t]*$//' | sed '/^$/d')
echo "Orders query completed at: $(date '+%Y-%m-%d %H:%M:%S')"

# Query Shopify Transactions  
echo ""
echo "=== QUERYING SHOPIFY TRANSACTIONS (LIMIT 100) ==="
echo "Executing query: SELECT * FROM shopify_transactions WHERE linked_store_id = '$STORE_ID' LIMIT 100"
echo "Timestamp: $(date '+%Y-%m-%d %H:%M:%S')"
TRANSACTIONS_JSON=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -t -c "SET search_path TO chargeback; SELECT COALESCE(json_agg(row_to_json(t)), '[]'::json) FROM (SELECT * FROM shopify_transactions WHERE linked_store_id = '$STORE_ID' LIMIT 100) t;" | sed 's/^[ \t]*//;s/[ \t]*$//' | sed '/^$/d')
echo "Transactions query completed at: $(date '+%Y-%m-%d %H:%M:%S')"

# Query Shopify Disputes
echo ""
echo "=== QUERYING SHOPIFY DISPUTES (LIMIT 100) ==="
echo "Executing query: SELECT * FROM shopify_disputes WHERE linked_store_id = '$STORE_ID' LIMIT 100"
echo "Timestamp: $(date '+%Y-%m-%d %H:%M:%S')"
DISPUTES_JSON=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -t -c "SET search_path TO chargeback; SELECT COALESCE(json_agg(row_to_json(t)), '[]'::json) FROM (SELECT * FROM shopify_disputes WHERE linked_store_id = '$STORE_ID' LIMIT 100) t;" | sed 's/^[ \t]*//;s/[ \t]*$//' | sed '/^$/d')
echo "Disputes query completed at: $(date '+%Y-%m-%d %H:%M:%S')"

# Process and validate JSON before saving
echo ""
echo "=== PROCESSING JSON DATA ==="
echo "Processing and validating JSON data..."

# Create temporary JSON file
TEMP_JSON=$(mktemp)

# Build JSON structure with validation
cat > "$TEMP_JSON" << EOF
{
  "store_id": "$STORE_ID",
  "query_timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "shopify_orders": $ORDERS_JSON,
  "shopify_transactions": $TRANSACTIONS_JSON,
  "shopify_disputes": $DISPUTES_JSON
}
EOF

# Validate and format JSON using jq (if available)
if command -v jq &> /dev/null; then
    echo "Validating and formatting JSON with jq..."
    if jq empty "$TEMP_JSON" 2>/dev/null; then
        echo "JSON validation successful!"
        # Format and save the final JSON
        jq . "$TEMP_JSON" > "$OUTPUT_FILE"
    else
        echo "Warning: JSON validation failed, saving unformatted JSON..."
        cp "$TEMP_JSON" "$OUTPUT_FILE"
    fi
else
    echo "Warning: jq not found, skipping JSON validation and formatting..."
    cp "$TEMP_JSON" "$OUTPUT_FILE"
fi

# Clean up temporary file
rm -f "$TEMP_JSON"

# Add data summary
ORDERS_COUNT=$(echo "$ORDERS_JSON" | jq 'length' 2>/dev/null || echo "unknown")
TRANSACTIONS_COUNT=$(echo "$TRANSACTIONS_JSON" | jq 'length' 2>/dev/null || echo "unknown")
DISPUTES_COUNT=$(echo "$DISPUTES_JSON" | jq 'length' 2>/dev/null || echo "unknown")

echo ""
echo "=== DATA SUMMARY ==="
echo "Orders: $ORDERS_COUNT records"
echo "Transactions: $TRANSACTIONS_COUNT records"
echo "Disputes: $DISPUTES_COUNT records"

echo ""
echo "JSON data saved to: $OUTPUT_FILE"
cat "$OUTPUT_FILE"

echo ""
echo "Query completed!"