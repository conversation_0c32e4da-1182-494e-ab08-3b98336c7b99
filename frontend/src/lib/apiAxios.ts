import axios, {
  AxiosInstance,
  AxiosRequestConfig,
} from "axios";

const API_URL = process.env.NEXT_PUBLIC_API_URL || "";
const API_KEY = process.env.NEXT_PUBLIC_API_KEY || "";

class ApiClient {
  private api: AxiosInstance;
  constructor() {
    this.api = axios.create({
      baseURL: API_URL,
      timeout: 30000, // 30 seconds
      headers: {
        "Content-Type": "application/json",
        "x-api-key": API_KEY,
      },
    });

    // Add response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 403 && error.response?.data?.message === "Invalid API key") {
          console.error('Invalid API key. Please check your NEXT_PUBLIC_API_KEY environment variable.');
        }
        return Promise.reject(error);
      }
    );
  }

  // Public method to get the configured axios instance
  public getAxiosInstance(): AxiosInstance {
    return this.api;
  }

  // Create an authenticated request with JW<PERSON> token and optional provider
  public createAuthenticatedRequest(token: string, provider?: string) {
    const baseHeaders = {
      Authorization: `Bearer ${token}`,
      ...(provider && { 'x-provider': provider }),
    };

    return {
      get: (url: string, config?: AxiosRequestConfig) => 
        this.api.get(url, {
          ...config,
          headers: {
            ...config?.headers,
            ...baseHeaders,
          },
        }),
      post: (url: string, data?: any, config?: AxiosRequestConfig) => 
        this.api.post(url, data, {
          ...config,
          headers: {
            ...config?.headers,
            ...baseHeaders,
          },
        }),
      put: (url: string, data?: any, config?: AxiosRequestConfig) => 
        this.api.put(url, data, {
          ...config,
          headers: {
            ...config?.headers,
            ...baseHeaders,
          },
        }),
      delete: (url: string, config?: AxiosRequestConfig) => 
        this.api.delete(url, {
          ...config,
          headers: {
            ...config?.headers,
            ...baseHeaders,
          },
        }),
    };
  }
}

// Create and export a singleton instance
const apiClient = new ApiClient();
export const apiAxios = apiClient.getAxiosInstance();
export const createAuthenticatedApi = (token: string, provider?: string) => apiClient.createAuthenticatedRequest(token, provider);
