/**
 * Format a date to a readable string
 * @param date - The date to format (string, number, or Date)
 * @param options - Intl.DateTimeFormat options
 * @returns Formatted date string
 */
export function formatDate(
  date: string | number | Date,
  options: Intl.DateTimeFormatOptions = {}
): string {
  const dateObj = new Date(date);
  
  // Default options for date formatting
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  };

  // Merge default options with provided options
  const formatterOptions = { ...defaultOptions, ...options };
  
  // Format the date
  return new Intl.DateTimeFormat('en-US', formatterOptions).format(dateObj);
}

/**
 * Format a date to a short date string (MM/DD/YYYY)
 * @param date - The date to format
 * @returns Formatted short date string
 */
export function formatShortDate(date: string | number | Date): string {
  return formatDate(date, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
}

/**
 * Format a date to a time string (HH:MM AM/PM)
 * @param date - The date to format
 * @returns Formatted time string
 */
export function formatTime(date: string | number | Date): string {
  return formatDate(date, {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  });
}

/**
 * Get relative time string (e.g., "2 hours ago", "3 days ago")
 * @param date - The date to compare
 * @returns Relative time string
 */
export function getRelativeTime(date: string | number | Date): string {
  const dateObj = new Date(date);
  const now = new Date();
  const diffInMs = now.getTime() - dateObj.getTime();
  const diffInSeconds = Math.floor(diffInMs / 1000);
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);
  
  if (diffInSeconds < 60) {
    return 'just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
  } else if (diffInDays < 30) {
    return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
  } else {
    return formatShortDate(dateObj);
  }
}