import { apiCache } from './api-cache';

/**
 * Cache manager for managing application-wide cache
 */
export const cacheManager = {
  /**
   * Clear all caches
   */
  clearAll: () => {
    apiCache.clear();
  },

  /**
   * Clear cache for a specific store
   * @param storeId Store ID
   */
  clearStoreCache: (storeId: string) => {
    // Clear alert info cache
    apiCache.clearPattern(`alert-info:store:${storeId}`);
    // Clear blocks/dashboard cache
    apiCache.clearPattern(`blocks:.*linkedStoreId=${storeId}`);
    apiCache.clearPattern(`chargeback-chart:.*linkedStoreId=${storeId}`);
    
  },

  /**
   * Clear dashboard-specific caches
   */
  clearDashboardCache: () => {
    apiCache.clearPattern(`blocks:`);
    apiCache.clearPattern(`chargeback-chart:`);
    
  },

  /**
   * Get cache statistics
   */
  getStats: () => {
    return apiCache.getStats();
  },

  /**
   * Prefetch dashboard data for better performance
   * @param storeId Store ID
   */
  prefetchDashboardData: async (storeId: string) => {
    // This can be implemented to prefetch critical dashboard data
    // when user is about to navigate to dashboard
  }
};