import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Format a number as currency
 * @param value - The value to format
 * @param currency - The currency code (default: USD)
 * @param options - Intl.NumberFormat options
 * @returns Formatted currency string
 */
export function formatCurrency(
  value: number | string,
  currency: string = "USD",
  options: Intl.NumberFormatOptions = {}
): string {
  // Convert string to number if needed
  const numericValue = typeof value === "string" ? parseFloat(value.replace(/,/g, "")) : value;
  
  // Default options for currency formatting
  const defaultOptions: Intl.NumberFormatOptions = {
    style: "currency",
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  };

  // Merge default options with provided options
  const formatterOptions = { ...defaultOptions, ...options };
  
  // Format the value
  return new Intl.NumberFormat("en-US", formatterOptions).format(numericValue);
}
