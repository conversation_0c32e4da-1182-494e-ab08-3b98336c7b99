// This file contains utility functions for Crisp chat integration

/**
 * Generate a verification signature for Crisp chat
 * This should ideally be done on the server side, but for demo purposes
 * we're implementing it in the browser using the Web Crypto API
 * 
 * @param email The email to verify
 * @param secretKey The secret key from <PERSON><PERSON>p
 * @returns Promise that resolves to the HMAC signature
 */
export async function generateCrispSignature(email: string, secretKey: string): Promise<string> {
  // Convert the message and key to ArrayBuffer
  const encoder = new TextEncoder();
  const keyData = encoder.encode(secretKey);
  const messageData = encoder.encode(email);
  
  // Import the key
  const cryptoKey = await window.crypto.subtle.importKey(
    'raw',
    keyData,
    { name: 'HM<PERSON>', hash: 'SHA-256' },
    false,
    ['sign']
  );
  
  // Sign the message
  const signature = await window.crypto.subtle.sign(
    'HMAC',
    cryptoKey,
    messageData
  );
  
  // Convert the signature to hex string
  return Array.from(new Uint8Array(signature))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}
