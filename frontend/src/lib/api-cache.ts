// Enhanced in-memory cache for API responses with deduplication
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

interface PendingRequest {
  promise: Promise<any>;
  timestamp: number;
}

class ApiCache {
  private cache = new Map<string, CacheEntry<any>>();
  private pendingRequests = new Map<string, PendingRequest>();
  private defaultTTL = 5 * 60 * 1000; // 5 minutes default
  private requestTimeout = 30 * 1000; // 30 seconds timeout for pending requests

  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const isExpired = Date.now() - entry.timestamp > entry.ttl;
    if (isExpired) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }
  
  // Check if there's a pending request for this key
  getPendingRequest(key: string): Promise<any> | null {
    const pending = this.pendingRequests.get(key);
    if (!pending) return null;
    
    // Clean up stale pending requests
    if (Date.now() - pending.timestamp > this.requestTimeout) {
      this.pendingRequests.delete(key);
      return null;
    }
    
    return pending.promise;
  }
  
  // Register a pending request to prevent duplicates
  setPendingRequest(key: string, promise: Promise<any>): void {
    this.pendingRequests.set(key, {
      promise,
      timestamp: Date.now()
    });
    
    // Clean up after completion
    promise.finally(() => {
      this.pendingRequests.delete(key);
    });
  }

  clear(): void {
    this.cache.clear();
    this.pendingRequests.clear();
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  // Clear entries matching a pattern
  clearPattern(pattern: string): void {
    const regex = new RegExp(pattern);
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
      }
    }
  }
  
  // Get cache statistics for debugging
  getStats(): { cacheSize: number; pendingRequests: number } {
    return {
      cacheSize: this.cache.size,
      pendingRequests: this.pendingRequests.size
    };
  }
}

export const apiCache = new ApiCache();