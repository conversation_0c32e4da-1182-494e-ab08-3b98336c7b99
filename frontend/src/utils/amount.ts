/**
 * Utility functions for handling amount conversions
 * Backend stores amounts as integers (cents/minor units)
 * Frontend displays as decimal (dollars/major units)
 */

/**
 * Convert amount from integer (cents) to decimal (dollars)
 * @param amount - Amount in cents (e.g., 1000 = $10.00)
 * @returns Amount in dollars (e.g., 10.00)
 */
export const formatAmountForDisplay = (amount: number | null | undefined): string => {
  if (amount === null || amount === undefined) {
    return '0.00';
  }
  return (amount / 100).toFixed(2);
};

/**
 * Convert amount from decimal (dollars) to integer (cents)
 * @param amount - Amount in dollars (e.g., "10.00")
 * @returns Amount in cents (e.g., 1000)
 */
export const formatAmountForStorage = (amount: string | number): number => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  return Math.round(numAmount * 100);
};

/**
 * Format amount with currency symbol
 * @param amount - Amount in cents
 * @param currency - Currency code (e.g., "USD")
 * @returns Formatted amount with currency (e.g., "$10.00")
 */
export const formatAmountWithCurrency = (amount: number | null | undefined, currency: string = 'USD'): string => {
  const displayAmount = formatAmountForDisplay(amount);
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  
  return formatter.format(parseFloat(displayAmount));
};