/**
 * Payment Setup Status Cache Utilities
 * 
 * Uses both localStorage and cookies to cache payment setup status for Stripe:
 * - localStorage: For client-side components
 * - Cookies: For middleware access (since middleware can't access localStorage)
 */

export type PaymentMethodType = 'stripe';

/**
 * Set payment setup status as completed
 * @param storeId Store ID
 * @param paymentMethod Payment method type ('stripe')
 */
export function setPaymentSetupCompleted(storeId: string, paymentMethod: PaymentMethodType): void {
  if (typeof window === 'undefined') return;
  
  const key = `${paymentMethod}_setup_completed_${storeId}`;
  
  // Set in localStorage for client-side access
  localStorage.setItem(key, 'true');
  
  // Set as cookie for middleware access
  // Cookie expires in 30 days
  const expiryDate = new Date();
  expiryDate.setDate(expiryDate.getDate() + 30);
  
  document.cookie = `${key}=true; expires=${expiryDate.toUTCString()}; path=/; secure; samesite=strict`;

  // Also set a general payment setup completed flag
  const generalKey = `payment_setup_completed_${storeId}`;
  localStorage.setItem(generalKey, 'true');
  document.cookie = `${generalKey}=true; expires=${expiryDate.toUTCString()}; path=/; secure; samesite=strict`;
}

/**
 * Check if any payment method setup is completed
 * @param storeId Store ID
 * @returns boolean indicating if any payment setup is completed
 */
export function isAnyPaymentSetupCompleted(storeId: string): boolean {
  if (typeof window === 'undefined') return false;
  
  // Check general payment setup flag first
  const generalKey = `payment_setup_completed_${storeId}`;
  const localStorageValue = localStorage.getItem(generalKey);
  if (localStorageValue === 'true') {
    return true;
  }
  
  // Fallback to checking cookies
  const cookies = document.cookie.split(';');
  const generalCookie = cookies.find(cookie => 
    cookie.trim().startsWith(`${generalKey}=`)
  );
  
  if (generalCookie) {
    const value = generalCookie.split('=')[1];
    if (value === 'true') return true;
  }

  // Fallback to checking individual payment methods
  return isPaymentSetupCompleted(storeId, 'stripe');
}

/**
 * Check if specific payment method setup is completed
 * @param storeId Store ID
 * @param paymentMethod Payment method type
 * @returns boolean indicating if setup is completed
 */
export function isPaymentSetupCompleted(storeId: string, paymentMethod: PaymentMethodType): boolean {
  if (typeof window === 'undefined') return false;
  
  const key = `${paymentMethod}_setup_completed_${storeId}`;
  
  // Check localStorage first
  const localStorageValue = localStorage.getItem(key);
  if (localStorageValue === 'true') {
    return true;
  }
  
  // Fallback to checking cookies
  const cookies = document.cookie.split(';');
  const paymentCookie = cookies.find(cookie => 
    cookie.trim().startsWith(`${key}=`)
  );
  
  if (paymentCookie) {
    const value = paymentCookie.split('=')[1];
    return value === 'true';
  }
  
  return false;
}

/**
 * Clear payment setup status (when user resets/disconnects)
 * @param storeId Store ID
 * @param paymentMethod Optional specific payment method, if not provided clears all
 */
export function clearPaymentSetupStatus(storeId: string, paymentMethod?: PaymentMethodType): void {
  if (typeof window === 'undefined') return;
  
  
  if (paymentMethod) {
    // Clear specific payment method
    const key = `${paymentMethod}_setup_completed_${storeId}`;
  localStorage.removeItem(key);
    document.cookie = `${key}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    
    
    // Since we only have Stripe, always clear the general cache when clearing Stripe
    const generalKey = `payment_setup_completed_${storeId}`;
    localStorage.removeItem(generalKey);
    document.cookie = `${generalKey}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  } else {
    // Clear all payment methods
    const stripeKey = `stripe_setup_completed_${storeId}`;
    const generalKey = `payment_setup_completed_${storeId}`;
    
    localStorage.removeItem(stripeKey);
    localStorage.removeItem(generalKey);
    
    document.cookie = `${stripeKey}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    document.cookie = `${generalKey}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    
  }
}

/**
 * Get the cache key for a store and payment method
 * @param storeId Store ID
 * @param paymentMethod Payment method type
 * @returns Cache key string
 */
export function getPaymentSetupCacheKey(storeId: string, paymentMethod: PaymentMethodType): string {
  return `${paymentMethod}_setup_completed_${storeId}`;
}

/**
 * Get the general payment setup cache key for middleware compatibility
 * @param storeId Store ID
 * @returns Cache key string
 */
export function getGeneralPaymentSetupCacheKey(storeId: string): string {
  return `payment_setup_completed_${storeId}`;
}

/**
 * Debug function to check all cache status for a store
 * @param storeId Store ID
 * @returns Object with all cache statuses
 */
export function debugCacheStatus(storeId: string) {
  if (typeof window === 'undefined') return null;
  
  const status = {
    localStorage: {
      stripe: localStorage.getItem(`stripe_setup_completed_${storeId}`),
      general: localStorage.getItem(`payment_setup_completed_${storeId}`)
    },
    cookies: {} as any
  };
  
  // Parse cookies
  const cookies = document.cookie.split(';');
  status.cookies = {
    stripe: cookies.find(c => c.trim().startsWith(`stripe_setup_completed_${storeId}=`))?.split('=')[1] || null,
    general: cookies.find(c => c.trim().startsWith(`payment_setup_completed_${storeId}=`))?.split('=')[1] || null
  };
  
  return status;
}

/**
 * Force clear all caches for a store (for debugging)
 * Can be called from browser console: window.forceClearPaymentCache('store-id')
 * @param storeId Store ID
 */
export function forceClearPaymentCache(storeId: string) {
  if (typeof window === 'undefined') return;
  
  // Debug before clearing
  debugCacheStatus(storeId);
  
  // Clear everything
  clearPaymentSetupStatus(storeId);
  
  // Debug after clearing
  debugCacheStatus(storeId);
}

// Make it available on window for debugging
if (typeof window !== 'undefined') {
  (window as any).forceClearPaymentCache = forceClearPaymentCache;
  (window as any).debugCacheStatus = debugCacheStatus;
}

// Legacy functions for backward compatibility
export const setCardSetupCompleted = (storeId: string) => setPaymentSetupCompleted(storeId, 'stripe');
export const isCardSetupCompleted = (storeId: string) => isPaymentSetupCompleted(storeId, 'stripe');
export const clearCardSetupStatus = (storeId: string) => clearPaymentSetupStatus(storeId, 'stripe');
export const getCardSetupCacheKey = (storeId: string) => getPaymentSetupCacheKey(storeId, 'stripe');

/**
 * Validate cache against backend reality and clear if stale
 * @param storeId Store ID
 * @param actualStripeSetup Actual Stripe setup status from backend
 */
export function validateAndCleanCache(storeId: string, actualStripeSetup: boolean): void {
  if (typeof window === 'undefined') return;
  
  
  const cachedStripe = isPaymentSetupCompleted(storeId, 'stripe');
  const cachedGeneral = isAnyPaymentSetupCompleted(storeId);
  
  
  let needsClearing = false;
  
  // Check if cache is stale
  if (cachedStripe && !actualStripeSetup) {
    needsClearing = true;
  }
  
  if (cachedGeneral && !actualStripeSetup) {
    needsClearing = true;
  }
  
  if (needsClearing) {
    clearPaymentSetupStatus(storeId);
  }
} 