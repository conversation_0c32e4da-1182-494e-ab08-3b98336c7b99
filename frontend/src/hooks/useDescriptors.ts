import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { fetchAlertInfosByStore } from '@/redux/slices/alert-info';
import { AlertInfo } from '@/services/alert-info.service';
import { selectStores, selectStoresLoading } from '@/redux/slices/store';

interface UseDescriptorsReturn {
  descriptors: AlertInfo[];
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  uniqueDescriptorValues: string[];
}

/**
 * Hook for fetching and managing descriptors
 * Features:
 * - Single fetch on mount
 * - Data deduplication
 * - Memoized unique values
 * - Batch parallel fetching
 * - Request deduplication
 */
export function useDescriptors(): UseDescriptorsReturn {
  const dispatch = useAppDispatch();
  const { data: session } = useSession();
  
  // Get stores from Redux store (already cached)
  const allStores = useAppSelector(selectStores);
  const loadingStores = useAppSelector(selectStoresLoading);
  
  const [descriptors, setDescriptors] = useState<AlertInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Track if we're currently fetching to prevent duplicate requests
  const fetchingRef = useRef(false);
  // Track if initial fetch has been done
  const hasFetchedRef = useRef(false);
  
  const accessToken = session?.user?.access_token;

  /**
   * Fetch function with deduplication
   */
  const fetchDescriptors = useCallback(async () => {
    // Prevent duplicate concurrent requests
    if (fetchingRef.current || !allStores.length || !accessToken) {
      if (!allStores.length || !accessToken) {
        setLoading(false);
      }
      return;
    }

    try {
      fetchingRef.current = true;
      setLoading(true);
      setError(null);

      // Create batch of promises for parallel fetching
      const batchSize = 5; // Process 5 stores at a time to avoid overwhelming the server
      const results: AlertInfo[] = [];
      
      for (let i = 0; i < allStores.length; i += batchSize) {
        const batch = allStores.slice(i, i + batchSize);
        
        const batchPromises = batch.map(async (store) => {
          try {
            const response = await dispatch(
              fetchAlertInfosByStore({
                storeId: store.id,
                token: accessToken,
                provider: store.provider,
              })
            ).unwrap();
            return response || [];
          } catch (err) {
            console.error(`Failed to fetch descriptors for store ${store.id}:`, err);
            return [];
          }
        });

        const batchResults = await Promise.all(batchPromises);
        batchResults.forEach(storeDescriptors => {
          if (Array.isArray(storeDescriptors)) {
            results.push(...storeDescriptors);
          }
        });
      }

      // Deduplicate descriptors by unique key (id + storeId + descriptor)
      const uniqueMap = new Map<string, AlertInfo>();
      results.forEach(descriptor => {
        const key = `${descriptor.id}_${descriptor.storeId}_${descriptor.descriptor}`;
        if (!uniqueMap.has(key)) {
          uniqueMap.set(key, descriptor);
        }
      });

      const uniqueDescriptors = Array.from(uniqueMap.values());
      setDescriptors(uniqueDescriptors);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch descriptors');
    } finally {
      setLoading(false);
      fetchingRef.current = false;
    }
  }, [allStores, accessToken, dispatch]);

  /**
   * Memoized unique descriptor values for dropdown
   */
  const uniqueDescriptorValues = useMemo(() => {
    const uniqueSet = new Set<string>();
    
    descriptors
      .filter(info => info.alertType === 'ETHOCA' && info.registrationStatus === 'EFFECTED')
      .forEach(info => {
        if (info.descriptor) {
          uniqueSet.add(info.descriptor);
        }
      });

    return Array.from(uniqueSet).sort();
  }, [descriptors]);

  /**
   * Initial fetch - only once when dependencies are ready
   */
  useEffect(() => {
    // Only fetch if we haven't fetched yet and dependencies are ready
    if (!loadingStores && allStores.length > 0 && accessToken && !hasFetchedRef.current) {
      hasFetchedRef.current = true;
      fetchDescriptors();
    } else if (!loadingStores && (allStores.length === 0 || !accessToken)) {
      // No stores or no token, set loading to false
      setLoading(false);
    }
  }, [loadingStores, allStores.length, accessToken]); // Remove fetchDescriptors from deps

  /**
   * Manual refresh function
   */
  const refresh = useCallback(async () => {
    // Allow refetch by resetting the flag
    hasFetchedRef.current = false;
    
    if (allStores.length > 0 && accessToken) {
      hasFetchedRef.current = true;
      await fetchDescriptors();
    }
  }, [allStores.length, accessToken]);

  return {
    descriptors,
    loading: loading || loadingStores,
    error,
    refresh,
    uniqueDescriptorValues
  };
}