"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useAppDispatch } from "@/redux/hooks";
import { resetStoreState } from "@/redux/slices/store";

/**
 * Hook to handle session persistence and cleanup
 * Ensures Redux state is properly synchronized with NextAuth session
 */
export function useSessionPersist() {
  const { status } = useSession();
  const dispatch = useAppDispatch();

  useEffect(() => {
    // Handle browser tab visibility changes
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible") {
        // When tab becomes visible, check if session is still valid
        if (status === "unauthenticated") {
          // Clear Redux state if session expired
          dispatch(resetStoreState());
        }
      }
    };

    // Handle storage events (changes from other tabs)
    const handleStorageChange = (e: StorageEvent) => {
      // If Redux persist storage was cleared in another tab
      if (e.key === "persist:root" && e.newValue === null) {
        // Clear local Redux state to match
        dispatch(resetStoreState());
      }
    };

    // Handle page unload - save state
    const handleBeforeUnload = () => {
      // Ensure Redux persist has flushed to storage
      if (typeof window !== "undefined" && window.localStorage) {
        try {
          // Force a sync to ensure state is saved
          const persistRoot = localStorage.getItem("persist:root");
          if (persistRoot) {
            localStorage.setItem("persist:root", persistRoot);
          }
        } catch (e) {
          console.warn("Failed to sync Redux persist state", e);
        }
      }
    };

    // Add event listeners
    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("storage", handleStorageChange);
    window.addEventListener("beforeunload", handleBeforeUnload);

    // Cleanup
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [status, dispatch]);

  // Handle session status changes
  useEffect(() => {
    if (status === "unauthenticated") {
      // Clear any stale Redux state when logged out
      const persistRoot = localStorage.getItem("persist:root");
      if (persistRoot) {
        try {
          const parsed = JSON.parse(persistRoot);
          // Check if there's any store data that shouldn't be there
          if (parsed.store && parsed.store !== "null") {
            dispatch(resetStoreState());
            localStorage.removeItem("persist:root");
          }
        } catch (e) {
          // If parsing fails, clear everything
          dispatch(resetStoreState());
          localStorage.removeItem("persist:root");
        }
      }
    }
  }, [status, dispatch]);

  return { status };
}