import { useEffect, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { 
  fetchStoresByUserId,
  selectStores,
  selectStoresLoading,
  selectProviderStatus,
  clearStoresCache
} from "@/redux/slices/store";

export function useProviderIntegration() {
  const { data: session } = useSession();
  const dispatch = useAppDispatch();
  
  // Get data from Redux store
  const stores = useAppSelector(selectStores);
  const loading = useAppSelector(selectStoresLoading);
  const error = useAppSelector(state => state.store.error);
  const providerStatus = useAppSelector(selectProviderStatus);
  
  // Memoized user ID to prevent unnecessary re-renders
  const userId = useMemo(() => session?.user?.user_id, [session?.user?.user_id]);

  // Fetch stores only when userId changes and we don't have data
  useEffect(() => {
    if (userId && stores.length === 0) {
      dispatch(fetchStoresByUserId(userId));
    }
  }, [userId]); // Remove dispatch from deps, only fetch when userId changes

  // Refresh function - force fetch new data
  const refreshStatus = useCallback(async () => {
    if (userId) {
      // Clear cache to force refresh
      dispatch(clearStoresCache());
      // Add a small delay to ensure any backend operations have completed
      await new Promise(resolve => setTimeout(resolve, 200));
      // Fetch fresh data
      await dispatch(fetchStoresByUserId(userId));
    }
  }, [userId, dispatch]);

  // Memoized convenience getters to prevent object recreation
  const memoizedReturn = useMemo(() => ({
    providerStatus,
    stores,
    loading,
    error,
    refreshStatus,
    // Convenience getters
    isShopifyConnected: providerStatus.shopify,
    isStripeConnected: providerStatus.stripe,
  }), [providerStatus, stores, loading, error, refreshStatus]);

  return memoizedReturn;
}
