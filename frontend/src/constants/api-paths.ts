/**
 * API Endpoints
 * Centralized collection of all API endpoint paths used in the application
 */

// Authentication endpoints
export const AUTH_ENDPOINTS = {
  SIGN_IN: "/auth/login",
  SIGN_UP: "/auth/register",
  REFRESH_TOKEN: "/auth/token",
  FORGOT_PASSWORD: "/auth/forgot-password",
  RESET_PASSWORD: "/auth/reset-password",
  PROFILE: "/auth/profile",
};

// User management endpoints
export const USER_ENDPOINTS = {
  LIST: "/users",
  DETAIL: (id: string) => `/users/${id}`,
  CREATE: "/users",
  UPDATE: (id: string) => `/users/${id}`,
  DELETE: (id: string) => `/users/${id}`,
};

// Linked Store endpoints
export const LINKED_STORE_ENDPOINTS = {
  BY_USER_ID: (userId: string) => `/linked-stores/user/${userId}`,
};

// Alert endpoints
export const ALERT_ENDPOINTS = {
  ALERTS: "/early-warning/alerts",
  ALERTS_BY_TYPE: (type: string) => `/early-warning/alerts?type=${type}`,
  ALERT_OVERVIEW: "/early-warning/alerts/overview",
  ALERTS_BY_SOURCE: "/early-warning/alerts/by-source",
  ALERTS_BY_DESCRIPTOR: "/early-warning/alerts/by-descriptor",
  ALERTS_BY_COUNTRY: "/early-warning/alerts/by-country",
  ALERTS_BY_BANK: "/early-warning/alerts/by-bank",
  ALERTS_BY_OUTCOME: "/early-warning/alerts/by-outcome",
  ALERTS_BY_CARD_TYPE: "/early-warning/alerts/by-card-type",
};

// Block endpoints
export const BLOCK_ENDPOINTS = {
  LIST: "/blocks",
  DETAIL: (id: string) => `/blocks/${id}`,
  CREATE: "/blocks",
  UPDATE: (id: string) => `/blocks/${id}`,
  DELETE: (id: string) => `/blocks/${id}`,
  CHARGEBACK_RATE_CHART: "/blocks/chart/chargeback-rate",
};

// Matched Block endpoints
export const MATCHED_BLOCK_ENDPOINTS = {
  LIST: "/matched-blocks",
  DETAIL: (id: string) => `/matched-blocks/${id}`,
  CREATE: "/matched-blocks",
  UPDATE: (id: string) => `/matched-blocks/${id}`,
  DELETE: (id: string) => `/matched-blocks/${id}`,
  CHARGEBACK_RATE_CHART: "/matched-blocks/chart/chargeback-rate",
};
