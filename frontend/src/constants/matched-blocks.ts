/**
 * Matched Blocks Constants
 * Enums and constants used in the matched blocks functionality
 */

// View modes for matched blocks display
export enum ViewMode {
  OVERVIEW = "overview",
  LIST = "list",
}

// Sorting options for matched blocks
export enum SortBy {
  DATE_ASC = "date_asc",
  DATE_DESC = "date_desc",
  AMOUNT_ASC = "amount_asc",
  AMOUNT_DESC = "amount_desc",
}

// Time range options (synced with backend)
export enum TimeRange {
  TODAY = "today",
  SEVEN_DAYS = "7d",
  THIRTY_DAYS = "30d",
  THREE_MONTHS = "3m",
  SIX_MONTHS = "6m",
  YTD = "ytd",
  ONE_YEAR = "1y",
  ALL_TIME = "all",
  CUSTOM = "custom",
}

// Block types (already exists in service but keeping here for consistency)
export enum BlockType {
  ETHOCA = "ETHOCA",
  RDR = "RDR",
}

// Feedback status (already exists in service but keeping here for consistency)
export enum FeedbackStatus {
  PENDING = "PENDING",
  SENT = "SENT",
  FAILED = "FAILED",
}