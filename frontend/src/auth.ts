import NextAuth from "next-auth";
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials";
import { JWT } from "next-auth/jwt";
import { Session } from "next-auth";

export const { handlers, auth, signIn, signOut } = NextAuth({
  providers: [
    CredentialsProvider({
      name: "Shopify",
      credentials: {
        authData: { type: "text" }, // Field to receive data from Shopify login
        email: { type: "email" }, // Field for traditional email/password login
        password: { type: "password" }, // Field for traditional email/password login
      },
      async authorize(credentials) {
        if (!credentials) {
          throw new Error("No authentication information provided");
        }

        try {
          // Handle OAuth-based authentication (admin login, shopify callback, stripe callback)
          if (credentials.authData) {
            try {
              // Parse the authentication data
              const authData = JSON.parse(credentials.authData as string);

              // Validate required fields - now using linkedStore ID as primary identifier
              if (!authData || !authData.id) {
                throw new Error("Invalid authentication data - missing id");
              }

              // Return data in the format expected by NextAuth
              return {
                id: authData.id, // LinkedStore ID (primary identifier)
                user_id: authData.user_id, // User ID
                name: authData.name || "User",
                email: authData.email || "",
                // Store OAuth provider-related data in separate fields
                shopify_store_id: authData.shopify_store_id,
                domain: authData.domain,
                myshopify_domain: authData.myshopify_domain,
                access_token: authData.access_token,
                provider: authData.provider || "shopify", // OAuth provider (shopify, stripe, etc.)
              };
            } catch (error) {
              throw new Error("Invalid authentication data - parse error");
            }
          }
          
          // Handle traditional email/password authentication (signup, signin pages)
          if (credentials.email && credentials.password) {
            try {
              // Call backend auth service for traditional login
              const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/signin`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  email: credentials.email,
                  password: credentials.password,
                }),
              });

              if (!response.ok) {
                throw new Error('Login failed');
              }

              const data = await response.json();
              
              if (data.success) {
                // Đảm bảo rằng id là LinkedStore ID, không phải user ID
                return {
                  id: data.data.id, // LinkedStore ID
                  name: data.data.fullName,
                  email: data.data.email,
                  access_token: data.data.token.accessToken,
                  provider: "credentials", // Email/password credentials provider
                };
              }
              
              throw new Error('Invalid credentials');
            } catch (error) {
              throw new Error('Authentication failed');
            }
          }
          
          return null;
        } catch (error) {
          throw new Error("Cannot authenticate");
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      if (trigger === "update" && session.user) {
        // Handle session updates
        return { ...token, ...session.user };
      }
      
      if (user) {
        // Initial sign in - copy all data to token
        return {
          ...token,
          id: user.id, // LinkedStore ID (primary identifier)
          user_id: user.user_id, // User ID
          name: user.name,
          email: user.email,
          shopify_store_id: user.shopify_store_id, // Shopify store ID
          domain: user.domain,
          myshopify_domain: user.myshopify_domain,
          access_token: user.access_token,
          provider: user.provider, // Auth provider
        };
      }
      
      // Return existing token during session checks
      return token;
    },
    async session({ session, token }: { session: Session; token: JWT }) {
      if (token) {
        session.user = {
          ...session.user,
          id: token.id as string, // LinkedStore ID (primary identifier)
          user_id: token.user_id as string, // User ID
          name: token.name as string,
          email: token.email as string,
          shopify_store_id: token.shopify_store_id as string, // Shopify store ID
          domain: token.domain as string,
          myshopify_domain: token.myshopify_domain as string,
          access_token: token.access_token as string,
          provider: token.provider as string, // Auth provider
        };
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Handle callback URLs properly
      const callbackUrl = new URL(url.startsWith('http') ? url : `${baseUrl}${url}`);
      
      // If it's a sign-in callback, redirect to the intended destination
      if (callbackUrl.searchParams.get('callbackUrl')) {
        const intendedUrl = callbackUrl.searchParams.get('callbackUrl');
        return intendedUrl?.startsWith('/') ? `${baseUrl}${intendedUrl}` : intendedUrl || `${baseUrl}/dashboard`;
      }
      
      // For auth pages, redirect to dashboard (except for callback pages)
      if (callbackUrl.pathname.includes('/auth/') && !callbackUrl.pathname.includes('/callback')) {
        return `${baseUrl}/dashboard`;
      }
      
      // Default behavior - respect the original URL
      if (url.startsWith(baseUrl)) {
        return url;
      } else if (url.startsWith("/")) {
        return `${baseUrl}${url}`;
      }
      
      return url;
    },
  },
  pages: {
    signIn: "/auth/connect",
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },
  cookies: {
    sessionToken: {
      name: process.env.NEXT_PUBLIC_NODE === "production" 
        ? `__Secure-authjs.session-token`
        : `authjs.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NEXT_PUBLIC_NODE === "production",
        // Remove domain configuration to let browser handle it automatically
        // This prevents cookie domain issues
      },
    },
    callbackUrl: {
      name: `authjs.callback-url`,
      options: {
        sameSite: "lax",
        path: "/",
        secure: process.env.NEXT_PUBLIC_NODE === "production",
      },
    },
    csrfToken: {
      name: process.env.NEXT_PUBLIC_NODE === "production"
        ? `__Host-authjs.csrf-token`
        : `authjs.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NEXT_PUBLIC_NODE === "production",
      },
    },
  },
  events: {
    async signIn() {
    },
    async signOut() {
    },
    async session() {
    },
  },
  debug: process.env.NEXT_PUBLIC_NODE === "development",
});
