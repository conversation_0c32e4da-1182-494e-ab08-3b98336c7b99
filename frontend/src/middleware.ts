import { NextResponse } from "next/server";
import { auth } from "./auth";
// Card setup imports removed - using Shopify Managed Pricing

export default auth(async (req) => {
  const url = req.nextUrl;
  const pathname = url.pathname;

  // Public routes (non-auth pages that anyone can access)
  const isPublicRoutes = [
    "/",
    "/contact",
    "/pricing",
    "/about-us",
    "/privacy-policy",
    "/shipping-policy",
    "/terms-conditions",
    "/refund-policy",
    "/screencast",
  ].includes(pathname);

  // Auth routes
  const isAuthRoutes = pathname.startsWith("/auth/");

  const isAuthenticated = !!req.auth;

  // Protected routes are those that are not public and not auth
  const isProtectedRoute = !isPublicRoutes && !isAuthRoutes;

  // Allow access to public routes for everyone
  if (isPublicRoutes) {
    return NextResponse.next();
  }

  // Handle auth routes
  if (isAuthRoutes) {
    // Redirect to dashboard when logged-in user tries to access /auth/connect
    if (pathname === "/auth/connect") {
      const forceLogout = url.searchParams.get("force_logout");
      if (isAuthenticated && !forceLogout) {
        // Create response that will force logout without confirmation
        const logoutUrl = new URL("/auth/connect", url);
        // Preserve all existing search parameters
        url.searchParams.forEach((value, key) => {
          logoutUrl.searchParams.set(key, value);
        });
        // Add the force_logout parameter
        logoutUrl.searchParams.set("force_logout", "true");
        const logoutResponse = NextResponse.redirect(logoutUrl);

        // Clear all auth-related cookies
        logoutResponse.cookies.delete("authjs.session-token");
        logoutResponse.cookies.delete("__Secure-authjs.session-token");
        logoutResponse.cookies.delete("authjs.csrf-token");
        logoutResponse.cookies.delete("__Host-authjs.csrf-token");
        logoutResponse.cookies.delete("authjs.callback-url");
        logoutResponse.cookies.delete("__Secure-authjs.callback-url");
        // Legacy NextAuth cookie names for compatibility
        logoutResponse.cookies.delete("next-auth.session-token");
        logoutResponse.cookies.delete("__Secure-next-auth.session-token");
        logoutResponse.cookies.delete("next-auth.csrf-token");
        logoutResponse.cookies.delete("__Secure-next-auth.csrf-token");
        logoutResponse.cookies.delete("next-auth.callback-url");
        logoutResponse.cookies.delete("__Secure-next-auth.callback-url");

        // Set headers to prevent caching and signal client to clear storage
        logoutResponse.headers.set(
          "Cache-Control",
          "no-store, no-cache, must-revalidate, proxy-revalidate"
        );
        logoutResponse.headers.set("Pragma", "no-cache");
        logoutResponse.headers.set("Expires", "0");
        logoutResponse.headers.set("Surrogate-Control", "no-store");
        logoutResponse.headers.set("Clear-Site-Data", '"storage"');

        return logoutResponse;
      }
      // If user is authenticated, redirect to dashboard
      if (isAuthenticated) {
        // Redirect to dashboard
        const dashboardRedirect = NextResponse.redirect(
          new URL("/dashboard", url)
        );
        dashboardRedirect.headers.set(
          "Cache-Control",
          "no-store, no-cache, must-revalidate, proxy-revalidate"
        );
        dashboardRedirect.headers.set("Pragma", "no-cache");
        dashboardRedirect.headers.set("Expires", "0");
        dashboardRedirect.headers.set("Surrogate-Control", "no-store");
        return dashboardRedirect;
      }

      // If not authenticated, allow access to /auth/connect
      const response = NextResponse.next();
      response.headers.set(
        "Cache-Control",
        "no-store, no-cache, must-revalidate, proxy-revalidate"
      );
      response.headers.set("Pragma", "no-cache");
      response.headers.set("Expires", "0");
      response.headers.set("Surrogate-Control", "no-store");
      return response;
    }

    const response = NextResponse.next();

    // Prevent caching of auth routes to avoid state issues
    response.headers.set(
      "Cache-Control",
      "no-store, no-cache, must-revalidate, proxy-revalidate"
    );
    response.headers.set("Pragma", "no-cache");
    response.headers.set("Expires", "0");
    response.headers.set("Surrogate-Control", "no-store");

    // If authenticated and trying to access other auth routes, redirect to dashboard
    if (isAuthenticated) {
      // Handle Stripe marketplace OAuth callback
      if (pathname === "/auth/connect/stripe/marketplace") {
        const code = url.searchParams.get("code");
        if (req.auth?.user?.user_id && code) {
          const apiUrl = process.env.NEXT_PUBLIC_API_URL || "https://be.vinast.com";
          const redirectUrl = new URL("/stripe/oauth/callback", apiUrl);
          redirectUrl.searchParams.set("code", code);
          // Backend expects user_id in state parameter as JSON
          const state = JSON.stringify({ user_id: req.auth.user.user_id });
          redirectUrl.searchParams.set("state", state);
          return NextResponse.redirect(redirectUrl.toString());
        }
        return NextResponse.next();
      }

      // Special handling for callback pages - allow them to complete
      if (
        pathname === "/auth/shopify-callback" ||
        pathname === "/auth/connect/callback" ||
        pathname.startsWith("/auth/connect/callback/") ||
        pathname.startsWith("/auth/connect/stripe/marketplace")
      ) {
        return NextResponse.next();
      }

      // For other auth routes, redirect to dashboard
      // Payment setup not needed with Shopify Managed Pricing
      const dashboardRedirect = NextResponse.redirect(
        new URL("/dashboard", url)
      );
      dashboardRedirect.headers.set(
        "Cache-Control",
        "no-store, no-cache, must-revalidate"
      );
      return dashboardRedirect;
    }

    // If not authenticated, allow access to auth routes
    return response;
  }

  // Handle protected routes
  if (isProtectedRoute) {
    // If not authenticated, redirect to auth
    if (!isAuthenticated) {
      const authRedirect = NextResponse.redirect(new URL("/auth/connect", url));
      // Prevent caching of redirects to ensure fresh authentication state
      authRedirect.headers.set(
        "Cache-Control",
        "no-store, no-cache, must-revalidate"
      );
      authRedirect.headers.set("Pragma", "no-cache");
      authRedirect.headers.set("Expires", "0");
      return authRedirect;
    }

    // Payment setup check removed - Shopify handles all billing
    // No need to redirect to setup-billing anymore

    // Set cache headers for faster navigation
    const response = NextResponse.next();
    if (isAuthenticated) {
      // Cache authenticated responses for 60 seconds
      response.headers.set("Cache-Control", "private, max-age=60");
    }
    // If authenticated, allow access
    return response;
  }

  // Default: allow request
  return NextResponse.next();
});

// Payment setup check removed - using Shopify Managed Pricing
// No longer needed as Shopify handles all billing automatically

export const config = {
  // Apply middleware to all routes except static files, images, and api routes
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - static files with common extensions
     */
    "/((?!api|_next/static|_next/image|favicon.ico|chargeback-flow.jpg|.*\\.jpg|.*\\.jpeg|.*\\.png|.*\\.gif|.*\\.svg|.*\\.webp|.*\\.ico|.*\\.css|.*\\.js|.*\\.woff|.*\\.woff2|.*\\.ttf|.*\\.eot|.*\\.mp4).*)",
  ],
};
