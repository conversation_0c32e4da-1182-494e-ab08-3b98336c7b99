import {
    configureStore,
    combineReducers,
    UnknownAction,
} from "@reduxjs/toolkit";
import {
    persistStore,
    persistReducer,
    FLUSH,
    REHYDRATE,
    PAUSE,
    PERSIST,
    PURGE,
    REGISTER,
} from "redux-persist";
import createWebStorage from "redux-persist/lib/storage/createWebStorage";

// Import reducers
import storeReducer from "./slices/store";
import alertInfoReducer from "./slices/alert-info";
import billReducer from "./slices/bill";
import paymentHistoryReducer from "./slices/payment-history";
import stripeReducer from "./slices/stripe";
import uiReducer from "./slices/ui";

// Create storage for Next.js SSR with better error handling
const createNoopStorage = () => {
    return {
        getItem(_key: string) {
            return Promise.resolve(null);
        },
        setItem(_key: string, value: any) {
            return Promise.resolve(value);
        },
        removeItem(_key: string) {
            return Promise.resolve();
        },
    };
};

// Create a more robust storage wrapper
const createSafeStorage = () => {
    if (typeof window === "undefined") {
        return createNoopStorage();
    }
    
    try {
        const storage = createWebStorage("local");
        // Test if storage is working
        const testKey = '__redux_persist_test__';
        storage.setItem(testKey, 'test');
        storage.removeItem(testKey);
        return storage;
    } catch (e) {
        console.warn('LocalStorage not available, using memory storage', e);
        return createNoopStorage();
    }
};

// Use appropriate storage for environment
const reduxStorage = createSafeStorage();

// Action type to reset state
export const RESET_STATE = "RESET_STATE";

export const resetState = () => {
    // Also clear localStorage when resetting
    if (typeof window !== 'undefined') {
        try {
            localStorage.removeItem('persist:root');
            localStorage.removeItem('redux-persist');
        } catch (e) {
            // Silent cleanup
        }
    }
    return { type: RESET_STATE };
};

// Configuration for redux-persist
const persistConfig = {
    key: "root",
    storage: reduxStorage,
    whitelist: ["store", "alertInfo", "bill", "paymentHistory", "stripe", "ui"],
    // Add a serialize/deserialize check for reset state
    transforms: [],
};

// Create root reducer with all combined reducers
const appReducer = combineReducers({
    store: storeReducer,
    alertInfo: alertInfoReducer,
    bill: billReducer,
    paymentHistory: paymentHistoryReducer,
    stripe: stripeReducer,
    ui: uiReducer,
});

// Root reducer handles RESET_STATE action
const rootReducer = (
    state: ReturnType<typeof appReducer> | undefined,
    action: UnknownAction
) => {
    // Reset state when RESET_STATE action is dispatched
    if (action.type === RESET_STATE) {
        // Return completely fresh state
        return appReducer(undefined, action);
    }

    return appReducer(state, action);
};

// Wrap root reducer with persistReducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Create Redux store with persisted reducer
export const store = configureStore({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
            serializableCheck: {
                ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
            },
        }),
});

// Create persistor for use in app
export const persistor = persistStore(store);

// Add a manual purge function
export const forceResetStore = async () => {
    // Clear localStorage first
    if (typeof window !== 'undefined') {
        try {
            localStorage.removeItem('persist:root');
            localStorage.removeItem('redux-persist');
            localStorage.clear();
        } catch (e) {
            // Silent cleanup
        }
    }
    
    // Dispatch reset action
    store.dispatch(resetState());
    
    // Purge persistor
    await persistor.purge();
    await persistor.flush();
};

// Type definitions
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch; 