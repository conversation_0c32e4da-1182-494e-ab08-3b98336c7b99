"use client";

import React, { useEffect, useState } from 'react';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from './store';

export function ReduxProvider({ children }: { children: React.ReactNode }) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // Ensure we're on the client before rendering PersistGate
    setIsClient(true);
  }, []);

  return (
    <Provider store={store}>
      {isClient ? (
        <PersistGate 
          loading={null} 
          persistor={persistor}
          onBeforeLift={() => {
            // Callback when the persisted state has been restored
            console.log('Redux state rehydrated');
          }}
        >
          {children}
        </PersistGate>
      ) : (
        // During SSR, render children without PersistGate
        children
      )}
    </Provider>
  );
} 