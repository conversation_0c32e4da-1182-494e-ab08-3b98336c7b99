import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import { RootState } from '@/redux/store';
import { 
  Bill, 
  CreateBillData, 
  UpdateBillData,
  BillQuery,
  PaginatedBillResponse,
  getBillsByStore,
  getBillById,
  createBill,
  updateBill,
  deleteBill
} from '@/services/bill.service';

interface BillState {
  bills: Bill[];
  loading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  totalItems: number;
}

// Initial state
const initialState: BillState = {
  bills: [],
  loading: true,
  error: null,
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
};

// Async thunks
export const fetchBillsByStore = createAsyncThunk(
  'bill/fetchByStore',
  async ({ storeId, query, token, provider }: { storeId: string; query?: BillQuery; token?: string; provider?: string }, { rejectWithValue }) => {
    try {
      const data = await getBillsByStore(storeId, query, token, provider);
      return data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch bills');
    }
  }
);

export const fetchBillById = createAsyncThunk(
  'bill/fetchById',
  async (id: string, { rejectWithValue }) => {
    try {
      const data = await getBillById(id);
      return data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch bill');
    }
  }
);

export const createBillAsync = createAsyncThunk(
  'bill/create',
  async (data: CreateBillData, { rejectWithValue }) => {
    try {
      const newBill = await createBill(data);
      return newBill;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create bill');
    }
  }
);

export const updateBillAsync = createAsyncThunk(
  'bill/update',
  async ({ id, data }: { id: string; data: UpdateBillData }, { rejectWithValue }) => {
    try {
      const updatedBill = await updateBill(id, data);
      return updatedBill;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update bill');
    }
  }
);

export const deleteBillAsync = createAsyncThunk(
  'bill/delete',
  async (id: string, { rejectWithValue }) => {
    try {
      await deleteBill(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete bill');
    }
  }
);


// Bill slice
const billSlice = createSlice({
  name: 'bill',
  initialState,
  reducers: {
    clearBillError: (state) => {
      state.error = null;
    },
    resetBillState: () => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch bills by store
      .addCase(fetchBillsByStore.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchBillsByStore.fulfilled, (state, action) => {
        state.loading = false;
        const { items, total, page, limit } = action.payload;
        
        // Always replace bills for page navigation
        state.bills = items;
        state.currentPage = page;
        state.totalItems = total;
        state.totalPages = Math.ceil(total / limit);
      })
      .addCase(fetchBillsByStore.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Fetch bill by id
      .addCase(fetchBillById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchBillById.fulfilled, (state, action) => {
        state.loading = false;
        // Add to bills array if not already present
        const exists = state.bills.find(item => item.id === action.payload.id);
        if (!exists) {
          state.bills.push(action.payload);
        }
      })
      .addCase(fetchBillById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Create bill
      .addCase(createBillAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createBillAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.bills.push(action.payload);
      })
      .addCase(createBillAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Update bill
      .addCase(updateBillAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateBillAsync.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.bills.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.bills[index] = action.payload;
        }
      })
      .addCase(updateBillAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Delete bill
      .addCase(deleteBillAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteBillAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.bills = state.bills.filter(item => item.id !== action.payload);
      })
      .addCase(deleteBillAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const { 
  clearBillError, 
  resetBillState 
} = billSlice.actions;

// Export selectors
export const selectBills = (state: RootState) => state.bill.bills;
export const selectBillLoading = (state: RootState) => state.bill.loading;
export const selectBillError = (state: RootState) => state.bill.error;
export const selectBillCurrentPage = (state: RootState) => state.bill.currentPage;
export const selectBillTotalPages = (state: RootState) => state.bill.totalPages;
export const selectBillTotalItems = (state: RootState) => state.bill.totalItems;

// Memoized selector to get bills by LinkedStore id
export const selectBillsByStoreId = createSelector(
  [
    (state: RootState) => state.bill?.bills,
    (_: RootState, storeId: string | undefined) => storeId
  ],
  (bills, storeId) => {
    if (!storeId) return [];
    if (!Array.isArray(bills)) return [];
    return bills.filter(bill => 
      bill.linkedStoreId === storeId
    );
  }
);


// Export reducer
export default billSlice.reducer;

// Export types
export type { Bill, BillState, CreateBillData, UpdateBillData }; 