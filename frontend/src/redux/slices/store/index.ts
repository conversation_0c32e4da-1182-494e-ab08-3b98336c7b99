import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '@/redux/store';
import { LinkedStore, ShopifyService } from '@/services/shopify.service';
import { StripeService } from '@/services/stripe.service';
import LinkedStoreService from '@/services/linked-store.service';

interface StoreState {
  store: LinkedStore | null;
  stores: LinkedStore[]; // All stores for the user
  loading: boolean;
  storesLoading: boolean; // Loading state for stores list
  error: string | null;
  isInitialized: boolean;
  lastFetchTimestamp: number; // Cache timestamp
  userId: string | null; // Cached user ID
}

// Initial state
const initialState: StoreState = {
  store: null,
  stores: [],
  loading: true,
  storesLoading: false,
  error: null,
  isInitialized: false,
  lastFetchTimestamp: 0,
  userId: null,
};

// Cache duration: 10 minutes
const CACHE_DURATION = 10 * 60 * 1000;

// Async thunks

// Fetch all stores for a user with caching
export const fetchStoresByUserId = createAsyncThunk(
  'store/fetchStoresByUserId',
  async (userId: string, { getState, rejectWithValue }) => {
    try {
      const state = getState() as RootState;
      const { stores, lastFetchTimestamp, userId: cachedUserId } = state.store;
      
      // Check if we have valid cached data for the same user
      const now = Date.now();
      const isCacheValid = 
        cachedUserId === userId &&
        stores.length > 0 && 
        (now - lastFetchTimestamp) < CACHE_DURATION;
      
      if (isCacheValid) {
        // Return cached data
        return {
          stores,
          fromCache: true,
          timestamp: lastFetchTimestamp,
          userId
        };
      }

      // Fetch fresh data
      const response = await LinkedStoreService.getLinkedStoresByUserId(userId);
      
      if (response.success) {
        return {
          stores: response.data || [],
          fromCache: false,
          timestamp: now,
          userId
        };
      } else {
        throw new Error(response.message || 'Failed to fetch stores');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch stores');
    }
  }
);

// Existing async thunks
export const fetchStore = createAsyncThunk(
  'store/fetchStore',
  async ({ storeId, provider }: { storeId: string; provider: string }, { rejectWithValue }) => {
    try {
      let storeDetails: LinkedStore;
      
      switch (provider) {
        case 'shopify':
          storeDetails = await ShopifyService.getStoreById(storeId);
          break;
        case 'stripe':
          storeDetails = await StripeService.getStoreById(storeId);
          break;
        default:
          throw new Error(`Unsupported provider: ${provider}`);
      }
      
      return storeDetails;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch store');
    }
  }
);

export const refreshStoreDetails = createAsyncThunk(
  'store/refresh',
  async ({ storeId, provider }: { storeId: string; provider: string }, { rejectWithValue }) => {
    try {
      let updatedStore: LinkedStore;
      
      switch (provider) {
        case 'shopify':
          updatedStore = await ShopifyService.refreshStoreDetails(storeId);
          break;
        case 'stripe':
          updatedStore = await StripeService.refreshStoreDetails(storeId);
          break;
        default:
          throw new Error(`Unsupported provider: ${provider}`);
      }
      
      return updatedStore;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to refresh store details');
    }
  }
);

export const disconnectStore = createAsyncThunk(
  'store/disconnect',
  async ({ storeId, provider }: { storeId: string; provider: string }, { rejectWithValue }) => {
    try {
      switch (provider) {
        case 'shopify':
          await ShopifyService.disconnectStore(storeId);
          break;
        case 'stripe':
          await StripeService.disconnectStore(storeId);
          break;
        default:
          throw new Error(`Unsupported provider: ${provider}`);
      }
      
      return storeId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to disconnect store');
    }
  }
);

// Store slice
const storeSlice = createSlice({
  name: 'store',
  initialState,
  reducers: {
    clearStoreError: (state) => {
      state.error = null;
    },
    resetStoreState: () => {
      return initialState;
    },
    // Manual stores update (e.g., after adding/removing a store)
    setStores: (state, action) => {
      // Map stores to ensure providerStoreId is null instead of undefined
      state.stores = action.payload.map((store: LinkedStore) => ({
        ...store,
        providerStoreId: store.providerStoreId ?? null
      }));
      state.lastFetchTimestamp = Date.now();
    },
    // Add a single store to the list
    addStoreToList: (state, action) => {
      const exists = state.stores.some(s => s.id === action.payload.id);
      if (!exists) {
        const store = {
          ...action.payload,
          providerStoreId: action.payload.providerStoreId ?? null
        };
        state.stores.push(store);
        state.lastFetchTimestamp = Date.now();
      }
    },
    // Remove a store from the list
    removeStoreFromList: (state, action) => {
      state.stores = state.stores.filter(s => s.id !== action.payload);
      state.lastFetchTimestamp = Date.now();
    },
    // Clear stores cache (force refresh on next access)
    clearStoresCache: (state) => {
      state.lastFetchTimestamp = 0;
      state.userId = null; // Force re-validation
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch store
      .addCase(fetchStore.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchStore.fulfilled, (state, action) => {
        state.loading = false;
        state.store = action.payload;
        state.isInitialized = true;
      })
      .addCase(fetchStore.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.isInitialized = true;
      })
      
      // Refresh store details
      .addCase(refreshStoreDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(refreshStoreDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.store = action.payload;
      })
      .addCase(refreshStoreDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Disconnect store
      .addCase(disconnectStore.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(disconnectStore.fulfilled, (state) => {
        state.loading = false;
        state.store = null;
      })
      .addCase(disconnectStore.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Fetch stores by user ID
      .addCase(fetchStoresByUserId.pending, (state) => {
        state.storesLoading = true;
        state.error = null;
      })
      .addCase(fetchStoresByUserId.fulfilled, (state, action) => {
        const { stores, fromCache, timestamp, userId } = action.payload;
        
        // Only update if not from cache or if data changed
        if (!fromCache) {
          // Map stores to ensure providerStoreId is null instead of undefined
          state.stores = stores.map(store => ({
            ...store,
            providerStoreId: store.providerStoreId ?? null
          }));
          state.lastFetchTimestamp = timestamp;
          state.userId = userId;
        }
        
        state.storesLoading = false;
        state.error = null;
      })
      .addCase(fetchStoresByUserId.rejected, (state, action) => {
        state.storesLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const { 
  clearStoreError, 
  resetStoreState,
  setStores,
  addStoreToList,
  removeStoreFromList,
  clearStoresCache
} = storeSlice.actions;

// Export selectors
export const selectStore = (state: RootState) => state.store.store;
export const selectStoreLoading = (state: RootState) => state.store.loading;
export const selectStoreError = (state: RootState) => state.store.error;
export const selectStoreIsInitialized = (state: RootState) => state.store.isInitialized;

// Stores list selectors
export const selectStores = (state: RootState) => state.store.stores;
export const selectStoresLoading = (state: RootState) => state.store.storesLoading;

// Provider status selector
export const selectProviderStatus = (state: RootState) => {
  const stores = state.store.stores;
  
  const status = {
    shopify: false,
    stripe: false,
  };
  
  for (const store of stores) {
    const provider = store.provider.toLowerCase();
    if (provider in status) {
      status[provider as keyof typeof status] = true;
    }
  }
  
  return status;
};

// Store by ID selector
export const selectStoreById = (storeId: string) => (state: RootState) =>
  state.store.stores.find(store => store.id === storeId);

// Stores by provider selector  
export const selectStoresByProvider = (provider: string) => (state: RootState) =>
  state.store.stores.filter(store => 
    store.provider.toLowerCase() === provider.toLowerCase()
  );

// Export reducer
export default storeSlice.reducer;

// Export types
export type { StoreState, LinkedStore }; 