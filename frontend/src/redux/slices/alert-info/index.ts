import { createSlice, createAsyncThunk, PayloadAction, createSelector } from '@reduxjs/toolkit';
import { RootState } from '@/redux/store';
import { 
  AlertInfo, 
  CreateAlertInfoData, 
  UpdateAlertInfoData,
  getAlertInfosByStoreId,
  getAlertInfoById,
  createAlertInfo,
  updateAlertInfo,
  deleteAlertInfo
} from '@/services/alert-info.service';

interface AlertInfoState {
  alertInfos: AlertInfo[];
  loading: boolean;
  error: string | null;
}

// Initial state
const initialState: AlertInfoState = {
  alertInfos: [],
  loading: true,
  error: null,
};

// Async thunks
export const fetchAlertInfosByStore = createAsyncThunk(
  'alertInfo/fetchByStore',
  async ({ storeId, token, provider }: { storeId: string; token?: string; provider?: string }, { rejectWithValue }) => {
    try {
      const data = await getAlertInfosByStoreId(storeId, token, provider);
      return data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch alert info accounts');
    }
  }
);

export const fetchAlertInfoById = createAsyncThunk(
  'alertInfo/fetchById',
  async ({ id, token, provider }: { id: string; token?: string; provider?: string }, { rejectWithValue }) => {
    try {
      const data = await getAlertInfoById(id, token, provider);
      return data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch alert info');
    }
  }
);

export const createAlertInfoAsync = createAsyncThunk(
  'alertInfo/create',
  async ({ data, token, provider }: { data: CreateAlertInfoData; token?: string; provider?: string }, { rejectWithValue }) => {
    try {
      const newAlertInfo = await createAlertInfo(data, token, provider);
      return newAlertInfo;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create alert info');
    }
  }
);

export const updateAlertInfoAsync = createAsyncThunk(
  'alertInfo/update',
  async ({ id, data, token, provider }: { id: string; data: UpdateAlertInfoData; token?: string; provider?: string }, { rejectWithValue }) => {
    try {
      const updatedAlertInfo = await updateAlertInfo(id, data, token, provider);
      return updatedAlertInfo;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update alert info');
    }
  }
);

export const deleteAlertInfoAsync = createAsyncThunk(
  'alertInfo/delete',
  async ({ id, token, provider }: { id: string; token?: string; provider?: string }, { rejectWithValue }) => {
    try {
      await deleteAlertInfo(id, token, provider);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete alert info');
    }
  }
);

// Alert Info slice
const alertInfoSlice = createSlice({
  name: 'alertInfo',
  initialState,
  reducers: {
    clearAlertInfoError: (state) => {
      state.error = null;
    },
    resetAlertInfoState: () => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch alert infos by store
      .addCase(fetchAlertInfosByStore.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAlertInfosByStore.fulfilled, (state, action) => {
        state.loading = false;
        // Merge alert infos instead of replacing
        const newAlertInfos = action.payload;
        newAlertInfos.forEach(newAlertInfo => {
          const existingIndex = state.alertInfos.findIndex(existing => existing.id === newAlertInfo.id);
          if (existingIndex >= 0) {
            // Update existing
            state.alertInfos[existingIndex] = newAlertInfo;
          } else {
            // Add new
            state.alertInfos.push(newAlertInfo);
          }
        });
      })
      .addCase(fetchAlertInfosByStore.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Fetch alert info by id
      .addCase(fetchAlertInfoById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAlertInfoById.fulfilled, (state, action) => {
        state.loading = false;
        // Add to alertInfos array if not already present
        const exists = state.alertInfos.find(item => item.id === action.payload.id);
        if (!exists) {
          state.alertInfos.push(action.payload);
        }
      })
      .addCase(fetchAlertInfoById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Create alert info
      .addCase(createAlertInfoAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createAlertInfoAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.alertInfos.push(action.payload);
      })
      .addCase(createAlertInfoAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Update alert info
      .addCase(updateAlertInfoAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateAlertInfoAsync.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.alertInfos.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.alertInfos[index] = action.payload;
        }
      })
      .addCase(updateAlertInfoAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Delete alert info
      .addCase(deleteAlertInfoAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteAlertInfoAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.alertInfos = state.alertInfos.filter(item => item.id !== action.payload);
      })
      .addCase(deleteAlertInfoAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const { 
  clearAlertInfoError, 
  resetAlertInfoState 
} = alertInfoSlice.actions;

// Export selectors
export const selectAlertInfos = (state: RootState) => state.alertInfo.alertInfos;
export const selectAlertInfoLoading = (state: RootState) => state.alertInfo.loading;
export const selectAlertInfoError = (state: RootState) => state.alertInfo.error;

// Memoized selector to get alert infos by LinkedStore id
export const selectAlertInfosByStoreId = createSelector(
  [
    selectAlertInfos,
    (state: RootState, storeId: string | undefined) => storeId
  ],
  (alertInfos, storeId) => {
    if (!storeId) return [];
    return alertInfos.filter(alertInfo => alertInfo.storeId === storeId);
  }
);


// Export reducer
export default alertInfoSlice.reducer;

// Export types
export type { AlertInfo, AlertInfoState, CreateAlertInfoData, UpdateAlertInfoData }; 