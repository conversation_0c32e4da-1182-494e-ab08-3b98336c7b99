import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface UIState {
  isOpenSidebar: boolean
}

const initialState: UIState = {
  isOpenSidebar: true
}

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setIsOpenSidebar: (state, action: PayloadAction<boolean>) => {
      state.isOpenSidebar = action.payload
    },
    toggleSidebar: (state) => {
      state.isOpenSidebar = !state.isOpenSidebar
    }
  }
})

export const { setIsOpenSidebar, toggleSidebar } = uiSlice.actions
export default uiSlice.reducer