import { createSlice, createAsyncThunk, PayloadAction, createSelector } from '@reduxjs/toolkit';
import { RootState } from '@/redux/store';
import { 
  PaymentHistory, 
  CreatePaymentHistoryData, 
  UpdatePaymentHistoryData,
  PaymentHistoryQuery,
  PaginatedPaymentHistoryResponse,
  getPaymentHistoryByStore,
  getPaymentHistoryById,
  createPaymentHistory,
  updatePaymentHistory,
  deletePaymentHistory
} from '@/services/payment-history.service';

interface PaymentHistoryState {
  paymentHistory: PaymentHistory[];
  loading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  totalItems: number;
}

// Initial state
const initialState: PaymentHistoryState = {
  paymentHistory: [],
  loading: true,
  error: null,
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
};

// Async thunks
export const fetchPaymentHistoryByStore = createAsyncThunk(
  'paymentHistory/fetchByStore',
  async ({ storeId, query, token, provider }: { storeId: string; query?: PaymentHistoryQuery; token?: string; provider?: string }, { rejectWithValue }) => {
    try {
      const data = await getPaymentHistoryByStore(storeId, query, token, provider);
      return data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch payment history');
    }
  }
);

export const fetchPaymentHistoryById = createAsyncThunk(
  'paymentHistory/fetchById',
  async ({ id, token, provider }: { id: string; token?: string; provider?: string }, { rejectWithValue }) => {
    try {
      const data = await getPaymentHistoryById(id, token, provider);
      return data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch payment history');
    }
  }
);

export const createPaymentHistoryAsync = createAsyncThunk(
  'paymentHistory/create',
  async ({ data, token, provider }: { data: CreatePaymentHistoryData; token?: string; provider?: string }, { rejectWithValue }) => {
    try {
      const newPaymentHistory = await createPaymentHistory(data, token, provider);
      return newPaymentHistory;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create payment history');
    }
  }
);

export const updatePaymentHistoryAsync = createAsyncThunk(
  'paymentHistory/update',
  async ({ id, data, token, provider }: { id: string; data: UpdatePaymentHistoryData; token?: string; provider?: string }, { rejectWithValue }) => {
    try {
      const updatedPaymentHistory = await updatePaymentHistory(id, data, token, provider);
      return updatedPaymentHistory;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update payment history');
    }
  }
);

export const deletePaymentHistoryAsync = createAsyncThunk(
  'paymentHistory/delete',
  async ({ id, token, provider }: { id: string; token?: string; provider?: string }, { rejectWithValue }) => {
    try {
      await deletePaymentHistory(id, token, provider);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete payment history');
    }
  }
);


// Payment History slice
const paymentHistorySlice = createSlice({
  name: 'paymentHistory',
  initialState,
  reducers: {
    clearPaymentHistoryError: (state) => {
      state.error = null;
    },
    resetPaymentHistoryState: (state) => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch payment history by store
      .addCase(fetchPaymentHistoryByStore.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPaymentHistoryByStore.fulfilled, (state, action) => {
        state.loading = false;
        const { items, total, page, limit } = action.payload;
        
        // Always replace payment history for page navigation
        state.paymentHistory = items;
        state.currentPage = page;
        state.totalItems = total;
        state.totalPages = Math.ceil(total / limit);
      })
      .addCase(fetchPaymentHistoryByStore.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Fetch payment history by id
      .addCase(fetchPaymentHistoryById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPaymentHistoryById.fulfilled, (state, action) => {
        state.loading = false;
        // Add to paymentHistory array if not already present
        const exists = state.paymentHistory.find(item => item.id === action.payload.id);
        if (!exists) {
          state.paymentHistory.push(action.payload);
        }
      })
      .addCase(fetchPaymentHistoryById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Create payment history
      .addCase(createPaymentHistoryAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createPaymentHistoryAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.paymentHistory.push(action.payload);
      })
      .addCase(createPaymentHistoryAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Update payment history
      .addCase(updatePaymentHistoryAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updatePaymentHistoryAsync.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.paymentHistory.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.paymentHistory[index] = action.payload;
        }
      })
      .addCase(updatePaymentHistoryAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Delete payment history
      .addCase(deletePaymentHistoryAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deletePaymentHistoryAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.paymentHistory = state.paymentHistory.filter(item => item.id !== action.payload);
      })
      .addCase(deletePaymentHistoryAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const { 
  clearPaymentHistoryError, 
  resetPaymentHistoryState 
} = paymentHistorySlice.actions;

// Export selectors
export const selectPaymentHistory = (state: RootState) => state.paymentHistory.paymentHistory;
export const selectPaymentHistoryLoading = (state: RootState) => state.paymentHistory.loading;
export const selectPaymentHistoryError = (state: RootState) => state.paymentHistory.error;
export const selectPaymentHistoryCurrentPage = (state: RootState) => state.paymentHistory.currentPage;
export const selectPaymentHistoryTotalPages = (state: RootState) => state.paymentHistory.totalPages;
export const selectPaymentHistoryTotalItems = (state: RootState) => state.paymentHistory.totalItems;

// Memoized selector to get payment history by LinkedStore id
export const selectPaymentHistoryByStoreId = createSelector(
  [
    (state: RootState) => state.paymentHistory?.paymentHistory,
    (_: RootState, storeId: string | undefined) => storeId
  ],
  (paymentHistory, storeId) => {
    if (!storeId) return [];
    if (!Array.isArray(paymentHistory)) return [];
    return paymentHistory.filter(payment => 
      payment.linkedStoreId === storeId
    );
  }
);


// Export reducer
export default paymentHistorySlice.reducer;

// Export types
export type { PaymentHistory, PaymentHistoryState, CreatePaymentHistoryData, UpdatePaymentHistoryData, PaymentHistoryQuery }; 