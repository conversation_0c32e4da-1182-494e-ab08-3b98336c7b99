import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '@/redux/store';
import { 
  StripeAccount, 
  CreateStripeAccountData, 
  UpdateStripeAccountData,
  getStripeAccountByStore,
  createStripeAccount,
  updateStripeAccount,
  deleteStripeAccount,
  createSetupIntent,
  confirmCardSetup,
  getCardSetupStatus,
  createPaymentIntent,
  confirmPayment,
  type SetupIntentResponse,
  type ConfirmCardSetupData,
  type PaymentIntentResponse,
  type CreatePaymentData,
  type ConfirmPaymentData
} from '@/services/stripe.service';

interface StripeState {
  stripeAccount: StripeAccount | null;
  loading: boolean;
  error: string | null;
  // Card setup state
  setupIntent: {
    clientSecret: string | null;
    customerId: string | null;
    status: 'idle' | 'loading' | 'succeeded' | 'failed';
    error: string | null;
  };
  cardSetup: {
    isLoading: boolean;
    isCompleted: boolean;
    error: string | null;
  };
  cardInfo: {
    setupCompleted: boolean;
    last4?: string;
    brand?: string;
    cardholderName?: string;
  } | null;
  // Payment Intent state
  paymentIntent: {
    clientSecret: string | null;
    paymentIntentId: string | null;
    status: 'idle' | 'loading' | 'succeeded' | 'failed';
    error: string | null;
  };
  payment: {
    isLoading: boolean;
    isCompleted: boolean;
    paymentId: string | null;
    error: string | null;
  };
}

// Initial state
const initialState: StripeState = {
  stripeAccount: null,
  loading: true,
  error: null,
  setupIntent: {
    clientSecret: null,
    customerId: null,
    status: 'idle',
    error: null,
  },
  cardSetup: {
    isLoading: false,
    isCompleted: false,
    error: null,
  },
  cardInfo: null,
  paymentIntent: {
    clientSecret: null,
    paymentIntentId: null,
    status: 'idle',
    error: null,
  },
  payment: {
    isLoading: false,
    isCompleted: false,
    paymentId: null,
    error: null,
  },
};

// Async thunks
export const fetchStripeAccountByStore = createAsyncThunk(
  'stripe/fetchAccountByStore',
  async (storeId: string, { rejectWithValue }) => {
    try {
      const data = await getStripeAccountByStore(storeId);
      return data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch Stripe account');
    }
  }
);


export const createStripeAccountAsync = createAsyncThunk(
  'stripe/createAccount',
  async (data: CreateStripeAccountData, { rejectWithValue }) => {
    try {
      const newStripeAccount = await createStripeAccount(data);
      return newStripeAccount;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create Stripe account');
    }
  }
);

export const updateStripeAccountAsync = createAsyncThunk(
  'stripe/updateAccount',
  async ({ id, data }: { id: string; data: UpdateStripeAccountData }, { rejectWithValue }) => {
    try {
      const updatedStripeAccount = await updateStripeAccount(id, data);
      return updatedStripeAccount;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update Stripe account');
    }
  }
);

export const deleteStripeAccountAsync = createAsyncThunk(
  'stripe/deleteAccount',
  async (id: string, { rejectWithValue }) => {
    try {
      const result = await deleteStripeAccount(id);
      return result;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete Stripe account');
    }
  }
);

// Card setup async thunks
export const createSetupIntentAsync = createAsyncThunk(
  'stripe/createSetupIntent',
  async ({ storeId, provider }: { storeId: string; provider?: string }, { rejectWithValue }) => {
    try {
      const response = await createSetupIntent(storeId, provider);
      return response;
    } catch (error: any) {
      return rejectWithValue(
        error?.response?.data?.message || error?.message || 'Failed to create setup intent'
      );
    }
  }
);

export const confirmCardSetupAsync = createAsyncThunk(
  'stripe/confirmCardSetup',
  async (data: ConfirmCardSetupData, { rejectWithValue }) => {
    try {
      const response = await confirmCardSetup(data);
      return response;
    } catch (error: any) {
      return rejectWithValue(
        error?.response?.data?.message || error?.message || 'Failed to confirm card setup'
      );
    }
  }
);

export const getCardSetupStatusAsync = createAsyncThunk(
  'stripe/getCardSetupStatus',
  async (storeId: string, { rejectWithValue }) => {
    try {
      const response = await getCardSetupStatus(storeId);
      return response;
    } catch (error: any) {
      return rejectWithValue(
        error?.response?.data?.message || error?.message || 'Failed to get card setup status'
      );
    }
  }
);

// Payment Intent async thunks
export const createPaymentIntentAsync = createAsyncThunk(
  'stripe/createPaymentIntent',
  async (data: CreatePaymentData, { rejectWithValue }) => {
    try {
      const response = await createPaymentIntent(data);
      return response;
    } catch (error: any) {
      return rejectWithValue(
        error?.response?.data?.message || error?.message || 'Failed to create payment intent'
      );
    }
  }
);

export const confirmPaymentAsync = createAsyncThunk(
  'stripe/confirmPayment',
  async (data: ConfirmPaymentData, { rejectWithValue }) => {
    try {
      const response = await confirmPayment(data);
      return response;
    } catch (error: any) {
      return rejectWithValue(
        error?.response?.data?.message || error?.message || 'Failed to confirm payment'
      );
    }
  }
);




// Stripe slice
const stripeSlice = createSlice({
  name: 'stripe',
  initialState,
  reducers: {
    setStripeAccount: (state, action: PayloadAction<StripeAccount | null>) => {
      state.stripeAccount = action.payload;
    },
    clearStripeError: (state) => {
      state.error = null;
    },
    resetStripeState: () => {
      return initialState;
    },
    // Card setup actions
    resetSetupIntent: (state) => {
      state.setupIntent = {
        clientSecret: null,
        customerId: null,
        status: 'idle',
        error: null,
      };
    },
    resetCardSetup: (state) => {
      state.cardSetup = {
        isLoading: false,
        isCompleted: false,
        error: null,
      };
    },
    setCardSetupError: (state, action: PayloadAction<string>) => {
      state.cardSetup.error = action.payload;
    },
    clearCardSetupError: (state) => {
      state.cardSetup.error = null;
    },
    // Payment actions
    resetPaymentIntent: (state) => {
      state.paymentIntent = {
        clientSecret: null,
        paymentIntentId: null,
        status: 'idle',
        error: null,
      };
    },
    resetPayment: (state) => {
      state.payment = {
        isLoading: false,
        isCompleted: false,
        paymentId: null,
        error: null,
      };
    },
    setPaymentError: (state, action: PayloadAction<string>) => {
      state.payment.error = action.payload;
    },
    clearPaymentError: (state) => {
      state.payment.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Stripe account by store
      .addCase(fetchStripeAccountByStore.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchStripeAccountByStore.fulfilled, (state, action) => {
        state.loading = false;
        state.stripeAccount = action.payload;
      })
      .addCase(fetchStripeAccountByStore.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Create Stripe account
      .addCase(createStripeAccountAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createStripeAccountAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.stripeAccount = action.payload;
      })
      .addCase(createStripeAccountAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Update Stripe account
      .addCase(updateStripeAccountAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateStripeAccountAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.stripeAccount = action.payload;
      })
      .addCase(updateStripeAccountAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Delete Stripe account
      .addCase(deleteStripeAccountAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteStripeAccountAsync.fulfilled, (state) => {
        state.loading = false;
        state.stripeAccount = null;
      })
      .addCase(deleteStripeAccountAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Create Setup Intent
      .addCase(createSetupIntentAsync.pending, (state) => {
        state.setupIntent.status = 'loading';
        state.setupIntent.error = null;
      })
      .addCase(createSetupIntentAsync.fulfilled, (state, action) => {
        state.setupIntent.status = 'succeeded';
        state.setupIntent.clientSecret = action.payload.clientSecret;
        state.setupIntent.customerId = action.payload.customerId;
        state.setupIntent.error = null;
      })
      .addCase(createSetupIntentAsync.rejected, (state, action) => {
        state.setupIntent.status = 'failed';
        state.setupIntent.error = action.payload as string;
      })

      // Confirm Card Setup
      .addCase(confirmCardSetupAsync.pending, (state) => {
        state.cardSetup.isLoading = true;
        state.cardSetup.error = null;
      })
      .addCase(confirmCardSetupAsync.fulfilled, (state) => {
        state.cardSetup.isLoading = false;
        state.cardSetup.isCompleted = true;
        state.cardSetup.error = null;
      })
      .addCase(confirmCardSetupAsync.rejected, (state, action) => {
        state.cardSetup.isLoading = false;
        state.cardSetup.isCompleted = false;
        state.cardSetup.error = action.payload as string;
      })

      // Get Card Setup Status
      .addCase(getCardSetupStatusAsync.pending, (state) => {
        // Don't set loading state for status checks
      })
      .addCase(getCardSetupStatusAsync.fulfilled, (state, action) => {
        state.cardInfo = action.payload;
        if (action.payload.setupCompleted) {
          state.cardSetup.isCompleted = true;
        }
      })
      .addCase(getCardSetupStatusAsync.rejected, (state, action) => {
        // Don't update error state for status checks unless critical
      })

      // Create Payment Intent
      .addCase(createPaymentIntentAsync.pending, (state) => {
        state.paymentIntent.status = 'loading';
        state.paymentIntent.error = null;
      })
      .addCase(createPaymentIntentAsync.fulfilled, (state, action) => {
        state.paymentIntent.status = 'succeeded';
        state.paymentIntent.clientSecret = action.payload.clientSecret;
        state.paymentIntent.paymentIntentId = action.payload.paymentIntentId;
        state.paymentIntent.error = null;
      })
      .addCase(createPaymentIntentAsync.rejected, (state, action) => {
        state.paymentIntent.status = 'failed';
        state.paymentIntent.error = action.payload as string;
      })

      // Confirm Payment
      .addCase(confirmPaymentAsync.pending, (state) => {
        state.payment.isLoading = true;
        state.payment.error = null;
      })
      .addCase(confirmPaymentAsync.fulfilled, (state, action) => {
        state.payment.isLoading = false;
        state.payment.isCompleted = true;
        state.payment.paymentId = action.payload.paymentId || null;
        state.payment.error = null;
      })
      .addCase(confirmPaymentAsync.rejected, (state, action) => {
        state.payment.isLoading = false;
        state.payment.isCompleted = false;
        state.payment.error = action.payload as string;
      });
  },
});

// Export actions
export const { 
  setStripeAccount, 
  clearStripeError, 
  resetStripeState,
  resetSetupIntent,
  resetCardSetup,
  setCardSetupError,
  clearCardSetupError,
  resetPaymentIntent,
  resetPayment,
  setPaymentError,
  clearPaymentError
} = stripeSlice.actions;

// Export selectors
export const selectStripeAccount = (state: RootState) => state.stripe.stripeAccount;
export const selectStripeLoading = (state: RootState) => state.stripe.loading;
export const selectStripeError = (state: RootState) => state.stripe.error;

// Card setup selectors
export const selectSetupIntent = (state: RootState) => state.stripe.setupIntent;
export const selectCardSetup = (state: RootState) => state.stripe.cardSetup;
export const selectCardInfo = (state: RootState) => state.stripe.cardInfo;
export const selectSetupIntentLoading = (state: RootState) => state.stripe.setupIntent.status === 'loading';
export const selectCardSetupLoading = (state: RootState) => state.stripe.cardSetup.isLoading;
export const selectCardSetupCompleted = (state: RootState) => state.stripe.cardSetup.isCompleted;

// Payment selectors
export const selectPaymentIntent = (state: RootState) => state.stripe.paymentIntent;
export const selectPayment = (state: RootState) => state.stripe.payment;
export const selectPaymentIntentLoading = (state: RootState) => state.stripe.paymentIntent.status === 'loading';
export const selectPaymentLoading = (state: RootState) => state.stripe.payment.isLoading;
export const selectPaymentCompleted = (state: RootState) => state.stripe.payment.isCompleted;

// Export reducer
export default stripeSlice.reducer;

// Export types
export type { StripeAccount, StripeState, CreateStripeAccountData, UpdateStripeAccountData }; 