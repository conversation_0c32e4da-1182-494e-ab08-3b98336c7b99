"use client";

import React, { useState, useEffect } from 'react';
import { useStripe, useElements, CardElement } from '@stripe/react-stripe-js';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CreditCard, Shield, Lock } from 'lucide-react';
import { createSetupIntent, confirmCardSetup, type CardData } from '@/services/stripe.service';

interface CardSetupFormProps {
  storeId: string;
  userEmail: string;
  provider?: string;
  onSuccess: () => void;
  onError: (error: string) => void;
}

export default function CardSetupForm({ storeId, userEmail, provider, onSuccess, onError }: CardSetupFormProps) {
  const stripe = useStripe();
  const elements = useElements();

  const [isLoading, setIsLoading] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [customerId, setCustomerId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [cardholderName, setCardholderName] = useState('');
  const [billingEmail, setBillingEmail] = useState(userEmail);
  const [retryAttempt, setRetryAttempt] = useState(0);

  // Debug Stripe loading
  useEffect(() => {
  }, [stripe, elements, clientSecret]);

  // Initialize setup intent
  useEffect(() => {
    const initializeSetupIntent = async () => {
      let retryCount = 0;
      const maxRetries = 3;
      const retryDelay = 1000; // 1 second
      setIsInitializing(true);

      const attemptInitialization = async (): Promise<void> => {
        try {
          setRetryAttempt(retryCount);
          console.log('Creating setup intent for storeId:', storeId, 'provider:', provider);
          const response = await createSetupIntent(storeId, provider);
          console.log('Setup intent response:', response);
          setClientSecret(response.clientSecret);
          setCustomerId(response.customerId);
          // Clear any previous errors on success
          setError(null);
          setIsInitializing(false);
        } catch (error: any) {
          console.error('Setup intent error:', error);
          console.error('Error response:', error?.response?.data);
          
          const isRetryableError = 
            error?.response?.status === 404 || 
            error?.response?.status === 500 ||
            error?.response?.status === 502 ||
            error?.response?.status === 503;

          if (isRetryableError && retryCount < maxRetries - 1) {
            retryCount++;
            setRetryAttempt(retryCount);
            await new Promise(resolve => setTimeout(resolve, retryDelay));
            return attemptInitialization();
          } else {
            // Only call onError for permanent failures or after all retries exhausted
            const errorMessage = error?.response?.data?.message || error?.message || 'Failed to initialize card setup';
            setError(errorMessage);
            setIsInitializing(false);
            onError(errorMessage);
          }
        }
      };

      await attemptInitialization();
    };

    initializeSetupIntent();
  }, [storeId, provider, onError]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements || !clientSecret) {
      return;
    }

    if (!cardholderName.trim()) {
      setError('Cardholder name is required');
      return;
    }

    if (!billingEmail.trim()) {
      setError('Billing email is required');
      return;
    }

    setIsLoading(true);
    setError(null);

    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      setError('Card element not found');
      setIsLoading(false);
      return;
    }

    try {
      // Confirm the setup intent
      const { error: stripeError, setupIntent } = await stripe.confirmCardSetup(clientSecret, {
        payment_method: {
          card: cardElement,
          billing_details: {
            name: cardholderName,
            email: billingEmail,
          },
        },
      });

      if (stripeError) {
        throw new Error(stripeError.message || 'Card setup failed');
      }

      if (setupIntent.status === 'succeeded' && setupIntent.payment_method) {
        
        // Get payment method ID safely
        const paymentMethodId = typeof setupIntent.payment_method === 'string' 
          ? setupIntent.payment_method 
          : setupIntent.payment_method.id;


        // Handle card data - sometimes Stripe returns just the ID, sometimes the full object
        let cardData: CardData;
        
        if (typeof setupIntent.payment_method === 'object' && setupIntent.payment_method.card) {
          // Full payment method object with card details
          const paymentMethod = setupIntent.payment_method as any;
          cardData = {
            last4: paymentMethod.card.last4 || 'Unknown',
            brand: paymentMethod.card.brand || 'Unknown',
            country: paymentMethod.card.country || 'Unknown',
            cardholderName,
            billingEmail,
          };
        } else {
          // Only payment method ID received, use fallback data
          cardData = {
            last4: 'Unknown', // Will be updated by backend when it fetches the payment method
            brand: 'Unknown',
            country: 'Unknown',
            cardholderName,
            billingEmail,
          };
        }


        // Save card info to backend
        await confirmCardSetup({
          storeId,
          paymentMethodId,
          cardData,
        });

        onSuccess();
      } else {
        throw new Error('Setup intent was not successful');
      }
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || error?.message || 'Card setup failed';
      setError(errorMessage);
      onError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#FFFFFF',
        fontFamily: 'Outfit, system-ui, sans-serif',
        '::placeholder': {
          color: '#71767B',
        },
      },
      invalid: {
        color: '#ff6b6b',
      },
    },
    hidePostalCode: false,
  };

  return (
    <Card className="w-full max-w-md mx-auto bg-[#16181C] border-[#2F3336] shadow-lg">
      <CardHeader className="text-center space-y-2 pb-4">
        <div className="flex items-center justify-center w-12 h-12 bg-[#1D9BF0]/10 rounded-full mx-auto">
          <CreditCard className="w-6 h-6 text-[#1D9BF0]" />
        </div>
        <CardTitle className="text-xl font-semibold text-white">
          Complete Billing Setup
        </CardTitle>
        <p className="text-[#71767B] text-sm">
          Enter your card details to activate your Chargeback account
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {error && (
          <Alert variant="destructive" className="bg-red-950/30 border-red-900 text-red-400">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Billing Email */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-white">
              Email
            </label>
            <Input
              type="email"
              value={billingEmail}
              onChange={(e) => setBillingEmail(e.target.value)}
              className="bg-[#16181C] border-[#2F3336] text-white focus:border-[#1D9BF0] focus:ring-2 focus:ring-[#1D9BF0]/20"
              placeholder="<EMAIL>"
              disabled={isLoading}
              required
            />
          </div>

          {/* Card Information */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-white">
              Card information
            </label>
            <div className="relative p-3 bg-[#16181C] border border-[#2F3336] rounded-lg focus-within:border-[#1D9BF0] focus-within:ring-2 focus-within:ring-[#1D9BF0]/20 transition-all duration-200">
              {isInitializing ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="w-5 h-5 animate-spin text-[#1D9BF0] mr-2" />
                  <span className="text-[#71767B] text-sm">Initializing payment form...</span>
                </div>
              ) : !stripe || !elements ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="w-5 h-5 animate-spin text-[#1D9BF0] mr-2" />
                  <span className="text-[#71767B] text-sm">Loading payment form...</span>
                </div>
              ) : (
                <CardElement options={cardElementOptions} />
              )}
            </div>
          </div>

          {/* Cardholder Name */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-white">
              Cardholder name
            </label>
            <Input
              type="text"
              value={cardholderName}
              onChange={(e) => setCardholderName(e.target.value)}
              className="bg-[#16181C] border-[#2F3336] text-white focus:border-[#1D9BF0] focus:ring-2 focus:ring-[#1D9BF0]/20"
              placeholder="Full name on card"
              disabled={isLoading}
              required
            />
          </div>

          {/* Security Notice */}
          <div className="flex items-center space-x-2 p-3 bg-[#1D9BF0]/5 border border-[#1D9BF0]/20 rounded-lg">
            <Shield className="w-4 h-4 text-[#1D9BF0] flex-shrink-0" />
            <p className="text-xs text-[#71767B]">
              Your payment information is secured by Stripe. We don&apos;t store your card details.
            </p>
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={!stripe || !clientSecret || isLoading || isInitializing}
            className="w-full cursor-pointer bg-[#1D9BF0] hover:bg-[#1A8CD8] text-white font-medium h-12 text-base disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin mr-2" />
                Processing...
              </>
            ) : isInitializing ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin mr-2" />
                Initializing...
              </>
            ) : (
              <>
                <Lock className="w-4 h-4 mr-2" />
                Save card
              </>
            )}
          </Button>
        </form>

        {/* Footer Text */}
        <div className="text-center space-y-2">
          <p className="text-xs text-[#71767B]">
            By saving your payment information, you allow Chargeback to charge you for future payments in accordance with their terms.
          </p>
          <div className="flex items-center justify-center space-x-1">
            <span className="text-xs text-[#71767B]">Powered by</span>
            <span className="text-xs font-medium text-[#1D9BF0]">stripe</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 