"use client";

import React from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';

// Load Stripe with publishable key
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface StripeProviderProps {
  children: React.ReactNode;
  options?: any;
}

export default function StripeProvider({ children, options }: StripeProviderProps) {
  const elementsOptions = {
    appearance: {
      theme: 'night' as const,
      variables: {
        colorPrimary: '#1D9BF0',
        colorBackground: '#000000',
        colorText: '#FFFFFF',
        colorDanger: '#ff6b6b',
        fontFamily: 'Outfit, system-ui, sans-serif',
        spacingUnit: '4px',
        borderRadius: '8px',
      },
      rules: {
        '.Input': {
          backgroundColor: '#16181C',
          border: '1px solid #2F3336',
          color: '#FFFFFF',
        },
        '.Input:focus': {
          border: '1px solid #1D9BF0',
          boxShadow: '0 0 0 2px rgba(29, 155, 240, 0.2)',
        },
        '.Label': {
          color: '#FFFFFF',
          fontSize: '14px',
          fontWeight: '500',
        },
      },
    },
    ...options,
  };

  return (
    <Elements stripe={stripePromise} options={elementsOptions}>
      {children}
    </Elements>
  );
} 