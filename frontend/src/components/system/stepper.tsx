"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface StepperContextValue {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  totalSteps: number;
}

const StepperContext = React.createContext<StepperContextValue>({
  activeStep: 1,
  setActiveStep: () => null,
  totalSteps: 0,
});

export const Stepper = ({
  activeStep = 1,
  children,
  className,
  ...props
}: {
  activeStep?: number;
  children: React.ReactNode;
  className?: string;
} & React.HTMLAttributes<HTMLDivElement>) => {
  const [currentStep, setCurrentStep] = React.useState(activeStep);
  const childrenArray = React.Children.toArray(children);
  const totalSteps = childrenArray.length;

  return (
    <StepperContext.Provider
      value={{
        activeStep: currentStep,
        setActiveStep: setCurrentStep,
        totalSteps,
      }}
    >
      <div className={cn("w-full", className)} {...props}>
        {childrenArray}
      </div>
    </StepperContext.Provider>
  );
};

export const Step = ({
  children,
  className,
  index,
  ...props
}: {
  children: React.ReactNode;
  className?: string;
  index: number;
} & React.HTMLAttributes<HTMLDivElement>) => {
  const { activeStep } = React.useContext(StepperContext);

  return (
    <div
      className={cn(
        "transition-opacity duration-300",
        activeStep === index ? "opacity-100" : "hidden opacity-0",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

export const StepIndicator = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  const { activeStep, totalSteps } = React.useContext(StepperContext);

  return (
    <div
      className={cn("flex items-center justify-center gap-1", className)}
      {...props}
    >
      {Array.from({ length: totalSteps }).map((_, index) => {
        const stepIndex = index + 1;
        return (
          <div
            key={index}
            className={cn(
              "h-2 w-2 rounded-full transition-all duration-300",
              activeStep === stepIndex
                ? "scale-125 bg-primary"
                : "bg-neutral-700"
            )}
          />
        );
      })}
    </div>
  );
};

export const StepperNav = ({
  children,
  className,
  ...props
}: {
  children?:
    | ((context: StepperContextValue) => React.ReactNode)
    | React.ReactNode;
  className?: string;
} & React.HTMLAttributes<HTMLDivElement>) => {
  const { activeStep, setActiveStep, totalSteps } =
    React.useContext(StepperContext);

  const goToNextStep = () => {
    setActiveStep((prev) => Math.min(prev + 1, totalSteps));
  };

  const goToPrevStep = () => {
    setActiveStep((prev) => Math.max(prev - 1, 1));
  };

  const contextValue = { activeStep, setActiveStep, totalSteps };

  return (
    <div
      className={cn("flex items-center justify-between mt-4", className)}
      {...props}
    >
      {typeof children === "function"
        ? children(contextValue)
        : children || (
            <>
              <button
                onClick={goToPrevStep}
                disabled={activeStep === 1}
                className={cn(
                  "px-4 py-2 rounded-md text-white",
                  activeStep === 1
                    ? "bg-neutral-700 cursor-not-allowed opacity-50"
                    : "bg-primary hover:bg-primary/90"
                )}
              >
                Previous
              </button>
              <button
                onClick={goToNextStep}
                disabled={activeStep === totalSteps}
                className={cn(
                  "px-4 py-2 rounded-md text-white",
                  activeStep === totalSteps
                    ? "bg-neutral-700 cursor-not-allowed opacity-50"
                    : "bg-primary hover:bg-primary/90"
                )}
              >
                {activeStep === totalSteps - 1 ? "Finish" : "Next"}
              </button>
            </>
          )}
    </div>
  );
};

export function useStepperContext() {
  const context = React.useContext(StepperContext);
  if (!context) {
    throw new Error("useStepperContext must be used within a Stepper");
  }
  return context;
}
