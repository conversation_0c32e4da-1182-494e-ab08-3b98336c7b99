"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Clock, CreditCard, Play, ExternalLink } from "lucide-react";
import { billingService } from "@/services/billing.service";
import { toast } from "sonner";

interface SubscriptionActivationProps {
  storeId: string;
  planName: string;
  cappedAmount: number;
  currency: string;
  accessToken: string;
  onActivationSuccess?: () => void;
}

export default function SubscriptionActivation({
  storeId,
  planName,
  cappedAmount,
  currency,
  accessToken,
  onActivationSuccess
}: SubscriptionActivationProps) {
  const [isActivating, setIsActivating] = useState(false);

  const handleActivateSubscription = async () => {
    setIsActivating(true);
    try {
      const response = await billingService.createSubscription(storeId, accessToken);
      
      if (response.success && response.confirmationUrl) {
        // Redirect to Shopify for subscription confirmation
        window.location.href = response.confirmationUrl;
      } else {
        throw new Error(response.error || 'Failed to create subscription');
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to activate subscription. Please try again.");
    } finally {
      setIsActivating(false);
    }
  };

  const formattedAmount = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2
  }).format(cappedAmount / 100);

  return (
    <Card className="bg-blue-900/10 border-blue-900/20">
      <CardHeader>
        <div className="flex items-start gap-3">
          <div className="p-2 bg-blue-900/20 rounded-lg">
            <Clock className="h-6 w-6 text-blue-400" />
          </div>
          <div className="flex-1">
            <CardTitle className="text-xl text-white">Subscription Setup Required</CardTitle>
            <CardDescription className="text-blue-200/80 mt-1">
              Activate your {planName} to start processing chargebacks
            </CardDescription>
          </div>
          <Badge variant="outline" className="bg-blue-900/20 text-blue-100 border-blue-900/30">
            Pending
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Plan Details */}
        <div className="bg-black/20 rounded-lg p-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-semibold text-white">{planName}</h4>
            <span className="text-2xl font-bold text-white">{formattedAmount}</span>
          </div>
          <div className="text-sm text-blue-200/80">
            Usage-based billing • Monthly cap: {formattedAmount}
          </div>
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-2 p-2 bg-yellow-900/20 border border-yellow-900/40 rounded text-xs">
              <span className="text-yellow-100">🧪 Test Mode: No actual charges will be made</span>
            </div>
          )}
        </div>

        {/* What You Get */}
        <div className="space-y-3">
          <h4 className="font-semibold text-white">What you get:</h4>
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <span className="text-sm text-white">Unlimited chargeback processing</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <span className="text-sm text-white">Pay only for what you use</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <span className="text-sm text-white">Monthly spending cap protection</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <span className="text-sm text-white">Integrated with Shopify billing</span>
            </div>
          </div>
        </div>

        {/* How It Works */}
        <div className="bg-gradient-to-r from-green-900/20 to-blue-900/20 border border-green-900/30 rounded-lg p-4">
          <h4 className="font-semibold text-white mb-2">How it works:</h4>
          <div className="space-y-1 text-sm text-muted-foreground">
            <div className="flex items-start gap-2">
              <span className="text-green-400 font-semibold">1.</span>
              <span>Click &quot;Activate Subscription&quot; below</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-green-400 font-semibold">2.</span>
              <span>Approve the subscription in Shopify</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-green-400 font-semibold">3.</span>
              <span>Start processing chargebacks immediately</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-green-400 font-semibold">4.</span>
              <span>
                {process.env.NODE_ENV === 'development' 
                  ? 'Test mode - no actual billing will occur' 
                  : 'Pay monthly via your Shopify invoice'
                }
              </span>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <Button
            onClick={handleActivateSubscription}
            disabled={isActivating}
            className="cursor-pointer flex-1 bg-[#1D9BF0] hover:bg-[#1A8CD8] text-white"
            size="lg"
          >
            {isActivating ? (
              <>
                <Play className="w-4 h-4 mr-2 animate-spin" />
                Activating...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                {process.env.NODE_ENV === 'development' 
                  ? 'Activate Test Subscription' 
                  : 'Activate Subscription'
                }
              </>
            )}
          </Button>
          <Button
            variant="outline"
            className="cursor-pointer border-[#2F3336] text-white hover:bg-[#2F3336]"
            onClick={() => window.open('https://help.shopify.com/en/manual/your-account/manage-billing/your-invoice/apps', '_blank')}
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            Learn More
          </Button>
        </div>

        {/* Info Box */}
        <div className="bg-yellow-900/20 border border-yellow-900/40 rounded-lg p-3">
          <div className="flex items-start gap-2">
            <CreditCard className="h-4 w-4 text-yellow-400 mt-0.5" />
            <div>
              <p className="text-sm text-yellow-100">
                <strong>Billing:</strong> {process.env.NODE_ENV === 'development' 
                  ? 'Test mode enabled - no actual charges will be made. Perfect for development and testing.' 
                  : 'You\'ll be charged monthly via Shopify based on your usage. No upfront costs or hidden fees.'
                }
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 