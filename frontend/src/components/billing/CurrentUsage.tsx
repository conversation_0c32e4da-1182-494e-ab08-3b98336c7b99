"use client";

import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { Loader2, AlertCircle } from "lucide-react";
import { usageTrackingService, UsageSummary, AccountBalance } from "@/services/usage-tracking.service";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
import UsageLimitReached from "./UsageLimitReached";

interface CurrentUsageProps {
  storeId: string;
  onRefresh?: () => void;
}

// Skeleton loading component
function CurrentUsageSkeleton() {
  return (
    <Card className="bg-[#16181C] border-[#2F3336]">
      <CardHeader>
        <CardTitle className="text-xl font-bold text-white">
          <Skeleton className="h-6 w-32 bg-[#2F3336]" />
        </CardTitle>
        <CardDescription className="text-[#71767B]">
          <Skeleton className="h-4 w-24 bg-[#2F3336]" />
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Account Balance Skeleton */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-28 bg-[#2F3336]" />
            <Skeleton className="h-4 w-32 bg-[#2F3336]" />
          </div>
          <Skeleton className="h-9 w-24 bg-[#2F3336]" />
          <Skeleton className="h-3 w-64 bg-[#2F3336]" />
        </div>

        {/* Divider */}
        <div className="border-t border-[#2F3336]" />

        {/* Usage Amount Skeleton */}
        <div className="space-y-2">
          <Skeleton className="h-4 w-40 bg-[#2F3336]" />
          <Skeleton className="h-9 w-20 bg-[#2F3336]" />
          <Skeleton className="h-3 w-48 bg-[#2F3336]" />
        </div>

        {/* Progress Bar Skeleton */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <Skeleton className="h-4 w-24 bg-[#2F3336]" />
            <Skeleton className="h-4 w-32 bg-[#2F3336]" />
          </div>
          <Skeleton className="h-2 w-full bg-[#2F3336]" />
          <Skeleton className="h-3 w-56 bg-[#2F3336]" />
        </div>
      </CardContent>
    </Card>
  );
}

export default function CurrentUsage({ storeId, onRefresh }: CurrentUsageProps) {
  const { data: session } = useSession();
  const [usage, setUsage] = useState<UsageSummary | null>(null);
  const [balance, setBalance] = useState<AccountBalance | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    if (!session?.user?.access_token) return;
    
    try {
      setLoading(true);
      setError(null);
      const [usageData, balanceData] = await Promise.all([
        usageTrackingService.getCurrentUsage(storeId, session.user.access_token, session.user.provider),
        usageTrackingService.getAccountBalance(storeId, session.user.access_token, session.user.provider)
      ]);
      setUsage(usageData);
      setBalance(balanceData);
    } catch (err: any) {
      setError(err.message || "Failed to load usage data");
      toast.error("Failed to load usage data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [storeId, session?.user?.access_token]);

  useEffect(() => {
    if (onRefresh) {
      window.addEventListener('usage-updated', fetchData);
      return () => window.removeEventListener('usage-updated', fetchData);
    }
  }, [onRefresh]);

  if (loading) {
    return <CurrentUsageSkeleton />;
  }

  if (error || !usage) {
    return (
      <Card className="bg-[#16181C] border-[#2F3336]">
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-muted-foreground">{error || "No usage data available"}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const isApproachingLimit = usage.totalAmount >= 70000; // $700+
  const isNearLimit = usage.totalAmount >= 75000; // $750+
  const isLimitReached = usage.totalAmount >= 79999; // $799.99

  // If limit is reached, show the limit reached component
  if (isLimitReached) {
    return <UsageLimitReached currentUsage={usage.totalAmount} period={usage.period} storeId={storeId} />;
  }

  return (
    <Card className="bg-[#16181C] border-[#2F3336]">
      <CardHeader>
        <CardTitle className="text-xl font-bold text-white">
          Current Usage
        </CardTitle>
        <CardDescription className="text-[#71767B]">
          {usageTrackingService.formatPeriod(usage.period)}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Account Balance */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <p className="text-sm text-[#71767B]">Account Balance</p>
            <p className="text-sm text-green-400">Auto-topup enabled</p>
          </div>
          <p className="text-3xl font-bold text-white">
            {balance?.formattedBalance || '$0.00'}
          </p>
          <p className="text-xs text-[#71767B]">Automatic $100 top-ups when balance falls below $10</p>
        </div>

        {/* Divider */}
        <div className="border-t border-[#2F3336]" />

        {/* Usage Amount */}
        <div className="space-y-2">
          <p className="text-sm text-[#71767B]">Total Top-ups This Month</p>
          <p className="text-3xl font-bold text-white">
            {usageTrackingService.formatCurrency(usage.totalAmount)}
          </p>
          <p className="text-xs text-[#71767B]">
            {usage.totalAmount > 0 ? `${usage.totalAmount / 10000} top-ups processed` : 'No top-ups yet this month'}
          </p>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-[#71767B]">Monthly Usage</span>
            <span className="text-[#71767B]">{usageTrackingService.formatCurrency(usage.totalAmount)} / $799.99</span>
          </div>
          <Progress
            value={(usage.totalAmount / 79999) * 100}
            className="h-2 bg-[#2F3336]"
            indicatorClassName={
              isNearLimit ? 'bg-red-500' :
                isApproachingLimit ? 'bg-yellow-500' :
                  'bg-green-500'
            }
          />
          <p className="text-xs text-[#71767B]">
            {usageTrackingService.formatCurrency(79999 - usage.totalAmount)} remaining before monthly limit
          </p>
        </div>

        {/* Warning Messages */}
        {isNearLimit && (
          <div className="bg-red-900/20 border border-red-900/40 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-4 w-4 text-red-400 mt-0.5" />
              <div className="flex-1">
                <p className="text-sm font-medium text-red-100">
                  Approaching monthly limit
                </p>
                <p className="text-xs mt-1 text-red-200">
                  You&apos;re close to the $799.99 monthly limit. Service will pause if the limit is reached.
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}