"use client";

import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2, Download } from "lucide-react";
import { usageTrackingService, PeriodDetails as PeriodDetailsType } from "@/services/usage-tracking.service";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

interface PeriodDetailsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  storeId: string;
  period: string;
}

export default function PeriodDetails({
  open,
  onOpenChange,
  storeId,
  period,
}: PeriodDetailsProps) {
  const { data: session } = useSession();
  const [details, setDetails] = useState<PeriodDetailsType | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open && period && session?.user?.access_token) {
      fetchDetails();
    }
  }, [open, period, storeId, session?.user?.access_token]);

  const fetchDetails = async () => {
    if (!session?.user?.access_token) return;
    
    try {
      setLoading(true);
      const data = await usageTrackingService.getPeriodDetails(storeId, period, session.user.access_token, session.user.provider);
      setDetails(data);
    } catch (err: any) {
      toast.error("Failed to load period details");
      onOpenChange(false);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = () => {
    if (!details) return;
    
    const headers = ['Date', 'Blocks', 'Amount'];
    const rows = details.dailyBreakdown.map(day => [
      day.date,
      day.count,
      usageTrackingService.formatCurrency(day.amount)
    ]);
    
    const csvContent = [
      `Period: ${usageTrackingService.formatPeriod(period)}`,
      `Total Blocks: ${details.totalBlocks}`,
      `Total Amount: ${usageTrackingService.formatCurrency(details.totalAmount)}`,
      '',
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `billing-details-${period}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    toast.success("Period details exported");
  };

  const chartData = details?.dailyBreakdown.map(day => ({
    date: new Date(day.date).getDate().toString(),
    amount: day.amount / 100,
    blocks: day.count
  })) || [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl bg-[#16181C] border-[#2F3336]">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-white">
                {period && usageTrackingService.formatPeriod(period)}
              </DialogTitle>
              <DialogDescription className="text-[#71767B]">
                Daily usage breakdown
              </DialogDescription>
            </div>
            <Button
              onClick={handleExport}
              variant="outline"
              size="sm"
              disabled={loading || !details}
              className="border-[#2F3336] text-[#71767B] hover:text-white hover:bg-[#2F3336]"
            >
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </DialogHeader>
        
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : details ? (
          <div className="space-y-6">
            {/* Summary Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-[#0D0E11] rounded-lg p-4">
                <p className="text-sm text-[#71767B]">Total Blocks</p>
                <p className="text-2xl font-bold text-white">{details.totalBlocks}</p>
              </div>
              <div className="bg-[#0D0E11] rounded-lg p-4">
                <p className="text-sm text-[#71767B]">Total Amount</p>
                <p className="text-2xl font-bold text-white">
                  {usageTrackingService.formatCurrency(details.totalAmount)}
                </p>
              </div>
            </div>

            {/* Chart */}
            {chartData.length > 0 && (
              <div className="bg-[#0D0E11] rounded-lg p-4">
                <h4 className="text-sm font-medium text-white mb-4">Daily Usage</h4>
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#2F3336" />
                    <XAxis 
                      dataKey="date" 
                      stroke="#71767B"
                      tick={{ fill: '#71767B' }}
                    />
                    <YAxis 
                      stroke="#71767B"
                      tick={{ fill: '#71767B' }}
                      tickFormatter={(value) => `$${value}`}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: '#16181C',
                        border: '1px solid #2F3336',
                        borderRadius: '8px'
                      }}
                      labelStyle={{ color: '#71767B' }}
                      formatter={(value: any, name: string) => {
                        if (name === 'amount') return [`$${value}`, 'Amount'];
                        return [value, 'Blocks'];
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="amount"
                      stroke="#1D9BF0"
                      strokeWidth={2}
                      dot={{ fill: '#1D9BF0' }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            )}

            {/* Daily Breakdown Table */}
            <div className="bg-[#0D0E11] rounded-lg p-4">
              <h4 className="text-sm font-medium text-white mb-4">Daily Breakdown</h4>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {details.dailyBreakdown.map((day) => (
                  <div 
                    key={day.date} 
                    className="flex justify-between items-center py-2 border-b border-[#2F3336] last:border-0"
                  >
                    <span className="text-sm text-[#71767B]">
                      {new Date(day.date).toLocaleDateString('en-US', { 
                        weekday: 'short', 
                        month: 'short', 
                        day: 'numeric' 
                      })}
                    </span>
                    <div className="flex items-center gap-4">
                      <span className="text-sm text-white">{day.count} blocks</span>
                      <span className="text-sm font-medium text-white">
                        {usageTrackingService.formatCurrency(day.amount)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : null}
      </DialogContent>
    </Dialog>
  );
}