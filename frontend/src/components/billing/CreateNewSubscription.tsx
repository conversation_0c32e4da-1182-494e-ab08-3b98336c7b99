"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, RefreshCw, ExternalLink, Check } from "lucide-react";
import { usageTrackingService } from "@/services/usage-tracking.service";
import { toast } from "sonner";
import {
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface CreateNewSubscriptionProps {
  storeId: string;
  currentUsage: number;
  currentPeriod: string;
  onSuccess: () => void;
}

export default function CreateNewSubscription({
  storeId,
  currentUsage,
  currentPeriod,
  onSuccess
}: CreateNewSubscriptionProps) {
  const [isProcessing, setIsProcessing] = useState(false);

  const handleCreateSubscription = async () => {
    setIsProcessing(true);
    try {
      const response = await usageTrackingService.startNewSubscriptionCycle();
      
      if (response.success && response.data?.confirmationUrl) {
        // Redirect to Shopify for charge confirmation
        window.location.href = response.data.confirmationUrl;
      } else {
        throw new Error(response.message || 'Failed to create subscription cycle');
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to start new subscription cycle. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      <DialogHeader>
        <DialogTitle className="text-2xl font-bold text-white">Start New Subscription Cycle</DialogTitle>
        <DialogDescription className="text-muted-foreground">
          Continue processing chargebacks by starting a new billing cycle
        </DialogDescription>
      </DialogHeader>

      <Card className="bg-yellow-900/10 border-yellow-900/20">
        <CardHeader className="pb-3">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-yellow-400 mt-0.5" />
            <div>
              <CardTitle className="text-lg text-yellow-100">Current Cycle at Limit</CardTitle>
              <CardDescription className="text-yellow-200/80 mt-1">
                You&apos;ve reached the $799.99 limit for {usageTrackingService.formatPeriod(currentPeriod)}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center bg-black/20 rounded-lg p-3">
            <span className="text-sm text-muted-foreground">Current usage</span>
            <span className="text-lg font-bold text-white">{usageTrackingService.formatCurrency(currentUsage)}</span>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <h3 className="font-semibold text-white">What happens when you start a new cycle?</h3>
        <div className="space-y-3">
          <div className="flex items-start gap-3">
            <div className="p-1 bg-green-900/20 rounded">
              <Check className="h-4 w-4 text-green-400" />
            </div>
            <div>
              <p className="text-white text-sm">Service resumes immediately</p>
              <p className="text-muted-foreground text-xs">Continue processing chargebacks without interruption</p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <div className="p-1 bg-green-900/20 rounded">
              <Check className="h-4 w-4 text-green-400" />
            </div>
            <div>
              <p className="text-white text-sm">New $799.99 limit available</p>
              <p className="text-muted-foreground text-xs">Fresh usage limit for the new cycle</p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <div className="p-1 bg-green-900/20 rounded">
              <Check className="h-4 w-4 text-green-400" />
            </div>
            <div>
              <p className="text-white text-sm">Billed through Shopify</p>
              <p className="text-muted-foreground text-xs">Charges appear on your next Shopify invoice</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-blue-900/20 border border-blue-900/40 rounded-lg p-4">
        <p className="text-sm text-blue-100">
          <strong>Note:</strong> You can start multiple subscription cycles in a month if needed. 
          Each cycle has its own $799.99 limit. All charges will be consolidated on your Shopify bill.
        </p>
      </div>

      <div className="flex gap-3">
        <Button
          onClick={handleCreateSubscription}
          disabled={isProcessing}
          className="flex-1 bg-[#1D9BF0] hover:bg-[#1A8CD8] text-white"
          size="lg"
        >
          {isProcessing ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              Starting New Cycle...
            </>
          ) : (
            <>
              <RefreshCw className="w-4 h-4 mr-2" />
              Start New Subscription Cycle
            </>
          )}
        </Button>
        <Button
          variant="outline"
          className="border-[#2F3336] text-white hover:bg-[#2F3336]"
          onClick={() => window.open('https://help.shopify.com/en/manual/your-account/manage-billing/your-invoice/apps', '_blank')}
        >
          <ExternalLink className="w-4 h-4 mr-2" />
          Learn More
        </Button>
      </div>
    </div>
  );
}