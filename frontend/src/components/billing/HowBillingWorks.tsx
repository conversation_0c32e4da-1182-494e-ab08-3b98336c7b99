"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Info, CreditCard, Wallet, TrendingUp } from "lucide-react";

export default function HowBillingWorks() {
  return (
    <Card className="bg-[#16181C] border-[#2F3336] h-full">
      <CardHeader className="bg-gradient-to-r from-blue-900/10 to-transparent">
        <CardTitle className="text-lg font-bold text-white flex items-center gap-2">
          <div className="p-2 bg-blue-900/20 rounded-lg">
            <Info className="h-4 w-4 text-blue-400" />
          </div>
          How It Works
        </CardTitle>
        <CardDescription className="text-[#71767B]">
          Simple usage-based billing
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex gap-3 p-3 rounded-lg bg-blue-900/10 border border-blue-900/20 hover:border-blue-900/30 transition-colors">
            <div className="p-2 bg-blue-900/20 rounded-lg h-fit">
              <Wallet className="h-4 w-4 text-blue-400" />
            </div>
            <div>
              <p className="text-sm font-semibold text-white">Free to Start</p>
              <p className="text-xs text-[#71767B] mt-1">
                No monthly fees, no setup costs
              </p>
            </div>
          </div>
          
          <div className="flex gap-3 p-3 rounded-lg bg-green-900/10 border border-green-900/20 hover:border-green-900/30 transition-colors">
            <div className="p-2 bg-green-900/20 rounded-lg h-fit">
              <TrendingUp className="h-4 w-4 text-green-400" />
            </div>
            <div>
              <p className="text-sm font-semibold text-white">Auto Top-ups</p>
              <p className="text-xs text-[#71767B] mt-1">
                $100 automatic top-ups as needed
              </p>
            </div>
          </div>
          
          <div className="flex gap-3 p-3 rounded-lg bg-purple-900/10 border border-purple-900/20 hover:border-purple-900/30 transition-colors">
            <div className="p-2 bg-purple-900/20 rounded-lg h-fit">
              <CreditCard className="h-4 w-4 text-purple-400" />
            </div>
            <div>
              <p className="text-sm font-semibold text-white">Shopify Billing</p>
              <p className="text-xs text-[#71767B] mt-1">
                Charged monthly via Shopify invoice
              </p>
            </div>
          </div>
          
          <div className="bg-gradient-to-r from-yellow-900/30 to-orange-900/30 border border-yellow-900/40 rounded-lg p-4 mt-4">
            <div className="flex items-start gap-3">
              <div className="p-1.5 bg-yellow-900/30 rounded">
                <svg className="w-4 h-4 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <div>
                <p className="text-sm font-semibold text-yellow-100">Monthly Cap</p>
                <p className="text-xs text-yellow-200/80 mt-0.5">
                  $799.99 per cycle • Service pauses at limit
                </p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}