"use client";

import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Loader2, Download, ChevronRight, Calendar } from "lucide-react";
import { usageTrackingService, BillingPeriod } from "@/services/usage-tracking.service";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
import { format } from "date-fns";

interface BillingHistoryProps {
  storeId: string;
  onPeriodClick?: (period: string) => void;
}

export default function BillingHistory({ storeId, onPeriodClick }: BillingHistoryProps) {
  const { data: session } = useSession();
  const [history, setHistory] = useState<BillingPeriod[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchHistory = async () => {
    if (!session?.user?.access_token) return;
    
    try {
      setLoading(true);
      setError(null);
      const data = await usageTrackingService.getBillingHistory(storeId, 12, session.user.access_token, session.user.provider);
      setHistory(data);
    } catch (err: any) {
      setError(err.message || "Failed to load billing history");
      toast.error("Failed to load billing history");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHistory();
  }, [storeId, session?.user?.access_token]);

  const handleExport = () => {
    // Create CSV content
    const headers = ['Period', 'Blocks', 'Amount', 'Status'];
    const rows = history.map(period => [
      usageTrackingService.formatPeriod(period.period),
      period.blockCount,
      usageTrackingService.formatCurrency(period.totalUsage),
      period.status
    ]);
    
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');
    
    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `billing-history-${storeId}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    toast.success("Billing history exported");
  };

  if (loading) {
    return (
      <Card className="bg-[#16181C] border-[#2F3336]">
        <CardContent className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="bg-[#16181C] border-[#2F3336]">
        <CardContent className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">{error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-[#16181C] border-[#2F3336] overflow-hidden">
      <CardHeader className="bg-gradient-to-r from-[#16181C] to-[#1a1c20] border-b border-[#2F3336]">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl font-bold text-white flex items-center gap-2">
              <Calendar className="h-5 w-5 text-blue-400" />
              Billing History
            </CardTitle>
            <CardDescription className="text-[#71767B] mt-1">
              Click any period to view detailed breakdown
            </CardDescription>
          </div>
          <Button
            onClick={handleExport}
            variant="outline"
            size="sm"
            className="border-[#2F3336] text-[#71767B] hover:text-white hover:bg-[#2F3336]"
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {history.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 text-[#71767B] mx-auto mb-4" />
            <p className="text-[#71767B]">No billing history yet</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="border-[#2F3336] hover:bg-transparent">
                  <TableHead className="text-[#71767B] font-medium">Period</TableHead>
                  <TableHead className="text-[#71767B] text-center font-medium">Blocks</TableHead>
                  <TableHead className="text-[#71767B] text-right font-medium">Amount</TableHead>
                  <TableHead className="text-[#71767B] text-center font-medium">Status</TableHead>
                  <TableHead className="text-[#71767B] w-10"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {history.map((period, index) => (
                  <TableRow 
                    key={period.period} 
                    className="border-[#2F3336] hover:bg-[#1a1c20] cursor-pointer transition-colors group"
                    onClick={() => onPeriodClick?.(period.period)}
                  >
                  <TableCell className="text-white font-medium">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      {usageTrackingService.formatPeriod(period.period)}
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <span className="text-white font-semibold">{period.blockCount}</span>
                    <span className="text-[#71767B] text-xs ml-1">blocks</span>
                  </TableCell>
                  <TableCell className="text-right">
                    <span className="text-white font-bold text-lg">
                      {usageTrackingService.formatCurrency(period.totalUsage)}
                    </span>
                  </TableCell>
                  <TableCell className="text-center">
                    <Badge 
                      variant={period.status === 'current' ? 'default' : 'secondary'}
                      className={
                        period.status === 'current' 
                          ? 'bg-green-900 text-green-100' 
                          : 'bg-[#2F3336] text-[#71767B]'
                      }
                    >
                      {period.status === 'current' ? 'Current' : 'Billed'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <ChevronRight className="w-4 h-4 text-[#71767B] group-hover:text-white transition-colors" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}