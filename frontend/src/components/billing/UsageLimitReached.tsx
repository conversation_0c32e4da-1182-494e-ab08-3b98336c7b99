"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, ExternalLink, Clock, CreditCard, RefreshCw } from "lucide-react";
import { usageTrackingService } from "@/services/usage-tracking.service";
import CreateNewSubscription from "./CreateNewSubscription";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useSession } from "next-auth/react";
import { toast } from "sonner";

interface UsageLimitReachedProps {
  currentUsage: number;
  period: string;
  storeId: string;
  onContactSupport?: () => void;
}

export default function UsageLimitReached({ 
  currentUsage, 
  period,
  storeId,
  onContactSupport 
}: UsageLimitReachedProps) {
  const [showNewSubscription, setShowNewSubscription] = useState(false);
  const { data: session } = useSession();
  
  const handleOpenShopifyBilling = () => {
    if (session?.user?.myshopify_domain) {
      // Extract store handle from myshopify domain (e.g., "store-name.myshopify.com" -> "store-name")
      const storeHandle = session.user.myshopify_domain.replace('.myshopify.com', '');
      window.open(`https://admin.shopify.com/store/${storeHandle}/settings/billing`, '_blank');
    } else {
      // Fallback - show error
      toast.error('Store information not available');
    }
  };

  const handleContactSupport = () => {
    if (onContactSupport) {
      onContactSupport();
    } else {
      // Default to opening support email
      window.location.href = 'mailto:<EMAIL>?subject=Usage Limit Reached';
    }
  };
  
  const handleSubscriptionSuccess = () => {
    setShowNewSubscription(false);
    // Refresh the page to show updated usage
    window.location.reload();
  };

  return (
    <>
      <Card className="bg-red-900/10 border-red-900/20">
      <CardHeader>
        <div className="flex items-start gap-3">
          <div className="p-2 bg-red-900/20 rounded-lg">
            <AlertCircle className="h-6 w-6 text-red-400" />
          </div>
          <div className="flex-1">
            <CardTitle className="text-xl text-white">Monthly Usage Limit Reached</CardTitle>
            <CardDescription className="text-red-200/80 mt-1">
              You&apos;ve reached the $799.99 monthly usage limit for {usageTrackingService.formatPeriod(period)}
            </CardDescription>
          </div>
          <Badge variant="destructive" className="bg-red-900 text-red-100">
            Service Paused
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Usage Summary */}
        <div className="bg-black/20 rounded-lg p-4">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Current Usage</span>
            <span className="text-2xl font-bold text-white">
              {usageTrackingService.formatCurrency(currentUsage)}
            </span>
          </div>
          <div className="mt-2">
            <div className="w-full bg-[#2F3336] rounded-full h-2">
              <div 
                className="bg-red-600 h-2 rounded-full" 
                style={{ width: '100%' }}
              />
            </div>
            <p className="text-xs text-muted-foreground mt-1">100% of monthly limit</p>
          </div>
        </div>

        {/* What Happens Next */}
        <div className="space-y-3">
          <h4 className="font-semibold text-white">What happens next?</h4>
          <div className="space-y-2 text-sm">
            <div className="flex items-start gap-2">
              <Clock className="h-4 w-4 text-blue-400 mt-0.5" />
              <div>
                <p className="text-white">Service will automatically resume</p>
                <p className="text-muted-foreground">At the start of your next billing cycle</p>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <CreditCard className="h-4 w-4 text-green-400 mt-0.5" />
              <div>
                <p className="text-white">You&apos;ll be charged by Shopify</p>
                <p className="text-muted-foreground">For the current month&apos;s usage ({usageTrackingService.formatCurrency(currentUsage)})</p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="space-y-3">
          <h4 className="font-semibold text-white">Need to continue processing?</h4>
          <p className="text-sm text-muted-foreground">
            Start a new subscription cycle to continue processing chargebacks immediately.
          </p>
          <div className="grid gap-3">
            <Button 
              onClick={() => setShowNewSubscription(true)}
              className="w-full bg-[#1D9BF0] hover:bg-[#1A8CD8] text-white"
              size="lg"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Start New Subscription Cycle
            </Button>
            <Button 
              onClick={handleOpenShopifyBilling}
              variant="outline"
              className="w-full border-[#2F3336] text-white hover:bg-[#2F3336]"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              View Billing in Shopify
            </Button>
          </div>
        </div>

        {/* Info Box */}
        <div className="bg-blue-900/20 border border-blue-900/40 rounded-lg p-3">
          <p className="text-xs text-blue-100">
            <strong>Note:</strong> Each subscription cycle has a $799.99 limit. You can start multiple cycles 
            in a month if needed. For enterprise needs, please contact our team.
          </p>
        </div>
      </CardContent>
    </Card>

    {/* New Subscription Dialog */}
    <Dialog open={showNewSubscription} onOpenChange={setShowNewSubscription}>
      <DialogContent className="max-w-2xl bg-[#0D0E11] border-[#2F3336]">
        <CreateNewSubscription 
          storeId={storeId}
          currentUsage={currentUsage}
          currentPeriod={period}
          onSuccess={handleSubscriptionSuccess}
        />
      </DialogContent>
    </Dialog>
    </>
  );
}