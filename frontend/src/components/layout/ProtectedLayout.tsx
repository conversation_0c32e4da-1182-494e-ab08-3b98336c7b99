"use client";

import { ReactNode } from "react";

interface ProtectedLayoutProps {
  children: ReactNode;
  title: string;
  description?: string;
  scrollContent?: boolean;
  action?: ReactNode;
}

export function ProtectedLayout({
  children,
  title,
  description,
  scrollContent = true,
  action,
}: ProtectedLayoutProps) {
  return (
    <div className="flex flex-col h-full text-white">
      {/* Fixed Header */}
      <div className="sticky top-0 z-50 border-b border-[#2F3336] bg-black">
        <div className="flex items-center justify-between p-8">
          <div>
            {title === 'Alerts' || title === 'Chargebacks' ? (
              <div className="flex items-center space-x-4 sm:space-x-6 mb-4 md:mb-0">
                <h1 className="text-4xl sm:text-5xl font-bold">{title}</h1>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-green-500 text-base">Live</span>
                </div>
              </div>
            ) : (
              <>
                <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
                {description && <p className="text-[#71767B]">{description}</p>}
              </>
            )}
          </div>
          {action && <div className="flex items-center gap-2">{action}</div>}
        </div>
      </div>

      {/* Content Area */}
      <div
        className={`flex-1 min-h-0 ${
          scrollContent ? "overflow-y-auto" : "overflow-hidden"
        }`}
      >
        <div className={`p-8 space-y-6 ${scrollContent ? "" : "h-full"}`}>
          {children}
        </div>
      </div>
    </div>
  );
}
