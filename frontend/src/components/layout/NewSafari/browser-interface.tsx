"use client"

import { useState, useEffect, useCallback } from "react"
import <PERSON><PERSON><PERSON><PERSON>ead<PERSON> from "./browser-header"
import SidebarMenu from "./sidebar-menu"
import PersonalSettingsMenu from "./personal-settings-menu"
import { SIDEBAR_NAV_ITEMS, TIME_FILTERS } from "./_lib/constants"
import { useAppDispatch, useAppSelector } from "@/redux/hooks"
import { setIsOpenSidebar } from "@/redux/slices/ui"

// Constants for layout calculations (assuming 1rem = 16px)
const HEADER_HEIGHT_PX = 56
const HEADER_TOP_INSET_PX = 8 // Corresponds to top-2 (0.5rem)
const HEADER_SIDE_INSET_PX = 8 // Corresponds to left-2 / right-2 (0.5rem)
const GAP_BELOW_HEADER_PX = 8 // Additional gap (0.5rem)

// Sidebar is also inset by HEADER_SIDE_INSET_PX
const SIDEBAR_WIDTH_PX = 288 // New width for sidebar (w-72)
// const SIDEBAR_TOP_OFFSET_PX = HEADER_TOP_INSET_PX + HEADER_HEIGHT_PX + GAP_BELOW_HEADER_PX; // 8 + 56 + 8 = 72px

// Content padding
const CONTENT_PT_PX = HEADER_TOP_INSET_PX + HEADER_HEIGHT_PX + GAP_BELOW_HEADER_PX // 72px
const CONTENT_PX_PX = HEADER_SIDE_INSET_PX // 8px

export default function BrowserInterface({
  children,
}: {
  children: React.ReactNode;
}) {
  const dispatch = useAppDispatch()
  const isOpenSidebar = useAppSelector((state) => state.ui.isOpenSidebar)
  
  const [activeTab, setActiveTab] = useState("exastudio")
  const [searchValue, setSearchValue] = useState("")
  const [activeSidebar, setActiveSidebar] = useState<"main" | "personal" | null>(null)
  
  // Handle sidebar state based on redux store
  useEffect(() => {
    if (isOpenSidebar && activeSidebar === null) {
      setActiveSidebar("main")
    } else if (!isOpenSidebar && activeSidebar === "main") {
      setActiveSidebar(null)
    }
  }, [isOpenSidebar, activeSidebar])
  
  // Handle sidebar visibility based on screen size
  useEffect(() => {
    // Track previous width to detect crossing the breakpoint
    let previousWidth = typeof window !== 'undefined' ? window.innerWidth : 0
    const lgBreakpoint = 1024
    
    const handleResize = () => {
      const currentWidth = window.innerWidth
      
      // If resizing down to mobile (crossing the breakpoint)
      if (previousWidth >= lgBreakpoint && currentWidth < lgBreakpoint && activeSidebar !== null) {
        // Close sidebar on mobile
        setActiveSidebar(null)
        dispatch(setIsOpenSidebar(false))
      } 
      // If resizing up to desktop (crossing the breakpoint)
      else if (previousWidth < lgBreakpoint && currentWidth >= lgBreakpoint && activeSidebar === null) {
        // Auto-show sidebar on desktop
        setActiveSidebar("main")
        dispatch(setIsOpenSidebar(true))
      }
      
      // Update previous width for next comparison
      previousWidth = currentWidth
    }
    
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [activeSidebar, dispatch])

  const handleNavigateHome = () => {
    if (activeSidebar === "personal") setActiveSidebar(null) // Close personal settings if navigating home
  }

  const toggleMainSidebar = () => {
    const newState = activeSidebar === "main" ? null : "main"
    setActiveSidebar(newState)
    dispatch(setIsOpenSidebar(newState === "main"))
  }

  const openPersonalSidebar = () => {
    setActiveSidebar("personal")
  }

  const closeAnySidebar = () => {
    setActiveSidebar(null)
  }

  const handleGoogleLogin = () => {
    alert("Initiating Google Sign-In... (simulation)")
  }

  const mainContentPaddingLeft =
    activeSidebar !== null ? `${SIDEBAR_WIDTH_PX + HEADER_SIDE_INSET_PX + 16}px` : `${CONTENT_PX_PX}px` // 16px is an additional gap

  return (
    <div className="relative w-full h-screen text-white flex flex-col bg-black">
      {/* Site-wide fixed elements are outside the main flow */}
      <BrowserHeader
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        searchValue={searchValue}
        setSearchValue={setSearchValue}
        onNavigateHome={handleNavigateHome}
        activeSidebar={activeSidebar}
        onToggleMainSidebar={toggleMainSidebar}
        onOpenPersonalSidebar={openPersonalSidebar}
        onGoogleLogin={handleGoogleLogin}
      />

      <SidebarMenu
          isOpen={activeSidebar === "main"}
        />
      <PersonalSettingsMenu isOpen={activeSidebar === "personal"} onClose={closeAnySidebar} />

      {/* Main Content Area Host with max-width constraint */}
      {/* This div will contain the scrollable content. It needs padding to clear fixed header and sidebars. */}
      <div className="w-full mx-auto flex-1 flex">
        <main
          className="flex-1 transition-all duration-300 flex flex-col min-h-0 overflow-y-auto"
          style={{
            paddingTop: `${CONTENT_PT_PX}px`,
            paddingLeft: mainContentPaddingLeft, // Dynamic padding when sidebar is open
            paddingRight: `${CONTENT_PX_PX}px`,
            paddingBottom: `${CONTENT_PX_PX}px`, // Consistent bottom padding
          }}
        >
         {children}
        </main>
      </div>
    </div>
  )
}
