import type React from "react";
import {
  LayoutDashboard,
  Blocks,
  ShoppingBag,
  Settings,
  CreditCard,
  type LucideIcon,
  CircleDollarSignIcon,
  Bell,
  UserCircle,
  Shield,
  Wallet,
  Home,
} from "lucide-react";

export type TimeFilterOption =
  | "Today"
  | "Yesterday"
  | "This Month"
  | "This Year";

export const TIME_FILTERS: readonly TimeFilterOption[] = [
  "Today",
  "Yesterday",
  "This Month",
  "This Year",
];

export interface SidebarNavItem {
  label: string;
  href: string;
  icon: LucideIcon;
  view?: string; // View to switch to, e.g., 'dashboard'
  count?: number;
  hasSubmenu?: boolean;
}

export interface Store {
  id: string;
  name: string;
  logo?: React.ElementType; // Optional: if stores have specific logos
}

export const MOCK_STORES: Store[] = [
  { id: "store1", name: "Quantum Threads Co." },
  { id: "store2", name: "Nebula Novelties Inc." },
  { id: "store3", name: "Galaxy Gadgets Ltd." },
  { id: "store4", name: "Cosmic Comforts" },
];

export const SIDEBAR_NAV_ITEMS: SidebarNavItem[] = [
  { icon: Home, label: "Dashboard", href: "/dashboard" },
  // { icon: BellDot, label: "Alerts", href: "/alerts" },
  { icon: Bell, label: "Alerts", href: "/blocks" },
  { icon: CreditCard, label: "Chargebacks", href: "/chargebacks" },
  // { icon: WalletCards, label: "Credits", href: "/credits" },
  // { icon: ShoppingBag, label: "Shopify", href: "/shopify" },
  // { icon: CreditCard, label: "Payments", href: "/payments/history" },
  { icon: CircleDollarSignIcon, label: "Pricing", href: "/pricing" },
  // { icon: Wallet, label: "Billing", href: "/billing" },
  // { icon: BoxesIcon, label: "Integrations", href: "/integrations" },
  { icon: Settings, label: "Settings", href: "/settings" },
];

export const PERSONAL_SETTINGS_NAV_ITEMS: SidebarNavItem[] = [
  // { icon: UserCircle, label: "Profile", href: "/settings/profile" },
  // { icon: Bell, label: "Notifications", href: "/notifications" },
  // { icon: Shield, label: "Security", href: "/security" },
];
