import type React from "react"

const TabOverviewIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg width="20" height="16" viewBox="0 0 20 16" fill="none" {...props}>
    <rect x="1" y="1" width="7" height="6" rx="1.5" stroke="currentColor" strokeWidth="1" fill="none" />
    <rect x="11" y="1" width="7" height="6" rx="1.5" stroke="currentColor" strokeWidth="1" fill="none" />
    <rect x="1" y="9" width="7" height="6" rx="1.5" stroke="currentColor" strokeWidth="1" fill="none" />
    <rect x="11" y="9" width="7" height="6" rx="1.5" stroke="currentColor" strokeWidth="1" fill="none" />
  </svg>
)

export default TabOverviewIcon
