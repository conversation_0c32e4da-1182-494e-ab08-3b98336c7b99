"use client"

import React, { useState } from "react"
import { LogOut } from "lucide-react"
import { signOut } from "next-auth/react"
import { forceResetStore } from "@/redux/store"

interface LogoutButtonProps {
  className?: string
}

const LogoutButton: React.FC<LogoutButtonProps> = ({ className }) => {
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  
  const handleLogout = async () => {
    // Prevent multiple logout attempts
    if (isLoggingOut) return
    
    setIsLoggingOut(true)
    
    try {
      // Clear all storage first to ensure clean state
      if (typeof window !== 'undefined') {
        try {
          // Clear specific Redux persist keys
          localStorage.removeItem('persist:root')
          localStorage.removeItem('redux-persist')
          
          // Clear NextAuth session storage
          sessionStorage.clear()
          
          // Clear any other app-specific storage
          const keysToRemove: string[] = []
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i)
            if (key && (key.includes('next-auth') || key.includes('auth'))) {
              keysToRemove.push(key)
            }
          }
          keysToRemove.forEach(key => localStorage.removeItem(key))
        } catch (e) {
          console.warn("Error clearing storage:", e)
        }
      }
      
      // Reset Redux store
      await forceResetStore()
      
      // Sign out with NextAuth - this will clear server-side session
      await signOut({ 
        redirect: false,
        callbackUrl: '/auth/connect'
      })
      
      // Force a hard redirect to clear any remaining client state
      window.location.href = "/auth/connect?force_logout=true"
      
    } catch (error) {
      console.error("Logout error:", error)
      
      // Even if there's an error, force clear everything and redirect
      if (typeof window !== 'undefined') {
        try {
          localStorage.clear()
          sessionStorage.clear()
          // Delete all cookies accessible from JavaScript
          document.cookie.split(";").forEach(function(c) { 
            document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/")
          })
        } catch (e) {
          console.warn("Error clearing storage in fallback:", e)
        }
      }
      
      // Force redirect after error
      window.location.href = "/auth/connect?force_logout=true"
    }
  }

  return (
    <div
      onClick={handleLogout}
      className={className || "flex items-center space-x-4 p-4 hover:bg-red-500/20 rounded-xl cursor-pointer transition-colors duration-150 text-red-400 hover:text-red-300"}
      role="menuitem"
    >
      <LogOut className="w-6 h-6" />
      <span className="text-sm font-medium">Logout</span>
    </div>
  )
}

export default LogoutButton