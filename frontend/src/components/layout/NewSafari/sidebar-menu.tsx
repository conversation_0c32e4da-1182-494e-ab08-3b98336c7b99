"use client";

import React, { useEffect, useCallback, useMemo } from "react";
import { ChevronDown, Store } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import Link from "next/link";
import type { SidebarNavItem } from "./_lib/constants";
import { Check } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import { selectStore, resetStoreState } from "@/redux/slices/store";
import { persistor, resetState, forceResetStore } from "@/redux/store";
import { cn } from "@/lib/utils";
import { SIDEBAR_NAV_ITEMS } from "./_lib/constants";
import ShopifyLogo from "@/components/svg/partners/ShopifyLogo";
import StripeLogo from "@/components/svg/partners/StripeLogo";
import { authService } from "@/services/auth.service";
import { useSession } from "next-auth/react";
import LogoutButton from "./LogoutButton";

interface SidebarMenuProps {
  isOpen: boolean;
}

// These should match or be derived from BrowserInterface constants
const SIDEBAR_TOP_OFFSET_CLASS = "top-[72px]"; // e.g., header height (56px) + header top inset (8px) + gap (8px)
const SIDEBAR_INSET_CLASS = "left-2 bottom-2"; // e.g., 0.5rem inset

// Memoized menu item component
const MenuItem = React.memo(
  ({
    item,
    isActiveRoute,
  }: {
    item: SidebarNavItem;
    isActiveRoute: (href?: string) => boolean;
  }) => {
    return (
      <Link
        href={item.href}
        className={cn(
          "flex items-center space-x-4 p-3 rounded-full w-full transition-colors hover:bg-gray-800 cursor-pointer",
          isActiveRoute(item.href) ? "text-white font-black" : "text-gray-300"
        )}
        role="menuitem"
      >
        <item.icon className="h-6 w-6 flex-shrink-0" />
        <span className="text-xl flex-1 text-left min-w-0">{item.label}</span>
        {item.count !== undefined && (
          <span className="bg-gray-300 text-gray-800 rounded-full px-2 py-0.5 text-xs font-medium flex-shrink-0 min-w-[28px] text-center">
            {item.count}
          </span>
        )}
        {item.hasSubmenu && <ChevronDown className="h-5 w-5 text-gray-400 flex-shrink-0" />}
      </Link>
    );
  }
);

MenuItem.displayName = "MenuItem";

// Component to render appropriate logo based on provider
const ProviderLogo = React.memo(
  ({ provider, className }: { provider?: string; className?: string }) => {
    switch (provider?.toLowerCase()) {
      case "stripe":
        return <StripeLogo className={className} />;
      case "shopify":
        return <ShopifyLogo className={className} />;
      default:
        return <Store className={className} />;
    }
  }
);

ProviderLogo.displayName = "ProviderLogo";

const SidebarMenu: React.FC<SidebarMenuProps> = React.memo(({ isOpen }) => {
  const pathname = usePathname();
  const router = useRouter();
  const store = useAppSelector(selectStore);
  const dispatch = useAppDispatch();
  const { data: session } = useSession();

  // Immediate cleanup if we're on auth page - this handles edge cases
  useEffect(() => {
    if (pathname?.startsWith("/auth/")) {
      forceResetStore();
    }
  }, [pathname]);

  // Debug: Log store changes
  useEffect(() => {
    // Store state tracking for cleanup verification
  }, [store, pathname]);

  // Memoize isActiveRoute function
  const isActiveRoute = useCallback(
    (href?: string): boolean => {
      if (!href) return false;
      return pathname.startsWith(href);
    },
    [pathname]
  );

  return (
    <div
      className={`fixed ${SIDEBAR_TOP_OFFSET_CLASS} bottom-2 z-30
                  ${isOpen ? "w-72 p-4 border-r" : "w-0 p-0"}
                  transition-all duration-300 
                  bg-black/70 backdrop-blur-lg
                  shadow-2xl border-white/10 
                  overflow-hidden flex-shrink-0 
                  left-2 rounded-lg`}
      aria-hidden={!isOpen}
    >
      {isOpen && (
        <div className="w-full h-full flex flex-col">
          <div className="mb-4">
            <div className="w-full flex items-center gap-2 p-2 bg-zinc-950 border border-gray-800 rounded-lg text-white hover:bg-zinc-900 transition-colors">
              <ProviderLogo
                provider={store?.provider}
                className="w-6 h-6 flex-shrink-0"
              />
              <span
                className="text-base font-medium truncate flex-1 text-left"
                title={store?.storeName || "No Store Connected"}
              >
                {store?.storeName || "No Store Connected"}
              </span>
              {store && (
                <Check className="w-5 h-5 text-green-400 flex-shrink-0" />
              )}
            </div>
          </div>
          <nav
            className="flex-1 space-y-1 overflow-y-auto"
            style={{ padding: "0.8rem" }}
          >
            {SIDEBAR_NAV_ITEMS.map((item: SidebarNavItem) => (
              <MenuItem
                key={item.label}
                item={item}
                isActiveRoute={isActiveRoute}
              />
            ))}
          </nav>
          {/* Logout button at the bottom */}
          {session && (
            <div className="mt-auto pt-4 border-t border-gray-800">
              <LogoutButton />
            </div>
          )}
        </div>
      )}
    </div>
  );
});

SidebarMenu.displayName = "SidebarMenu";

export default SidebarMenu;
