"use client"

import type React from "react"
import { X, UserCircle, Bell, Shield, ChevronDown } from "lucide-react"
import { useSession } from "next-auth/react"
import LogoutButton from "./LogoutButton"
import { PERSONAL_SETTINGS_NAV_ITEMS, SidebarNavItem } from "./_lib/constants"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import Link from "next/link"

interface PersonalSettingsMenuProps {
  isOpen: boolean
  onClose: () => void
}

// These should match or be derived from BrowserInterface constants
const SIDEBAR_TOP_OFFSET_CLASS = "top-[72px]" // e.g., header height (56px) + header top inset (8px) + gap (8px)
const SIDEBAR_INSET_CLASS = "left-2 bottom-2" // e.g., 0.5rem inset

const PersonalSettingsMenu: React.FC<PersonalSettingsMenuProps> = ({ isOpen, onClose }) => {
  const { data: session } = useSession()
  const pathname = usePathname()
  // const settingsItems = [
  //   { label: "Profile", icon: UserCircle },
  //   { label: "Notifications", icon: Bell },
  //   { label: "Security", icon: Shield },
  // ]

  return (
    <div
      className={`fixed ${SIDEBAR_TOP_OFFSET_CLASS} z-30
                  ${isOpen ? "w-72 p-4" : "w-0 p-0"} // Adjusted width and padding
                  transition-all duration-300 
                  bg-black/70 backdrop-blur-lg 
                  rounded-lg shadow-2xl border border-white/10 
                  overflow-hidden flex-shrink-0 
                  left-2`}
      aria-hidden={!isOpen}
    >
      {isOpen && (
        <div className="w-full h-full flex flex-col">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-gray-100">Personal Settings</h2>
            <button
              onClick={onClose}
              className="p-1.5 text-gray-400 hover:text-white hover:bg-white/10 rounded-md"
              aria-label="Close personal settings menu"
            >
              <X size={18} />
            </button>
          </div>
          <nav className="flex-1 space-y-2 overflow-y-auto pr-1 -mr-1">
            {/* Adjusted spacing, added scroll padding */}
            {PERSONAL_SETTINGS_NAV_ITEMS.map((item: SidebarNavItem) => {
              const isActive = pathname.startsWith(item.href)
              
              return (
                <Link
                  key={item.label}
                  href={item.href}
                  className={cn(
                    "flex items-center justify-between p-4 hover:bg-white/10 rounded-xl cursor-pointer transition-colors duration-150 backdrop-blur-sm border border-transparent hover:border-white/15",
                    isActive && "bg-white/15 border-white/20"
                  )}
                  role="menuitem"
                >
                  <div className="flex items-center space-x-4">
                    <item.icon className={cn(
                      "w-6 h-6",
                      isActive ? "text-white" : "text-gray-200"
                    )} />
                    <span className={cn(
                      "text-lg font-medium",
                      isActive ? "text-white font-bold" : "text-gray-100"
                    )}>
                      {item.label}
                    </span>
                  </div>
                  {item.count !== undefined && (
                    <div className="bg-white/15 text-gray-100 text-sm px-2 py-1 rounded-full backdrop-blur-sm">
                      {item.count}
                    </div>
                  )}
                  {item.hasSubmenu && <ChevronDown className="w-5 h-5 text-gray-400" />}
                </Link>
              )
            })}
          </nav>
          {/* Logout button at the bottom */}
          {session && (
            <div className="mt-auto pt-4 border-t border-white/10">
              <LogoutButton />
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default PersonalSettingsMenu
