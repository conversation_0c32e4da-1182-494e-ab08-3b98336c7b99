"use client"

import type React from "react"
import { Search, RotateCcw, BlocksIcon, UserCircle2, ChevronRight } from "lucide-react"
import TabOverviewIcon from "./icons/tab-overview-icon"
import CloseIcon from "./icons/close-icon"
import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"

interface BrowserHeaderProps {
  activeTab: string
  setActiveTab: (tab: string) => void
  searchValue: string
  setSearchValue: (value: string) => void
  onNavigateHome: () => void
  activeSidebar: "main" | "personal" | null
  onToggleMainSidebar: () => void
  onOpenPersonalSidebar: () => void
  onGoogleLogin: () => void
}

const HEADER_INSET_CLASSES = "left-2 right-2"

const BrowserHeader: React.FC<BrowserHeaderProps> = ({
  activeTab,
  setActiveTab,
  searchValue,
  setSearchValue,
  onNavigateHome,
  activeSidebar,
  onToggleMainSidebar,
  onOpenPersonalSidebar,
  onGoogleLogin,
}) => {
  const isMainSidebarOpen = activeSidebar === "main"

  const router = useRouter()

  return (
    <div
      className={`fixed ${HEADER_INSET_CLASSES} z-40 flex items-center bg-black/70 backdrop-blur-lg p-3 h-[56px] rounded-lg shadow-2xl border border-white/10 mx-auto`}
    >
      {/* Left side - Tab controls and tabs */}
      <div className="flex items-center">
        <button
          onClick={onToggleMainSidebar}
          className="p-2 hover:bg-white/10 rounded-lg mr-3 transition-transform duration-300 ease-in-out"
          aria-label={isMainSidebarOpen ? "Close main menu" : "Open main menu"}
          aria-expanded={isMainSidebarOpen}
        >
          <div className="relative w-5 h-5">
            <div
              className={`absolute transition-all duration-300 ease-in-out ${isMainSidebarOpen ? "opacity-0 rotate-90 scale-50" : "opacity-100 rotate-0 scale-100"
                }`}
            >
              <TabOverviewIcon className="w-5 h-5 text-white" />
            </div>
            <div
              className={`absolute transition-all duration-300 ease-in-out ${isMainSidebarOpen ? "opacity-100 rotate-0 scale-100" : "opacity-0 -rotate-90 scale-50"
                }`}
            >
              <CloseIcon className="w-5 h-5 text-white" />
            </div>
          </div>
        </button>

        {/* Tabs */}
        <div className="flex items-center space-x-1">
          <div
            className={`hidden sm:flex items-center space-x-2 px-3 py-1.5 rounded-md cursor-pointer text-sm ${activeTab === "exastudio"
                ? "bg-black/50 text-white font-medium"
                : "bg-white/10 hover:bg-white/20 text-gray-200"
              }`}
            onClick={() => {
              setActiveTab("exastudio")
              onNavigateHome()
            }}
            role="tab"
            aria-selected={activeTab === "exastudio"}
          >
            <BlocksIcon className="w-4 h-4" />
            <span className="font-medium">Zero Chargeback Rate</span>
          </div>
        </div>
      </div>

      {/* Center - Address bar */}
      <div className="flex-1 mx-8">
      </div>

      {/* Right side - Login and Get in touch buttons */}
      <div className="flex items-center space-x-2">
        <Button
          onClick={() => router.replace("/contact")}
          className="flex items-center bg-white text-black px-4 py-2 rounded-full hover:bg-gray-200 transition-colors text-sm font-semibold cursor-pointer">
          <span>Get in touch</span>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  )
}

export default BrowserHeader
