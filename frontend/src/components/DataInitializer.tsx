"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { usePathname } from "next/navigation";

// Import store selectors and actions
import { fetchStore, selectStore, selectStoreIsInitialized, resetStoreState, fetchStoresByUserId } from "@/redux/slices/store";

// Import all the async thunks
import { fetchAlertInfosByStore } from "@/redux/slices/alert-info";
import { fetchBillsByStore } from "@/redux/slices/bill";
import { fetchPaymentHistoryByStore } from "@/redux/slices/payment-history";
// Temporarily disabled for Shopify-only billing
// import { fetchStripeAccountByStore } from "@/redux/slices/stripe";

export function DataInitializer() {
  const { data: session, status } = useSession();
  const dispatch = useAppDispatch();
  const store = useAppSelector(selectStore);
  const isStoreInitialized = useAppSelector(selectStoreIsInitialized);
  const pathname = usePathname();
  
  // Check if we're on an auth page
  const isAuthPage = pathname?.startsWith("/auth/");
  
  // Extract session values to stable variables
  const storeId = session?.user?.id || '';
  const provider = session?.user?.provider || '';
  const userId = session?.user?.user_id || '';
  const accessToken = session?.user?.access_token || '';

  // Clear store data when user is not authenticated or on auth pages
  useEffect(() => {
    if (status === "unauthenticated") {
      // Only reset if we're truly unauthenticated, not during loading
      dispatch(resetStoreState());
    } else if (isAuthPage && pathname !== "/auth/connect/callback" && !pathname.includes("/callback")) {
      // Only reset on auth pages that aren't callback pages
      dispatch(resetStoreState());
    }
  }, [status, isAuthPage, pathname, dispatch]);

  // Fetch store AND stores list when user is authenticated and NOT on auth pages
  useEffect(() => {
    if (status === "authenticated" && storeId && provider && !isStoreInitialized && !isAuthPage) {
      // Store data is essential, fetch immediately but don't block UI
      dispatch(fetchStore({ 
        storeId, 
        provider 
      })); // session.user.id is now LinkedStore ID
      
      // Also fetch all stores for the user (cached in Redux)
      if (userId) {
        dispatch(fetchStoresByUserId(userId));
      }
    }
  }, [status, storeId, provider, userId, isStoreInitialized, isAuthPage, dispatch]);

  // Fetch secondary data when store is available with smart loading based on route
  useEffect(() => {
    if (status === "authenticated" && store?.id && !isAuthPage && accessToken && provider) {
      const currentStoreId = store.id;
      
      // Determine if we're on dashboard page for priority loading
      const isDashboardPage = pathname?.includes('/dashboard');
      const isPaymentsPage = pathname?.includes('/payments');
      const isBlocksPage = pathname?.includes('/blocks');
      
      // Use setTimeout to make data fetching non-blocking and defer to next tick
      const timeoutId = setTimeout(() => {
        // Fetch critical data first (alert infos - needed for dashboard, but not blocks page)
        if (!isBlocksPage) {
          dispatch(fetchAlertInfosByStore({ storeId: currentStoreId, token: accessToken, provider }));
        }
        
        // Background loading with route awareness - longer delays for non-critical pages
        const backgroundDelay = isDashboardPage ? 500 : (isPaymentsPage ? 1000 : 2000);
        
        const backgroundTimeoutId = setTimeout(() => {
          // Only load billing data in background if not on payments pages (they'll load it themselves)
          if (!isPaymentsPage) {
            Promise.allSettled([
              // Temporarily disabled for Shopify-only billing
              // dispatch(fetchStripeAccountByStore(currentStoreId)),
            ]).catch(() => {
              // Silently handle errors for background data
            });
          }
        }, backgroundDelay);
        
        // Store timeout for cleanup
        return () => clearTimeout(backgroundTimeoutId);
      }, 0); // Defer to next tick to avoid blocking initial render
      
      // Cleanup function
      return () => {
        clearTimeout(timeoutId);
      };
    }
  }, [status, store?.id, isAuthPage, accessToken, provider]); // Remove pathname and dispatch from deps to prevent re-fetching on route change

  // This component doesn't render anything
  return null;
}