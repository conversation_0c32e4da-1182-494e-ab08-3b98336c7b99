"use client";

import { useEffect } from "react";
import { Crisp } from "crisp-sdk-web";
import { generateCrispSignature } from "@/lib/crispUtils";
import { useSession } from "next-auth/react";

export default function CrispChat() {
  const { data: session } = useSession();

  useEffect(() => {
    const initCrisp = async () => {
      // Initialize Crisp only on the client side
      Crisp.configure("e3c751cc-d9fa-4603-ba26-c5ca0599d7f8", {
        autoload: true,
      });

      // If user is logged in, set their information
      if (session?.user) {
        const email = session.user.email;
        const nickname = session.user.name || session.user.email?.split('@')[0] || "User";
        
        // TODO: Add your Crisp secret key from your Crisp account settings
        // You can get this from: Crisp Dashboard > Settings > Workspace Settings > Setup & Integrations
        const secretKey = process.env.NEXT_PUBLIC_CRISP_SECRET_KEY || "";

        if (email) {
          try {
            if (secretKey) {
              // Generate the verification signature for secure user identification
              const signature = await generateCrispSignature(email, secretKey);

              // console.log("signature", signature);
              
              // Set user email with verification signature
              Crisp.user.setEmail(email, signature);
            } else {
              // Fallback to unverified setup if no secret key
              Crisp.user.setEmail(email);
            }

            // Set other user information
            Crisp.user.setNickname(nickname);

            // console.log("Crisp chat initialized with user:", { email, nickname });
          } catch (error) {
            console.error("Error setting up Crisp verification:", error);

            // Fallback to unverified setup if verification fails
            Crisp.user.setEmail(email);
            Crisp.user.setNickname(nickname);
          }
        }
      }
    };

    initCrisp();
  }, [session]);

  // This component doesn't render anything visible
  // The Crisp chat widget is injected into the DOM by the Crisp SDK
  return null;
} 