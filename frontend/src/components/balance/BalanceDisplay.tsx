'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DollarSign, Plus, RefreshCw, AlertTriangle } from 'lucide-react';
import { getStoreBalance, triggerBalanceMaintenance } from '@/services/store-balance.service';
import { useAppSelector } from '@/redux/hooks';
import { selectStore } from '@/redux/slices/store';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

interface BalanceDisplayProps {
  onTopupClick?: () => void;
}

const BalanceDisplay: React.FC<BalanceDisplayProps> = ({ onTopupClick }) => {
  const { data: session } = useSession();
  const [balance, setBalance] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const currentStore = useAppSelector(selectStore);

  const fetchBalance = async () => {
    if (!currentStore?.id || !session?.user?.access_token) return;

    try {
      setRefreshing(true);
      const data = await getStoreBalance(currentStore.id, session.user.access_token, session.user.provider);
      setBalance(data.balance);
    } catch (error: any) {
      toast.error('Failed to fetch balance');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefreshClick = async () => {
    if (!session?.user?.access_token || !currentStore?.id) return;

    try {
      setRefreshing(true);
      
      // First trigger balance maintenance check for current store only
      await triggerBalanceMaintenance(currentStore.id, session.user.access_token, session.user.provider);
      toast.success('Balance check completed');
      
      // Then refresh the current store balance
      await fetchBalance();
      
    } catch (error: any) {
      toast.error('Failed to check');
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchBalance();

    // Refresh balance every 30 seconds
    const interval = setInterval(fetchBalance, 30000);

    // Listen for balance update events
    const handleBalanceUpdate = () => {
      fetchBalance();
    };

    window.addEventListener('balance-updated', handleBalanceUpdate);

    return () => {
      clearInterval(interval);
      window.removeEventListener('balance-updated', handleBalanceUpdate);
    };
  }, [currentStore?.id, session?.user?.access_token]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount / 100);
  };

  const getBalanceStatus = () => {
    if (balance >= 5000) return { color: 'bg-green-500', text: 'Healthy', icon: null };
    if (balance >= 1000) return { color: 'bg-yellow-500', text: 'Low', icon: <AlertTriangle className="h-3 w-3" /> };
    return { color: 'bg-red-500', text: 'Critical', icon: <AlertTriangle className="h-3 w-3" /> };
  };

  const balanceStatus = getBalanceStatus();

  if (loading && !refreshing) {
    return (
      <Card className="bg-black border-gray-800">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-black border-gray-800">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-300">Current Balance</CardTitle>
        <DollarSign className="h-4 w-4 text-gray-500" />
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <div className="text-2xl font-bold text-white">
              {formatCurrency(balance)}
            </div>
            <div className="flex items-center space-x-2 mt-2">
              <Badge className={`${balanceStatus.color} text-white`}>
                <span className="flex items-center gap-1">
                  {balanceStatus.icon}
                  {balanceStatus.text}
                </span>
              </Badge>
              {balance < 1000 && (
                <span className="text-xs text-red-400">Auto-topup will activate</span>
              )}
            </div>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefreshClick}
              disabled={refreshing}
              className="cursor-pointer border-gray-700 hover:bg-gray-800"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
            {/* <Button
              size="sm"
              onClick={onTopupClick}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-1" />
              Top Up
            </Button> */}
          </div>
        </div>
        <div className="mt-4 pt-4 border-t border-gray-800">
          <p className="text-xs text-gray-400">
            Funds are automatically deducted when processing blocks. Auto-topup activates when balance falls below $10.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default BalanceDisplay;