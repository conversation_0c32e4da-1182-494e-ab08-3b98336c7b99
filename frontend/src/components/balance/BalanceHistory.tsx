'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  History, 
  ArrowUpRight, 
  ArrowDownRight, 
  Loader2,
  RefreshCw 
} from 'lucide-react';
import { getBalanceHistory, BalanceHistoryItem } from '@/services/store-balance.service';
import { useAppSelector } from '@/redux/hooks';
import { selectStore } from '@/redux/slices/store';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';
import { format } from 'date-fns';

interface BalanceHistoryProps {
  maxHeight?: string;
}

const BalanceHistory: React.FC<BalanceHistoryProps> = ({ maxHeight = '400px' }) => {
  const { data: session } = useSession();
  const [history, setHistory] = useState<BalanceHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const currentStore = useAppSelector(selectStore);

  const fetchHistory = async (currentPage: number = 1) => {
    if (!currentStore?.id || !session?.user?.access_token) return;
    
    try {
      setLoading(true);
      const data = await getBalanceHistory(currentStore.id, currentPage, 10, session.user.access_token, session.user.provider);
      
      if (currentPage === 1) {
        setHistory(data.items);
      } else {
        setHistory(prev => [...prev, ...data.items]);
      }
      
      setTotal(data.total);
      setHasMore(data.items.length * currentPage < data.total);
      setPage(currentPage);
    } catch (error: any) {
      toast.error('Failed to fetch balance history');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHistory();
    
    // Listen for balance update events
    const handleBalanceUpdate = () => {
      fetchHistory(1);
    };
    
    window.addEventListener('balance-updated', handleBalanceUpdate);
    
    return () => {
      window.removeEventListener('balance-updated', handleBalanceUpdate);
    };
  }, [currentStore?.id, session?.user?.access_token]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount / 100);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { color: string; text: string }> = {
      'PAID': { color: 'bg-green-500', text: 'Completed' },
      'PENDING': { color: 'bg-yellow-500', text: 'Pending' },
      'FAILED': { color: 'bg-red-500', text: 'Failed' },
      'CANCELLED': { color: 'bg-gray-500', text: 'Cancelled' }
    };

    const config = statusConfig[status] || { color: 'bg-gray-500', text: status };
    
    return (
      <Badge className={`${config.color} text-white`}>
        {config.text}
      </Badge>
    );
  };

  if (loading && history.length === 0) {
    return (
      <Card className="bg-black border-gray-800">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-black border-gray-800">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-sm font-medium text-gray-300 flex items-center gap-2">
          <History className="h-4 w-4" />
          Balance History
        </CardTitle>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => fetchHistory(1)}
          disabled={loading}
          className="text-gray-400 hover:text-white"
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea style={{ height: maxHeight }}>
          <div className="px-6 pb-4">
            {history.length === 0 ? (
              <p className="text-center text-gray-500 py-8">No transaction history yet</p>
            ) : (
              <div className="space-y-3">
                {history.map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center justify-between p-3 rounded-lg bg-gray-800 hover:bg-gray-750 transition-colors"
                  >
                    <div className="flex items-start gap-3">
                      <div className={`p-2 rounded-full ${
                        item.type === 'TOPUP' ? 'bg-green-900' : 'bg-red-900'
                      }`}>
                        {item.type === 'TOPUP' ? (
                          <ArrowUpRight className="h-4 w-4 text-green-400" />
                        ) : (
                          <ArrowDownRight className="h-4 w-4 text-red-400" />
                        )}
                      </div>
                      <div>
                        <p className="text-sm font-medium text-white">
                          {item.description}
                        </p>
                        <p className="text-xs text-gray-400 mt-1">
                          {format(new Date(item.createdAt), 'MMM dd, yyyy HH:mm')}
                          {item.paymentMethod && ` • ${item.paymentMethod}`}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`text-sm font-semibold ${
                        item.type === 'TOPUP' ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {item.type === 'TOPUP' ? '+' : '-'}{formatCurrency(item.amount)}
                      </p>
                      <div className="mt-1">
                        {getStatusBadge(item.status)}
                      </div>
                    </div>
                  </div>
                ))}
                
                {hasMore && (
                  <div className="text-center pt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchHistory(page + 1)}
                      disabled={loading}
                      className="border-gray-700 hover:bg-gray-800"
                    >
                      {loading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Loading...
                        </>
                      ) : (
                        'Load More'
                      )}
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default BalanceHistory;