'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { toast } from 'sonner';
import { Loader2, CreditCard, DollarSign } from 'lucide-react';
import { manualTopup, getPaymentMethods } from '@/services/store-balance.service';
import { useAppSelector } from '@/redux/hooks';
import { selectStore } from '@/redux/slices/store';
import { useSession } from 'next-auth/react';

interface ManualTopupDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

const PRESET_AMOUNTS = [25, 50, 100, 250, 500]; // in dollars

const ManualTopupDialog: React.FC<ManualTopupDialogProps> = ({
  open,
  onOpenChange,
  onSuccess
}) => {
  const { data: session } = useSession();
  const [amount, setAmount] = useState<string>('100');
  const [paymentMethod, setPaymentMethod] = useState<'STRIPE'>('STRIPE');
  const [loading, setLoading] = useState(false);
  const [availableMethods, setAvailableMethods] = useState<{ stripe: boolean }>({ stripe: false });
  
  const currentStore = useAppSelector(selectStore);

  React.useEffect(() => {
    if (open && currentStore?.id && session?.user?.access_token) {
      // Fetch available payment methods
      getPaymentMethods(currentStore.id, session.user.access_token, session.user.provider).then(methods => {
        setAvailableMethods({ stripe: methods.stripe });
        // Set default payment method to Stripe
        if (methods.stripe) {
          setPaymentMethod('STRIPE');
        }
      }).catch(() => {
        toast.error('Failed to fetch payment methods');
      });
    }
  }, [open, currentStore?.id, session?.user?.access_token]);

  const handleTopup = async () => {
    if (!currentStore?.id) {
      toast.error('No store selected');
      return;
    }

    const amountInCents = Math.round(parseFloat(amount) * 100);
    
    if (isNaN(amountInCents) || amountInCents < 1000) {
      toast.error('Minimum topup amount is $10');
      return;
    }

    if (amountInCents > 100000) {
      toast.error('Maximum topup amount is $1000');
      return;
    }

    setLoading(true);

    try {
      const result = await manualTopup(currentStore.id, {
        amount: amountInCents,
        paymentMethod
      }, session?.user?.access_token);

      toast.success(`Successfully topped up ${result.formatted}`);
      onSuccess?.();
      onOpenChange(false);
      
      // Reset form
      setAmount('100');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to process topup');
    } finally {
      setLoading(false);
    }
  };

  const hasPaymentMethods = availableMethods.stripe;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px] bg-gray-900 border-gray-800">
        <DialogHeader>
          <DialogTitle className="text-white">Top Up Balance</DialogTitle>
          <DialogDescription className="text-gray-400">
            Add funds to your account balance. Minimum $10, maximum $1000.
          </DialogDescription>
        </DialogHeader>
        
        {!hasPaymentMethods ? (
          <div className="py-8 text-center">
            <p className="text-gray-400 mb-4">No payment methods configured</p>
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="border-gray-700 hover:bg-gray-800"
            >
              Close
            </Button>
          </div>
        ) : (
          <>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="amount" className="text-gray-300">Amount (USD)</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                  <Input
                    id="amount"
                    type="number"
                    min="10"
                    max="1000"
                    step="0.01"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    className="pl-9 bg-gray-800 border-gray-700 text-white"
                    placeholder="100.00"
                  />
                </div>
                
                {/* Preset amounts */}
                <div className="flex gap-2 mt-2">
                  {PRESET_AMOUNTS.map((preset) => (
                    <Button
                      key={preset}
                      size="sm"
                      variant="outline"
                      onClick={() => setAmount(preset.toString())}
                      className="border-gray-700 hover:bg-gray-800 text-gray-300"
                    >
                      ${preset}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-gray-300">Payment Method</Label>
                <RadioGroup
                  value={paymentMethod}
                  onValueChange={(value) => setPaymentMethod(value as 'STRIPE')}
                  className="space-y-2"
                >
                  {availableMethods.stripe && (
                    <div className="flex items-center space-x-2 p-3 rounded-lg border border-gray-700 hover:bg-gray-800">
                      <RadioGroupItem value="STRIPE" id="stripe" />
                      <Label
                        htmlFor="stripe"
                        className="flex items-center gap-2 cursor-pointer flex-1"
                      >
                        <span>Stripe</span>
                      </Label>
                    </div>
                  )}
                </RadioGroup>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
                className="border-gray-700 hover:bg-gray-800"
              >
                Cancel
              </Button>
              <Button
                onClick={handleTopup}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <CreditCard className="mr-2 h-4 w-4" />
                    Top Up ${amount}
                  </>
                )}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ManualTopupDialog;