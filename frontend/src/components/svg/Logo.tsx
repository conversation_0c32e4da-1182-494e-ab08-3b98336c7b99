import * as React from "react";
import { Shapes } from "lucide-react";

interface LogoSVGProps extends React.HTMLAttributes<HTMLDivElement> {
  size: number;
}

const LogoSVG = ({ size, ...props }: LogoSVGProps) => {
  const height = size; // The height will be the given size (50)
  const width = size * 4; // The width will be 4x the height to maintain 50:200 ratio
  const iconSize = Math.round(size * 0.8); // Adjusted icon size to better fit the height
  const fontSize = Math.round(size * 0.5); // Adjusted text size to be more readable

  return (
    <div
      className="flex items-center space-x-2"
      style={{
        height: `${height}px`,
        width: `${width}px`,
      }}
      {...props}
    >
      <Shapes size={iconSize} color="#00c951" strokeWidth={2} />
      <span
        style={{
          fontSize: `${fontSize}px`,
          fontWeight: "bold",
          color: "#00c951",
          whiteSpace: "nowrap",
        }}
      >
        QuantChargeback
      </span>
    </div>
  );
};

export default LogoSVG;
