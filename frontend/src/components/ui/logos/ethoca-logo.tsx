import React from 'react';

interface EthocaLogoProps {
    className?: string;
    width?: number;
    height?: number;
}

export function EthocaLogo({ className, width = 21, height = 20 }: EthocaLogoProps) {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 21 20">
            <rect width={20} height={20} x={0.5} fill="#000" rx={5} />
            <path
                fill="#EB001B"
                d="M7.986 13.973a3.985 3.985 0 1 0 0-7.97 3.985 3.985 0 0 0 0 7.97Z"
            />
            <path
                fill="#F79E1B"
                d="M13.299 13.973a3.985 3.985 0 0 0 2.819-6.805 3.986 3.986 0 1 0-2.82 6.805Z"
            />
            <path
                fill="#FF5F00"
                d="M10.641 12.959a3.974 3.974 0 0 0 1.33-2.972 3.975 3.975 0 0 0-1.33-2.971 3.978 3.978 0 0 0-1.329 2.971 3.974 3.974 0 0 0 1.33 2.972Z"
            />
        </svg>
    );
}