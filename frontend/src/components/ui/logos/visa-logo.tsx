import React from 'react';

interface VisaLogoProps {
    className?: string;
    width?: number;
    height?: number;
}

export function VisaLogo({ className, width = 69, height = 47 }: VisaLogoProps) {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 -11 70 70"
            className={className}
            width={width}
            height={height}
        >
            <rect
                width={69}
                height={47}
                x={0.5}
                y={0.5}
                fill="#fff"
                stroke="#D9D9D9"
                rx={5.5}
            />
            <path
                fill="#172B85"
                fillRule="evenodd"
                d="M21.25 32.517h-4.24l-3.18-12.132c-.151-.558-.472-1.052-.943-1.284-1.176-.584-2.473-1.05-3.887-1.284v-.467h6.831c.943 0 1.65.701 1.768 1.516l1.65 8.751 4.239-10.267h4.122l-6.36 15.166Zm8.718 0h-4.005L29.26 17.35h4.005l-3.297 15.166Zm8.479-10.966c.118-.816.825-1.284 1.65-1.284 1.296-.117 2.708.118 3.887.7l.707-3.265A10.138 10.138 0 0 0 41.039 17c-3.887 0-6.715 2.1-6.715 5.017 0 2.218 2.003 3.382 3.418 4.084 1.53.7 2.119 1.166 2.001 1.866 0 1.05-1.178 1.517-2.355 1.517-1.414 0-2.828-.35-4.123-.935l-.707 3.268c1.414.582 2.944.817 4.359.817 4.358.115 7.067-1.984 7.067-5.134 0-3.967-5.537-4.2-5.537-5.949ZM58 32.517 54.82 17.35h-3.416c-.707 0-1.414.467-1.65 1.166l-5.888 14h4.123l.823-2.216h5.065l.472 2.216H58Zm-6.006-11.083 1.176 5.716h-3.298l2.122-5.716Z"
                clipRule="evenodd"
            />
        </svg>
    );
}