import React from 'react';

interface VerifiLogoProps {
    className?: string;
    width?: number;
    height?: number;
}

export function VerifiLogo({ className, width = 21, height = 20 }: VerifiLogoProps) {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 21 20">
            <rect width={20} height={20} x={0.5} fill="#1A1F71" rx={5} />
            <path
                fill="#fff"
                d="M13.517 4.866c.161-.139.439-.37.617-.513.183-.143.345-.261.365-.261.017 0-.054.08-.162.18a10.61 10.61 0 0 0-.46.45c-.145.152-.427.484-.626.737-.195.256-.51.71-.7 1.01-.187.302-.49.832-.672 1.177-.179.35-.436.867-.564 1.157-.125.29-.324.761-.44 1.052-.116.29-.328.875-.477 1.304a37.68 37.68 0 0 0-.468 1.493c-.112.395-.27.98-.345 1.304-.078.324-.178.782-.22 1.022-.045.236-.086.43-.099.43-.012 0-.041-.034-.066-.072-.025-.043-.179-.282-.336-.539-.158-.256-.39-.652-.51-.883-.125-.232-.328-.64-.452-.905-.12-.265-.315-.732-.427-1.03a14.426 14.426 0 0 1-.68-2.713 10.722 10.722 0 0 1-.12-1.64c-.013-.674-.009-.85.024-.695.021.118.087.43.141.694.058.265.179.732.27 1.03.095.304.228.7.303.884.07.185.257.593.414.905.158.31.365.677.456.811.096.135.187.244.208.24.016 0 .116-.176.224-.387.103-.214.31-.61.46-.875.15-.265.386-.664.522-.883a30.4 30.4 0 0 1 .502-.766c.137-.197.41-.568.606-.82a28.127 28.127 0 0 1 1.97-2.2c.248-.244.584-.56.742-.698Z"
            />
        </svg>
    );
}