import React from 'react';

interface MastercardLogoProps {
    className?: string;
    width?: number;
    height?: number;
}

export function MastercardLogo({ className, width = 69, height = 47 }: MastercardLogoProps) {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            fill="none"
            viewBox="0 -11 70 70"
            xmlns="http://www.w3.org/2000/svg">
            <rect
                width={69}
                height={47}
                x={0.5}
                y={0.5}
                fill="#fff"
                stroke="#D9D9D9"
                rx={5.5}
            />
            <path
                fill="#ED0006"
                fillRule="evenodd"
                d="M35.395 34.762a13.502 13.502 0 0 1-8.853 3.298c-7.537 0-13.648-6.181-13.648-13.806 0-7.625 6.11-13.806 13.648-13.806 3.378 0 6.47 1.242 8.852 3.298a13.502 13.502 0 0 1 8.853-3.298c7.537 0 13.648 6.181 13.648 13.806 0 7.625-6.11 13.806-13.648 13.806-3.378 0-6.47-1.242-8.852-3.298Z"
                clipRule="evenodd"
            />
            <path
                fill="#F9A000"
                fillRule="evenodd"
                d="M35.395 34.762a13.841 13.841 0 0 0 4.795-10.508 13.84 13.84 0 0 0-4.795-10.508 13.502 13.502 0 0 1 8.852-3.298c7.537 0 13.648 6.181 13.648 13.806 0 7.625-6.11 13.806-13.648 13.806-3.378 0-6.47-1.242-8.852-3.298Z"
                clipRule="evenodd"
            />
            <path
                fill="#FF5E00"
                fillRule="evenodd"
                d="M35.395 13.746a13.841 13.841 0 0 1 4.795 10.508c0 4.208-1.861 7.976-4.795 10.508A13.841 13.841 0 0 1 30.6 24.254c0-4.208 1.86-7.976 4.795-10.508Z"
                clipRule="evenodd"
            />
        </svg>
    );
}