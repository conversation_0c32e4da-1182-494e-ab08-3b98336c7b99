"use client";

import React from "react";

interface LoadingProps {
  size?: "sm" | "md" | "lg";
  text?: string;
  className?: string;
}

export function Loading({ size = "md", text = "Loading...", className = "" }: LoadingProps) {
  const sizeMap = {
    sm: "h-8 w-8",
    md: "h-12 w-12",
    lg: "h-16 w-16",
  };

  return (
    <div className={`w-full h-64 flex items-center justify-center ${className}`}>
      <div className="text-center">
        <div className={`animate-spin rounded-full ${sizeMap[size]} border-b-2 border-primary mx-auto mb-4`}></div>
        <p className="text-muted-foreground">{text}</p>
      </div>
    </div>
  );
}