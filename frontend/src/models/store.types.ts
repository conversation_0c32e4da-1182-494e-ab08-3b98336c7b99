/**
 * Store related types and interfaces
 */

// Shopify shop data interface
export interface IShopifyShop {
  id: number;
  zip: string;
  city: string;
  name: string;
  email: string;
  phone: string;
  domain: string;
  source: string | null;
  country: string;
  address1: string;
  address2: string;
  currency: string;
  finances: boolean;
  latitude: number;
  province: string;
  timezone: string;
  longitude: number;
  plan_name: string;
  created_at: string;
  shop_owner: string;
  updated_at: string;
  weight_unit: string;
  country_code: string;
  country_name: string;
  county_taxes: boolean;
  money_format: string;
  tax_shipping: string | null;
  has_discounts: boolean;
  iana_timezone: string;
  province_code: string;
  customer_email: string;
  has_gift_cards: boolean;
  has_storefront: boolean;
  primary_locale: string;
  setup_required: boolean;
  taxes_included: boolean;
  myshopify_domain: string;
  password_enabled: boolean;
  plan_display_name: string;
  google_apps_domain: string | null;
  pre_launch_enabled: boolean;
  primary_location_id: number;
  eligible_for_payments: boolean;
  checkout_api_supported: boolean;
  money_in_emails_format: string;
  multi_location_enabled: boolean;
  google_apps_login_enabled: string | null;
  money_with_currency_format: string;
  transactional_sms_disabled: boolean;
  auto_configure_tax_inclusivity: string | null;
  enabled_presentment_currencies: string[];
  requires_extra_payments_agreement: boolean;
  money_with_currency_in_emails_format: string;
  marketing_sms_consent_enabled_at_checkout: boolean;
}

// Store data interface
export interface IStoreData {
  shop: IShopifyShop;
  domain: string;
  accessToken: string;
  lastFetchedAt: string;
}

// Provider types
export type StoreProvider = 'shopify' | 'other';

// Main store interface
export interface IStore {
  id: string; // LinkedStore ID (primary identifier)
  storeName: string;
  provider: StoreProvider;
  providerStoreId: string;
  data: IStoreData;
  createdAt: string;
  updatedAt: string;
}

// Store state interface - simplified for single store
export interface StoreState {
  currentStore: IStore | null;
  loading: boolean;
  error: string | null;
}

 