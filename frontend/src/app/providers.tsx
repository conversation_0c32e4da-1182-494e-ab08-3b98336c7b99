"use client";

import { SessionProvider } from "next-auth/react";
import { Toaster } from "sonner";
import { ThemeProvider } from "next-themes";
import { AppProviders } from "@/providers";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider>
      <ThemeProvider
        attribute="class"
        defaultTheme="dark"
        enableSystem={false}
        forcedTheme="dark"
        disableTransitionOnChange
        enableColorScheme={false}
      >
        <AppProviders>
          <Toaster position="top-center" richColors closeButton theme="dark" />
          {children}
        </AppProviders>
      </ThemeProvider>
    </SessionProvider>
  );
}
