import type { <PERSON>ada<PERSON> } from "next";
import { Outfit } from "next/font/google";
import "./globals.css";
import { Providers } from "./providers";
import BrowserInterface from "@/components/layout/NewSafari/browser-interface";

const outfit = Outfit({
  variable: "--font-outfit",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Zero Chargeback Rate",
  description: "Zero Chargeback Rate - Chargeback Prevention and Management",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark" style={{ colorScheme: "dark" }} suppressHydrationWarning>
      <body className={`${outfit.className} antialiased`}>
        <Providers>
          <BrowserInterface>
            {children}
          </BrowserInterface>
        </Providers>
      </body>
    </html>
  );
}
