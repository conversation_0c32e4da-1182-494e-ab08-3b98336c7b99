"use client";

import { Suspense, useState } from "react";
import { Loader2, Settings, DollarSign } from "lucide-react";
import { useSession } from "next-auth/react";
import { useAppSelector } from "@/redux/hooks";
import { selectStore, selectStoreLoading } from "@/redux/slices/store";
import { StripeAccountComponent } from "../_components/payment/stripe-account";
import BalanceDisplay from "@/components/balance/BalanceDisplay";
import ManualTopupDialog from "@/components/balance/ManualTopupDialog";
import { Separator } from "@/components/ui/separator";
import { PaymentHistoryComponent } from "../_components/payment-history";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

function BillingContent() {
  const { data: session, status: sessionStatus } = useSession();
  const store = useAppSelector(selectStore);
  const shopifyLoading = useAppSelector(selectStoreLoading);
  const [topupDialogOpen, setTopupDialogOpen] = useState(false);

  // Authentication checks
  const accessToken = session?.user?.access_token;
  const isAuthenticating = sessionStatus === "loading";
  const isUnauthenticated = sessionStatus === "unauthenticated";

  // Show loading state while authenticating
  if (isAuthenticating) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <p className="text-muted-foreground">Loading authentication...</p>
        </div>
      </div>
    );
  }

  // Show error state if not authenticated
  if (isUnauthenticated || !accessToken) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center gap-4">
          <Settings className="h-12 w-12 text-muted-foreground" />
          <p className="text-muted-foreground">Authentication required to access billing settings.</p>
          <p className="text-sm text-muted-foreground">Please log in again.</p>
        </div>
      </div>
    );
  }

  if (!store) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center gap-4">
          <Settings className="h-12 w-12 text-muted-foreground" />
          <p className="text-muted-foreground">No store connected.</p>
          <p className="text-sm text-muted-foreground">Please connect a store to access billing settings.</p>
        </div>
      </div>
    );
  }

  if (shopifyLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <p className="text-muted-foreground">Loading store information...</p>
        </div>
      </div>
    );
  }

  const isStripeProvider = session?.user?.provider === "stripe";
  const isShopifyProvider = session?.user?.provider === "shopify";

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Billing & Balance</h3>
        <p className="text-sm text-muted-foreground">
          {isStripeProvider 
            ? `Manage your account balance and billing for ${store.storeName || "your store"}.`
            : `View your balance for ${store.storeName || "your store"}. Billing is managed through Shopify.`
          }
        </p>
      </div>
      
      {/* Balance Section */}
      <div>
        <h4 className="text-base font-medium mb-3">Account Balance</h4>
        <div className="grid gap-4 md:grid-cols-1">
          <BalanceDisplay onTopupClick={() => setTopupDialogOpen(true)} />
          {/* <BalanceHistory /> */}
        </div>
      </div>
      
      {/* Billing Method Section */}
      <div>
        <h4 className="text-base font-medium mb-3">Billing Method</h4>
        
        {/* Shopify Billing Notice - Only show for Shopify provider */}
        {isShopifyProvider && (
          <Card className="border-blue-200 bg-blue-50 dark:bg-blue-950/20 dark:border-blue-900/40">
            <CardHeader className="pb-3">
              <CardTitle className="text-base text-blue-900 dark:text-blue-100">
                <DollarSign className="w-4 h-4 inline mr-2" />
                Shopify Billing Active
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-blue-800 dark:text-blue-200">
                All charges are processed through your Shopify subscription. 
                Visit the <a 
                  href={`https://admin.shopify.com/store/${store.data?.domain?.replace('.myshopify.com', '') || 'your-store'}/settings/billing`} 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="underline hover:text-blue-600 dark:hover:text-blue-100 font-medium"
                >
                  Shopify Billing Settings
                </a> to manage your usage and billing.
              </p>
            </CardContent>
          </Card>
        )}
        
        {/* Stripe Billing Notice */}
        {isStripeProvider && (
          <Card className="border-purple-200 bg-purple-50 dark:bg-purple-950/20 dark:border-purple-900/40">
            <CardHeader className="pb-3">
              <CardTitle className="text-base text-purple-900 dark:text-purple-100">
                <DollarSign className="w-4 h-4 inline mr-2" />
                Manual Billing Active
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-purple-800 dark:text-purple-200">
                You&apos;re using manual top-ups for billing. {" "}
                {!isStripeProvider ? (
                  <>
                    Connect a payment gateway in{" "}
                    <a 
                      href="/settings/payment-gateway" 
                      className="underline hover:text-purple-600 dark:hover:text-purple-100 font-medium"
                    >
                      Payment Gateway Settings
                    </a>{" "}
                    to enable automatic billing.
                  </>
                ) : (
                  "Set up a payment method below to enable automatic billing."
                )}
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Payment Method Setup Section - Only for Stripe users */}
      {isStripeProvider && (
        <>
          <Separator className="bg-gray-800" />
          
          <div>
            <h4 className="text-base font-medium mb-3">Payment Method</h4>
            <p className="text-sm text-muted-foreground mb-4">
              Set up your payment method for automatic billing and top-ups.
            </p>
            
            <StripeAccountComponent
              storeId={store.id}
              storeName={store.storeName || "My Store"}
            />
          </div>
        </>
      )}
      
      <Separator className="bg-gray-800" />
      
      {/* Payment History Section */}
      <div className="space-y-6">
        <div className="flex flex-row items-center justify-between">
          <div className="flex flex-col gap-1">
            <h4 className="text-base font-medium">Payment History</h4>
            <p className="text-sm text-muted-foreground">
              View all payment transactions for your store
            </p>
          </div>
        </div>
        
        {store && session?.user?.access_token && (
          <PaymentHistoryComponent 
            storeId={store.id} 
            token={session.user.access_token}
            provider={session.user.provider}
          />
        )}
      </div>
      
      {/* Manual Topup Dialog */}
      <ManualTopupDialog
        open={topupDialogOpen}
        onOpenChange={setTopupDialogOpen}
      />
    </div>
  );
}

export default function SettingsBillingPage() {
  return (
    <Suspense fallback={
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Billing & Balance</h3>
          <p className="text-sm text-muted-foreground">
            Manage your account balance and billing settings.
          </p>
        </div>
        
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <p className="text-muted-foreground">Loading billing settings...</p>
          </div>
        </div>
      </div>
    }>
      <BillingContent />
    </Suspense>
  );
}