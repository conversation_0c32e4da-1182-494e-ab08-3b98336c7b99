"use client";

import { useEffect, Suspense, useRef, useState } from "react";
import { useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { Loader2, Settings } from "lucide-react";
import { useSession } from "next-auth/react";
import { useProviderIntegration } from "@/hooks/useProviderIntegration";
import {
  StripeIntegrationCard,
  ShopifyIntegrationCard,
  RequestIntegrationCard,
} from "./_components";

function PaymentGatewayContent() {
  const searchParams = useSearchParams();
  const { data: session, status: sessionStatus } = useSession();
  const { 
    isShopifyConnected, 
    isStripeConnected, 
    stores,
    loading: providerLoading,
    error: providerError,
    refreshStatus
  } = useProviderIntegration();

  // Ref to track if we've already shown success toasts to prevent duplicates
  const hasShownStripeSuccess = useRef(false);
  const hasShownShopifySuccess = useRef(false);
  const isProcessingSuccess = useRef(false);
  const [uiStable, setUiStable] = useState(false);


  // Authentication checks
  const accessToken = session?.user?.access_token;
  const isAuthenticating = sessionStatus === "loading";
  const isUnauthenticated = sessionStatus === "unauthenticated";

  // Find connected store IDs
  // Backend now only returns active stores (isActive = true)
  const stripeStore = stores.find(store => 
    store.provider.toLowerCase() === 'stripe'
  );


  useEffect(() => {
    // Handle OAuth callback parameters
    const stripeConnected = searchParams.get("stripe_connected");
    const stripeError = searchParams.get("stripe_error");
    const shopifyConnected = searchParams.get("shopify_connected");
    const shopifyError = searchParams.get("shopify_error");

    // Early return if no relevant parameters
    if (!stripeConnected && !stripeError && !shopifyConnected && !shopifyError) {
      return;
    }

    // Handle Stripe OAuth callback success
    if (stripeConnected === "true" && !hasShownStripeSuccess.current && !isProcessingSuccess.current) {
      // Set processing flag immediately to prevent race conditions
      isProcessingSuccess.current = true;
      
      // Check if we've already shown this success in this browser session
      const lastShown = sessionStorage.getItem('stripe_success_shown');
      
      if (lastShown && (Date.now() - parseInt(lastShown)) < 15000) { // 15 seconds window
        hasShownStripeSuccess.current = true;
        isProcessingSuccess.current = false; // Reset processing flag
        return;
      }
      
      // Double-check that no other instance is currently processing
      const processingFlag = sessionStorage.getItem('stripe_processing');
      if (processingFlag && (Date.now() - parseInt(processingFlag)) < 5000) {
        hasShownStripeSuccess.current = true;
        isProcessingSuccess.current = false; // Reset processing flag
        return;
      }
      
      // Set processing flag in sessionStorage
      sessionStorage.setItem('stripe_processing', Date.now().toString());
      
      hasShownStripeSuccess.current = true;
      sessionStorage.setItem('stripe_success_shown', Date.now().toString());
      
      toast.success("Stripe Connected Successfully!", {
        description:
          "Your Stripe account has been connected and is ready to process payments.",
        duration: 5000,
      });

      // Clean up URL parameters first to prevent re-running - use replaceState to avoid rerender
      const url = new URL(window.location.href);
      url.searchParams.delete("stripe_connected");
      url.searchParams.delete("store_id");
      url.searchParams.delete("t"); // Remove timestamp parameter
      
      // Use replaceState instead of router methods to avoid component rerender
      if (url.toString() !== window.location.href) {
        window.history.replaceState({}, "", url.toString());
      }

      // Clear processing flag after a short delay
      setTimeout(() => {
        sessionStorage.removeItem('stripe_processing');
        isProcessingSuccess.current = false;
      }, 1000);

      // Refresh provider status after successful connection (delayed to avoid race condition)
      const timeoutId = setTimeout(() => {
        refreshStatus();
      }, 1000); // Increase delay to prevent UI jerking

      // Cleanup function if component unmounts
      return () => {
        clearTimeout(timeoutId);
        sessionStorage.removeItem('stripe_processing');
        isProcessingSuccess.current = false;
      };
    }

    // Handle Stripe OAuth callback error
    if (stripeError) {
      toast.error("Stripe Connection Failed", {
        description: decodeURIComponent(stripeError),
        duration: 7000,
      });

      // Clean up URL parameters
      const url = new URL(window.location.href);
      url.searchParams.delete("stripe_error");
      window.history.replaceState({}, "", url.toString());
    }

    // Handle Shopify OAuth callback success
    if (shopifyConnected === "true" && !hasShownShopifySuccess.current) {
      hasShownShopifySuccess.current = true;
      
      toast.success("Shopify Connected Successfully!", {
        description:
          "Your Shopify store has been connected and is ready for integration.",
        duration: 5000,
      });

      // Clean up URL parameters first
      const url = new URL(window.location.href);
      url.searchParams.delete("shopify_connected");
      url.searchParams.delete("store_id");
      window.history.replaceState({}, "", url.toString());

      // Refresh provider status after successful connection
      const timeoutId = setTimeout(() => {
        refreshStatus();
      }, 500);

      // Cleanup function if component unmounts
      return () => clearTimeout(timeoutId);
    }

    // Handle Shopify OAuth callback error
    if (shopifyError) {
      toast.error("Shopify Connection Failed", {
        description: decodeURIComponent(shopifyError),
        duration: 7000,
      });

      // Clean up URL parameters
      const url = new URL(window.location.href);
      url.searchParams.delete("shopify_error");
      window.history.replaceState({}, "", url.toString());
    }
  }, [searchParams.get("stripe_connected"), searchParams.get("stripe_error"), searchParams.get("shopify_connected"), searchParams.get("shopify_error")]); // Only depend on specific params we care about

  // Set UI stable after provider loading is complete
  useEffect(() => {
    if (!providerLoading && !isAuthenticating) {
      const timer = setTimeout(() => setUiStable(true), 100);
      return () => clearTimeout(timer);
    }
  }, [providerLoading, isAuthenticating]);

  // Show loading state while authenticating or loading provider status
  if (isAuthenticating || (providerLoading && !uiStable)) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <p className="text-muted-foreground">
            {isAuthenticating ? "Loading authentication..." : "Loading integrations..."}
          </p>
        </div>
      </div>
    );
  }

  // Show error state if not authenticated
  if (isUnauthenticated || !accessToken) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center gap-4">
          <Settings className="h-12 w-12 text-muted-foreground" />
          <p className="text-muted-foreground">
            Authentication required to access payment gateway settings.
          </p>
          <p className="text-sm text-muted-foreground">Please log in again.</p>
        </div>
      </div>
    );
  }

  // Show error state if provider loading failed
  if (providerError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center gap-4">
          <Settings className="h-12 w-12 text-muted-foreground" />
          <p className="text-muted-foreground">Failed to load integration status.</p>
          <p className="text-sm text-muted-foreground">{providerError}</p>
          <button 
            onClick={refreshStatus}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Integrations</h3>
        <p className="text-sm text-muted-foreground">
          Integrate your gateway to prevent chargebacks.
        </p>
      </div>

      {/* Integrations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <ShopifyIntegrationCard
          isConnected={isShopifyConnected}
          isAvailable={true}
        />

        <StripeIntegrationCard
          isConnected={isStripeConnected}
          isAvailable={true}
          connectedStoreId={stripeStore?.id}
          onStatusChange={refreshStatus}
        />

        <RequestIntegrationCard />
      </div>
    </div>
  );
}

export default function SettingsPaymentGatewayPage() {
  return (
    <Suspense
      fallback={
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium">Integrations</h3>
            <p className="text-sm text-muted-foreground">
              Integrate your gateway to prevent chargebacks.
            </p>
          </div>

          <div className="flex items-center justify-center h-64">
            <div className="flex flex-col items-center gap-4">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              <p className="text-muted-foreground">Loading integrations...</p>
            </div>
          </div>
        </div>
      }
    >
      <PaymentGatewayContent />
    </Suspense>
  );
}
