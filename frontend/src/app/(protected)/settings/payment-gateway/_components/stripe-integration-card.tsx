"use client";

import { useState } from "react";
import { toast } from "sonner";
import { useSession } from "next-auth/react";
import { StripeService } from "@/services/stripe.service";
import { IntegrationCard } from "./integration-card";
import StripeSquareLogo from "@/components/svg/partners/StripeSquareLogo";
import { getAlertInfosByStoreId } from "@/services/alert-info.service";

interface StripeIntegrationCardProps {
  isConnected: boolean;
  isAvailable: boolean;
  connectedStoreId?: string;
  onStatusChange?: () => void;
}

export function StripeIntegrationCard({ isConnected, isAvailable, connectedStoreId, onStatusChange }: StripeIntegrationCardProps) {
  const [isConnecting, setIsConnecting] = useState(false);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const { data: session } = useSession();


  const handleConnect = async () => {
    setIsConnecting(true);
    
    try {
      // Get the Stripe OAuth URL from the service with user_id
      const userId = session?.user?.user_id;
      const authUrl = await StripeService.getAuthUrl(userId);
      
      // Redirect to Stripe OAuth
      window.location.href = authUrl;
    } catch (error: any) {
      toast.error("Failed to connect to Stripe. Please try again.");
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    if (!connectedStoreId) {
      toast.error("Unable to disconnect: Store ID not found. Please refresh the page and try again.");
      return;
    }

    setIsDisconnecting(true);
    
    try {
      // First check if there are any active alert infos for this store
      const alertInfos = await getAlertInfosByStoreId(connectedStoreId);
      
      // Filter out alert infos that are not in CLOSED status
      const activeAlertInfos = alertInfos.filter(
        (alertInfo: any) => alertInfo.registrationStatus !== 'CLOSED'
      );
      
      if (activeAlertInfos.length > 0) {
        toast.warning(
          `Please close all active descriptors before disconnecting Stripe (${activeAlertInfos.length} active)`,
          { duration: 5000 }
        );
        setIsDisconnecting(false);
        return;
      }
      
      // If no active alert infos, proceed with disconnection
      const result = await StripeService.disconnectIntegration(connectedStoreId);
      
      if (result.success) {
        toast.success("Stripe integration disconnected successfully!");
        onStatusChange?.();
      } else {
        toast.error(result.message || "Failed to disconnect Stripe integration");
      }
    } catch (error: any) {
      // Check if the error is related to active alert infos from backend
      if (error.response?.data?.message?.includes('active alert registrations')) {
        toast.error(error.response.data.message, { duration: 6000 });
      } else {
        toast.error("Failed to disconnect Stripe integration. Please try again.");
      }
    } finally {
      setIsDisconnecting(false);
    }
  };

  const integration = {
    id: "stripe",
    name: "Stripe",
    description: "Connect your Stripe account to fully automate your Stripe disputes.",
    icon: <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
      <StripeSquareLogo className="w-6 h-6" />
    </div>,
    isConnected,
    isAvailable,
    onConnect: handleConnect,
    onDisconnect: handleDisconnect
  };

  return (
    <IntegrationCard
      integration={integration}
      isConnecting={isConnecting}
      isDisconnecting={isDisconnecting}
    />
  );
}