"use client";

import { Plus } from "lucide-react";
import { IntegrationCard } from "./integration-card";

export function RequestIntegrationCard() {
  const handleRequestIntegration = () => {
    // You could implement a modal or redirect to a form
    // For now, just show a simple alert
    alert("Thank you for your interest! We'll add this to our integration roadmap.");
  };

  const integration = {
    id: "request",
    name: "Integration missing?",
    description: "Let our team know which integration you are missing and we will add it as soon as possible.",
    icon: <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
      <Plus className="w-6 h-6 text-gray-600" />
    </div>,
    isConnected: false,
    isAvailable: true,
    isRequest: true,
    onConnect: handleRequestIntegration
  };

  return (
    <IntegrationCard
      integration={integration}
      isConnecting={false}
    />
  );
}