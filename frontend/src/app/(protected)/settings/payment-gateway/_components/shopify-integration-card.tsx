"use client";

import { useState } from "react";
import { toast } from "sonner";
import { ShopifyService } from "@/services/shopify.service";
import { IntegrationCard } from "./integration-card";
import ShopifyLogo from "@/components/svg/partners/ShopifyLogo";

interface ShopifyIntegrationCardProps {
  isConnected: boolean;
  isAvailable: boolean;
}

export function ShopifyIntegrationCard({ isConnected, isAvailable }: ShopifyIntegrationCardProps) {
  const [isConnecting, setIsConnecting] = useState(false);

  const handleConnect = async () => {
    setIsConnecting(true);
    
    try {
      // For Shopify, we would typically need a shop domain
      // This is a placeholder - in real implementation, you might need a dialog to get shop name
      const shopDomain = prompt("Enter your Shopify store domain (without .myshopify.com):");
      
      if (!shopDomain) {
        setIsConnecting(false);
        return;
      }

      // Get the Shopify OAuth URL from the service
      const authUrl = await ShopifyService.getAuthUrl(`${shopDomain}.myshopify.com`);
      
      // Redirect to Shopify OAuth
      window.location.href = authUrl;
    } catch (error: any) {
      toast.error("Failed to connect to Shopify. Please try again.");
      setIsConnecting(false);
    }
  };

  const integration = {
    id: "shopify",
    name: "Shopify Payments",
    description: "Connect your Shopify Payments account to fully automate your Shopify disputes.",
    icon: <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
      <ShopifyLogo className="w-6 h-6" />
    </div>,
    isConnected,
    isAvailable,
    onConnect: handleConnect,
    disableDisconnect: true
  };

  return (
    <IntegrationCard
      integration={integration}
      isConnecting={isConnecting}
    />
  );
}