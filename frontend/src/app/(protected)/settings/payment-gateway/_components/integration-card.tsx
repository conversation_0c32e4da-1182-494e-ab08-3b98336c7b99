"use client";

import { ReactNode, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Plus } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export interface Integration {
  id: string;
  name: string;
  description: string;
  icon: ReactNode;
  isConnected?: boolean;
  isAvailable?: boolean;
  isRequest?: boolean;
  badge?: string;
  onConnect?: () => void;
  onDisconnect?: () => void;
  disableDisconnect?: boolean;
}

interface IntegrationCardProps {
  integration: Integration;
  isConnecting?: boolean;
  isDisconnecting?: boolean;
}

export function IntegrationCard({ integration, isConnecting = false, isDisconnecting = false }: IntegrationCardProps) {
  const isLoading = isConnecting || isDisconnecting;
  const [showDisconnectDialog, setShowDisconnectDialog] = useState(false);

  const handleDisconnectClick = () => {
    if (integration.disableDisconnect || !integration.onDisconnect) return;
    setShowDisconnectDialog(true);
  };

  const confirmDisconnect = () => {
    if (integration.onDisconnect) {
      integration.onDisconnect();
    }
    setShowDisconnectDialog(false);
  };

  return (
    <>
      <Card className="p-6 hover:shadow-md transition-shadow">
        <CardContent className="p-0">
          <div className="space-y-4">
            {/* Header with icon and badge */}
            <div className="flex items-start justify-between">
              {integration.icon}
              {integration.badge && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-100">
                  {integration.badge}
                </Badge>
              )}
            </div>
            
            {/* Title and description */}
            <div className="space-y-2">
              <h4 className="font-medium text-base">{integration.name}</h4>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {integration.description}
              </p>
            </div>
            
            {/* Action button */}
            <div className="pt-2">
              {integration.isRequest ? (
                <Button variant="outline" className="w-full" disabled>
                  <Plus className="w-4 h-4 mr-2" />
                  Request integration
                </Button>
              ) : integration.isConnected ? (
                <Button 
                  variant="outline"
                  className={`w-full ${
                    integration.disableDisconnect 
                      ? '!bg-slate-100 !text-slate-600 !border-slate-300 cursor-not-allowed' 
                      : '!bg-red-500 hover:!bg-red-600 !text-white !border-red-500 hover:!border-red-600'
                  }`}
                  onClick={integration.disableDisconnect ? undefined : handleDisconnectClick}
                  disabled={isLoading || integration.disableDisconnect}
                >
                  {isDisconnecting ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Disconnecting...
                    </>
                  ) : integration.disableDisconnect ? (
                    "Connected"
                  ) : (
                    "Disconnect"
                  )}
                </Button>
              ) : integration.isAvailable ? (
                <Button 
                  variant="outline" 
                  className="w-full !bg-blue-500 hover:!bg-blue-600 !text-white !border-blue-500 hover:!border-blue-600" 
                  onClick={integration.onConnect}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Connecting...
                    </>
                  ) : (
                    "Connect"
                  )}
                </Button>
              ) : (
                <Button variant="outline" className="w-full" disabled>
                  Connect
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
      
      <AlertDialog open={showDisconnectDialog} onOpenChange={setShowDisconnectDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently disconnect your {integration.name} integration
              and you&apos;ll need to reconnect to use it again.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="cursor-pointer">Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDisconnect} className="cursor-pointer">
              Disconnect
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}