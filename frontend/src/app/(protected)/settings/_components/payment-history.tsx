"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Loader2, RefreshCcw, Download, FileX, Eye, History } from "lucide-react";
import { toast } from "sonner";
import { TableSkeleton } from "@/components/ui/table-skeleton";
import { Loading } from "@/components/ui/loading";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
  fetchPaymentHistoryByStore,
  selectPaymentHistoryByStoreId,
  selectPaymentHistoryLoading,
  selectPaymentHistoryError,
  selectPaymentHistoryTotalPages,
  selectPaymentHistoryCurrentPage,
  selectPaymentHistoryTotalItems
} from "@/redux/slices/payment-history";
import { TablePagination } from "@/components/ui/table-pagination";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";
import { getBillById, Bill } from "@/services/bill.service";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";


interface PaymentHistoryProps {
  storeId: string;
  token: string;
  provider?: string;
}

export function PaymentHistoryComponent({ storeId, token, provider }: PaymentHistoryProps) {
  const dispatch = useAppDispatch();
  const payments = useAppSelector(state => selectPaymentHistoryByStoreId(state, storeId));
  const isLoading = useAppSelector(selectPaymentHistoryLoading);
  const error = useAppSelector(selectPaymentHistoryError);
  const totalPages = useAppSelector(selectPaymentHistoryTotalPages);
  const currentPage = useAppSelector(selectPaymentHistoryCurrentPage);
  const totalItems = useAppSelector(selectPaymentHistoryTotalItems);

  const pageSize = 10;

  const [selectedBill, setSelectedBill] = useState<Bill | null>(null);
  const [isBillDialogOpen, setIsBillDialogOpen] = useState(false);
  const [isLoadingBill, setIsLoadingBill] = useState(false);

  useEffect(() => {
    if (error) {
      toast.error("Error loading payment history");
      setIsRefreshing(false);
    }
  }, [error]);

  useEffect(() => {
    if (!isLoading && isRefreshing) {
      setIsRefreshing(false);
    }
  }, [isLoading]);

  useEffect(() => {
    // Fetch payment history when component mounts
    fetchPaymentHistory();
  }, [storeId, token, provider]);

  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchPaymentHistory = () => {
    if (storeId && token) {
      setIsRefreshing(true);
      dispatch(fetchPaymentHistoryByStore({
        storeId,
        query: { page: 1, limit: pageSize },
        token,
        provider
      }));
    }
  };

  const handlePageChange = (page: number) => {
    if (storeId && token) {
      dispatch(fetchPaymentHistoryByStore({
        storeId,
        query: { page, limit: pageSize },
        token,
        provider
      }));
    }
  };

  const handleViewBill = async (billId: string) => {
    if (!billId || billId === "N/A") {
      toast.error("No bill associated with this payment");
      return;
    }

    if (!token) {
      toast.error("Authentication required");
      return;
    }

    try {
      setIsLoadingBill(true);
      setIsBillDialogOpen(true);
      const bill = await getBillById(billId, token, provider);
      setSelectedBill(bill);
    } catch (error) {
      toast.error("Failed to load bill details");
      setIsBillDialogOpen(false);
    } finally {
      setIsLoadingBill(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString() + ' ' +
      new Date(dateString).toLocaleTimeString();
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
    }).format(amount / 100); // Convert from cents to dollars
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'succeeded':
      case 'paid':
        return 'bg-green-100 text-green-900 px-2.5 py-1 rounded-full text-xs font-medium';
      case 'pending':
      case 'processing':
        return 'bg-amber-100 text-amber-900 px-2.5 py-1 rounded-full text-xs font-medium';
      case 'failed':
        return 'bg-red-100 text-red-900 px-2.5 py-1 rounded-full text-xs font-medium';
      default:
        return 'bg-orange-100 text-orange-900 px-2.5 py-1 rounded-full text-xs font-medium';
    }
  };

  const getBillStatusBadge = (status: string) => {
    switch (status.toUpperCase()) {
      case 'PAID':
        return <Badge variant="default" className="bg-green-500">Paid</Badge>;
      case 'PENDING':
        return <Badge variant="secondary">Pending</Badge>;
      case 'FAILED':
        return <Badge variant="destructive">Failed</Badge>;
      case 'CANCELLED':
        return <Badge variant="outline">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const exportToCSV = () => {
    if (payments.length === 0) {
      toast.error("No payment data to export");
      return;
    }

    const headers = [
      "Date",
      "Bill ID",
      "Description",
      "Amount",
      "Status",
      "Payment Date"
    ];

    const dataRows = payments.map(payment => [
      formatDate(payment.createdAt),
      payment.billId || "N/A",
      payment.description || "N/A",
      formatCurrency(payment.amount, payment.currency),
      payment.status,
      formatDate(payment.paymentDate)
    ]);

    const csvContent = [
      headers.join(","),
      ...dataRows.map(row => row.join(","))
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `payment-history-${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (isLoading && currentPage === 1 && !isRefreshing) {
    return <Loading text="Loading payment history..." />;
  }

  return (
    <>
      <div className="space-y-6">
        <div className="flex flex-row items-center justify-between">
          <div className="flex flex-row gap-2 items-center">
            {/* <History className="h-10 w-10" />
            <div className="flex flex-col gap-1">
              <h1 className="text-2xl font-semibold tracking-tight">Payment History</h1>
              <p className="text-sm text-muted-foreground">
                View all payment transactions for your store
              </p>
            </div> */}
          </div>
          <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={fetchPaymentHistory}
                className="cursor-pointer"
                disabled={isRefreshing}
              >
                {isRefreshing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Refreshing...
                  </>
                ) : (
                  <>
                    <RefreshCcw className="h-4 w-4 mr-2" />
                    Refresh
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={exportToCSV}
                disabled={payments.length === 0}
                className="cursor-pointer disabled:cursor-not-allowed"
              >
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
            </div>
        </div>

        {isRefreshing && <Loading size="sm" text="Refreshing data..." className="h-32" />}
        {!isRefreshing && payments.length > 0 ? (
          <>
            <div className="border border-border rounded-lg overflow-hidden">
              <Table>
                <TableHeader className="sticky top-0 z-10">
                  <TableRow className="hover:bg-transparent bg-gray-950">
                    <TableHead className="font-semibold text-muted-foreground uppercase border-r border-border">Description</TableHead>
                    <TableHead className="font-semibold text-muted-foreground uppercase border-r border-border">Bill ID</TableHead>
                    <TableHead className="font-semibold text-muted-foreground uppercase border-r border-border">Created Date</TableHead>
                    <TableHead className="font-semibold text-muted-foreground uppercase border-r border-border text-right">Amount</TableHead>
                    <TableHead className="font-semibold text-muted-foreground uppercase border-r border-border">Currency</TableHead>
                    <TableHead className="font-semibold text-muted-foreground uppercase text-center border-r border-border">Status</TableHead>
                    <TableHead className="font-semibold text-muted-foreground uppercase border-r border-border">Payment Date</TableHead>
                    <TableHead className="font-semibold text-muted-foreground uppercase">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payments.map((payment) => (
                    <TableRow key={payment.id} className="hover:bg-muted/50 transition-colors border-b border-border">
                      <TableCell className="border-r border-border">
                        <div className="font-medium max-w-xs truncate">{payment.description || payment.bill?.description || "N/A"}</div>
                      </TableCell>
                      <TableCell className="border-r border-border">
                        <div className="font-medium">{payment.billId || "N/A"}</div>
                      </TableCell>
                      <TableCell className="whitespace-nowrap  border-r border-border">{formatDate(payment.createdAt)}</TableCell>
                      <TableCell className={`font-mono border-r border-border text-right ${payment.amount > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {payment.amount > 0 ? '+' : ''}{formatCurrency(payment.amount, payment.currency)}
                      </TableCell>
                      <TableCell className=" border-r border-border">{payment.currency}</TableCell>
                      <TableCell className="text-center border-r border-border">
                        <span className={getStatusColor(payment.status)}>
                          {payment.status.charAt(0).toUpperCase() + payment.status.slice(1).toLowerCase()}
                        </span>
                      </TableCell>
                      <TableCell className="whitespace-nowrap  border-r border-border">{formatDate(payment.paymentDate)}</TableCell>
                      <TableCell className="">
                        {payment.billId && payment.billId !== "N/A" && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewBill(payment.billId)}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            View Bill
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            <TablePagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalItems}
              pageSize={pageSize}
              onPageChange={handlePageChange}
              isLoading={isLoading}
            />
          </>
        ) : (
          <div className="flex flex-col items-center justify-center py-32">
            <div className="text-center space-y-3">
              <div className="w-12 h-12 bg-gray-100 rounded-full mx-auto flex items-center justify-center">
                <FileX className="h-6 w-6 text-gray-400" />
              </div>
              <h3 className="font-medium">No payment history found</h3>
              <p className="text-sm text-muted-foreground max-w-sm">
                No payment history found. Payments will appear here once customers start paying your bills.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Bill Details Dialog */}
      <Dialog open={isBillDialogOpen} onOpenChange={setIsBillDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Bill Details</DialogTitle>
            <DialogDescription>
              View detailed information about this bill
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {isLoadingBill ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : selectedBill ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-muted-foreground">Bill ID</Label>
                    <p className="font-medium">{selectedBill.id}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Status</Label>
                    <div className="mt-1">{getBillStatusBadge(selectedBill.status)}</div>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Amount</Label>
                    <p className="font-medium font-mono">
                      {formatCurrency(selectedBill.amount, selectedBill.currency)}
                    </p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Due Date</Label>
                    <p className="font-medium">{formatDate(selectedBill.dueDate)}</p>
                  </div>
                  <div className="col-span-2">
                    <Label className="text-muted-foreground">Description</Label>
                    <p className="font-medium">{selectedBill.description || "N/A"}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Created At</Label>
                    <p className="font-medium">{formatDate(selectedBill.createdAt)}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Updated At</Label>
                    <p className="font-medium">{formatDate(selectedBill.updatedAt)}</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No bill details available
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsBillDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
