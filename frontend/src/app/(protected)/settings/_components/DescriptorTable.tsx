"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Trash2Icon } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Shopify<PERSON>ogo from "@/components/svg/partners/ShopifyLogo";
import StripeSquareLogo from "@/components/svg/partners/StripeSquareLogo";

interface Descriptor {
  id: string;
  name: string;
  status: string;
  paymentGateway?: string;
}

interface DescriptorTableProps {
  descriptors: Descriptor[];
  className?: string;
  onDelete?: (id: string) => void;
}

export default function DescriptorTable({
  descriptors,
  className = "",
  onDelete,
}: DescriptorTableProps) {
  const getPaymentGatewayLogo = (gateway: string | undefined) => {
    if (!gateway) return null;
    
    const gatewayLower = gateway.toLowerCase();
    if (gatewayLower.includes('shopify')) {
      return <ShopifyLogo width={20} height={20} />;
    } else if (gatewayLower.includes('stripe')) {
      return <StripeSquareLogo width={20} height={20} />;
    }
    return <span className="text-sm text-muted-foreground">-</span>;
  };
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "EFFECTED":
        return (
          <Badge 
            variant="default" 
            className="bg-green-100 text-green-800 hover:bg-green-100 border-green-200"
          >
            {status}
          </Badge>
        );
      case "WAITING":
        return (
          <Badge 
            variant="secondary" 
            className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100 border-yellow-200"
          >
            {status}
          </Badge>
        );
      case "CLOSING":
        return (
          <Badge 
            variant="outline" 
            className="bg-orange-100 text-orange-800 hover:bg-orange-100 border-orange-200"
          >
            {status}
          </Badge>
        );
      case "CLOSED":
        return (
          <Badge 
            variant="outline" 
            className="bg-red-50 text-red-700 hover:bg-red-50 border-red-200"
          >
            {status}
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };

  return (
    <div className={`rounded-md border ${className}`}>
      <Table>
        <TableHeader>
          <TableRow className="bg-muted/50">
            <TableHead className="font-medium">Descriptor</TableHead>
            <TableHead className="text-right font-medium">Status</TableHead>
            {onDelete && <TableHead className="text-right font-medium">Action</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          {descriptors.map((descriptor) => {
            const canDelete = descriptor.status !== 'CLOSING' && descriptor.status !== 'CLOSED';
            return (
              <TableRow key={descriptor.id}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getPaymentGatewayLogo(descriptor.paymentGateway)}
                    <span className="font-medium">{descriptor.name}</span>
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  {getStatusBadge(descriptor.status)}
                </TableCell>
                {onDelete && (
                  <TableCell className="text-right">
                    {canDelete && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDelete(descriptor.id)}
                        className="text-destructive hover:text-destructive hover:bg-destructive/10"
                      >
                        <Trash2Icon className="h-4 w-4" />
                      </Button>
                    )}
                  </TableCell>
                )}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}