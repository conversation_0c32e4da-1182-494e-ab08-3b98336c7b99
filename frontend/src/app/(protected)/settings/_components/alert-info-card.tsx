"use client";

import { AlertInfo } from "@/services/alert-info.service";
import AlertProviderCard from "./AlertProviderCard";
import DescriptorTable from "./DescriptorTable";
import { Veri<PERSON>Logo, EthocaLogo } from "@/components/ui/logos";
import { useProviderIntegration } from "@/hooks/useProviderIntegration";

interface AlertAccountsTableProps {
  loading: boolean;
  alertAccounts: AlertInfo[];
  onDelete: (accountId: string) => void;
  onAdd?: (alertType?: "RDR" | "ETHOCA") => void;
}

export default function AlertAccountsTable({
  loading,
  alertAccounts,
  onDelete,
  onAdd,
}: AlertAccountsTableProps) {
  const { stores } = useProviderIntegration();

  // Helper function to get payment gateway for a store
  const getPaymentGateway = (storeId: string): string => {
    const store = stores.find((s) => s.id === storeId);
    if (!store) return "-";
    return store.provider === "shopify" ? "Shopify Payments" : "Stripe";
  };

  // Group descriptors by provider with actual status and payment gateway
  const groupedDescriptors = alertAccounts.reduce((acc, account) => {
    const key =
      account.alertType === "ETHOCA" ? "ETHOCA_MASTERCARD" : account.alertType;
    if (!acc[key]) {
      acc[key] = [];
    }

    acc[key].push({
      id: account.id,
      name: account.descriptor,
      status: account.registrationStatus,
      paymentGateway: getPaymentGateway(account.storeId),
    });
    return acc;
  }, {} as Record<string, Array<{ id: string; name: string; status: string; paymentGateway: string }>>);

  if (loading) {
    return (
      <div className="w-full h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading descriptors...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Provider Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Ethoca Mastercard Card */}
        <div className="space-y-4">
          <AlertProviderCard
            providerName="Ethoca"
            partnerName="Mastercard"
            logo={<EthocaLogo width={32} height={32} />}
            price="$25"
            priceUnit="per alert"
            networkCoverage="Worldwide"
            coveragePercentage="95%"
            enrollmentTime="Up to 12 hours"
            paymentGateway="Depends"
            hasDescriptors={groupedDescriptors["ETHOCA_MASTERCARD"]?.length > 0}
            onAddDescriptor={() => onAdd?.("ETHOCA")}
            providerType="Ethoca"
          />
          {groupedDescriptors["ETHOCA_MASTERCARD"] && (
            <DescriptorTable
              descriptors={groupedDescriptors["ETHOCA_MASTERCARD"]}
              onDelete={onDelete}
            />
          )}
        </div>

        {/* RDR Visa Card */}
        <div className="space-y-4">
          <AlertProviderCard
            providerName="RDR"
            partnerName="Visa"
            logo={<VerifiLogo width={32} height={32} />}
            price="$15"
            priceUnit="per alert"
            networkCoverage="Worldwide"
            coveragePercentage="97%"
            enrollmentTime="Up to 72 hours"
            paymentGateway="Not needed"
            hasDescriptors={groupedDescriptors["RDR"]?.length > 0}
            onAddDescriptor={() => onAdd?.("RDR")}
            providerType="RDR"
          />
          {groupedDescriptors["RDR"] && (
            <DescriptorTable
              descriptors={groupedDescriptors["RDR"]}
              onDelete={onDelete}
            />
          )}
        </div>
      </div>
    </div>
  );
}
