import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useSession } from "next-auth/react";
import AddAlertAccountDialog from "./add-alert-account-dialog";
import AlertAccountsTable from "./alert-info-card";
import { toast } from "sonner";
import { useAppDispatch } from "@/redux/hooks";
import { deleteAlertInfoAsync } from "@/redux/slices/alert-info";
import { useProviderIntegration } from "@/hooks/useProviderIntegration";
import { useDescriptors } from "@/hooks/useDescriptors";

export default function AlertAccountsTab() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { data: session, status: sessionStatus } = useSession();

  // Get all stores using the hook
  const { stores: allStores } = useProviderIntegration();
  
  // Use descriptors hook
  const { descriptors: allAlertInfos, loading: loadingDescriptors, refresh: refreshDescriptors } = useDescriptors();

  // Component state
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteAccountId, setDeleteAccountId] = useState<string>("");
  const [defaultAlertType, setDefaultAlertType] = useState<'RDR' | 'ETHOCA' | undefined>(undefined);

  // Authentication checks
  const accessToken = session?.user?.access_token;
  const provider = session?.user?.provider; // Add provider from session
  const isAuthenticating = sessionStatus === "loading";
  const isUnauthenticated = sessionStatus === "unauthenticated";

  // Authentication checks moved here for clarity

  // Show loading state while authenticating
  if (isAuthenticating) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <p className="text-muted-foreground">Loading authentication...</p>
        </div>
      </div>
    );
  }

  // Show error state if not authenticated
  if (isUnauthenticated || !accessToken) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center gap-4">
          <p className="text-muted-foreground">
            Authentication required to access descriptors.
          </p>
          <Button onClick={() => router.push("/auth/connect")}>Sign In</Button>
        </div>
      </div>
    );
  }

  // Use merged descriptors from all stores
  const alertAccounts = allAlertInfos;

  const handleAddAccount = (alertType?: 'RDR' | 'ETHOCA') => {
    setDefaultAlertType(alertType);
    setShowAddDialog(true);
  };

  const handleDeleteAccount = (accountId: string) => {
    setDeleteAccountId(accountId);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (!deleteAccountId || !accessToken) return;

    try {
      await dispatch(
        deleteAlertInfoAsync({
          id: deleteAccountId,
          token: accessToken,
          provider,
        })
      );
      toast.success("Descriptor deleted successfully");
      setShowDeleteDialog(false);
      setDeleteAccountId("");
    } catch (error) {
      toast.error("Failed to delete descriptor");
    }
  };

  const handleAddComplete = async () => {
    setShowAddDialog(false);
    setDefaultAlertType(undefined);
    // Use optimized refresh function
    await refreshDescriptors();
  };

  if (loadingDescriptors) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <p className="text-muted-foreground">
            Loading descriptors from all payment gateways...
          </p>
        </div>
      </div>
    );
  }

  if (!allStores.length) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <p className="text-muted-foreground">
          You don&apos;t have any linked payment gateways.
        </p>
        <Button
          className="mt-4 cursor-pointer"
          onClick={() => (window.location.href = "/auth/connect")}
        >
          Add Payment Gateway
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Descriptors</h3>
        </div>
        <div className="text-sm text-muted-foreground">
          Manage your Descriptor configurations and settings.
        </div>
      </div>

      <AlertAccountsTable
        loading={false}
        alertAccounts={alertAccounts}
        onDelete={handleDeleteAccount}
        onAdd={handleAddAccount}
      />

      {showAddDialog && (
        <AddAlertAccountDialog
          accessToken={accessToken}
          provider={provider}
          open={showAddDialog}
          onClose={() => {
            setShowAddDialog(false);
            setDefaultAlertType(undefined);
          }}
          onComplete={handleAddComplete}
          defaultAlertType={defaultAlertType}
        />
      )}

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              descriptor account and remove all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="cursor-pointer">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="cursor-pointer"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
