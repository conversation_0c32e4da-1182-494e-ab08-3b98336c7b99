"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { useAppDispatch } from "@/redux/hooks";
import { createAlertInfoAsync } from "@/redux/slices/alert-info";
import { useProviderIntegration } from "@/hooks/useProviderIntegration";
import ShopifyLogo from "@/components/svg/partners/ShopifyLogo";
import StripeSquareLogo from "@/components/svg/partners/StripeSquareLogo";

interface AddAlertAccountDialogProps {
  accessToken: string;
  provider?: string;
  open: boolean;
  onClose: () => void;
  onComplete: () => void;
  defaultAlertType?: 'RDR' | 'ETHOCA';
}

export default function AddAlertAccountDialog({
  accessToken,
  provider,
  open,
  onClose,
  onComplete,
  defaultAlertType,
}: AddAlertAccountDialogProps) {
  const dispatch = useAppDispatch();
  const { stores, loading: storesLoading } = useProviderIntegration();
  
  const [selectedStoreId, setSelectedStoreId] = useState<string>("");
  const [alertType, setAlertType] = useState<"RDR" | "ETHOCA">(defaultAlertType || "RDR");
  const [descriptor, setDescriptor] = useState("");
  const [bin, setBin] = useState("");
  const [caid, setCaid] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Function to get provider icon
  const getProviderIcon = (providerName: string) => {
    const providerLower = providerName.toLowerCase();
    switch (providerLower) {
      case 'shopify':
        return <ShopifyLogo width={20} height={20} className="mr-2 flex-shrink-0" />;
      case 'stripe':
        return <StripeSquareLogo width={20} height={20} className="mr-2 flex-shrink-0" />;
      default:
        return <div className="w-5 h-5 bg-gray-400 rounded mr-2 flex-shrink-0" />;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedStoreId) {
      toast.error("Please select a payment gateway");
      return;
    }

    if (!descriptor.trim()) {
      toast.error("Descriptor is required");
      return;
    }

    // For RDR, BIN and CAID are required
    if (alertType === "RDR") {
      if (!bin.trim()) {
        toast.error("BIN is required for RDR");
        return;
      }
      if (!caid.trim()) {
        toast.error("CAID is required for RDR");
        return;
      }
    }

    setIsSubmitting(true);

    try {
      await dispatch(createAlertInfoAsync({
        data: {
          alertType,
          descriptor: descriptor.trim(),
          bin: bin.trim() || null,
          caid: caid.trim() || null,
          storeId: selectedStoreId,
        },
        token: accessToken,
        provider,
      })).unwrap();

      toast.success("Descriptor created successfully");
      onComplete();
      
      // Reset form
      setSelectedStoreId("");
      setDescriptor("");
      setBin("");
      setCaid("");
      setAlertType("RDR");
    } catch (error) {
      toast.error("Failed to create Descriptor");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Descriptor</DialogTitle>
          <DialogDescription>
            Enter the details for the new Descriptor.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="store-select">Payment Gateway</Label>
            <Select
              value={selectedStoreId}
              onValueChange={setSelectedStoreId}
              disabled={storesLoading}
            >
              <SelectTrigger id="store-select">
                <SelectValue placeholder={storesLoading ? "Loading payment gateways..." : "Select payment gateway"}>
                  {selectedStoreId && (() => {
                    const selectedStore = stores.find(store => store.id === selectedStoreId);
                    return selectedStore ? (
                      <div className="flex items-center">
                        {getProviderIcon(selectedStore.provider)}
                        <span className="truncate">{selectedStore.storeName}</span>
                      </div>
                    ) : null;
                  })()}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {stores.map((store) => (
                  <SelectItem key={store.id} value={store.id} className="flex items-center">
                    <div className="flex items-center w-full">
                      {getProviderIcon(store.provider)}
                      <span className="truncate">{store.storeName}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="alert-type">Alert Type</Label>
            <Select
              value={alertType}
              onValueChange={(value) => setAlertType(value as "RDR" | "ETHOCA")}
            >
              <SelectTrigger id="alert-type">
                <SelectValue placeholder="Select alert type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="RDR">RDR</SelectItem>
                <SelectItem value="ETHOCA">ETHOCA</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="descriptor">Descriptor</Label>
            <Input
              id="descriptor"
              value={descriptor}
              onChange={(e) => setDescriptor(e.target.value)}
              placeholder="Enter descriptor"
              required
            />
          </div>

          {alertType !== "ETHOCA" && (
            <>
              <div className="space-y-2">
                <Label htmlFor="bin">BIN {alertType === "RDR" && <span className="text-destructive">*</span>}</Label>
                <Input
                  id="bin"
                  value={bin}
                  onChange={(e) => setBin(e.target.value)}
                  placeholder="Enter BIN"
                  required={alertType === "RDR"}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="caid">CAID {alertType === "RDR" && <span className="text-destructive">*</span>}</Label>
                <Input
                  id="caid"
                  value={caid}
                  onChange={(e) => setCaid(e.target.value)}
                  placeholder="Enter CAID"
                  required={alertType === "RDR"}
                />
              </div>
            </>
          )}

          <DialogFooter className="pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
              className="cursor-pointer"
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting} className="cursor-pointer">
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
