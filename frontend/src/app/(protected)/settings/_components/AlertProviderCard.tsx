"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckIcon, PlusIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface AlertProviderCardProps {
  providerName: string;
  partnerName?: string;
  logo: React.ReactNode;
  price: string;
  priceUnit: string;
  networkCoverage: string;
  coveragePercentage: string;
  enrollmentTime: string;
  paymentGateway: string;
  hasDescriptors?: boolean;
  onAddDescriptor?: () => void;
  providerType?: string;
  className?: string;
}

export default function AlertProviderCard({
  providerName,
  partnerName,
  logo,
  price,
  priceUnit,
  networkCoverage,
  coveragePercentage,
  enrollmentTime,
  paymentGateway,
  hasDescriptors = false,
  onAddDescriptor,
  providerType,
  className,
}: AlertProviderCardProps) {
  return (
    <Card className={cn("relative p-0", className)}>
      <CardContent className="p-6">
        {/* Header */}
        <div className="flex items-center gap-3 mb-4">
          {logo}
          <div className="flex items-center gap-2">
            <span className="font-semibold text-lg">{providerName}</span>
            {partnerName && (
              <>
                <span className="text-muted-foreground">•</span>
                <span className="text-muted-foreground">{partnerName}</span>
              </>
            )}
          </div>
        </div>

        {/* Price */}
        <div className="flex items-baseline gap-2 mb-6">
          <span className="text-3xl font-bold">{price}</span>
          <span className="text-muted-foreground">{priceUnit}</span>
        </div>

        {/* Features List */}
        <div className="space-y-3 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CheckIcon className="h-4 w-4 text-green-500" />
              <span className="text-sm">Network coverage</span>
            </div>
            <span className="text-sm font-medium">{networkCoverage}</span>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CheckIcon className="h-4 w-4 text-green-500" />
              <span className="text-sm">Coverage percentage</span>
            </div>
            <span className="text-sm font-medium">{coveragePercentage}</span>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CheckIcon className="h-4 w-4 text-green-500" />
              <span className="text-sm">Enrollment time</span>
            </div>
            <span className="text-sm font-medium">{enrollmentTime}</span>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CheckIcon className="h-4 w-4 text-green-500" />
              <span className="text-sm">Payment gateway</span>
            </div>
            <span className="text-sm font-medium">{paymentGateway}</span>
          </div>
        </div>

        {/* Action Button */}
        {onAddDescriptor && (
          <Button 
            variant="outline" 
            className="w-full gap-2"
            onClick={onAddDescriptor}
          >
            <PlusIcon className="h-4 w-4" />
            Add {providerType || providerName} Descriptor
          </Button>
        )}
      </CardContent>
    </Card>
  );
}