"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Loader2, Unlink, CreditCard, Shield, AlertTriangle, ExternalLink } from "lucide-react";
import { toast } from "sonner";
import { apiAxios } from "@/lib/apiAxios";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { selectStripeAccount, fetchStripeAccountByStore } from "@/redux/slices/stripe";
import { clearPaymentSetupStatus, debugCacheStatus } from "@/utils/card-setup-cache";
import StripeProvider from "@/components/stripe/StripeProvider";
import CardSetupForm from "@/components/stripe/CardSetupForm";
import { useSession } from "next-auth/react";

interface StripeAccountProps {
  storeId: string;
  storeName: string;
}

export function StripeAccountComponent({
  storeId,
  storeName
}: StripeAccountProps) {
  const dispatch = useAppDispatch();
  const stripeAccount = useAppSelector(selectStripeAccount);
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showDisconnectDialog, setShowDisconnectDialog] = useState<boolean>(false);

  // Get values from Redux state
  const isConnected = stripeAccount?.status === "active" || stripeAccount?.setupCompleted;
  const stripeAccountId = stripeAccount?.stripeAccountId || null;
  const localAccountId = stripeAccount?.id || null;
  
  // Check if card setup is completed (billing setup)
  const isCardSetupCompleted = stripeAccount?.setupCompleted || false;
  const cardInfo = stripeAccount ? {
    last4: stripeAccount.cardLast4,
    brand: stripeAccount.cardBrand,
    cardholderName: stripeAccount.cardholderName,
    billingEmail: stripeAccount.billingEmail
  } : null;

  // Load Stripe account info on mount
  useEffect(() => {
    if (storeId) {
      dispatch(fetchStripeAccountByStore(storeId));
    }
  }, [storeId, dispatch]);


  const openDashboard = async () => {
    try {
      setIsLoading(true);
      const response = await apiAxios.get(`/stripe/accounts/${stripeAccountId}/dashboard`);

      const data = response.data;
      if (data.url) {
        window.open(data.url, '_blank');
        toast.success("Opening Stripe Dashboard");
      } else {
        toast.error("No dashboard URL returned");
      }
    } catch (error: any) {
      toast.error("Failed to open dashboard", {
        description: error.response?.data?.message || "An unknown error occurred",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCardSetupSuccess = async () => {
    // Refresh Stripe account info
    await dispatch(fetchStripeAccountByStore(storeId));
    toast.success("Card setup completed successfully!");
  };

  const handleCardSetupError = (error: string) => {
    toast.error("Card setup failed", {
      description: error,
    });
  };


  const handleDisconnectClick = () => {
    if (!localAccountId) {
      toast.error("Cannot disconnect: Account information not available");
      return;
    }
    setShowDisconnectDialog(true);
  };

  const confirmDisconnect = async () => {
    if (!localAccountId) return;

    const isFullAccount = !!stripeAccountId;

    try {
      setIsLoading(true);
      setShowDisconnectDialog(false);
      
      // Use the local database ID for deletion, not the Stripe account ID
      await apiAxios.delete(`/stripe/accounts/${localAccountId}`);

      // Debug cache before clearing
      debugCacheStatus(storeId);

      // Clear the card setup cache AND general payment cache
      clearPaymentSetupStatus(storeId); // This clears all payment caches including general cache
      
      // Debug cache after clearing
      debugCacheStatus(storeId);

      // Refresh Stripe info from Redux
      await dispatch(fetchStripeAccountByStore(storeId));

      const successMessage = isFullAccount
        ? "Stripe account disconnected successfully"
        : "Billing setup reset successfully";
      
      toast.success(successMessage);
    } catch (error: any) {
      const errorMessage = isFullAccount
        ? "Failed to disconnect Stripe account"
        : "Failed to reset billing setup";
        
      toast.error(errorMessage, {
        description: error.response?.data?.message || "An unknown error occurred",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="overflow-hidden border border-gray-800 shadow-xl bg-black-900 p-0">
      <CardHeader className="py-4">
        <div className="flex items-center space-x-3">
          <div className={`p-2.5 rounded-lg ${isConnected ? 'bg-green-900/50' : 'bg-gray-800'}`}>
            <CreditCard className={`h-6 w-6 ${isConnected ? 'text-green-400' : 'text-gray-400'}`} />
          </div>
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-white">
              Stripe Payment Gateway
            </CardTitle>
            <CardDescription className="text-sm text-gray-400 mt-1">
              {isConnected ? 'Connected and configured' : 'Secure payment processing'} for {storeName}
            </CardDescription>
          </div>
          <div>
            {isConnected && (
              <AlertDialog open={showDisconnectDialog} onOpenChange={setShowDisconnectDialog}>
                <AlertDialogTrigger asChild>
                  <Button
                    onClick={handleDisconnectClick}
                    disabled={isLoading}
                    variant="outline"
                    className="border-red-700 cursor-pointer text-red-400 hover:bg-red-950/50 hover:border-red-600 bg-transparent"
                  >
                    <Unlink className="mr-2 h-4 w-4" />
                    {stripeAccountId ? 'Disconnect' : 'Reset Setup'}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent className="bg-gray-900 border-gray-700">
                  <AlertDialogHeader>
                    <AlertDialogTitle className="text-white">
                      {stripeAccountId ? 'Disconnect Stripe Account' : 'Reset Billing Setup'}
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-gray-400">
                      {stripeAccountId 
                        ? "Are you sure you want to disconnect your Stripe account? This will stop payment processing for this store and remove all associated payment methods."
                        : "Are you sure you want to reset your billing setup? This will remove your saved payment method and you'll need to set it up again."
                      }
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel className="cursor-pointer bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700">
                      Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                      onClick={confirmDisconnect}
                      className="cursor-pointer bg-red-600 hover:bg-red-700 text-white"
                    >
                      {stripeAccountId ? 'Disconnect' : 'Reset Setup'}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {isConnected ? (
          <div className="space-y-4">
            <div className="bg-green-950/30 border border-green-800/50 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Shield className="h-5 w-5 text-green-400 mt-0.5" />
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-green-300 mb-1">
                    {stripeAccountId ? 'Payment Processing Active' : 'Billing Setup Complete'}
                  </h4>
                  <p className="text-xs text-green-400/80">
                    {stripeAccountId 
                      ? 'Your store is ready to accept payments through Stripe.'
                      : 'Your billing setup is complete. Ready for payment processing.'}
                  </p>
                </div>
              </div>
            </div>

            {/* Show card information if available */}
            {isCardSetupCompleted && cardInfo && (
              <div className="bg-gray-900/50 border border-gray-700 rounded-lg p-4">
                <h4 className="text-sm font-medium text-white mb-3 flex items-center">
                  <CreditCard className="h-4 w-4 mr-2" />
                  Payment Method
                </h4>
                <div className="grid grid-cols-2 gap-3 text-xs">
                  <div>
                    <span className="text-gray-400">Card</span>
                    <p className="text-white font-mono">•••• •••• •••• {cardInfo.last4}</p>
                  </div>
                  <div>
                    <span className="text-gray-400">Brand</span>
                    <p className="text-white capitalize">{cardInfo.brand}</p>
                  </div>
                  <div>
                    <span className="text-gray-400">Cardholder</span>
                    <p className="text-white">{cardInfo.cardholderName || 'Not provided'}</p>
                  </div>
                  <div>
                    <span className="text-gray-400">Email</span>
                    <p className="text-white">{cardInfo.billingEmail || 'Not provided'}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Show Stripe account ID if connected */}
            {stripeAccountId && (
              <div className="bg-gray-900/50 border border-gray-700 rounded-lg p-3">
                <div className="flex justify-between items-center">
                  <span className="text-xs font-medium text-gray-400 uppercase tracking-wide">
                    Stripe Account ID
                  </span>
                  <span className="text-sm font-mono text-white bg-gray-800 px-2 py-1 rounded">
                    {stripeAccountId.substring(0, 12)}...
                  </span>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div>
            {/* Embedded Card Setup Form when not connected */}
            <div className="mt-6">
              <StripeProvider>
                <CardSetupForm
                  storeId={storeId}
                  userEmail={session?.user?.email || ""}
                  provider={session?.user?.provider}
                  onSuccess={handleCardSetupSuccess}
                  onError={handleCardSetupError}
                />
              </StripeProvider>
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="mb-4">
        

        {/* Show Dashboard button if fully setup */}
        {isConnected && stripeAccountId && isCardSetupCompleted && (
          <Button
            onClick={openDashboard}
            disabled={isLoading}
            variant="outline"
            className={`w-full border-blue-600 text-blue-400 hover:bg-blue-950/50 hover:border-blue-500 bg-transparent ${!isLoading ? 'cursor-pointer' : ''}`}
          >
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <ExternalLink className="mr-2 h-4 w-4" />
            )}
            Open Stripe Dashboard
          </Button>
        )}
      </CardFooter>


    </Card>
  );
}