"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AlertInfo } from "@/services/alert-info.service";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { useAppDispatch } from "@/redux/hooks";
import { updateAlertInfoAsync } from "@/redux/slices/alert-info";

interface EditAlertAccountDialogProps {
  account: AlertInfo;
  accessToken: string;
  provider?: string;
  open: boolean;
  onClose: () => void;
  onComplete: () => void;
}

export default function EditAlertAccountDialog({
  account,
  accessToken,
  provider,
  open,
  onClose,
  onComplete,
}: EditAlertAccountDialogProps) {
  const dispatch = useAppDispatch();
  const [alertType, setAlertType] = useState<"RDR" | "ETHOCA">("RDR");
  const [descriptor, setDescriptor] = useState("");
  const [bin, setBin] = useState("");
  const [caid, setCaid] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with account data
  useEffect(() => {
    if (account) {
      setAlertType(account.alertType as "RDR" | "ETHOCA");
      setDescriptor(account.descriptor || "");
      setBin(account.bin || "");
      setCaid(account.caid || "");
    }
  }, [account]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!descriptor.trim()) {
      toast.error("Descriptor is required");
      return;
    }

    setIsSubmitting(true);

    try {
      await dispatch(updateAlertInfoAsync({
        id: account.id,
        data: {
          alertType,
          descriptor: descriptor.trim(),
          bin: bin.trim() || null,
          caid: caid.trim() || null,
        },
        token: accessToken,
        provider,
      })).unwrap();

      toast.success("Descriptor updated successfully");
      onComplete();
    } catch (error) {
      toast.error("Failed to update Descriptor");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Descriptor</DialogTitle>
          <DialogDescription>
            Make changes to the Descriptor details.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="alert-type">Alert Type</Label>
            <Select
              value={alertType}
              onValueChange={(value) => setAlertType(value as "RDR" | "ETHOCA")}
            >
              <SelectTrigger id="alert-type">
                <SelectValue placeholder="Select alert type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="RDR">RDR</SelectItem>
                <SelectItem value="ETHOCA">ETHOCA</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="descriptor">Descriptor</Label>
            <Input
              id="descriptor"
              value={descriptor}
              onChange={(e) => setDescriptor(e.target.value)}
              placeholder="Enter descriptor"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="bin">BIN (optional)</Label>
            <Input
              id="bin"
              value={bin}
              onChange={(e) => setBin(e.target.value)}
              placeholder="Enter BIN"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="caid">CAID (optional)</Label>
            <Input
              id="caid"
              value={caid}
              onChange={(e) => setCaid(e.target.value)}
              placeholder="Enter CAID"
            />
          </div>

          <DialogFooter className="pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
              className="cursor-pointer"
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting} className="cursor-pointer">
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
