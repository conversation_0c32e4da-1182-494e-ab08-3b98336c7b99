"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import {
  Shield,
  CreditCard,
  DollarSign,
  Plug,
  User,
  Bell,
  Monitor,
  Settings,
  Loader2,
} from "lucide-react";
import { ProtectedLayout } from "@/components/layout";
import { useSession } from "next-auth/react";
import { useAppSelector } from "@/redux/hooks";
import {
  selectStore,
  selectStoreLoading,
  selectStoreError,
} from "@/redux/slices/store";
import { Loading } from "@/components/ui/loading";

const sidebarItems = [
  {
    title: "Descriptors",
    href: "/settings/alerts",
    icon: Shield,
  },
  {
    title: "Billing",
    href: "/settings/billing",
    icon: DollarSign,
  },
  {
    title: "Payment Gateway",
    href: "/settings/payment-gateway",
    icon: Plug,
  },
  // {
  //   title: "Profile",
  //   href: "/settings/profile",
  //   icon: User,
  // },
  // {
  //   title: "Notifications",
  //   href: "/settings/notifications",
  //   icon: Bell,
  // },
  // {
  //   title: "Display",
  //   href: "/settings/display",
  //   icon: Monitor,
  // },
];

interface SettingsLayoutProps {
  children: React.ReactNode;
}

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  const pathname = usePathname();
  const { data: session, status: sessionStatus } = useSession();
  const store = useAppSelector(selectStore);
  const storeLoading = useAppSelector(selectStoreLoading);
  const storeError = useAppSelector(selectStoreError);

  // Check authentication requirements
  const accessToken = session?.user?.access_token;
  const isAuthenticating = sessionStatus === "loading";
  const isUnauthenticated = sessionStatus === "unauthenticated";

  // Show loading state while session is loading
  if (isAuthenticating) {
    return (
      <ProtectedLayout
        title="Settings"
        description="View and manage all settings."
      >
        <Loading text="Loading settings..." />
      </ProtectedLayout>
    );
  }

  // Show error state if not authenticated
  if (isUnauthenticated || !accessToken) {
    return (
      <ProtectedLayout
        title="Settings"
        description="View and manage all settings."
      >
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center gap-4">
            <Settings className="h-12 w-12 text-muted-foreground" />
            <p className="text-muted-foreground">
              Authentication required to access settings.
            </p>
            <p className="text-sm text-muted-foreground">
              Please log in again.
            </p>
          </div>
        </div>
      </ProtectedLayout>
    );
  }

  // Show loading state while store is loading
  if (storeLoading) {
    return (
      <ProtectedLayout
        title="Settings"
        description="View and manage all settings."
      >
        <Loading text="Loading store information..." />
      </ProtectedLayout>
    );
  }

  // Show error state if store failed to load
  if (storeError) {
    return (
      <ProtectedLayout
        title="Settings"
        description="View and manage all settings."
      >
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center gap-4">
            <Settings className="h-12 w-12 text-muted-foreground" />
            <p className="text-muted-foreground">
              Failed to load store information.
            </p>
            <p className="text-sm text-muted-foreground">Error: {storeError}</p>
          </div>
        </div>
      </ProtectedLayout>
    );
  }

  // Show message if no store is connected
  if (!store) {
    return (
      <ProtectedLayout
        title="Settings"
        description="View and manage all settings."
      >
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center gap-4">
            <Settings className="h-12 w-12 text-muted-foreground" />
            <p className="text-muted-foreground">No store connected.</p>
            <p className="text-sm text-muted-foreground">
              Please connect a store to access settings.
            </p>
          </div>
        </div>
      </ProtectedLayout>
    );
  }

  return (
    <ProtectedLayout
      title="Settings"
      description="View and manage all settings."
    >
      <div className="flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0 relative">
        {/* Left Sidebar */}
        <aside className="lg:w-64 flex-shrink-0 lg:border-r border-border lg:pr-8 sticky top-0">
          <nav className="space-y-1">
            {sidebarItems.map((item) => {
              const isActive = pathname === item.href;
              const Icon = item.icon;

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors cursor-pointer",
                    isActive
                      ? "bg-accent text-accent-foreground"
                      : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                  )}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.title}</span>
                </Link>
              );
            })}
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 min-w-0 w-full max-w-[calc(100%-76px-3rem)] lg:max-w-[calc(100%-256px-3rem)]">
          {children}
        </main>
      </div>
    </ProtectedLayout>
  );
}
