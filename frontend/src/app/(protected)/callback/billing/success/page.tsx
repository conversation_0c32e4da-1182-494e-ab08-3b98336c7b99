"use client";

import { Suspense } from "react";
import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, ArrowRight, BarChart3 } from "lucide-react";

function BillingSuccessContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [countdown, setCountdown] = useState(3);

  const status = searchParams.get('status');
  const storeId = searchParams.get('store_id');

  useEffect(() => {
    // Auto-redirect after 5 seconds
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          handleContinue();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleContinue = () => {
    router.push('/dashboard');
  };

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <Card className="bg-[#16181C] border-[#2F3336] max-w-2xl w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-green-900/20 rounded-full">
              <CheckCircle className="h-12 w-12 text-green-400" />
            </div>
          </div>
          <CardTitle className="text-3xl font-bold text-white">
            Subscription Activated!
          </CardTitle>
          <CardDescription className="text-[#71767B] mt-2">
            Your Shopify billing subscription is now active and ready to use
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Success Message */}
          <div className="bg-green-900/20 border border-green-900/40 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-green-400 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-green-100">
                  Subscription Successfully Activated
                </p>
                <p className="text-xs text-green-200/80 mt-1">
                  You can now start processing chargebacks. Usage will be billed monthly via Shopify.
                </p>
              </div>
            </div>
          </div>

          {/* What's Next */}
          <div className="space-y-3">
            <h3 className="font-semibold text-white">What&apos;s next?</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-3 p-3 bg-black/20 rounded-lg">
                <BarChart3 className="h-4 w-4 text-blue-400" />
                <div>
                  <p className="text-sm text-white">Monitor your usage</p>
                  <p className="text-xs text-[#71767B]">Track your chargeback processing and monthly spending</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-black/20 rounded-lg">
                <CheckCircle className="h-4 w-4 text-green-400" />
                <div>
                  <p className="text-sm text-white">Process chargebacks</p>
                  <p className="text-xs text-[#71767B]">Start protecting your revenue immediately</p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Button */}
          <div className="flex gap-3">
            <Button
              onClick={handleContinue}
              className="cursor-pointer flex-1 bg-[#1D9BF0] hover:bg-[#1A8CD8] text-white"
              size="lg"
            >
              <ArrowRight className="w-4 h-4 mr-2" />
              Continue to Billing Dashboard
            </Button>
          </div>

          {/* Auto-redirect notice */}
          <div className="text-center">
            <p className="text-xs text-[#71767B]">
              Auto-redirecting in {countdown} seconds...
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function BillingSuccessPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-black flex items-center justify-center p-4">
        <div className="text-center space-y-3">
          <div className="w-12 h-12 border-4 border-gray-200 border-t-primary rounded-full animate-spin mx-auto"></div>
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    }>
      <BillingSuccessContent />
    </Suspense>
  );
}