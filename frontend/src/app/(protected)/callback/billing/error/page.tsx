"use client";

import { Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertCircle, RefreshCw, ArrowLeft, ExternalLink } from "lucide-react";

function BillingErrorContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const error = searchParams.get('error');
  const storeId = searchParams.get('store_id');

  const handleRetry = () => {
    router.push('/billing');
  };

  const handleContactSupport = () => {
    window.location.href = 'mailto:<EMAIL>?subject=Billing Activation Error';
  };

  const getErrorMessage = (errorCode: string | null) => {
    switch (errorCode) {
      case 'billing_callback_failed':
        return 'Failed to process billing callback from Shopify';
      case 'subscription_not_found':
        return 'Subscription not found in our system';
      case 'invalid_charge':
        return 'Invalid charge configuration';
      case 'user_cancelled':
        return 'Billing activation was cancelled';
      default:
        return 'An unexpected error occurred during billing activation';
    }
  };

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <Card className="bg-[#16181C] border-[#2F3336] max-w-2xl w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-red-900/20 rounded-full">
              <AlertCircle className="h-12 w-12 text-red-400" />
            </div>
          </div>
          <CardTitle className="text-3xl font-bold text-white">
            Billing Activation Failed
          </CardTitle>
          <CardDescription className="text-[#71767B] mt-2">
            There was an issue activating your subscription
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Error Message */}
          <div className="bg-red-900/20 border border-red-900/40 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-red-400 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-red-100">
                  {getErrorMessage(error)}
                </p>
                <p className="text-xs text-red-200/80 mt-1">
                  Please try again or contact support if the issue persists.
                </p>
              </div>
            </div>
          </div>

          {/* Troubleshooting Steps */}
          <div className="space-y-3">
            <h3 className="font-semibold text-white">Troubleshooting steps:</h3>
            <div className="space-y-2">
              <div className="flex items-start gap-3 p-3 bg-black/20 rounded-lg">
                <RefreshCw className="h-4 w-4 text-blue-400 mt-0.5" />
                <div>
                  <p className="text-sm text-white">Try again</p>
                  <p className="text-xs text-[#71767B]">The issue might be temporary</p>
                </div>
              </div>
              <div className="flex items-start gap-3 p-3 bg-black/20 rounded-lg">
                <ExternalLink className="h-4 w-4 text-green-400 mt-0.5" />
                <div>
                  <p className="text-sm text-white">Check Shopify billing settings</p>
                  <p className="text-xs text-[#71767B]">Verify your Shopify account can accept app charges</p>
                </div>
              </div>
              <div className="flex items-start gap-3 p-3 bg-black/20 rounded-lg">
                <AlertCircle className="h-4 w-4 text-yellow-400 mt-0.5" />
                <div>
                  <p className="text-sm text-white">Contact support</p>
                  <p className="text-xs text-[#71767B]">We&apos;re here to help resolve this issue</p>
                </div>
              </div>
            </div>
          </div>

          {/* Technical Details */}
          {error && (
            <div className="bg-gray-900/20 border border-gray-900/40 rounded-lg p-3">
              <p className="text-xs text-gray-400">
                <strong>Error Code:</strong> {error}
              </p>
              {storeId && (
                <p className="text-xs text-gray-400 mt-1">
                  <strong>Store ID:</strong> {storeId}
                </p>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              onClick={handleRetry}
              className="cursor-pointer flex-1 bg-[#1D9BF0] hover:bg-[#1A8CD8] text-white"
              size="lg"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
            <Button
              onClick={handleContactSupport}
              variant="outline"
              className="cursor-pointer border-[#2F3336] text-white hover:bg-[#2F3336]"
              size="lg"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Contact Support
            </Button>
          </div>

          {/* Back to Dashboard */}
          <div className="text-center">
            <Button
              onClick={() => router.push('/dashboard')}
              variant="ghost"
              className="cursor-pointer text-[#71767B] hover:text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function BillingErrorPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-black flex items-center justify-center p-4">
        <div className="text-center space-y-3">
          <div className="w-12 h-12 border-4 border-gray-200 border-t-primary rounded-full animate-spin mx-auto"></div>
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    }>
      <BillingErrorContent />
    </Suspense>
  );
}