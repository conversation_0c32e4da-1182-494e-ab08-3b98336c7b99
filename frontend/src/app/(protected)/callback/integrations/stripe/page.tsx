"use client";

import { useRouter, useSearch<PERSON>ara<PERSON> } from "next/navigation";
import { Suspense, useEffect, useState } from "react";
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Loading } from "@/components/ui/loading";
import StripeLogo from "@/components/svg/partners/StripeLogo";
import { useAppDispatch } from "@/redux/hooks";
import { clearStoresCache, fetchStoresByUserId } from "@/redux/slices/store";
import { useSession } from "next-auth/react";

// Function to get user-friendly error message from error key
function getErrorMessage(errorKey: string): string {
  const errorMessages: Record<string, string> = {
    missing_parameters: "Missing required parameters for Stripe authentication",
    invalid_code: "Invalid authorization code. Please try connecting again.",
    state_mismatch: "Security validation failed. Please try connecting again.",
    exchange_failed: "Failed to obtain authorization from <PERSON><PERSON>. Please try again.",
    connection_failed: "Failed to connect Stripe account. Please try again.",
    integration_error: "An error occurred during the integration process. Please try again.",
  };
  
  return errorMessages[errorKey] || "An unexpected error occurred while connecting to Stripe";
}

function StripeCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useAppDispatch();
  const { data: session } = useSession();
  const [status, setStatus] = useState<"loading" | "success" | "error">("loading");
  const [errorKey, setErrorKey] = useState<string>("");
  const [authStep, setAuthStep] = useState<string>("Processing connection");

  useEffect(() => {
    // Check for error parameter first
    const errorParam = searchParams.get("error");
    if (errorParam) {
      setErrorKey(errorParam);
      setStatus("error");
      return;
    }

    // Extract success parameters
    const stripeAccountId = searchParams.get("stripe_account_id");
    const storeName = searchParams.get("name");
    const linkedStoreId = searchParams.get("id");

    // Validate required parameters
    if (!stripeAccountId || !storeName || !linkedStoreId) {
      setErrorKey("missing_parameters");
      setStatus("error");
      return;
    }

    // Process successful connection
    const processConnection = async () => {
      try {
        setAuthStep("Processing connection");
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setAuthStep("Updating integration status");
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setAuthStep("Refreshing store data");
        
        // Clear stores cache and fetch fresh data
        dispatch(clearStoresCache());
        
        // Ensure we have the userId to fetch stores
        const userId = session?.user?.user_id;
        if (userId) {
          // Wait a bit to ensure backend has committed the transaction
          await new Promise(resolve => setTimeout(resolve, 500));
          // Fetch fresh store data before redirecting
          await dispatch(fetchStoresByUserId(userId));
        }
        
        setAuthStep("Integration completed");
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Redirect to payment gateway settings with success flag
        router.replace(`/settings/payment-gateway?stripe_connected=true&t=${Date.now()}`);
      } catch (error) {
        setErrorKey("integration_error");
        setStatus("error");
      }
    };

    processConnection();
  }, [searchParams, router, dispatch, session]);

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-[80vh] p-4">
        <div className="w-full max-w-md mx-auto">
          {/* Header */}
          <div className="text-center space-y-2 mb-6">
            <h1 className="text-2xl font-semibold tracking-tight">
              Connecting Stripe Integration
            </h1>
            <p className="text-sm text-muted-foreground">
              Please wait while we set up your Stripe integration
            </p>
          </div>

          {/* Content Card */}
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/10 via-purple-400/10 to-indigo-500/10 rounded-2xl blur-xl" />
            
            <div className="relative bg-card rounded-2xl border shadow-lg p-8 min-h-[300px] flex items-center justify-center">
              <div className="flex flex-col items-center justify-center space-y-6">
                {/* Animated Stripe Icon */}
                <div className="relative">
                  <div className="absolute inset-0 bg-indigo-500/20 rounded-full blur-2xl animate-pulse" />
                  <StripeLogo className="w-16 h-16" />
                </div>

                {/* Loading indicators */}
                <div className="flex flex-col items-center space-y-3">
                  <div className="flex space-x-2">
                    <div className="w-2 h-2 bg-indigo-600 rounded-full animate-bounce" style={{ animationDelay: "0ms" }} />
                    <div className="w-2 h-2 bg-indigo-600 rounded-full animate-bounce" style={{ animationDelay: "150ms" }} />
                    <div className="w-2 h-2 bg-indigo-600 rounded-full animate-bounce" style={{ animationDelay: "300ms" }} />
                  </div>
                  <p className="text-sm font-medium text-muted-foreground animate-pulse">
                    {authStep}
                  </p>
                </div>

                {/* Progress steps */}
                <div className="w-full space-y-2 px-4">
                  {["Processing connection", "Updating integration status", "Refreshing store data", "Integration completed"].map((step) => {
                    const isActive = authStep === step;
                    const isPassed = 
                      (step === "Processing connection" && (authStep === "Updating integration status" || authStep === "Refreshing store data" || authStep === "Integration completed")) ||
                      (step === "Updating integration status" && (authStep === "Refreshing store data" || authStep === "Integration completed")) ||
                      (step === "Refreshing store data" && authStep === "Integration completed");
                    
                    return (
                      <div key={step} className={`flex items-center space-x-3 text-sm ${!isActive && !isPassed ? "opacity-50" : ""}`}>
                        {isActive ? (
                          <Loader2 className="h-4 w-4 animate-spin text-indigo-600" />
                        ) : isPassed ? (
                          <div className="h-4 w-4 rounded-full bg-indigo-600 flex items-center justify-center">
                            <svg className="h-2.5 w-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        ) : (
                          <div className="h-4 w-4 rounded-full border-2 border-muted-foreground/30" />
                        )}
                        <span className="text-muted-foreground">{step}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (status === "error") {
    return (
      <div className="flex items-center justify-center min-h-[80vh] p-4">
        <div className="w-full max-w-md mx-auto">
          {/* Header */}
          <div className="text-center space-y-2 mb-6">
            <h1 className="text-2xl font-semibold tracking-tight">
              Integration Failed
            </h1>
          </div>

          {/* Error Card */}
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-red-500/10 via-orange-400/10 to-red-500/10 rounded-2xl blur-xl" />
            
            <div className="relative bg-card rounded-2xl border shadow-lg p-8">
              <div className="flex flex-col items-center justify-center space-y-6 text-center">
                {/* Error Icon */}
                <div className="relative">
                  <div className="absolute inset-0 bg-red-500/20 rounded-full blur-xl" />
                  <div className="relative bg-red-100 dark:bg-red-900/20 rounded-full p-4">
                    <svg className="w-12 h-12 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>

                {/* Error Message */}
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Connection Failed</h3>
                  <p className="text-sm text-muted-foreground">
                    {getErrorMessage(errorKey)}
                  </p>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 w-full">
                  <Button
                    onClick={() => router.push('/settings/payment-gateway')}
                    className="flex-1"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Back to Settings
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => window.location.reload()}
                    className="flex-1"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Try Again
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
}

export default function StripeIntegrationCallback() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-[80vh]">
        <Loading size="lg" text="Loading Stripe integration..." />
      </div>
    }>
      <StripeCallbackContent />
    </Suspense>
  );
}