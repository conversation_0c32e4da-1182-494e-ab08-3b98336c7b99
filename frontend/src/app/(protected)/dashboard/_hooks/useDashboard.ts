import { useState, useEffect, useCallback, useMemo, useRef } from "react"
import { matchedBlockService, ViewMode, MatchedBlockChargebackRateChartData } from "@/services/matched-block.service";
import {
  DashboardGoal,
  TimeRange,
  type DashboardMetric
} from "../_lib/constants"
import { useSession } from "next-auth/react";

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
interface UseDashboardProps {}

// Use the type from the service instead of defining it locally
type ChargebackRateData = MatchedBlockChargebackRateChartData

interface UseDashboardReturn {
  timeRange: TimeRange,
  customDateRange?: { from: Date; to: Date }
  metrics: DashboardMetric[] | null
  goal: DashboardGoal | null
  chargebackRateData: ChargebackRateData[]
  loading: boolean
  metricsLoading: boolean
  chartLoading: boolean
  error: string | null
  lastRefresh: Date | null
  // Actions
  setTimeRange: (timeRange: TimeRange, dateRange?: { from: Date; to: Date }) => void
  refetch: () => Promise<void>
}

function isMatchedBlockOverviewResponse(
  response: any
): response is { data: { blocks: { count: number; blocked: number }; rate: { actualChargeback: string; blocked: string }; goal: { actualChargeback: string; chargebackGoal: string; profitGoalProgress: string } } } {
  return (
    response &&
    typeof response.data === "object" &&
    "blocks" in response.data
  );
}


export function useDashboard({}: UseDashboardProps): UseDashboardReturn {
  const { data: session } = useSession()
  // State
  const [timeRange, setTimeRangeState] = useState<TimeRange>(TimeRange.TODAY)
  const [customDateRange, setCustomDateRange] = useState<{ from: Date; to: Date } | undefined>()
  const [metrics, setMetrics] = useState<DashboardMetric[]>([])
  const [goal, setGoal] = useState<DashboardGoal | null>(null)
  const [chargebackRateData, setChargebackRateData] = useState<ChargebackRateData[]>([])
  const [loading, setLoading] = useState(false)
  const [metricsLoading, setMetricsLoading] = useState(false)
  const [chartLoading, setChartLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null)
  
  // Use refs to track ongoing requests and prevent duplicates
  const fetchingOverview = useRef(false)
  const fetchingChart = useRef(false)

  
  // Memoize filters to prevent recalculation on every render
  const memoizedFilters = useMemo(() => {
    const filters: any = {
      timeRange: timeRange,
      viewMode: "overview" as ViewMode,
    }

    // Add custom date range as separate ISO strings if it's selected
    if (timeRange === TimeRange.CUSTOM && customDateRange) {
      filters.startDate = customDateRange.from.toISOString()
      filters.endDate = customDateRange.to.toISOString()
    }

    return filters
  }, [timeRange, customDateRange])

  // Memoize chart filters separately for chart data
  const memoizedChartFilters = useMemo(() => {
    const filters: Record<string, any> = { 
      timeRange: timeRange,
    }

    // Add custom date range if applicable
    if (timeRange === TimeRange.CUSTOM && customDateRange?.from && customDateRange?.to) {
      filters.startDate = customDateRange.from.toISOString();
      filters.endDate = customDateRange.to.toISOString();
    }

    return filters
  }, [timeRange, customDateRange])

  // Fetch chargeback rate data for current month
  const fetchChargebackRateData = useCallback(async () => {
    if (fetchingChart.current || !session?.user?.access_token || !session?.user?.user_id) return;
    
    try {
      fetchingChart.current = true;
      setChartLoading(true)
      // Fetch chargeback rate chart data
      const response = await matchedBlockService.getChargebackRateChart(session.user.user_id, memoizedChartFilters)

      if (response && response.success && response.data) {
        setChargebackRateData(response.data)
      } else {
        setChargebackRateData([])
      }
    } catch (error) {
      setChargebackRateData([])
    } finally {
      setChartLoading(false)
      fetchingChart.current = false;
    }
  }, [memoizedChartFilters, session?.user?.access_token, session?.user?.user_id])


  // Fetch overview data
  const fetchOverview = useCallback(async () => {
    if (fetchingOverview.current || !session?.user?.access_token || !session?.user?.user_id) return;
    
    try {
      fetchingOverview.current = true;
      setMetricsLoading(true)
      setError(null)

      // Fetch overview data
      const userId = session.user.user_id;
      const overviewResponse = await matchedBlockService.getMatchedBlocks(userId, 1, 1, memoizedFilters);

      if (isMatchedBlockOverviewResponse(overviewResponse)) {
        const metricsData: DashboardMetric[] = []
        if (overviewResponse.data.blocks) {
          metricsData.push({
            id: '1',
            label: 'Alerts',
            value: overviewResponse.data.blocks.count,
            format: "number",
          })
          metricsData.push({
            id: '2',
            label: 'Blocked',
            value: overviewResponse.data.blocks.blocked,
            format: "number",
          })
        }
        if (overviewResponse.data.rate) {
          metricsData.push({
            id: '3',
            label: 'Chargeback Rate',
            value: parseFloat(overviewResponse.data.rate.actualChargeback),
            format: "percentage",
          })
          metricsData.push({
            id: '4',
            label: 'Chargeback Rate (Projected)',
            value: parseFloat(overviewResponse.data.rate.blocked),
            format: "percentage",
          })
        }

        // Set goal from overview response
        if (overviewResponse.data.goal) {
          setGoal({
            id: '1',
            label: 'Chargeback Goal',
            actualChargeback: {
              value: parseFloat(overviewResponse.data.goal.actualChargeback),
              format: "percentage"
            },
            chargebackGoal: {
              value: parseFloat(overviewResponse.data.goal.chargebackGoal),
              format: "percentage"
            },
            profitGoalProgress: {
              value: parseFloat(overviewResponse.data.goal.profitGoalProgress),
              format: "percentage"
            }
          })
        }
        setMetrics(metricsData)
      }

      setLastRefresh(new Date())
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch dashboard data")
    } finally {
      setMetricsLoading(false)
      fetchingOverview.current = false;
    }
  }, [memoizedFilters, session?.user?.access_token, session?.user?.user_id])

  // Refetch function - runs both metrics and chart in parallel
  const refetch = useCallback(async () => {
    setLoading(true)
    await Promise.all([
      fetchOverview(),
      fetchChargebackRateData()
    ])
    setLoading(false)
  }, [fetchOverview, fetchChargebackRateData])

  // Track if initial load is complete
  const initialLoadComplete = useRef(false);
  
  // Effect to fetch data when dependencies change - reduced dependencies
  useEffect(() => {
    if (session?.user?.access_token && session?.user?.user_id) {
      // Skip if already fetching
      if (fetchingOverview.current || fetchingChart.current) {
        return;
      }
      // Run overview and chart data fetch in parallel for faster loading
      Promise.all([
        fetchOverview(),
        fetchChargebackRateData()
      ]).finally(() => {
        setLoading(false);
        initialLoadComplete.current = true;
      })
    }
  }, [fetchOverview, fetchChargebackRateData, session?.user?.access_token, session?.user?.user_id])

  // Handle time range change
  const setTimeRange = useCallback((newTimeRange: TimeRange, dateRange?: { from: Date; to: Date }) => {
    setTimeRangeState(newTimeRange)
    if (newTimeRange === TimeRange.CUSTOM && dateRange) {
      setCustomDateRange(dateRange)
    } else if (newTimeRange !== TimeRange.CUSTOM) {
      setCustomDateRange(undefined)
    }
  }, [])

  return {
    // State
    timeRange,
    customDateRange,
    metrics,
    goal,
    chargebackRateData,
    loading: loading || metricsLoading, // Combined loading state
    metricsLoading,
    chartLoading,
    error,
    lastRefresh,

    // Actions
    setTimeRange,
    refetch,
  }
}