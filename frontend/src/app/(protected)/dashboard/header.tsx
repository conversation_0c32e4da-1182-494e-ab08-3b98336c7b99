"use client"

import type React from "react"
import { TimeRange } from "./_lib/constants"
import TimeRangeSelector from "./_components/time-range"

interface DashboardHeaderProps {
  timeRange: TimeRange
  onTimeRangeChange: (timeRange: TimeRange, dateRange?: { from: Date; to: Date }) => void
  customDateRange?: { from: Date; to: Date }
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({ timeRange, onTimeRangeChange, customDateRange }) => {
  return (
    <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-8 sm:mb-12">
      <div className="flex items-center space-x-4 sm:space-x-6 mb-4 md:mb-0">
        <h1 className="text-4xl sm:text-5xl font-bold">Dashboard</h1>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-green-500 text-base">Live</span>
        </div>
      </div>

      <TimeRangeSelector value={timeRange} onChange={onTimeRangeChange} customDateRange={customDateRange} />
    </div>
  )
}

export default DashboardHeader
