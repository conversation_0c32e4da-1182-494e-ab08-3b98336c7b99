"use client";

import React, { useState } from "react";
import { Calendar } from "lucide-react";
import { TimeRange } from "../_lib/constants";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { format, addMonths, subMonths } from "date-fns";
import { DateRange } from "react-day-picker";
import { cn } from "@/lib/utils";

interface TimeRangeProps {
  value: TimeRange;
  onChange: (value: TimeRange, dateRange?: { from: Date; to: Date }) => void;
  customDateRange?: { from: Date; to: Date };
}

const TIME_RANGE_OPTIONS = [
  { value: TimeRange.TODAY, label: "Today" },
  { value: TimeRange.SEVEN_DAYS, label: "7D" },
  { value: TimeRange.THIRTY_DAYS, label: "30D" },
  { value: TimeRange.THREE_MONTHS, label: "3M" },
  { value: TimeRange.SIX_MONTHS, label: "6M" },
  { value: TimeRange.YTD, label: "YTD" },
  { value: TimeRange.ONE_YEAR, label: "1Y" },
  { value: TimeRange.ALL_TIME, label: "ALL" },
  { value: TimeRange.CUSTOM, label: "Custom" },
];

const getTimeRangeDisplayText = (timeRange: TimeRange): string => {
  const now = new Date();

  switch (timeRange) {
    case TimeRange.TODAY:
      return now.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      });
    case TimeRange.SEVEN_DAYS:
      return "Last 7 Days";
    case TimeRange.THIRTY_DAYS:
      return "Last 30 Days";
    case TimeRange.THREE_MONTHS:
      return "Last 3 Months";
    case TimeRange.SIX_MONTHS:
      return "Last 6 Months";
    case TimeRange.YTD:
      return `YTD ${now.getFullYear()}`;
    case TimeRange.ONE_YEAR:
      return "Last 12 Months";
    case TimeRange.ALL_TIME:
      return "All Time";
    default:
      return now.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      });
  }
};

const TimeRangeSelector: React.FC<TimeRangeProps> = ({
  value,
  onChange,
  customDateRange,
}) => {
  const [date, setDate] = useState<DateRange | undefined>(
    customDateRange
      ? { from: customDateRange.from, to: customDateRange.to }
      : undefined
  );
  const [tempDate, setTempDate] = useState<DateRange | undefined>(
    customDateRange
      ? { from: customDateRange.from, to: customDateRange.to }
      : undefined
  );
  const [isOpen, setIsOpen] = useState(false);
  const [leftMonth, setLeftMonth] = useState(subMonths(new Date(), 1));
  const [rightMonth, setRightMonth] = useState(new Date());

  const handlePresetClick = (preset: TimeRange) => {
    if (preset === TimeRange.CUSTOM) {
      onChange(preset); // Set the value to CUSTOM first
      setIsOpen(true);
      // Reset temp date to current selection when opening
      setTempDate(date);
      // Reset months to show last month and current month
      const today = new Date();
      setLeftMonth(subMonths(today, 1));
      setRightMonth(today);
    } else {
      onChange(preset);
    }
  };

  const handleDateSelect = (selectedDate: DateRange | undefined) => {
    setTempDate(selectedDate);
  };

  const handleConfirm = () => {
    if (tempDate?.from && tempDate?.to) {
      setDate(tempDate);
      onChange(TimeRange.CUSTOM, { from: tempDate.from, to: tempDate.to });
      setIsOpen(false);
    }
  };

  const handleCancel = () => {
    // Reset temp date to current confirmed date
    setTempDate(date);
    setIsOpen(false);
  };

  const formatDateRange = () => {
    if (date?.from && date?.to) {
      if (date.from.getTime() === date.to.getTime()) {
        return format(date.from, "MMM dd, yyyy");
      }
      // Always show year for both dates to avoid confusion
      return `${format(date.from, "MMM dd, yyyy")} - ${format(
        date.to,
        "MMM dd, yyyy"
      )}`;
    }
    return "Select date range";
  };

  const formatTempDateRange = () => {
    if (tempDate?.from && tempDate?.to) {
      if (tempDate.from.getTime() === tempDate.to.getTime()) {
        return format(tempDate.from, "MMM dd, yyyy");
      }
      // Always show year for both dates to avoid confusion
      return `${format(tempDate.from, "MMM dd, yyyy")} - ${format(
        tempDate.to,
        "MMM dd, yyyy"
      )}`;
    } else if (tempDate?.from) {
      return `${format(tempDate.from, "MMM dd, yyyy")} - Select end date`;
    }
    return "Select date range";
  };

  // Ensure right month is always after left month
  const handleLeftMonthChange = (newMonth: Date) => {
    setLeftMonth(newMonth);
    // If right month is before or same as new left month, adjust it
    if (rightMonth <= newMonth) {
      setRightMonth(addMonths(newMonth, 1));
    }
  };

  const handleRightMonthChange = (newMonth: Date) => {
    setRightMonth(newMonth);
    // If left month is after or same as new right month, adjust it
    if (leftMonth >= newMonth) {
      setLeftMonth(subMonths(newMonth, 1));
    }
  };

  return (
    <div className="flex items-center space-x-4 gap-2">
      <div className="flex items-center bg-white/5 rounded-xl border border-white/10 p-1 gap-2">
        {TIME_RANGE_OPTIONS.map((option) => (
          <button
            key={option.value}
            onClick={() => handlePresetClick(option.value)}
            className={cn(
              "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
              value === option.value
                ? "bg-white text-black shadow-sm"
                : "text-white/70 hover:text-white hover:bg-white/10"
            )}
          >
            {option.label}
          </button>
        ))}
        {value === TimeRange.CUSTOM && (
          <Popover
            open={isOpen}
            onOpenChange={(open) => {
              if (!open) {
                handleCancel();
              }
            }}
          >
            <PopoverTrigger asChild>
              <div className="flex items-center space-x-2 text-white/60 bg-white/5 px-3 py-2 rounded-lg border border-white/10 cursor-pointer hover:bg-white/10 transition-all duration-200">
                <Calendar className="w-4 h-4" />
                <span className="text-sm font-medium">
                  {customDateRange ? formatDateRange() : "Select date range"}
                </span>
              </div>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0 mt-2" align="end">
              <div className="p-4">
                <div className="flex gap-4">
                  {/* Combined calendar wrapper for seamless range selection */}
                  <div className="flex gap-4 [&_.rdp-range_start]:rounded-l-md [&_.rdp-range_end]:rounded-r-md [&_.rdp-range_middle]:rounded-none">
                    {/* Left Calendar */}
                    <CalendarComponent
                      mode="range"
                      defaultMonth={leftMonth}
                      month={leftMonth}
                      onMonthChange={handleLeftMonthChange}
                      selected={tempDate}
                      onSelect={handleDateSelect}
                      disabled={(date) => date > new Date()}
                      className="rounded-md border-0"
                      captionLayout="dropdown"
                      classNames={{
                        month: "space-y-4",
                        table: "w-full border-collapse space-y-1",
                        head_row: "flex",
                        head_cell:
                          "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
                        row: "flex w-full mt-2",
                        cell: cn(
                          "relative p-0 text-center text-sm focus-within:relative focus-within:z-20",
                          "first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md",
                          "[&:has([aria-selected].day-range-end)]:rounded-r-md",
                          "[&:has([aria-selected].day-range-start)]:rounded-l-md",
                          "[&:has([aria-selected].day-range-middle)]:rounded-none"
                        ),
                      }}
                    />

                    {/* Right Calendar */}
                    <CalendarComponent
                      mode="range"
                      defaultMonth={rightMonth}
                      month={rightMonth}
                      onMonthChange={handleRightMonthChange}
                      selected={tempDate}
                      onSelect={handleDateSelect}
                      disabled={(date) => date > new Date()}
                      className="rounded-md border-0"
                      captionLayout="dropdown"
                      classNames={{
                        month: "space-y-4",
                        table: "w-full border-collapse space-y-1",
                        head_row: "flex",
                        head_cell:
                          "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
                        row: "flex w-full mt-2",
                        cell: cn(
                          "relative p-0 text-center text-sm focus-within:relative focus-within:z-20",
                          "first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md",
                          "[&:has([aria-selected].day-range-end)]:rounded-r-md",
                          "[&:has([aria-selected].day-range-start)]:rounded-l-md",
                          "[&:has([aria-selected].day-range-middle)]:rounded-none"
                        ),
                      }}
                    />
                  </div>
                </div>

                <div className="mt-4 space-y-2">
                  <div className="text-sm text-muted-foreground text-center">
                    {formatTempDateRange()}
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" size="sm" onClick={handleCancel}>
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleConfirm}
                      disabled={!tempDate?.from || !tempDate?.to}
                    >
                      Confirm
                    </Button>
                  </div>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        )}
      </div>
    </div>
  );
};

export default TimeRangeSelector;
