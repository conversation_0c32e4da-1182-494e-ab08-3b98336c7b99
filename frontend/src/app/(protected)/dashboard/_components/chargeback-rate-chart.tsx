"use client"

import React, { memo } from "react"
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { TimeRange } from "../_lib/constants"

interface ChargebackRateChartProps {
  data: Array<{
    date: string
    rate: number
    disputeCount?: number
    transactionCount?: number
  }>
  loading?: boolean
  timeRange?: string
}

const ChargebackRateChart: React.FC<ChargebackRateChartProps> = memo(({ data, loading, timeRange = TimeRange.TODAY }) => {
  const getTimeRangeTitle = () => {
    switch (timeRange) {
      case TimeRange.CUSTOM:
        return 'Custom Range'
      case TimeRange.TODAY:
        return 'Today'
      case TimeRange.SEVEN_DAYS:
        return 'Last 7 Days'
      case TimeRange.THIRTY_DAYS:
        return 'Last 30 Days'
      case TimeRange.THREE_MONTHS:
        return 'Last 3 Months'
      case TimeRange.SIX_MONTHS:
        return 'Last 6 Months'
      case TimeRange.YTD:
        return 'Year to Date'
      case TimeRange.ONE_YEAR:
        return 'Last 12 Months'
      case TimeRange.ALL_TIME:
        return 'All Time'
      
      default:
        return 'All Time'
    }
  }

  // Debug logging for data (development only)
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development' && data && data.length > 0) {
      console.log(`📊 ${getTimeRangeTitle()} Chart Data:`, {
        timeRange,
        dataLength: data.length,
        firstPoint: data[0],
        lastPoint: data[data.length - 1]
      });
    }
  }, [data, timeRange]);

  // Calculate tick interval based on timeRange and data length
  const getXAxisTickInterval = () => {
    // Long time ranges with many data points need spacing
    if (timeRange === TimeRange.ALL_TIME && data.length > 6) {
      return Math.ceil(data.length / 6)
    }
    if ((timeRange === TimeRange.ONE_YEAR || timeRange === TimeRange.YTD) && data.length > 8) {
      return Math.ceil(data.length / 8)
    }
    if ((timeRange === TimeRange.SIX_MONTHS || timeRange === TimeRange.THREE_MONTHS) && data.length > 6) {
      return Math.ceil(data.length / 6)
    }
    // For shorter ranges, show all or most ticks
    return 0
  }

  // Format X-axis labels based on timeRange
  const formatXAxisTick = (tickItem: string) => {
    if (timeRange === TimeRange.ALL_TIME) {
      // For all time, keep the existing format but ensure it's readable
      return tickItem
    }
    return tickItem
  }

  if (loading) {
    return (
      <div className="bg-transparent border border-zinc-800 rounded-2xl p-6">
        <div className="space-y-4">
          <div className="h-6 w-48 bg-zinc-800 rounded animate-pulse"></div>
          <div className="h-64 bg-zinc-800 rounded animate-pulse"></div>
        </div>
      </div>
    )
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-transparent border border-zinc-800 rounded-2xl p-6">
        <h3 className="text-lg font-normal text-white mb-4">Chargeback Rate - {getTimeRangeTitle()}</h3>
        <div className="h-64 flex items-center justify-center text-gray-400">
          <p>No data available for {getTimeRangeTitle().toLowerCase()}</p>
        </div>
      </div>
    )
  }

  const renderTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const dataPoint = payload[0].payload;
      return (
        <div className="bg-[#16181C] border border-[#2F3336] rounded-lg p-3 shadow-lg">
          <p className="text-white text-sm font-medium">{`${label}`}</p>
          <p className="text-[#1D9BF0] text-sm">
            {`Rate: ${payload[0].value.toFixed(2)}%`}
          </p>
          {dataPoint.disputeCount !== undefined && (
            <p className="text-gray-400 text-xs">
              {`Disputes: ${dataPoint.disputeCount} / Transactions: ${dataPoint.transactionCount || 0}`}
            </p>
          )}
        </div>
      )
    }
    return null
  }

  return (
    <div className="bg-transparent border border-zinc-800 rounded-2xl p-6">
      <h3 className="text-lg font-normal text-white mb-6">Chargeback Rate - {getTimeRangeTitle()}</h3>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart
            data={data}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: timeRange === TimeRange.ALL_TIME ? 25 : 5, // More bottom margin for all time
            }}
          >
            <defs>
              <linearGradient id="colorRate" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#ffffff" stopOpacity={0.3}/>
                <stop offset="95%" stopColor="#ffffff" stopOpacity={0}/>
              </linearGradient>
            </defs>
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke="#2F3336" 
              horizontal={true}
              vertical={true}
            />
            <XAxis 
              dataKey="date" 
              stroke="#71767B"
              fontSize={timeRange === TimeRange.ALL_TIME ? 10 : 12} // Smaller font for all time
              tickLine={false}
              axisLine={false}
              interval={getXAxisTickInterval()}
              tickFormatter={formatXAxisTick}
              angle={timeRange === TimeRange.ALL_TIME ? -45 : 0} // Angle labels for all time
              textAnchor={timeRange === TimeRange.ALL_TIME ? 'end' : 'middle'}
            />
            <YAxis 
              stroke="#71767B"
              fontSize={12}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => `${value.toFixed(0)}%`}
              dx={-10}
            />
            <Tooltip content={renderTooltip} />
            <Area
              type="monotone"
              dataKey="rate"
              stroke="#ffffff"
              strokeWidth={3}
              fillOpacity={1}
              fill="url(#colorRate)"
              connectNulls={false}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  )
})

ChargebackRateChart.displayName = 'ChargebackRateChart'

export default ChargebackRateChart 