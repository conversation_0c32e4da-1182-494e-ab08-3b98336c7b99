"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import {
  statisticsService,
  ChargebackReasonStats,
} from "@/services/statistics.service";

export default function TopChargebackReasons() {
  const { data: session } = useSession();
  const [data, setData] = useState<ChargebackReasonStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const formatCount = (count: number) => {
    return new Intl.NumberFormat("en-US").format(count);
  };

  // Fetch data when component mounts or session changes
  useEffect(() => {
    const fetchData = async () => {
      if (!session?.user?.access_token || !session?.user?.user_id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const reasons = await statisticsService.getTopChargebackReasons(
          session.user.user_id,
          {
            limit: 5,
          }
        );
        setData(reasons);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch data");
        console.error("Error fetching chargeback reasons:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [session?.user?.access_token, session?.user?.user_id]);

  // Loading state
  if (loading) {
    return (
      <div className="bg-transparent border border-zinc-800 rounded-2xl p-6">
        <h3 className="text-xl font-normal text-white mb-6">
          Top Chargeback Reasons
        </h3>
        <div className="space-y-4">
          {[...Array(5)].map((_, index) => (
            <div
              key={index}
              className="flex items-center justify-between py-3 border-b border-zinc-800 last:border-b-0 min-h-[60px]"
            >
              <div className="flex-1">
                <div className="h-4 bg-zinc-700 rounded animate-pulse mb-1"></div>
              </div>
              <div className="w-16 h-6 bg-zinc-700 rounded animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-transparent border border-zinc-800 rounded-2xl p-6">
        <h3 className="text-xl font-normal text-white mb-6">
          Top Chargeback Reasons
        </h3>
        <div className="text-center py-8">
          <p className="text-red-400 mb-2">Failed to load data</p>
          <p className="text-gray-500 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  // Empty state
  if (data.length === 0) {
    return (
      <div className="bg-transparent border border-zinc-800 rounded-2xl p-6">
        <h3 className="text-xl font-normal text-white mb-6">
          Top Chargeback Reasons
        </h3>
        <div className="text-center py-8">
          <p className="text-gray-400">No chargeback data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-transparent border border-zinc-800 rounded-2xl p-6">
      <h3 className="text-xl font-normal text-white mb-6">
        Top Chargeback Reasons
      </h3>

      <div className="space-y-4">
        {data.map((item, index) => (
          <div
            key={item.reasonCode}
            className="flex items-center justify-between py-3 border-b border-zinc-800 last:border-b-0 min-h-[60px]"
          >
            <div className="flex-1">
              <h4 className="text-white font-medium text-base mb-1">
                {item.reasonName}
              </h4>
            </div>

            <div className="flex items-center space-x-3">
              <span className="text-white font-semibold text-lg">
                {formatCount(item.count)}
              </span>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 pt-4 border-t border-zinc-800">
        <button className="text-blue-400 hover:text-blue-300 text-sm font-medium transition-colors">
          View All Reasons
        </button>
      </div>
    </div>
  );
}
