"use client";

import type React from "react";
import { memo, useMemo } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>riangle } from "lucide-react";
import DashboardHeader from "./header";
import MetricCard from "./_components/metric-card-optimized";
import ChargebackRateChart from "./_components/chargeback-rate-chart";
import { useDashboard } from "./_hooks";
import { MetricCardSkeleton } from "./_components/skeleton";
import StatsSection from "./_components/metrics/stats-section";

const DashboardContent: React.FC = () => {
  const {
    timeRange,
    customDateRange,
    metrics,
    goal,
    chargebackRateData,
    metricsLoading,
    chartLoading,
    error,
    setTimeRange,
    refetch,
  } = useDashboard({});

  // Memoize metrics processing
  const CHARGEBACK_METRICS = useMemo(() => {
    const getMetricValue = (label: string) => {
      const metric = metrics?.find((m) => m.label === label);
      if (!metric) return "0";

      if (metric.format === "percentage") {
        return typeof metric.value === "number"
          ? `${metric.value.toFixed(2)}%`
          : `${metric.value}%`;
      }
      return metric.value.toString();
    };

    return [
      {
        title: "Alerts",
        value: getMetricValue("Alerts"),
        IconComponent: BellRing,
        iconProps: { className: "w-6 h-6 text-gray-400" },
      },
      {
        title: "Chargebacks",
        value: getMetricValue("Blocked"),
        IconComponent: ShieldCheck,
        iconProps: { className: "w-6 h-6 text-gray-400" },
        valueClassName: "",
      },
      {
        title: "Chargeback Rate",
        value: getMetricValue("Chargeback Rate"),
        IconComponent: Percent,
        iconProps: { className: "text-2xl text-gray-400" },
        valueClassName: "text-green-500",
      },
      {
        title: "Chargeback Rate (Projected)",
        value: getMetricValue("Chargeback Rate (Projected)"),
        IconComponent: AlertTriangle,
        iconProps: { className: "w-6 h-6 text-gray-400" },
        valueClassName: "",
      },
    ];
  }, [metrics]);

  return (
    <div className="relative w-full bg-black text-white p-4">
      <div className="relative z-10 mx-auto">
        <DashboardHeader
          timeRange={timeRange}
          onTimeRangeChange={setTimeRange}
          customDateRange={customDateRange}
        />

        {/* Error state */}
        {error && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6">
            <p className="text-red-400">Error loading dashboard: {error}</p>
            <button
              onClick={refetch}
              className="mt-2 text-sm text-red-300 hover:text-red-200 underline"
            >
              Try again
            </button>
          </div>
        )}

        {/* Chargeback Rate Chart Section - Independent Loading */}
        <div className="my-8 sm:my-12">
          <ChargebackRateChart
            data={chargebackRateData}
            loading={chartLoading}
            timeRange={timeRange}
          />
        </div>

        {/* Metrics Section - Progressive Loading */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
          {metricsLoading
            ? // Show skeleton only for metrics loading
              [1, 2, 3, 4].map((i) => <MetricCardSkeleton key={i} />)
            : // Show metrics immediately when available
              CHARGEBACK_METRICS.map((metric) => (
                <MetricCard key={metric.title} {...metric} />
              ))}
        </div>

        {/* Stats Sections*/}
        <div className="mt-8 sm:mt-12">
          <StatsSection />
        </div>
      </div>
    </div>
  );
};

export default DashboardContent;
