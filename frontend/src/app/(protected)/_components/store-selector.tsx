"use client";

import { Store, Loader2 } from "lucide-react";
import { useAppSelector } from "@/redux/hooks";
import { selectStore, selectStoreLoading, selectStoreError } from "@/redux/slices/store";

export function StoreSelector() {
  const store = useAppSelector(selectStore);
  const isLoading = useAppSelector(selectStoreLoading);
  const error = useAppSelector(selectStoreError);

  return (
    <div className="px-4 mb-6">
      <div className="w-full bg-neutral-900 border border-neutral-800 rounded-full px-4 py-6 text-lg font-semibold flex items-center gap-3">
        {isLoading ? <Loader2 className="animate-spin" /> : <Store />}
        <span>
          {isLoading ? "Loading store..." : 
           error ? `Error: ${error}` :
           store?.storeName || "No store connected"}
        </span>
      </div>
    </div>
  );
}
