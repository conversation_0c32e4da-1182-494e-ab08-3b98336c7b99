"use client";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  LogOutIcon,
  Settings2Icon,
  HelpCircleIcon,
  HomeIcon,
  CreditCardIcon,
  BellRingIcon,
} from "lucide-react";
import LogoSVG from "@/components/svg/Logo";
import { authService } from "@/services/auth.service";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useAppDispatch } from "@/redux/hooks";
import { resetState } from "@/redux/store";
import { useSession } from "next-auth/react";

export function DashboardHeader() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const { data: session } = useSession();

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      dispatch(resetState());
      await authService.logout();
      router.push("/auth/connect");
    } catch (error) {
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
      <div className="h-16 px-4 flex items-center justify-between">
        <div className="flex items-center">
          <Link href="/" className="flex items-center font-bold text-xl mr-6">
            <LogoSVG size={175} />
          </Link>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
              >
                <div className="w-5 h-5 rounded-full bg-green-500 text-white flex items-center justify-center text-xs">
                  C
                </div>
                <span>Cuong Dinh</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuLabel>
                Cuong Dinh{" "}
                <span className="text-xs text-muted-foreground">Owner</span>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Link href="#" className="flex w-full">
                  Create new account
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Link href="#" className="flex w-full">
                  Business settings
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center space-x-4">
          <nav className="flex items-center space-x-4">
            <Link
              href="/dashboard"
              className="text-sm font-medium flex items-center"
            >
              <HomeIcon className="h-4 w-4 mr-1.5" /> Dashboard
            </Link>
            <Link
              href="/alerts"
              className="text-sm font-medium flex items-center"
            >
              <BellRingIcon className="h-4 w-4 mr-1.5" /> Alerts{" "}
              <span className="ml-1 text-xs bg-muted rounded px-1">0</span>
            </Link>
            <Link
              href="/credits"
              className="text-sm font-medium flex items-center"
            >
              <CreditCardIcon className="h-4 w-4 mr-1.5" /> Credits{" "}
              <span className="ml-1 text-xs bg-muted rounded px-1">0</span>
            </Link>
          </nav>

          <div className="flex items-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="icon" variant="ghost" className="rounded-full">
                  <div className="w-8 h-8 rounded-full bg-green-600 text-white flex items-center justify-center text-xs">
                    DL
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>DINH CUONG LE</DropdownMenuLabel>
                <DropdownMenuItem className="text-xs text-muted-foreground">
                  <EMAIL>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Settings2Icon className="mr-2 h-4 w-4" />
                  <span>Personal settings</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <HelpCircleIcon className="mr-2 h-4 w-4" />
                  <span>Help & Support</span>
                </DropdownMenuItem>
                {session && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="text-red-500 cursor-pointer"
                      onClick={handleLogout}
                      disabled={isLoggingOut}
                    >
                      <LogOutIcon className="mr-2 h-4 w-4" />
                      <span>{isLoggingOut ? "Logging out..." : "Logout"}</span>
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  );
}
