"use client";

import * as React from "react";
import { ChevronsUpDown } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";

export interface Descriptor {
  value: string;
  label: string;
}

// Default descriptors if none are provided
const defaultDescriptors: Descriptor[] = [
  { value: "all", label: "All" },
  { value: "chic_add_ons", label: "CHIC ADD ONS" },
  { value: "chicaddons", label: "CHICADDONS" },
  // Add more descriptors here
];

interface DescriptorSelectProps {
  options?: Descriptor[];
  value?: string | null;
  onChange?: (value: string) => void;
  multiple?: boolean;
}

export function DescriptorSelect({
  options = defaultDescriptors,
  value = null,
  onChange,
  multiple = false,
}: DescriptorSelectProps) {
  const [open, setOpen] = React.useState(false);
  const [selectedValues, setSelectedValues] = React.useState<string[]>(
    value ? [value] : ["all"]
  );

  // Update local state when value prop changes
  React.useEffect(() => {
    if (value !== null) {
      setSelectedValues([value]);
    }
  }, [value]);

  const handleSelect = (selectValue: string) => {
    if (multiple) {
      // Multiple selection logic (original behavior)
      if (selectValue === "all") {
        const newValues = selectedValues.includes("all") ? [] : ["all"];
        setSelectedValues(newValues);
        if (onChange) {
          onChange(newValues[0] || "");
        }
      } else {
        setSelectedValues((prev) => {
          const newValues = prev.filter((v) => v !== "all"); // Remove 'all' if other items are selected
          if (newValues.includes(selectValue)) {
            return newValues.filter((v) => v !== selectValue);
          } else {
            return [...newValues, selectValue];
          }
        });
      }
    } else {
      // Single selection
      const newValue = selectValue === value ? null : selectValue;
      setSelectedValues(newValue ? [newValue] : []);

      if (onChange) {
        onChange(newValue || "");
      }

      // Close the dropdown after selection in single mode
      setOpen(false);
    }
  };

  const getButtonLabel = () => {
    if (selectedValues.includes("all")) {
      return "Descriptors: All";
    }
    if (selectedValues.length === 0) {
      return "Select descriptors...";
    }
    if (selectedValues.length === 1) {
      return (
        options.find((d) => d.value === selectedValues[0])?.label ??
        "Select descriptors..."
      );
    }
    return `${selectedValues.length} selected`;
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-[200px] justify-between text-xs"
        >
          {getButtonLabel()}
          <ChevronsUpDown className="ml-2 h-3 w-3 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput
            placeholder="Search descriptors..."
            className="h-9 text-xs"
          />
          <CommandList>
            <CommandEmpty>No descriptor found.</CommandEmpty>
            <CommandGroup>
              {options.map((descriptor) => {
                const isSelected = selectedValues.includes(descriptor.value);
                return (
                  <CommandItem
                    key={descriptor.value}
                    value={descriptor.value}
                    onSelect={() => handleSelect(descriptor.value)}
                    className="text-xs flex items-center cursor-pointer"
                  >
                    <Checkbox
                      id={`desc-${descriptor.value}`}
                      checked={isSelected}
                      className="mr-2 h-4 w-4"
                    />
                    {descriptor.label}
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
