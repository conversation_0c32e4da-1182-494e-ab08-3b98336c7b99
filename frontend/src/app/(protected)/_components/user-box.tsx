"use client";

import { useState, useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { authService } from "@/services/auth.service";
import { useRouter } from "next/navigation";
import { LogOut, User, Settings, HelpCircle, Store, CreditCard } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import { selectStore, selectStoreLoading, selectStoreError, refreshStoreDetails, disconnectStore } from "@/redux/slices/store";
import { resetState } from "@/redux/store";
import { useSession } from "next-auth/react";

export function UserBox() {
  const router = useRouter();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const store = useAppSelector(selectStore);
  const isLoading = useAppSelector(selectStoreLoading);
  const error = useAppSelector(selectStoreError);
  const dispatch = useAppDispatch();
  const { data: session } = useSession();

  // Add client-side hydration state
  const [isClient, setIsClient] = useState(false);

  // Mark as client-rendered after hydration
  useEffect(() => {
    setIsClient(true);
  }, [store]);

  // Extract user information from session
  const user = {
    name: session?.user?.name || "Guest Store",
    email: session?.user?.email || "",
    domain: session?.user?.domain || "",
  };

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      dispatch(resetState());
      await authService.logout();
    } catch (error) {
    } finally {
      setIsLoggingOut(false);
    }
  };

  const handleBillingNavigation = () => {
    if (!store) return;
    
    switch (store.provider.toLowerCase()) {
      case "shopify":
        // Remove the .myshopify.com suffix if it exists
        const shopDomain = store.data.domain.replace(/\.myshopify\.com$/, "");
        window.open(`https://admin.shopify.com/store/${shopDomain}/settings/billing`, "_blank");
        break;
      case "woocommerce":
        window.open("https://woocommerce.com/my-account/", "_blank");
        break;
      case "magento":
        window.open("https://marketplace.magento.com/customer/account/", "_blank");
        break;
      default:
        break;
    }
  };

  // Get initials for avatar
  const getInitials = (name: string) => {
    return name.charAt(0).toUpperCase();
  };

  const initials = getInitials(user.name);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <button className="w-full flex items-center gap-3 p-3 rounded-full hover:bg-neutral-800 transition-colors">
          {isClient && store ? (
            <>
              <div className="h-10 w-10 flex items-center justify-center rounded-full bg-primary/40">
                <Store className="h-5 w-5 text-primary" />
              </div>
              <div className="flex-1 text-left overflow-hidden">
                <p className="font-bold text-sm leading-tight truncate">
                  {store.storeName}
                </p>
                <p className="text-neutral-500 text-xs leading-tight uppercase">
                  {store.provider}
                </p>
              </div>
            </>
          ) : (
            <>
              <Avatar className="h-10 w-10">
                <AvatarFallback className="bg-primary/40 text-primary font-bold">
                  {initials}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 text-left">
                <p className="font-bold text-sm leading-tight">{user.name}</p>
                <p className="text-neutral-500 text-xs leading-tight">
                  {user.domain}
                </p>
              </div>
            </>
          )}
          <div className="text-neutral-500">
            <svg viewBox="0 0 24 24" aria-hidden="true" className="h-5 w-5">
              <path
                d="M3 12c0-1.1.9-2 2-2s2 .9 2 2-.9 2-2 2-2-.9-2-2zm9 2c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm7 0c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z"
                fill="currentColor"
              />
            </svg>
          </div>
        </button>
      </PopoverTrigger>
      <PopoverContent
        className="w-72 p-0 bg-neutral-900 border border-neutral-800 rounded-2xl shadow-lg"
        align="start"
        sideOffset={10}
      >
        <div className="p-4 border-b border-neutral-800">
          <div className="flex items-center gap-3">
            <Avatar className="h-12 w-12">
              <AvatarFallback className="bg-primary/40 text-primary font-bold text-xl">
                {initials}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <p className="font-bold">{user.name}</p>
              <p className="text-neutral-500 text-sm">{user.domain}</p>
            </div>
          </div>
        </div>
        <div className="py-2">
          <button className="w-full flex items-center px-4 py-3 text-left hover:bg-neutral-800 transition-colors">
            <User className="h-5 w-5 mr-3" />
            <span>Profile</span>
          </button>
          <button className="w-full flex items-center px-4 py-3 text-left hover:bg-neutral-800 transition-colors">
            <Settings className="h-5 w-5 mr-3" />
            <span>Settings</span>
          </button>
          <button className="w-full flex items-center px-4 py-3 text-left hover:bg-neutral-800 transition-colors">
            <HelpCircle className="h-5 w-5 mr-3" />
            <span>Help Center</span>
          </button>
          {store && (
            <button 
              onClick={handleBillingNavigation} 
              className="w-full flex items-center px-4 py-3 text-left hover:bg-neutral-800 transition-colors"
            >
              <CreditCard className="h-5 w-5 mr-3" />
              <span>Billing</span>
            </button>
          )}
        </div>
        {session && (
          <div className="border-t border-neutral-800 py-2">
            <button
              onClick={handleLogout}
              disabled={isLoggingOut}
              className="w-full flex items-center px-4 py-3 text-left text-red-500 hover:bg-neutral-800 transition-colors"
            >
              <LogOut className="h-5 w-5 mr-3" />
              <span>{isLoggingOut ? "Logging out..." : "Log out"}</span>
            </button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}
