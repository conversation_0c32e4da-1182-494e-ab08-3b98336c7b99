"use client";

import { useRouter } from "next/navigation";
import { Store, CirclePlus, Loader2 } from "lucide-react";
import { useEffect, useState } from "react";
import { useAppSelector } from "@/redux/hooks";
import { selectStore, selectStoreLoading, selectStoreError } from "@/redux/slices/store";

export function LinkedStore() {
  const router = useRouter();
  const store = useAppSelector(selectStore);
  const isLoading = useAppSelector(selectStoreLoading);
  const error = useAppSelector(selectStoreError);
  
  // Add client-side hydration state
  const [isClient, setIsClient] = useState(false);
  
  // Mark as client-rendered after hydration
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Common class for the container
  const baseClass = "w-full bg-neutral-900 border border-neutral-800 rounded-full px-5 py-3 flex items-center gap-3";

  // During initial render (server-side or hydration), always show loading state
  if (!isClient) {
    return (
      <div className="px-4 mb-6">
        <div className={baseClass}>
          <Loader2 className="animate-spin" size={20} />
          <span>Loading store...</span>
        </div>
      </div>
    );
  }
  
  // Only render actual content after hydration is complete
  return (
    <div className="px-4 mb-6">
      {isLoading ? (
        <div className={baseClass}>
          <Loader2 className="animate-spin" size={20} />
          <span>Loading store...</span>
        </div>
      ) : error ? (
        <div className={`${baseClass} text-red-500`}>
          <span>Error loading store: {error}</span>
        </div>
      ) : !store ? (
        <div
          className={`${baseClass} hover:bg-neutral-800 cursor-pointer`}
          onClick={() => router.push("/add-store")}
        >
          <CirclePlus size={20} />
          <span>Connect a store</span>
        </div>
      ) : (
        <div className={`${baseClass} text-lg font-semibold`}>
          <Store size={20} />
          <span>{store.storeName}</span>
        </div>
      )}
    </div>
  );
}
