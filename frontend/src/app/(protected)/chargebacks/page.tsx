"use client";

import { useState, useCallback, useMemo, useEffect, useRef } from "react";
import { useSession } from "next-auth/react";
import { useAppSelector, useAppDispatch } from "@/redux/hooks";
import { selectStoresByProvider } from "@/redux/slices/store";
import { useProviderIntegration } from "@/hooks/useProviderIntegration";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import ShopifyLogo from "@/components/svg/partners/ShopifyLogo";
import StripeSquareLogo from "@/components/svg/partners/StripeSquareLogo";
import { Search, Filter, Download, Calendar, ChevronDown, Receipt, ArrowUpDown, AlertCircle, Send, Trophy, XCircle, FileText } from "lucide-react";
import { chargebackService, ChargebackItem, ChargebackStats } from "@/services/chargeback.service";
import { Loading } from "@/components/ui/loading";
import { format } from "date-fns";
import { toast } from "sonner";
import { TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { TablePagination } from "@/components/ui/table-pagination";

interface Tab {
  id: string;
  label: string;
  icon: any;
  count: number;
  status: string | null;
}

interface FilterState {
  fromAmount: string;
  toAmount: string;
  isOpen: boolean;
}

interface DateRangeState {
  startDate: string;
  endDate: string;
  isOpen: boolean;
}

export default function ChargebacksPage() {
  const { data: session } = useSession();
  const linkedStoreId = session?.user?.id;
  const accessToken = session?.user?.access_token;
  const dispatch = useAppDispatch();

  // Payment gateway state - default to 'shopify'
  const [selectedPaymentGateway, setSelectedPaymentGateway] =
    useState<string>("shopify");

  // Get provider integration data
  const { providerStatus } = useProviderIntegration();

  // Get stores by provider from Redux
  const shopifyStores = useAppSelector((state) =>
    selectStoresByProvider("shopify")(state)
  );
  const stripeStores = useAppSelector((state) =>
    selectStoresByProvider("stripe")(state)
  );

  // Get current store based on selected payment gateway
  const currentStore = useMemo(() => {
    switch (selectedPaymentGateway) {
      case "shopify":
        return shopifyStores[0];
      case "stripe":
        return stripeStores[0];
      default:
        return null;
    }
  }, [selectedPaymentGateway, shopifyStores, stripeStores]);

  // State management
  const [activeTab, setActiveTab] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [sortBy, setSortBy] = useState<string>("created_desc");
  const [autoResponse, setAutoResponse] = useState(true);
  const [enableAlerts, setEnableAlerts] = useState(false);
  
  // Data state
  const [chargebacks, setChargebacks] = useState<ChargebackItem[]>([]);
  const [stats, setStats] = useState<ChargebackStats | null>(null);
  const [loading, setLoading] = useState(false);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Filter states
  const [filters, setFilters] = useState<FilterState>({
    fromAmount: "",
    toAmount: "",
    isOpen: false,
  });

  const [dateRange, setDateRange] = useState<DateRangeState>({
    startDate: "",
    endDate: "",
    isOpen: false,
  });

  const [appliedFilters, setAppliedFilters] = useState({
    fromAmount: "",
    toAmount: "",
  });

  const [appliedDateRange, setAppliedDateRange] = useState({
    startDate: "",
    endDate: "",
  });

  // Track loaded store to prevent unnecessary fetches
  const loadedStoreRef = useRef<string | null>(null);

  // Define tabs based on chargeback status
  const tabs: Tab[] = [
    { 
      id: "all", 
      label: "All", 
      icon: FileText, 
      count: stats?.total || 0,
      status: null 
    },
    { 
      id: "open", 
      label: "Open", 
      icon: AlertCircle, 
      count: stats?.byStatus?.open || 0,
      status: "open" 
    },
    { 
      id: "submitted", 
      label: "Submitted", 
      icon: Send, 
      count: stats?.byStatus?.submitted || 0,
      status: "submitted" 
    },
    { 
      id: "won", 
      label: "Won", 
      icon: Trophy, 
      count: stats?.byStatus?.won || 0,
      status: "won" 
    },
    { 
      id: "lost", 
      label: "Lost", 
      icon: XCircle, 
      count: stats?.byStatus?.lost || 0,
      status: "lost" 
    },
  ];

  // Fetch chargebacks
  const fetchChargebacks = useCallback(async () => {
    if (!currentStore?.id) return;
    
    try {
      setLoading(true);
      
      const activeTabData = tabs.find(t => t.id === activeTab);
      const status = activeTabData?.status || undefined;
      
      const response = await chargebackService.getChargebacksByStore(currentStore.id, {
        page: currentPage,
        pageSize,
        status,
        startDate: appliedDateRange.startDate ? new Date(appliedDateRange.startDate) : undefined,
        endDate: appliedDateRange.endDate ? new Date(appliedDateRange.endDate) : undefined
      });
      
      setChargebacks(response.chargebacks);
      setTotalCount(response.pagination.totalCount);
      setTotalPages(response.pagination.totalPages);
    } catch (error: any) {
      console.error("Failed to fetch chargebacks:", error);
      toast.error("Failed to load chargebacks");
    } finally {
      setLoading(false);
    }
  }, [currentStore?.id, currentPage, pageSize, activeTab, appliedDateRange]);

  // Fetch statistics
  const fetchStats = useCallback(async () => {
    if (!currentStore?.id) return;
    
    try {
      const statsData = await chargebackService.getChargebackStats(currentStore.id);
      setStats(statsData);
    } catch (error: any) {
      console.error("Failed to fetch chargeback stats:", error);
    }
  }, [currentStore?.id]);

  // Effect to fetch data when dependencies change
  useEffect(() => {
    fetchChargebacks();
  }, [fetchChargebacks]);

  // Effect to fetch stats when store changes
  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    setCurrentPage(1);
  };

  const handlePaymentGatewayChange = useCallback(
    (gateway: string) => {
      setSelectedPaymentGateway(gateway);
      setActiveTab("all");
      setCurrentPage(1);
    },
    []
  );

  // Available payment gateways based on provider status
  const availableGateways = useMemo(() => {
    const gateways: { value: string; label: string; logo: React.ReactNode }[] = [];
    if (providerStatus.shopify) {
      gateways.push({
        value: "shopify",
        label: "Shopify Payments",
        logo: <ShopifyLogo width={20} height={20} />,
      });
    }
    if (providerStatus.stripe) {
      gateways.push({
        value: "stripe",
        label: "Stripe",
        logo: <StripeSquareLogo width={20} height={20} />,
      });
    }
    return gateways;
  }, [providerStatus]);

  // Effect to ensure selected gateway is available
  useEffect(() => {
    if (availableGateways.length > 0) {
      const isCurrentGatewayAvailable = availableGateways.some(g => g.value === selectedPaymentGateway);
      if (!isCurrentGatewayAvailable) {
        setSelectedPaymentGateway(availableGateways[0].value);
      }
    }
  }, [availableGateways.length]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Filter chargebacks by search query locally
  const filteredChargebacks = chargebacks.filter(item => {
    if (!searchQuery) return true;
    const query = searchQuery.toLowerCase();
    return (
      item.id.toLowerCase().includes(query) ||
      item.transactionId?.toLowerCase().includes(query) ||
      item.orderId?.toLowerCase().includes(query) ||
      item.orderName?.toLowerCase().includes(query) ||
      item.reason?.toLowerCase().includes(query)
    );
  });

  // Format date
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '-';
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch {
      return dateString;
    }
  };

  // Map database status to display status
  type DisplayStatus = 'Open' | 'Submitted' | 'Won' | 'Lost';
  
  const getDisplayStatus = (status: string | null | undefined): DisplayStatus | null => {
    if (!status) return null;
    const statusLower = status.toLowerCase();
    
    // Map detailed statuses to main categories
    if (statusLower === 'needs_response' || statusLower === 'warning_needs_response' || statusLower === 'open') {
      return 'Open';
    } else if (statusLower === 'under_review' || statusLower === 'warning_under_review' || statusLower === 'submitted') {
      return 'Submitted';
    } else if (statusLower === 'won' || statusLower === 'accepted') {
      return 'Won';
    } else if (statusLower === 'lost' || statusLower === 'charge_refunded') {
      return 'Lost';
    }
    
    // Return Open as default for unknown statuses
    return 'Open';
  };
  
  // Get status badge class
  const getStatusBadgeClass = (status: string | null | undefined) => {
    const displayStatus = getDisplayStatus(status);
    if (!displayStatus) return "bg-gray-500/10 text-gray-600 border-gray-500/20";
    
    switch (displayStatus) {
      case 'Open':
        return "bg-yellow-500/10 text-yellow-600 border-yellow-500/20";
      case 'Submitted':
        return "bg-blue-500/10 text-blue-600 border-blue-500/20";
      case 'Won':
        return "bg-green-500/10 text-green-600 border-green-500/20";
      case 'Lost':
        return "bg-red-500/10 text-red-600 border-red-500/20";
      default:
        return "bg-gray-500/10 text-gray-600 border-gray-500/20";
    }
  };

  const renderFilters = () => {
    if (!filters.isOpen) return null;

    return (
      <div className="absolute mt-2 p-4 bg-zinc-900 rounded-lg border border-gray-800 shadow-lg z-10 w-80">
        <div className="space-y-4">
          <div>
            <label className="block text-sm text-gray-400 mb-1">Min Amount</label>
            <input
              type="text"
              value={filters.fromAmount}
              onChange={(e) => setFilters(prev => ({ ...prev, fromAmount: e.target.value }))}
              placeholder="$0.00"
              className="w-full bg-black border border-gray-800 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:border-green-500"
            />
          </div>

          <div>
            <label className="block text-sm text-gray-400 mb-1">Max Amount</label>
            <input
              type="text"
              value={filters.toAmount}
              onChange={(e) => setFilters(prev => ({ ...prev, toAmount: e.target.value }))}
              placeholder="$1000.00"
              className="w-full bg-black border border-gray-800 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:border-green-500"
            />
          </div>

          <div className="flex justify-end gap-2 pt-2 border-t border-gray-800">
            <button
              onClick={() => {
                setFilters({ fromAmount: "", toAmount: "", isOpen: false });
                setAppliedFilters({ fromAmount: "", toAmount: "" });
              }}
              className="px-4 py-2 text-gray-300 hover:bg-black-800 rounded-lg"
            >
              Clear
            </button>
            <button
              onClick={() => {
                setAppliedFilters({
                  fromAmount: filters.fromAmount,
                  toAmount: filters.toAmount,
                });
                setFilters(prev => ({ ...prev, isOpen: false }));
              }}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              Apply
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderDateRangePicker = () => {
    if (!dateRange.isOpen) return null;

    return (
      <div className="absolute right-0 mt-2 p-4 bg-zinc-900 rounded-lg border border-gray-800 shadow-lg z-10 w-80">
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => {
                const today = new Date();
                const lastWeek = new Date(today);
                lastWeek.setDate(lastWeek.getDate() - 7);
                setDateRange(prev => ({
                  ...prev,
                  startDate: lastWeek.toISOString().split('T')[0],
                  endDate: today.toISOString().split('T')[0]
                }));
              }}
              className="px-3 py-2 text-sm text-gray-300 bg-black hover:bg-zinc-800 rounded-lg"
            >
              Last 7 days
            </button>
            <button
              onClick={() => {
                const today = new Date();
                const lastMonth = new Date(today);
                lastMonth.setDate(lastMonth.getDate() - 30);
                setDateRange(prev => ({
                  ...prev,
                  startDate: lastMonth.toISOString().split('T')[0],
                  endDate: today.toISOString().split('T')[0]
                }));
              }}
              className="px-3 py-2 text-sm text-gray-300 bg-black hover:bg-zinc-800 rounded-lg"
            >
              Last 30 days
            </button>
          </div>

          <div>
            <label className="block text-sm text-gray-400 mb-1">Start Date</label>
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              className="w-full bg-black border border-gray-800 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:border-green-500 [color-scheme:dark]"
              style={{ colorScheme: 'dark' }}
            />
          </div>

          <div>
            <label className="block text-sm text-gray-400 mb-1">End Date</label>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              className="w-full bg-black border border-gray-800 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:border-green-500 [color-scheme:dark]"
              style={{ colorScheme: 'dark' }}
            />
          </div>

          <div className="flex justify-end gap-2 pt-2 border-t border-gray-800">
            <button
              onClick={() => {
                setDateRange({ startDate: "", endDate: "", isOpen: false });
                setAppliedDateRange({ startDate: "", endDate: "" });
              }}
              className="px-4 py-2 text-gray-300 hover:bg-black-800 rounded-lg"
            >
              Clear
            </button>
            <button
              onClick={() => {
                setAppliedDateRange({
                  startDate: dateRange.startDate,
                  endDate: dateRange.endDate,
                });
                setDateRange(prev => ({ ...prev, isOpen: false }));
              }}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              Apply
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-black">
      {/* Header */}
      <div className="px-6 mt-6">
        <h1 className="text-4xl font-bold text-white">Chargebacks</h1>
      </div>

      <div className="px-6">
        {/* Tabs */}
        <div className="my-4 flex gap-2 overflow-x-auto scrollbar-hide border-b border-gray-800">
          {tabs.map(({ id, label, icon: Icon, count }) => (
            <button
              key={id}
              onClick={() => handleTabChange(id)}
              className={`flex items-center gap-2 px-4 py-3 text-base font-medium transition-colors
              ${activeTab === id
                ? 'text-white border-b-2 border-green-500'
                : 'text-zinc-300 hover:text-white'}`}
            >
              <Icon className="w-4 h-4" />
              {label}
              <span className={`px-1.5 py-0.5 rounded-full text-xs ${
                activeTab === id 
                  ? 'bg-green-600 text-white' 
                  : 'bg-green-100 text-green-700'
              }`}>
                {count.toLocaleString()}
              </span>
            </button>
          ))}
        </div>

        {/* Search and Filters */}
        <div className="py-2">
          <div className="flex flex-col sm:flex-row gap-2">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search chargebacks..."
                  className="w-full h-10 pl-10 pr-4 bg-transparent border border-gray-800 rounded-lg text-gray-300 focus:outline-none focus:border-green-500 hover:border-green-500 transition-colors"
                />
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              <div className="relative flex-1 sm:flex-none">
                <button
                  onClick={() => {
                    setFilters(prev => ({ ...prev, isOpen: !prev.isOpen }));
                    setDateRange(prev => ({ ...prev, isOpen: false }));
                  }}
                  className={`w-full sm:w-auto h-10 px-4 bg-transparent border ${filters.isOpen ? 'border-green-500' : 'border-gray-800'} rounded-lg text-gray-300 flex items-center gap-2 hover:border-green-500 transition-colors justify-center sm:justify-start focus:outline-none focus:border-green-500`}
                >
                  <Filter size={18} />
                  Filters
                  <ChevronDown size={16} />
                </button>
                {renderFilters()}
              </div>
              <div className="relative flex-1 sm:flex-none">
                <button
                  onClick={() => {
                    setDateRange(prev => ({ ...prev, isOpen: !prev.isOpen }));
                    setFilters(prev => ({ ...prev, isOpen: false }));
                  }}
                  className={`w-full sm:w-auto h-10 px-4 bg-transparent border ${dateRange.isOpen ? 'border-green-500' : 'border-gray-800'} rounded-lg text-gray-300 flex items-center gap-2 hover:border-green-500 transition-colors justify-center sm:justify-start focus:outline-none focus:border-green-500`}
                >
                  <Calendar size={18} />
                  {appliedDateRange.startDate && appliedDateRange.endDate ? (
                    <span className="text-sm truncate">
                      {new Date(appliedDateRange.startDate).toLocaleDateString()} - {new Date(appliedDateRange.endDate).toLocaleDateString()}
                    </span>
                  ) : (
                    'Date Range'
                  )}
                  <ChevronDown size={16} />
                </button>
                {renderDateRangePicker()}
              </div>
              <div className="relative flex-1 sm:flex-none">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="w-full sm:w-auto h-10 px-4 bg-transparent border border-gray-800 rounded-lg text-gray-300 focus:outline-none focus:border-green-500 hover:border-green-500 transition-colors cursor-pointer flex items-center gap-2 justify-center sm:justify-start">
                      <ArrowUpDown size={18} />
                      <span>
                        {sortBy === "created_desc" && "Newest First"}
                        {sortBy === "created_asc" && "Oldest First"}
                        {sortBy === "amount_desc" && "Amount ↓"}
                        {sortBy === "amount_asc" && "Amount ↑"}
                      </span>
                      <ChevronDown size={16} />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="bg-zinc-900 border border-gray-800">
                    <DropdownMenuItem 
                      onClick={() => setSortBy("created_desc")}
                      className="text-gray-300 hover:bg-zinc-800 hover:text-white cursor-pointer"
                    >
                      Newest First
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => setSortBy("created_asc")}
                      className="text-gray-300 hover:bg-zinc-800 hover:text-white cursor-pointer"
                    >
                      Oldest First
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => setSortBy("amount_desc")}
                      className="text-gray-300 hover:bg-zinc-800 hover:text-white cursor-pointer"
                    >
                      Amount ↓
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => setSortBy("amount_asc")}
                      className="text-gray-300 hover:bg-zinc-800 hover:text-white cursor-pointer"
                    >
                      Amount ↑
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="mt-6 space-y-6">
          <div className="bg-black rounded-lg border border-gray-800 overflow-x-auto">
            <div className="min-w-full">
              {/* Mobile Card View */}
              <div className="block sm:hidden">
                {loading ? (
                  <div className="flex items-center justify-center py-32">
                    <Loading size="sm" />
                  </div>
                ) : filteredChargebacks.length === 0 ? (
                  <div className="flex items-center justify-center py-32">
                    <div className="text-center space-y-3">
                      <div className="w-12 h-12 bg-gray-100 rounded-full mx-auto flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-6 w-6 text-gray-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </div>
                      <h3 className="font-medium">No chargebacks found</h3>
                      <p className="text-sm text-muted-foreground">
                        {searchQuery 
                          ? "No chargebacks found matching your search." 
                          : activeTab !== 'all' 
                            ? `No ${activeTab} chargebacks found for the selected time period.`
                            : "No chargebacks found for the selected time period."}
                      </p>
                    </div>
                  </div>
                ) : filteredChargebacks.map((item) => (
                  <div
                    key={item.id}
                    className="p-4 border-b border-gray-800 cursor-pointer hover:bg-zinc-900"
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <div className="text-green-400 hover:text-green-300">
                          {item.id}
                        </div>
                        <div className="text-sm text-gray-400">
                          {item.transactionId || item.chargeId || item.orderId || '-'}
                        </div>
                      </div>
                      <Badge
                        variant="outline"
                        className={getStatusBadgeClass(item.status)}
                      >
                        {getDisplayStatus(item.status) || item.status}
                      </Badge>
                    </div>

                    <div className="space-y-3">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-gray-400">Amount:</span>
                          <span className="text-gray-300 ml-2">{item.amount ? item.amount.toFixed(2) : '-'}</span>
                        </div>
                        <div>
                          <span className="text-gray-400">Currency:</span>
                          <span className="text-gray-300 ml-2">{item.currency || 'USD'}</span>
                        </div>
                      </div>
                      
                      <div className="text-sm">
                        <span className="text-gray-400">Initiated:</span>
                        <span className="text-gray-300 ml-2">{formatDate(item.initiatedAt || item.createdAt)}</span>
                      </div>

                      <div className="text-sm">
                        <span className="text-gray-400">Evidence Due:</span>
                        <span className="text-gray-300 ml-2">{formatDate(item.evidenceDueBy)}</span>
                      </div>

                      <div className="text-sm">
                        <span className="text-gray-400">Reason:</span>
                        <div className="text-gray-300 mt-1">{item.reason || '-'}</div>
                      </div>

                      <div className="flex justify-end mt-4">
                        <button className="px-3 py-1.5 bg-green-500 text-white rounded-lg hover:bg-green-600 text-sm">
                          View Details
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Desktop Table View */}
              <table className="hidden sm:table w-full min-w-[1200px]">
                <TableHeader>
                  <TableRow className="border-b border-gray-800 bg-gray-950">
                    <TableHead className="border-r border-gray-800 px-4 py-3 text-left text-sm font-medium text-gray-400 w-12">
                      <Checkbox />
                    </TableHead>
                    <TableHead className="border-r border-gray-800 px-4 py-3 text-left text-sm font-medium text-gray-400">Chargeback ID</TableHead>
                    <TableHead className="border-r border-gray-800 px-4 py-3 text-left text-sm font-medium text-gray-400">
                      {selectedPaymentGateway === 'stripe' ? 'Charge ID' : 'Transaction'}
                    </TableHead>
                    <TableHead className="border-r border-gray-800 px-4 py-3 text-center text-sm font-medium text-gray-400">Status</TableHead>
                    <TableHead className="border-r border-gray-800 px-4 py-3 text-right text-sm font-medium text-gray-400">Amount</TableHead>
                    <TableHead className="border-r border-gray-800 px-4 py-3 text-center text-sm font-medium text-gray-400">Currency</TableHead>
                    <TableHead className="border-r border-gray-800 px-4 py-3 text-left text-sm font-medium text-gray-400">Reason</TableHead>
                    <TableHead className="border-r border-gray-800 px-4 py-3 text-left text-sm font-medium text-gray-400">Initiated</TableHead>
                    <TableHead className="border-r border-gray-800 px-4 py-3 text-left text-sm font-medium text-gray-400">Evidence Due</TableHead>
                    <TableHead className="px-4 py-3 text-center text-sm font-medium text-gray-400">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="divide-y divide-gray-800">
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-16">
                        <div className="flex flex-col items-center justify-center space-y-3">
                          <div className="w-12 h-12 border-4 border-gray-200 border-t-green-500 rounded-full animate-spin"></div>
                          <p className="text-sm text-muted-foreground">Loading chargebacks...</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredChargebacks.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-16">
                        <div className="flex flex-col items-center justify-center space-y-3">
                          <div className="w-12 h-12 bg-gray-100 rounded-full mx-auto flex items-center justify-center">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-6 w-6 text-gray-400"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M6 18L18 6M6 6l12 12"
                              />
                            </svg>
                          </div>
                          <h3 className="font-medium">No chargebacks found</h3>
                          <p className="text-sm text-muted-foreground">
                            {searchQuery 
                              ? "No chargebacks found matching your search." 
                              : activeTab !== 'all' 
                                ? `No ${activeTab} chargebacks found for the selected time period.`
                                : "No chargebacks found for the selected time period."}
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredChargebacks.map((item) => (
                      <TableRow
                        key={item.id}
                        className="hover:bg-zinc-700"
                      >
                        <TableCell className="border-r border-gray-800 px-4 py-4">
                          <Checkbox />
                        </TableCell>
                        <TableCell className="border-r border-gray-800 px-4 py-4">
                          <span 
                            className="text-green-400 text-sm cursor-pointer hover:text-green-300 hover:underline underline"
                          >
                            {item.id}
                          </span>
                        </TableCell>
                        <TableCell className="border-r border-gray-800 px-4 py-4 text-sm text-gray-300">
                          {item.transactionId || item.chargeId || item.orderId || '-'}
                        </TableCell>
                        <TableCell className="border-r border-gray-800 px-4 py-4 text-center">
                          <Badge
                            variant="outline"
                            className={getStatusBadgeClass(item.status)}
                          >
                            {getDisplayStatus(item.status) || item.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="border-r border-gray-800 px-4 py-4">
                          <div className="text-right text-sm text-green-500">
                            {item.amount ? item.amount.toFixed(2) : '-'}
                          </div>
                        </TableCell>
                        <TableCell className="border-r border-gray-800 px-4 py-4 text-sm text-center text-gray-300">
                          {item.currency || 'USD'}
                        </TableCell>
                        <TableCell className="border-r border-gray-800 px-4 py-4 text-sm text-gray-300">
                          {item.reason || '-'}
                        </TableCell>
                        <TableCell className="border-r border-gray-800 px-4 py-4 text-sm text-gray-300 text-nowrap">
                          {formatDate(item.initiatedAt || item.createdAt)}
                        </TableCell>
                        <TableCell className="border-r border-gray-800 px-4 py-4 text-sm text-gray-300 text-nowrap">
                          {formatDate(item.evidenceDueBy)}
                        </TableCell>
                        <TableCell className="px-4 py-4 text-center">
                          <div className="flex justify-center space-x-2">
                            <button className="px-3 py-1.5 bg-green-500 text-white rounded-lg hover:bg-green-600 text-sm">
                              View Details
                            </button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </table>
            </div>
          </div>

          {/* Pagination */}
          <TablePagination
            currentPage={loading ? 1 : currentPage}
            totalPages={loading ? 1 : totalPages}
            totalItems={loading ? 0 : totalCount}
            pageSize={pageSize}
            onPageChange={handlePageChange}
            isLoading={loading}
          />
        </div>
      </div>
    </div>
  );
}