import { useState, useEffect, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { 
  matchedBlockService, 
  BlockType, 
  SortBy,
  IMatchedBlock, 
  MatchedBlockListResponse,
  MatchedBlockOverviewResponse 
} from "@/services/matched-block.service";

interface BlockCounts {
  all: number;
  ethoca: number;
  rdr: number;
}

interface UseBlocksProps {
  selectedType: BlockType | null;
  pageSize?: number;
  searchQuery?: string;
  appliedFilters?: Record<string, any>;
  appliedDateRange?: { startDate: string; endDate: string };
  sortBy?: string;
}

interface UseBlocksReturn {
  // Data
  blocks: IMatchedBlock[];
  blockCounts: BlockCounts;
  
  // Pagination
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
  
  // Loading states
  loading: boolean;
  
  // Dialog state
  selectedBlock: IMatchedBlock | null;
  viewDetailsDialogOpen: boolean;
  
  // Actions
  handlePageChange: (page: number) => void;
  resetToFirstPage: () => void;
  openViewDetailsDialog: (block: IMatchedBlock) => void;
  setViewDetailsDialogOpen: (open: boolean) => void;
  refetch: () => Promise<void>;
}

export function useBlocks({ 
  selectedType, 
  pageSize = 10, 
  searchQuery,
  appliedFilters,
  appliedDateRange,
  sortBy = SortBy.DATE_DESC
}: UseBlocksProps): UseBlocksReturn {
  const { data: session } = useSession();
  
  // Data state
  const [blocks, setBlocks] = useState<IMatchedBlock[]>([]);
  const [blockCounts, setBlockCounts] = useState<BlockCounts>({
    all: 0,
    ethoca: 0,
    rdr: 0,
  });
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  
  // Loading state
  const [loading, setLoading] = useState(true);
  
  // Dialog state
  const [selectedBlock, setSelectedBlock] = useState<IMatchedBlock | null>(null);
  const [viewDetailsDialogOpen, setViewDetailsDialogOpen] = useState(false);

  // Memoize user ID to prevent unnecessary re-renders
  const userId = useMemo(() => session?.user?.user_id, [session?.user?.user_id]);

  // Type guard for list response
  const isListResponse = (response: any): response is MatchedBlockListResponse => {
    return response?.data && 'items' in response.data;
  };

  // No longer needed - counts are fetched with the list data
  // This reduces API calls and improves performance

  // Fetch blocks list
  const fetchBlocks = useCallback(async () => {
    if (!userId) return;

    setLoading(true);
    try {
      const filters: Record<string, any> = {};
      if (selectedType) {
        filters.type = selectedType;
      }

      // Add search query
      if (searchQuery && searchQuery.trim()) {
        filters.search = searchQuery.trim();
      }

      // Add applied filters with special handling for amount fields
      if (appliedFilters) {
        Object.entries(appliedFilters).forEach(([key, value]) => {
          if (value && value.toString().trim()) {
            // Convert amount fields to cents (x100) for database comparison
            if (key === 'minAmount' || key === 'maxAmount') {
              const numericValue = parseFloat(value.toString());
              if (!isNaN(numericValue)) {
                filters[key] = Math.round(numericValue * 100); // Convert to cents and round
              }
            } else {
              filters[key] = value;
            }
          }
        });
      }

      // Add date range filters with improved logic
      if (appliedDateRange?.startDate || appliedDateRange?.endDate) {
        // If we have any date range values, use custom timeRange
        if (appliedDateRange.startDate && appliedDateRange.endDate) {
          filters.timeRange = 'custom';
          filters.startDate = appliedDateRange.startDate;
          filters.endDate = appliedDateRange.endDate;
        } else if (appliedDateRange.startDate && !appliedDateRange.endDate) {
          // Only start date provided - from start date to today
          filters.timeRange = 'custom';
          filters.startDate = appliedDateRange.startDate;
          filters.endDate = new Date().toISOString().split("T")[0];
        } else if (!appliedDateRange.startDate && appliedDateRange.endDate) {
          // Only end date provided - from very early date to end date
          filters.timeRange = 'custom';
          filters.startDate = '2020-01-01'; // Default early date
          filters.endDate = appliedDateRange.endDate;
        }
      }

      // Add sortBy if it's not the default
      if (sortBy && sortBy !== SortBy.DATE_DESC) {
        filters.sortBy = sortBy;
      }

      const result = await matchedBlockService.getMatchedBlocks(
        userId,
        currentPage,
        pageSize,
        filters
      );

      if (isListResponse(result)) {
        setBlocks(result.data.items);
        setTotalPages(result.data.pagination.totalPages);
        setTotalItems(result.data.pagination.total);
        
        // Use metadata for accurate type-specific counts
        if (result.data.meta) {
          setBlockCounts({
            all: result.data.pagination.total,
            ethoca: result.data.meta.totalEthoca,
            rdr: result.data.meta.totalRdr,
          });
        }
      } else {
        setBlocks([]);
        setTotalPages(1);
        setTotalItems(0);
      }
    } catch (error) {
      setBlocks([]);
      setTotalPages(1);
      setTotalItems(0);
    } finally {
      setLoading(false);
    }
  }, [currentPage, selectedType, pageSize, userId, searchQuery, appliedFilters, appliedDateRange, sortBy]);

  // Simplified refetch function - only one API call needed
  const refetch = useCallback(async () => {
    await fetchBlocks();
  }, [fetchBlocks]);

  // Initial data fetch - single API call gets everything
  useEffect(() => {
    if (userId) {
      fetchBlocks();
    }
  }, [fetchBlocks]);

  // Page change handler
  const handlePageChange = useCallback((page: number) => {
    if (page < 1 || page > totalPages || page === currentPage) return;
    setCurrentPage(page);
  }, [totalPages, currentPage]);

  // Reset to first page
  const resetToFirstPage = useCallback(() => {
    if (currentPage === 1) return;
    setCurrentPage(1);
  }, [currentPage]);

  // Dialog handlers
  const openViewDetailsDialog = useCallback((block: IMatchedBlock) => {
    setSelectedBlock(block);
    setViewDetailsDialogOpen(true);
  }, []);

  return {
    // Data
    blocks,
    blockCounts,
    
    // Pagination
    currentPage,
    totalPages,
    totalItems,
    pageSize,
    
    // Loading states
    loading,
    
    // Dialog state
    selectedBlock,
    viewDetailsDialogOpen,
    
    // Actions
    handlePageChange,
    resetToFirstPage,
    openViewDetailsDialog,
    setViewDetailsDialogOpen,
    refetch,
  };
}