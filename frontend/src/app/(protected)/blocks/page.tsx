"use client";

import { useState } from "react";
import { BlockType } from "@/services/matched-block.service";
import { BlocksTable, ViewDetailsDialog } from "./_components";
import { useBlocks } from "./_hooks";
import { SearchAndFilters } from "./_components/SearchAndFilters";
import { Veri<PERSON><PERSON><PERSON>, Eth<PERSON>a<PERSON>ogo } from "@/components/ui/logos";
import { Bell } from "lucide-react";

interface Tab {
  id: string;
  label: string;
  icon: any;
  count: number;
  type: BlockType | null;
}

export default function BlocksPage() {
  const [selectedType, setSelectedType] = useState<BlockType | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("date_desc");

  // Filter states
  const [appliedFilters, setAppliedFilters] = useState({
    feedbackStatus: "",
    minAmount: "",
    maxAmount: "",
    currency: "",
    cardBin: "",
    authCode: "",
    descriptor: "",
  });

  const [appliedDateRange, setAppliedDateRange] = useState({
    startDate: "",
    endDate: "",
  });

  // Use unified hook for all blocks functionality
  const {
    blocks,
    blockCounts,
    currentPage,
    totalPages,
    totalItems,
    pageSize,
    loading,
    selectedBlock: viewDetailsSelectedBlock,
    viewDetailsDialogOpen,
    handlePageChange,
    resetToFirstPage,
    openViewDetailsDialog,
    setViewDetailsDialogOpen,
  } = useBlocks({
    selectedType,
    searchQuery,
    appliedFilters,
    appliedDateRange,
    sortBy,
  });

  // Define tabs based on block types
  const tabs: Tab[] = [
    {
      id: "all",
      label: "All",
      icon: Bell,
      count: blockCounts.all,
      type: null,
    },
    {
      id: "ethoca",
      label: "Mastercard",
      icon: EthocaLogo,
      count: blockCounts.ethoca,
      type: BlockType.ETHOCA,
    },
    {
      id: "rdr",
      label: "Visa",
      icon: VerifiLogo,
      count: blockCounts.rdr,
      type: BlockType.RDR,
    },
  ];

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    const tab = tabs.find((t) => t.id === tabId);
    if (tab) {
      setSelectedType(tab.type);
      resetToFirstPage();
    }
  };

  return (
    <div className="min-h-screen bg-black">
      {/* Header */}
      <div className="px-6 mt-6">
        <h1 className="text-4xl font-bold text-white">Alerts</h1>
      </div>

      <div className="px-6">
        {/* Tabs */}
        <div className="my-4 flex gap-2 overflow-x-auto scrollbar-hide border-b border-gray-800">
          {tabs.map(({ id, label, icon: Icon, count }) => (
            <button
              key={id}
              onClick={() => handleTabChange(id)}
              className={`flex items-center gap-2 px-4 py-3 text-base font-medium transition-colors
              ${
                activeTab === id
                  ? "text-white border-b-2 border-green-500"
                  : "text-zinc-300 hover:text-white"
              }`}
            >
              <Icon className="w-4 h-4" />
              {label}
              <span
                className={`px-1.5 py-0.5 rounded-full text-xs ${
                  activeTab === id
                    ? "bg-green-600 text-white"
                    : "bg-green-100 text-green-700"
                }`}
              >
                {count.toLocaleString()}
              </span>
            </button>
          ))}
        </div>

        {/* Search and Filters */}
        <SearchAndFilters
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          onFiltersApply={setAppliedFilters}
          onDateRangeApply={setAppliedDateRange}
          appliedFilters={appliedFilters}
          appliedDateRange={appliedDateRange}
          sortBy={sortBy}
          setSortBy={setSortBy}
        />

        {/* Table */}
        <div className="mt-6">
          <BlocksTable
            loading={loading}
            blocks={blocks}
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            pageSize={pageSize}
            selectedType={selectedType}
            onPageChange={handlePageChange}
            onViewDetails={openViewDetailsDialog}
            searchQuery={searchQuery}
            appliedFilters={appliedFilters}
            appliedDateRange={appliedDateRange}
          />
        </div>
      </div>

      <ViewDetailsDialog
        open={viewDetailsDialogOpen}
        onOpenChange={setViewDetailsDialogOpen}
        selectedBlock={viewDetailsSelectedBlock}
      />
    </div>
  );
}
