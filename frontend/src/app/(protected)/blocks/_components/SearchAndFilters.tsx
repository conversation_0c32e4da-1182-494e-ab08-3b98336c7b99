"use client";

import { useState, useEffect } from "react";
import {
  Search,
  Filter,
  Calendar,
  ChevronDown,
  ArrowUpDown,
  ArrowDownUp,
  ArrowDown01,
  ArrowUp01,
  CalendarArrowDown,
} from "lucide-react";
import { TimeRange } from "../../dashboard/_lib/constants";
import { SortBy } from "@/constants/matched-blocks";

// Shared types
interface FilterState {
  feedbackStatus: string;
  minAmount: string;
  maxAmount: string;
  currency: string;
  cardBin: string;
  authCode: string;
  descriptor: string;
  isOpen: boolean;
}

interface DateRangeState {
  startDate: string;
  endDate: string;
  isOpen: boolean;
  selectedRange: TimeRange.THIRTY_DAYS | TimeRange.YTD | TimeRange.ONE_YEAR | TimeRange.ALL_TIME | TimeRange.CUSTOM;
}

interface SortState {
  isOpen: boolean;
}

// Component interfaces
interface SearchBarProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
}

interface FiltersProps {
  onFiltersApply: (filters: Omit<FilterState, "isOpen">) => void;
  appliedFilters: Omit<FilterState, "isOpen">;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  onCloseOthers: () => void;
}

interface DateRangeProps {
  onDateRangeApply: (dateRange: { startDate: string; endDate: string }) => void;
  appliedDateRange: { startDate: string; endDate: string };
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  onCloseOthers: () => void;
}

interface SortProps {
  sortBy: string;
  setSortBy: (sortBy: string) => void;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  onCloseOthers: () => void;
}

interface SearchAndFiltersProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  onFiltersApply: (filters: Omit<FilterState, "isOpen">) => void;
  onDateRangeApply: (dateRange: { startDate: string; endDate: string }) => void;
  appliedFilters: Omit<FilterState, "isOpen">;
  appliedDateRange: { startDate: string; endDate: string };
  sortBy: string;
  setSortBy: (sortBy: string) => void;
}

// SearchBar Component
function SearchBar({ searchQuery, setSearchQuery }: SearchBarProps) {
  return (
    <div className="flex-1">
      <div className="relative">
        <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search alerts..."
          className="w-full h-10 pl-10 pr-4 bg-transparent border border-gray-800 rounded-lg text-gray-300 focus:outline-none focus:border-green-500 hover:border-green-500 transition-colors"
        />
      </div>
    </div>
  );
}

// Filters Component
function Filters({
  onFiltersApply,
  appliedFilters,
  isOpen,
  setIsOpen,
  onCloseOthers,
}: FiltersProps) {
  const [filters, setFilters] = useState<FilterState>({
    feedbackStatus: appliedFilters.feedbackStatus || "",
    minAmount: appliedFilters.minAmount || "",
    maxAmount: appliedFilters.maxAmount || "",
    currency: appliedFilters.currency || "",
    cardBin: appliedFilters.cardBin || "",
    authCode: appliedFilters.authCode || "",
    descriptor: appliedFilters.descriptor || "",
    isOpen: false,
  });

  // Sync local state with applied filters when they change
  useEffect(() => {
    setFilters({
      feedbackStatus: appliedFilters.feedbackStatus || "",
      minAmount: appliedFilters.minAmount || "",
      maxAmount: appliedFilters.maxAmount || "",
      currency: appliedFilters.currency || "",
      cardBin: appliedFilters.cardBin || "",
      authCode: appliedFilters.authCode || "",
      descriptor: appliedFilters.descriptor || "",
      isOpen: false,
    });
  }, [appliedFilters]);

  const handleToggle = () => {
    onCloseOthers();
    setIsOpen(!isOpen);
  };

  // Validate amount inputs
  const validateAmountRange = (min: string, max: string): boolean => {
    if (!min && !max) return true;
    const minVal = parseFloat(min);
    const maxVal = parseFloat(max);

    if (min && isNaN(minVal)) return false;
    if (max && isNaN(maxVal)) return false;
    if (min && minVal < 0) return false;
    if (max && maxVal < 0) return false;
    if (min && max && minVal > maxVal) return false;

    return true;
  };

  // Check if current filters have any values
  const hasActiveFilters = () => {
    return Object.values(filters).some(
      (value) => typeof value === "string" && value.trim() !== ""
    );
  };

  const renderFilterDropdown = () => {
    if (!isOpen) return null;

    return (
      <div className="absolute mt-2 p-4 bg-zinc-900 rounded-lg border border-gray-800 shadow-lg z-10 w-96 max-h-96 overflow-y-auto">
        <div className="space-y-4">
          {/* Feedback Status */}
          <div>
            <label className="block text-sm text-gray-400 mb-1">
              Feedback Status
            </label>
            <select
              value={filters.feedbackStatus}
              onChange={(e) =>
                setFilters((prev) => ({
                  ...prev,
                  feedbackStatus: e.target.value,
                }))
              }
              className="w-full bg-black border border-gray-800 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:border-green-500"
            >
              <option value="">All Status</option>
              <option value="PENDING">Pending</option>
              <option value="SENT">Sent</option>
              <option value="FAILED">Failed</option>
            </select>
          </div>

          {/* Amount Range */}
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-sm text-gray-400 mb-1">
                Min Amount
              </label>
              <input
                type="number"
                step="0.01"
                value={filters.minAmount}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, minAmount: e.target.value }))
                }
                placeholder="0.00"
                className="w-full bg-black border border-gray-800 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:border-green-500"
              />
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">
                Max Amount
              </label>
              <input
                type="number"
                step="0.01"
                value={filters.maxAmount}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, maxAmount: e.target.value }))
                }
                placeholder="999.99"
                className="w-full bg-black border border-gray-800 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:border-green-500"
              />
            </div>
          </div>

          {/* Currency */}
          <div>
            <label className="block text-sm text-gray-400 mb-1">Currency</label>
            <select
              value={filters.currency}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, currency: e.target.value }))
              }
              className="w-full bg-black border border-gray-800 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:border-green-500"
            >
              <option value="">All Currencies</option>
              <option value="USD">USD</option>
              <option value="EUR">EUR</option>
              <option value="GBP">GBP</option>
              <option value="CAD">CAD</option>
              <option value="AUD">AUD</option>
            </select>
          </div>

          {/* Card BIN */}
          <div>
            <label className="block text-sm text-gray-400 mb-1">Card BIN</label>
            <input
              type="text"
              value={filters.cardBin}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, cardBin: e.target.value }))
              }
              placeholder="424242"
              className="w-full bg-black border border-gray-800 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:border-green-500"
            />
          </div>

          {/* Auth Code */}
          <div>
            <label className="block text-sm text-gray-400 mb-1">
              Auth Code
            </label>
            <input
              type="text"
              value={filters.authCode}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, authCode: e.target.value }))
              }
              placeholder="123456"
              className="w-full bg-black border border-gray-800 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:border-green-500"
            />
          </div>

          {/* Descriptor */}
          <div>
            <label className="block text-sm text-gray-400 mb-1">
              Descriptor
            </label>
            <input
              type="text"
              value={filters.descriptor}
              onChange={(e) =>
                setFilters((prev) => ({ ...prev, descriptor: e.target.value }))
              }
              placeholder="STORE NAME"
              className="w-full bg-black border border-gray-800 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:border-green-500"
            />
          </div>

          {/* Validation Message */}
          {filters.minAmount &&
            filters.maxAmount &&
            !validateAmountRange(filters.minAmount, filters.maxAmount) && (
              <div className="text-red-400 text-xs">
                Min amount must be less than max amount
              </div>
            )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 pt-2 border-t border-gray-800">
            <button
              onClick={() => {
                const clearedFilters = {
                  feedbackStatus: "",
                  minAmount: "",
                  maxAmount: "",
                  currency: "",
                  cardBin: "",
                  authCode: "",
                  descriptor: "",
                  isOpen: false,
                };
                setFilters(clearedFilters);
                onFiltersApply({
                  feedbackStatus: "",
                  minAmount: "",
                  maxAmount: "",
                  currency: "",
                  cardBin: "",
                  authCode: "",
                  descriptor: "",
                });
                setIsOpen(false);
              }}
              className="px-4 py-2 text-gray-300 hover:bg-zinc-800 rounded-lg transition-colors"
              disabled={!hasActiveFilters()}
            >
              Clear
            </button>
            <button
              onClick={() => {
                // Validate before applying
                if (
                  !validateAmountRange(filters.minAmount, filters.maxAmount)
                ) {
                  return; // Don't apply if validation fails
                }

                // Clean and trim filter values before applying
                // Note: Amount conversion to cents (x100) is handled in useBlocks hook
                const cleanFilters = {
                  feedbackStatus: filters.feedbackStatus.trim(),
                  minAmount: filters.minAmount.trim(), // Keep as dollar amount for display
                  maxAmount: filters.maxAmount.trim(), // Keep as dollar amount for display
                  currency: filters.currency.trim(),
                  cardBin: filters.cardBin.trim(),
                  authCode: filters.authCode.trim(),
                  descriptor: filters.descriptor.trim(),
                };

                onFiltersApply(cleanFilters);
                setIsOpen(false);
              }}
              className={`px-4 py-2 rounded-lg transition-colors ${
                !validateAmountRange(filters.minAmount, filters.maxAmount)
                  ? "bg-gray-600 text-gray-400 cursor-not-allowed"
                  : "bg-green-600 text-white hover:bg-green-700"
              }`}
              disabled={
                !validateAmountRange(filters.minAmount, filters.maxAmount)
              }
            >
              Apply
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Check if any applied filters are active
  const hasAppliedFilters = Object.values(appliedFilters).some(
    (value) => typeof value === "string" && value.trim() !== ""
  );

  return (
    <div className="relative flex-1 sm:flex-none">
      <button
        onClick={handleToggle}
        className={`w-full sm:w-auto h-10 px-4 bg-transparent border ${
          isOpen
            ? "border-green-500"
            : hasAppliedFilters
            ? "border-green-400"
            : "border-gray-800"
        } rounded-lg ${
          hasAppliedFilters ? "text-green-400" : "text-gray-300"
        } flex items-center gap-2 hover:border-green-500 transition-colors justify-center sm:justify-start focus:outline-none focus:border-green-500`}
      >
        <Filter
          size={18}
          className={hasAppliedFilters ? "text-green-400" : ""}
        />
        <span>Filters</span>
        {hasAppliedFilters && (
          <span className="bg-green-600 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[16px] text-center">
            {
              Object.values(appliedFilters).filter(
                (value) => typeof value === "string" && value.trim() !== ""
              ).length
            }
          </span>
        )}
        <ChevronDown size={16} />
      </button>
      {renderFilterDropdown()}
    </div>
  );
}

// DateRange Component
function DateRangeFilter({
  onDateRangeApply,
  appliedDateRange,
  isOpen,
  setIsOpen,
  onCloseOthers,
}: DateRangeProps) {
  const [dateRange, setDateRange] = useState<DateRangeState>({
    startDate: appliedDateRange.startDate || "",
    endDate: appliedDateRange.endDate || "",
    isOpen: false,
    selectedRange: TimeRange.ALL_TIME,
  });

  // Sync local state with applied date range when it changes
  useEffect(() => {
    setDateRange((prev) => ({
      ...prev,
      startDate: appliedDateRange.startDate || "",
      endDate: appliedDateRange.endDate || "",
    }));

    // Determine the selected range based on applied dates
    if (!appliedDateRange.startDate && !appliedDateRange.endDate) {
      setDateRange((prev) => ({ ...prev, selectedRange: TimeRange.ALL_TIME }));
    } else if (appliedDateRange.startDate || appliedDateRange.endDate) {
      // Check if it matches current patterns, otherwise set to custom
      const today = new Date();
      const thirtyDaysAgo = new Date(today);
      thirtyDaysAgo.setDate(today.getDate() - 30);
      const thirtyDaysStart = thirtyDaysAgo.toISOString().split("T")[0];
      
      const oneYearAgo = new Date(today);
      oneYearAgo.setFullYear(today.getFullYear() - 1);
      const oneYearStart = oneYearAgo.toISOString().split("T")[0];
      
      const thisYearStart = new Date(today.getFullYear(), 0, 1)
        .toISOString()
        .split("T")[0];

      if (appliedDateRange.startDate === thirtyDaysStart) {
        setDateRange((prev) => ({ ...prev, selectedRange: TimeRange.THIRTY_DAYS }));
      } else if (appliedDateRange.startDate === thisYearStart) {
        setDateRange((prev) => ({ ...prev, selectedRange: TimeRange.YTD }));
      } else if (appliedDateRange.startDate === oneYearStart) {
        setDateRange((prev) => ({ ...prev, selectedRange: TimeRange.ONE_YEAR }));
      } else {
        setDateRange((prev) => ({ ...prev, selectedRange: TimeRange.CUSTOM }));
      }
    }
  }, [appliedDateRange]);

  const handleToggle = () => {
    onCloseOthers();
    setIsOpen(!isOpen);
  };

  // Validate date range
  const validateDateRange = (start: string, end: string): boolean => {
    if (!start || !end) return true; // Allow partial dates
    const startDate = new Date(start);
    const endDate = new Date(end);
    const today = new Date();

    // Check if dates are valid
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) return false;

    // Start date should not be after end date
    if (startDate > endDate) return false;

    // Future dates are not allowed for end date
    if (endDate > today) return false;

    return true;
  };

  // Helper function to get display text based on selected range and applied dates
  const getDateRangeDisplayText = (): string => {
    // If we have applied dates, show them
    if (appliedDateRange.startDate || appliedDateRange.endDate) {
      if (appliedDateRange.startDate && appliedDateRange.endDate) {
        const startDateObj = new Date(appliedDateRange.startDate);
        const endDateObj = new Date(appliedDateRange.endDate);
        return `${startDateObj.toLocaleDateString()} - ${endDateObj.toLocaleDateString()}`;
      } else if (appliedDateRange.startDate) {
        return `From ${new Date(
          appliedDateRange.startDate
        ).toLocaleDateString()}`;
      } else if (appliedDateRange.endDate) {
        return `Until ${new Date(
          appliedDateRange.endDate
        ).toLocaleDateString()}`;
      }
    }

    // Fallback to selected range display
    const today = new Date();
    switch (dateRange.selectedRange) {
      case TimeRange.THIRTY_DAYS:
        return "Last 30 Days";
      case TimeRange.YTD:
        return `YTD ${today.getFullYear()}`;
      case TimeRange.ONE_YEAR:
        return "Last 12 Months";
      case TimeRange.ALL_TIME:
        return "All Time";
      case TimeRange.CUSTOM:
        return "Custom Range";
      default:
        return "Date Range";
    }
  };

  // Handle time range selection
  const handleRangeSelect = (
    range: TimeRange.THIRTY_DAYS | TimeRange.YTD | TimeRange.ONE_YEAR | TimeRange.ALL_TIME | TimeRange.CUSTOM
  ) => {
    setDateRange((prev) => ({ ...prev, selectedRange: range }));

    if (range === TimeRange.CUSTOM) {
      return; // Don't auto-apply for custom, let user select dates
    }

    const today = new Date();
    let startDate = "";
    let endDate = "";

    switch (range) {
      case TimeRange.THIRTY_DAYS:
        const thirtyDaysAgo = new Date(today);
        thirtyDaysAgo.setDate(today.getDate() - 30);
        startDate = thirtyDaysAgo.toISOString().split("T")[0];
        endDate = today.toISOString().split("T")[0];
        break;
      case TimeRange.YTD:
        // Year to Date: January 1st of current year to today
        const thisYearStart = new Date(today.getFullYear(), 0, 1);
        startDate = thisYearStart.toISOString().split("T")[0];
        endDate = today.toISOString().split("T")[0];
        break;
      case TimeRange.ONE_YEAR:
        // Last 12 Months: Rolling 12 months from today
        const oneYearAgo = new Date(today);
        oneYearAgo.setFullYear(today.getFullYear() - 1);
        startDate = oneYearAgo.toISOString().split("T")[0];
        endDate = today.toISOString().split("T")[0];
        break;
      case TimeRange.ALL_TIME:
        // For all time, send empty dates to let backend fetch all data
        startDate = "";
        endDate = "";
        break;
    }

    setDateRange((prev) => ({ ...prev, startDate, endDate }));
    // Send the date range to parent component which will sync with useBlocks hook
    onDateRangeApply({ startDate, endDate });
    setIsOpen(false);
  };

  const renderDateRangePicker = () => {
    if (!isOpen) return null;

    const timeRangeOptions = [
      { value: TimeRange.THIRTY_DAYS, label: "Last 30 Days" },
      { value: TimeRange.YTD, label: "Year to Date" },
      { value: TimeRange.ONE_YEAR, label: "Last 12 Months" },
      { value: TimeRange.ALL_TIME, label: "All Time" },
      { value: TimeRange.CUSTOM, label: "Custom" },
    ];

    return (
      <div className="absolute right-0 mt-2 p-4 bg-zinc-900 rounded-lg border border-gray-800 shadow-lg z-10 w-80">
        <div className="space-y-4">
          {/* Time Range Options */}
          <div className="grid grid-cols-2 gap-2">
            {timeRangeOptions.map((option) => (
              <button
                key={option.value}
                onClick={() =>
                  handleRangeSelect(
                    option.value as TimeRange.THIRTY_DAYS | TimeRange.YTD | TimeRange.ONE_YEAR | TimeRange.ALL_TIME | TimeRange.CUSTOM
                  )
                }
                className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                  dateRange.selectedRange === option.value
                    ? "bg-green-600 text-white"
                    : "text-gray-300 bg-black hover:bg-zinc-800"
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>

          {/* Custom Date Range - Only show when Custom is selected */}
          {dateRange.selectedRange === TimeRange.CUSTOM && (
            <>
              <div className="border-t border-gray-800"></div>
              <div>
                <label className="block text-sm text-gray-400 mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  value={dateRange.startDate}
                  max={new Date().toISOString().split("T")[0]} // Don't allow future dates
                  onChange={(e) =>
                    setDateRange((prev) => ({
                      ...prev,
                      startDate: e.target.value,
                    }))
                  }
                  className="w-full bg-black border border-gray-800 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:border-green-500 [color-scheme:dark]"
                  style={{ colorScheme: "dark" }}
                />
              </div>

              <div>
                <label className="block text-sm text-gray-400 mb-1">
                  End Date
                </label>
                <input
                  type="date"
                  value={dateRange.endDate}
                  min={dateRange.startDate || undefined} // End date should be after start date
                  max={new Date().toISOString().split("T")[0]} // Don't allow future dates
                  onChange={(e) =>
                    setDateRange((prev) => ({
                      ...prev,
                      endDate: e.target.value,
                    }))
                  }
                  className="w-full bg-black border border-gray-800 rounded-lg px-3 py-2 text-gray-300 focus:outline-none focus:border-green-500 [color-scheme:dark]"
                  style={{ colorScheme: "dark" }}
                />
              </div>

              {/* Validation Message */}
              {dateRange.startDate &&
                dateRange.endDate &&
                !validateDateRange(dateRange.startDate, dateRange.endDate) && (
                  <div className="text-red-400 text-xs">
                    {new Date(dateRange.startDate) > new Date(dateRange.endDate)
                      ? "Start date must be before end date"
                      : "Invalid date range"}
                  </div>
                )}

              {/* Action Buttons for Custom Range */}
              <div className="flex justify-end gap-2 pt-2 border-t border-gray-800">
                <button
                  onClick={() => {
                    const clearedDateRange = { startDate: "", endDate: "" };
                    setDateRange((prev) => ({ ...prev, ...clearedDateRange }));
                    onDateRangeApply(clearedDateRange);
                    setIsOpen(false);
                  }}
                  className="px-4 py-2 text-gray-300 hover:bg-zinc-800 rounded-lg transition-colors"
                >
                  Clear
                </button>
                <button
                  onClick={() => {
                    // Validate before applying
                    if (
                      !validateDateRange(dateRange.startDate, dateRange.endDate)
                    ) {
                      return; // Don't apply if validation fails
                    }

                    onDateRangeApply({
                      startDate: dateRange.startDate.trim(),
                      endDate: dateRange.endDate.trim(),
                    });
                    setIsOpen(false);
                  }}
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    !validateDateRange(dateRange.startDate, dateRange.endDate)
                      ? "bg-gray-600 text-gray-400 cursor-not-allowed"
                      : "bg-green-600 text-white hover:bg-green-700"
                  }`}
                  disabled={
                    !validateDateRange(dateRange.startDate, dateRange.endDate)
                  }
                >
                  Apply
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    );
  };

  // Check if any applied date range is active
  const hasAppliedDateRange =
    appliedDateRange.startDate || appliedDateRange.endDate;

  return (
    <div className="relative flex-1 sm:flex-none">
      <button
        onClick={handleToggle}
        className={`w-full sm:w-auto h-10 px-4 bg-transparent border ${
          isOpen
            ? "border-green-500"
            : hasAppliedDateRange
            ? "border-green-400"
            : "border-gray-800"
        } rounded-lg ${
          hasAppliedDateRange ? "text-green-400" : "text-gray-300"
        } flex items-center gap-2 hover:border-green-500 transition-colors justify-center sm:justify-start focus:outline-none focus:border-green-500`}
      >
        <Calendar
          size={18}
          className={hasAppliedDateRange ? "text-green-400" : ""}
        />
        <span>{getDateRangeDisplayText()}</span>
        {hasAppliedDateRange && (
          <span className="bg-green-600 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[16px] text-center">
            1
          </span>
        )}
        <ChevronDown size={16} />
      </button>
      {renderDateRangePicker()}
    </div>
  );
}

// Sort Component
function Sort({
  sortBy,
  setSortBy,
  isOpen,
  setIsOpen,
  onCloseOthers,
}: SortProps) {
  // Define simple sort options - only date and amount with clear text
  const sortOptions = [
    {
      value: SortBy.DATE_DESC,
      label: "Newest First",
      icon: CalendarArrowDown,
      category: "date",
    },
    {
      value: SortBy.DATE_ASC,
      label: "Oldest First",
      icon: CalendarArrowDown,
      category: "date",
    },
    {
      value: SortBy.AMOUNT_DESC,
      label: "High Amount",
      icon: ArrowDown01,
      category: "amount",
    },
    {
      value: SortBy.AMOUNT_ASC,
      label: "Low Amount",
      icon: ArrowUp01,
      category: "amount",
    },
  ];

  const handleToggle = () => {
    onCloseOthers();
    setIsOpen(!isOpen);
  };

  const handleSortSelect = (sortValue: string) => {
    setSortBy(sortValue);
    setIsOpen(false);
  };

  // Get current sort option details
  const getCurrentSortOption = () => {
    return (
      sortOptions.find((option) => option.value === sortBy) || {
        label: "Sort",
        icon: ArrowUpDown,
        category: "default",
      }
    );
  };

  // Check if current sort is non-default
  const hasCustomSort = sortBy && sortBy !== SortBy.DATE_DESC;

  const renderSortDropdown = () => {
    if (!isOpen) return null;

    return (
      <div className="absolute right-0 mt-2 p-2 bg-zinc-900 rounded-lg border border-gray-800 shadow-lg z-10 w-48">
        <div className="space-y-1">
          {/* Date Sort Options */}
          {sortOptions
            .filter((opt) => opt.category === "date")
            .map((option) => {
              const IconComponent = option.icon;
              return (
                <button
                  key={option.value}
                  onClick={() => handleSortSelect(option.value)}
                  className={`w-full text-left px-3 py-2 text-sm rounded-lg transition-colors flex items-center justify-between ${
                    sortBy === option.value
                      ? "bg-green-600 text-white"
                      : "text-gray-300 hover:bg-zinc-800 hover:text-white"
                  }`}
                >
                  <span>{option.label}</span>
                  <IconComponent size={16} />
                </button>
              );
            })}

          {/* Divider */}
          <div className="border-t border-gray-800 my-2"></div>

          {/* Amount Sort Options */}
          {sortOptions
            .filter((opt) => opt.category === "amount")
            .map((option) => {
              const IconComponent = option.icon;
              return (
                <button
                  key={option.value}
                  onClick={() => handleSortSelect(option.value)}
                  className={`w-full text-left px-3 py-2 text-sm rounded-lg transition-colors flex items-center justify-between ${
                    sortBy === option.value
                      ? "bg-green-600 text-white"
                      : "text-gray-300 hover:bg-zinc-800 hover:text-white"
                  }`}
                >
                  <span>{option.label}</span>
                  <IconComponent size={16} />
                </button>
              );
            })}
        </div>
      </div>
    );
  };

  const currentSort = getCurrentSortOption();

  return (
    <div className="relative flex-1 sm:flex-none">
      <button
        onClick={handleToggle}
        className={`w-full sm:w-auto h-10 px-4 bg-transparent border ${
          isOpen
            ? "border-green-500"
            : hasCustomSort
            ? "border-green-400"
            : "border-gray-800"
        } rounded-lg ${
          hasCustomSort ? "text-green-400" : "text-gray-300"
        } flex items-center gap-2 hover:border-green-500 transition-colors justify-center sm:justify-start focus:outline-none focus:border-green-500`}
      >
        <span className="flex items-center gap-2">
          <currentSort.icon
            size={16}
            className={hasCustomSort ? "text-green-400" : ""}
          />
          {currentSort.label}
        </span>
        {hasCustomSort && (
          <span className="bg-green-600 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[16px] text-center">
            1
          </span>
        )}
        <ChevronDown size={16} />
      </button>
      {renderSortDropdown()}
    </div>
  );
}

export function SearchAndFilters({
  searchQuery,
  setSearchQuery,
  onFiltersApply,
  onDateRangeApply,
  appliedFilters,
  appliedDateRange,
  sortBy,
  setSortBy,
}: SearchAndFiltersProps) {
  // State for dropdown open/close coordination
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [dateRangeOpen, setDateRangeOpen] = useState(false);
  const [sortOpen, setSortOpen] = useState(false);

  // Close all other dropdowns when one opens
  const closeOthersExcept = (except: "filters" | "dateRange" | "sort") => {
    if (except !== "filters") setFiltersOpen(false);
    if (except !== "dateRange") setDateRangeOpen(false);
    if (except !== "sort") setSortOpen(false);
  };

  return (
    <div className="py-2">
      <div className="flex flex-col sm:flex-row gap-2">
        <SearchBar searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
        <div className="flex flex-wrap gap-2">
          <Filters
            onFiltersApply={onFiltersApply}
            appliedFilters={appliedFilters}
            isOpen={filtersOpen}
            setIsOpen={setFiltersOpen}
            onCloseOthers={() => closeOthersExcept("filters")}
          />
          <DateRangeFilter
            onDateRangeApply={onDateRangeApply}
            appliedDateRange={appliedDateRange}
            isOpen={dateRangeOpen}
            setIsOpen={setDateRangeOpen}
            onCloseOthers={() => closeOthersExcept("dateRange")}
          />
          <Sort
            sortBy={sortBy}
            setSortBy={setSortBy}
            isOpen={sortOpen}
            setIsOpen={setSortOpen}
            onCloseOthers={() => closeOthersExcept("sort")}
          />
        </div>
      </div>
    </div>
  );
}
