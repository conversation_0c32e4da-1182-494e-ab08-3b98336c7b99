"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Eye,
} from "lucide-react";
import {
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  BlockType,
  FeedbackStatus,
  IMatchedBlock,
} from "@/services/matched-block.service";
import { formatAmountForDisplay } from "@/utils/amount";
import { TablePagination } from "@/components/ui/table-pagination";
import { useMemo } from "react";

interface BlocksTableProps {
  loading: boolean;
  blocks: IMatchedBlock[];
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
  selectedType: BlockType | null;
  onPageChange: (page: number) => void;
  onViewDetails: (block: IMatchedBlock) => void;
  isLoading?: boolean;
  searchQuery?: string;
  appliedFilters?: { 
    feedbackStatus: string;
    minAmount: string;
    maxAmount: string;
    currency: string;
    cardBin: string;
    authCode: string;
    descriptor: string;
  };
  appliedDateRange?: { startDate: string; endDate: string };
}

export function BlocksTable({
  loading,
  blocks,
  currentPage,
  totalPages,
  totalItems,
  pageSize,
  selectedType,
  onPageChange,
  onViewDetails,
  isLoading = false,
  searchQuery = "",
  appliedFilters = { 
    feedbackStatus: "",
    minAmount: "",
    maxAmount: "",
    currency: "",
    cardBin: "",
    authCode: "",
    descriptor: "",
  },
  appliedDateRange = { startDate: "", endDate: "" },
}: BlocksTableProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusBadge = (status: FeedbackStatus | undefined) => {
    const displayStatus = status || FeedbackStatus.PENDING;
    
    switch (displayStatus) {
      case FeedbackStatus.PENDING:
        return <Badge className="bg-yellow-100 text-yellow-900 hover:bg-yellow-500">Pending</Badge>;
      case FeedbackStatus.SENT:
        return <Badge className="bg-green-100 text-green-900 hover:bg-green-500">Sent</Badge>;
      case FeedbackStatus.FAILED:
        return <Badge className="bg-red-100 text-red-900 hover:bg-red-500">Failed</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-900 hover:bg-gray-500">Unknown</Badge>;
    }
  };

  // Filter blocks based on search and filters
  const filteredBlocks = useMemo(() => {
    let filtered = blocks;

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(block =>
        block.id.toLowerCase().includes(query) ||
        block.descriptor?.toLowerCase().includes(query) ||
        block.type.toLowerCase().includes(query)
      );
    }


    // Apply date range filter
    if (appliedDateRange.startDate) {
      filtered = filtered.filter(block => 
        new Date(block.alertTime) >= new Date(appliedDateRange.startDate)
      );
    }
    if (appliedDateRange.endDate) {
      filtered = filtered.filter(block => 
        new Date(block.alertTime) <= new Date(appliedDateRange.endDate)
      );
    }

    return filtered;
  }, [blocks, searchQuery, appliedFilters, appliedDateRange]);



  return (
    <div className="space-y-6">
      <div className="bg-black rounded-lg border border-gray-800 overflow-x-auto">
        <div className="min-w-full">
          {/* Mobile Card View */}
          <div className="block sm:hidden">
            {filteredBlocks.length === 0 && !loading ? (
              <div className="flex items-center justify-center py-32">
                <div className="text-center space-y-3">
                  <div className="w-12 h-12 bg-gray-100 rounded-full mx-auto flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </div>
                  <h3 className="font-medium">No alerts found</h3>
                  <p className="text-sm text-muted-foreground">
                    {selectedType
                      ? `No ${selectedType} alerts found for the selected time period.`
                      : "No alerts found for the selected time period."}
                  </p>
                </div>
              </div>
            ) : filteredBlocks.map((block) => (
              <div
                key={block.id}
                className="p-4 border-b border-gray-800 cursor-pointer hover:bg-zinc-900"
                onClick={() => onViewDetails(block)}
              >
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <div className="text-green-400 hover:text-green-300">
                      {block.id}
                    </div>
                    <div className="text-sm text-gray-400">
                      {block.type}
                    </div>
                  </div>
                  <div>{getStatusBadge(block.feedbackStatus)}</div>
                </div>

                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-gray-400">Amount:</span>
                      <span className="text-gray-300 ml-2">{formatAmountForDisplay(block.amount)}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Currency:</span>
                      <span className="text-gray-300 ml-2">{block.currency}</span>
                    </div>
                  </div>
                  
                  <div className="text-sm">
                    <span className="text-gray-400">Date:</span>
                    <span className="text-gray-300 ml-2">{formatDate(block.alertTime)}</span>
                  </div>

                  <div className="text-sm">
                    <span className="text-gray-400">Descriptor:</span>
                    <div className="text-gray-300 mt-1">{block.descriptor}</div>
                  </div>

                  {block.cardBin && (
                    <div className="text-sm">
                      <span className="text-gray-400">Card BIN:</span>
                      <span className="text-gray-300 ml-2">{block.cardBin}</span>
                    </div>
                  )}

                  {block.type === BlockType.RDR && "caid" in block && block.caid && (
                    <div className="text-sm">
                      <span className="text-gray-400">CAID:</span>
                      <span className="text-gray-300 ml-2">{block.caid}</span>
                    </div>
                  )}

                  <div className="flex justify-end mt-4" onClick={(e) => e.stopPropagation()}>
                    <Button
                      variant="outline"
                      size="sm"
                      className="gap-1 bg-green-500 text-white hover:bg-green-600"
                      onClick={(e) => {
                        e.stopPropagation();
                        onViewDetails(block);
                      }}
                    >
                      <Eye className="h-4 w-4" />
                      <span>View Details</span>
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Desktop Table View */}
          <table className="hidden sm:table w-full min-w-[1200px]">
            <TableHeader>
              <TableRow className="border-b border-gray-800 bg-gray-950">
                <TableHead className="border-r border-gray-800 px-4 py-3 text-left text-sm font-medium text-gray-400">Alert ID</TableHead>
                <TableHead className="border-r border-gray-800 px-4 py-3 text-center text-sm font-medium text-gray-400">Type</TableHead>
                <TableHead className="border-r border-gray-800 px-4 py-3 text-left text-sm font-medium text-gray-400">Date</TableHead>
                <TableHead className="border-r border-gray-800 px-4 py-3 text-right text-sm font-medium text-gray-400">Amount</TableHead>
                <TableHead className="border-r border-gray-800 px-4 py-3 text-center text-sm font-medium text-gray-400">Currency</TableHead>
                <TableHead className="border-r border-gray-800 px-4 py-3 text-center text-sm font-medium text-gray-400">Status</TableHead>
                <TableHead className="border-r border-gray-800 px-4 py-3 text-left text-sm font-medium text-gray-400">Descriptor</TableHead>
                <TableHead className="border-r border-gray-800 px-4 py-3 text-center text-sm font-medium text-gray-400">Card BIN</TableHead>
                <TableHead className="border-r border-gray-800 px-4 py-3 text-center text-sm font-medium text-gray-400">CAID</TableHead>
                <TableHead className="px-4 py-3 text-center text-sm font-medium text-gray-400">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="divide-y divide-gray-800">
              {loading ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-16">
                    <div className="flex flex-col items-center justify-center space-y-3">
                      <div className="w-12 h-12 border-4 border-gray-200 border-t-green-500 rounded-full animate-spin"></div>
                      <p className="text-sm text-muted-foreground">Loading alerts...</p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredBlocks.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} className="text-center py-16">
                    <div className="flex flex-col items-center justify-center space-y-3">
                      <div className="w-12 h-12 bg-gray-100 rounded-full mx-auto flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-6 w-6 text-gray-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </div>
                      <h3 className="font-medium">No alerts found</h3>
                      <p className="text-sm text-muted-foreground">
                        {selectedType
                          ? `No ${selectedType} alerts found for the selected time period.`
                          : "No alerts found for the selected time period."}
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredBlocks.map((block) => (
                <TableRow
                  key={block.id}
                  className="hover:bg-zinc-700"
                >
                  <TableCell className="border-r border-gray-800 px-4 py-4">
                    <span 
                      className="text-green-400 text-sm cursor-pointer hover:text-green-300 hover:underline underline"
                      onClick={() => onViewDetails(block)}
                    >
                      {block.id}
                    </span>
                  </TableCell>
                  <TableCell className="border-r border-gray-800 px-4 py-4 text-center">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                      ${block.type === BlockType.ETHOCA ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}`}>
                      {block.type}
                    </span>
                  </TableCell>
                  <TableCell className="border-r border-gray-800 px-4 py-4 text-sm text-gray-300 text-nowrap">
                    {formatDate(block.alertTime)}
                  </TableCell>
                  <TableCell className="border-r border-gray-800 px-4 py-4">
                    <div className="text-right text-sm text-green-500">
                      {formatAmountForDisplay(block.amount)}
                    </div>
                  </TableCell>
                  <TableCell className="border-r border-gray-800 px-4 py-4 text-sm text-center text-gray-300">
                    {block.currency}
                  </TableCell>
                  <TableCell className="border-r border-gray-800 px-4 py-4 text-center">
                    {getStatusBadge(block.feedbackStatus)}
                  </TableCell>
                  <TableCell className="border-r border-gray-800 px-4 py-4 text-sm text-gray-300">
                    {block.descriptor}
                  </TableCell>
                  <TableCell className="border-r border-gray-800 px-4 py-4 text-sm text-gray-300 text-center">
                    {block.cardBin || "-"}
                  </TableCell>
                  <TableCell className="border-r border-gray-800 px-4 py-4 text-sm text-gray-300 text-center">
                    {block.type === BlockType.RDR && "caid" in block
                      ? block.caid || "-"
                      : "-"}
                  </TableCell>
                  <TableCell className="px-4 py-4 text-center">
                    <div className="flex justify-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="gap-1 bg-green-500 text-white hover:bg-green-600"
                        onClick={(e) => {
                          e.stopPropagation();
                          onViewDetails(block);
                        }}
                      >
                        <Eye className="h-4 w-4" />
                        <span>View Details</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </table>
        </div>
      </div>

      <TablePagination
        currentPage={loading ? 1 : currentPage}
        totalPages={loading ? 1 : totalPages}
        totalItems={loading ? 0 : totalItems}
        pageSize={pageSize}
        onPageChange={onPageChange}
        isLoading={isLoading || loading}
      />
    </div>
  );
}