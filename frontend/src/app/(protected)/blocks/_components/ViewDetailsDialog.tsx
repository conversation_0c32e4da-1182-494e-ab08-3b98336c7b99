"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Ta<PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON>ist,
  Ta<PERSON>Trigger,
} from "@/components/system/tabs";
import {
  BlockType,
  IMatchedBlock,
  IEthocaMatchedBlock,
  IRdrMatchedBlock,
} from "@/services/matched-block.service";
import { formatAmountForDisplay } from "@/utils/amount";

interface ViewDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedBlock: IMatchedBlock | null;
  linkedStoreId?: string;
}

export function ViewDetailsDialog({
  open,
  onOpenChange,
  selectedBlock,
  linkedStoreId,
}: ViewDetailsDialogProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Matched Block Details</DialogTitle>
          <DialogDescription>
            Detailed information for matched block {selectedBlock?.id}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4 overflow-y-auto">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid grid-cols-2 mb-4 w-full">
              <TabsTrigger value="basic" className="w-full cursor-pointer">
                Basic Information
              </TabsTrigger>
              <TabsTrigger
                value="specific"
                disabled={!selectedBlock?.type}
                className="w-full cursor-pointer"
              >
                Type-Specific Fields
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Block ID</Label>
                  <p className="text-sm font-medium">{selectedBlock?.id}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">User ID</Label>
                  <p className="text-sm font-medium">{selectedBlock?.userId}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Type</Label>
                  <p className="text-sm font-medium">{selectedBlock?.type}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Alert Time</Label>
                  <p className="text-sm font-medium">
                    {selectedBlock?.alertTime &&
                      formatDate(selectedBlock.alertTime)}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Amount</Label>
                  <p className="text-sm font-medium">{selectedBlock?.amount ? formatAmountForDisplay(selectedBlock.amount) : "-"}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Currency</Label>
                  <p className="text-sm font-medium">{selectedBlock?.currency}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Descriptor</Label>
                  <p className="text-sm font-medium">{selectedBlock?.descriptor}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Card BIN</Label>
                  <p className="text-sm font-medium">{selectedBlock?.cardBin || "-"}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Auth Code</Label>
                  <p className="text-sm font-medium">{selectedBlock?.authCode || "-"}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Card Number</Label>
                  <p className="text-sm font-medium">{selectedBlock?.cardNumber || "-"}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Chargeback Code</Label>
                  <p className="text-sm font-medium">
                    {selectedBlock?.chargebackCode || "-"}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Dispute Amount</Label>
                  <p className="text-sm font-medium">
                    {selectedBlock?.disputeAmount ? formatAmountForDisplay(selectedBlock.disputeAmount) : "-"}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Dispute Currency</Label>
                  <p className="text-sm font-medium">
                    {selectedBlock?.disputeCurrency || "-"}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Transaction Time</Label>
                  <p className="text-sm font-medium">
                    {selectedBlock?.transactionTime ? formatDate(selectedBlock.transactionTime) : "-"}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Provider</Label>
                  <p className="text-sm font-medium">
                    {selectedBlock?.provider || "-"}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Feedback Status</Label>
                  <p className="text-sm font-medium">
                    {selectedBlock?.feedbackStatus || "-"}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Created At</Label>
                  <p className="text-sm font-medium">
                    {selectedBlock?.createdAt
                      ? formatDate(selectedBlock.createdAt.toString())
                      : "-"}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Updated At</Label>
                  <p className="text-sm font-medium">
                    {selectedBlock?.updatedAt
                      ? formatDate(selectedBlock.updatedAt.toString())
                      : "-"}
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="specific">
              {selectedBlock?.type === BlockType.ETHOCA && (
                <div className="space-y-4">
                  <h3 className="font-medium">ETHOCA Specific Fields</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Age</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IEthocaMatchedBlock).age}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Alert Source</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IEthocaMatchedBlock).alertSource || "-"}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">ARN</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IEthocaMatchedBlock).arn || "-"}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Issuer</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IEthocaMatchedBlock).issuer || "-"}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Initiated By</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IEthocaMatchedBlock).initiatedBy || "-"}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Liability</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IEthocaMatchedBlock).liability || "-"}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Merchant Category Code</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IEthocaMatchedBlock).merchantCategoryCode ||
                          "-"}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Transaction ID</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IEthocaMatchedBlock).transactionId || "-"}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Transaction Type</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IEthocaMatchedBlock).transactionType || "-"}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {selectedBlock?.type === BlockType.RDR && (
                <div className="space-y-4">
                  <h3 className="font-medium">RDR Specific Fields</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Age</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IRdrMatchedBlock).age || "-"}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Acquirer BIN</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IRdrMatchedBlock).acquirerBin || "-"}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Acquirer Reference Number</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IRdrMatchedBlock).acquirerReferenceNumber ||
                          "-"}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Alert Status</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IRdrMatchedBlock).alertStatus || "-"}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">CAID</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IRdrMatchedBlock).caid || "-"}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Descriptor Contact</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IRdrMatchedBlock).descriptorContact || "-"}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Rule Name</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IRdrMatchedBlock).ruleName || "-"}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-muted-foreground">Rule Type</Label>
                      <p className="text-sm font-medium">
                        {(selectedBlock as IRdrMatchedBlock).ruleType || "-"}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

          </Tabs>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}