"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Mail, Phone, MapPin, MessageCircle, Clock } from "lucide-react";

const contactInfo = [
  {
    icon: Mail,
    title: "Email",
    value: "<EMAIL>",
    description: "Send us an email anytime"
  },
  {
    icon: Phone,
    title: "Phone",
    value: "0376216631",
    description: "Mon-Fri from 8 AM to 5 PM"
  },
  {
    icon: MapPin,
    title: "Office",
    value: "Peninsula Shopping Complex, 179804, Singapore",
    description: "Come say hello at our office"
  },
  {
    icon: MessageCircle,
    title: "Live Chat",
    value: "Available 24/7",
    description: "Get instant support"
  }
];

const benefits = [
  {
    icon: Clock,
    title: "Quick Response",
    description: "We respond to all inquiries within 24 hours"
  },
  {
    icon: Phone,
    title: "Expert Support",
    description: "Our team of chargeback experts is here to help"
  },
  {
    icon: MessageCircle,
    title: "Multiple Channels",
    description: "Reach us via email, phone, or live chat"
  }
];


export default function ContactComponent() {

  return (
    <>
      {/* Hero Section */}
      <section className="py-16 px-4 md:px-6 lg:py-24 bg-gradient-to-br from-background via-background to-muted/30">
        <div className="container mx-auto text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            Get in Touch
          </h1>
          <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            Have questions about our chargeback prevention services? We&apos;re here to help you protect your business.
          </p>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-16 px-4 md:px-6">
        <div className="container mx-auto">
          <div className="max-w-5xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Contact Information</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Choose the best way to reach us. We&apos;re available through multiple channels.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
              {contactInfo.map((info, index) => {
                const Icon = info.icon;
                return (
                  <Card key={index} className="p-6 hover:shadow-lg transition-all duration-300 border-b-primary/20 hover:border-b-primary">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0">
                        <Icon className="w-6 h-6 text-primary" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-lg mb-1">{info.title}</h3>
                        <p className="text-primary font-medium mb-1">{info.value}</p>
                        <p className="text-sm text-muted-foreground">{info.description}</p>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>

            {/* Business Hours */}
            <div className="mx-auto">
              <Card className="p-8 bg-gradient-to-br from-muted/30 to-muted/10">
                <div className="text-center mb-6">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Clock className="w-8 h-8 text-primary" />
                  </div>
                  <h3 className="font-semibold text-xl mb-2">Business Hours</h3>
                  <p className="text-sm text-muted-foreground">We&apos;re here when you need us</p>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center py-2 border-b border-border/50">
                    <span className="font-medium">Monday - Friday</span>
                    <span className="text-primary font-semibold">8:00 AM - 6:00 PM PST</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-border/50">
                    <span className="font-medium">Saturday</span>
                    <span className="text-primary font-semibold">9:00 AM - 2:00 PM PST</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-border/50">
                    <span className="font-medium">Sunday</span>
                    <span className="text-muted-foreground">Closed</span>
                  </div>
                  <div className="mt-6 pt-4 border-primary/20 bg-primary/5 rounded-lg p-4">
                    <p className="text-center text-sm font-medium text-primary">
                      🚨 Emergency support available 24/7 for existing customers
                    </p>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 px-4 md:px-6 bg-gradient-to-br from-muted/30 via-muted/20 to-background">
        <div className="container mx-auto">
          <div className="max-w-5xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                Why Choose QuantChargeback?
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                We&apos;re committed to providing exceptional support and service to help protect your business.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => {
                const Icon = benefit.icon;
                return (
                  <Card key={index} className="p-8 text-center hover:shadow-xl transition-all duration-300 border-0 bg-background/80 backdrop-blur-sm">
                    <div className="w-20 h-20 bg-gradient-to-br from-primary/20 to-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
                      <Icon className="w-10 h-10 text-primary" />
                    </div>
                    <h3 className="text-xl font-semibold mb-3">{benefit.title}</h3>
                    <p className="text-muted-foreground leading-relaxed">{benefit.description}</p>
                  </Card>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 md:px-6">
        <div className="container mx-auto max-w-4xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Frequently Asked Questions</h2>
            <p className="text-muted-foreground">
              Quick answers to common questions about our services.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="p-6">
              <h3 className="font-semibold mb-2">How quickly can I get started?</h3>
              <p className="text-sm text-muted-foreground">
                Most merchants can be onboarded within 24-48 hours after initial consultation.
              </p>
            </Card>
            
            <Card className="p-6">
              <h3 className="font-semibold mb-2">Do you offer custom pricing?</h3>
              <p className="text-sm text-muted-foreground">
                Yes, we offer volume discounts and custom pricing for high-volume merchants.
              </p>
            </Card>
            
            <Card className="p-6">
              <h3 className="font-semibold mb-2">What payment processors do you support?</h3>
              <p className="text-sm text-muted-foreground">
                We support all major payment processors including Stripe, Square, and more.
              </p>
            </Card>
            
            <Card className="p-6">
              <h3 className="font-semibold mb-2">Is there a setup fee?</h3>
              <p className="text-sm text-muted-foreground">
                No setup fees. You only pay per successful chargeback prevention alert.
              </p>
            </Card>
          </div>
        </div>
      </section>
    </>
  );
}