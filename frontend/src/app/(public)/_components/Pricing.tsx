"use client";

// No need for shadcn UI components as we're using custom styling
import { Check, X, Star, Shield, Zap, Clock } from "lucide-react";
import { MastercardLogo, VerifiLogo, VisaLogo, EthocaLogo } from "@/components/ui/logos";
import { useRouter, usePathname } from "next/navigation";

const pricingPlans = [
  {
    name: "Mastercard",
    provider: "Mastercard",
    poweredBy: "Powered by Ethoca",
    price: 25,
    description: "Prevents disputes from Mastercard transactions with worldwide coverage",
    coverage: "Worldwide coverage",
    coveragePercent: "95%",
    enrollmentTime: "Up to 12 hour",
    refundOptions: "Auto refund",
    features: [
      "Worldwide coverage",
      "95% coverage percentage",
      "Up to 12 hour enrollment time",
      "Auto refund",
      "Mastercard network support"
    ],
    popular: false
  },
  {
    name: "Visa",
    provider: "Visa",
    poweredBy: "Powered by Verifi",
    price: 15,
    description: "Prevents disputes from Visa transactions with worldwide coverage",
    coverage: "Worldwide coverage",
    coveragePercent: "97%",
    enrollmentTime: "Up to 5 day",
    refundOptions: "Auto refund",
    features: [
      "Worldwide coverage",
      "97% coverage percentage",
      "Up to 5 day enrollment time",
      "Auto refund",
      "Visa network support"
    ],
    popular: false
  }
];

const benefits = [
  {
    icon: Shield,
    title: "Fast onboarding",
    description: "Set up your Chargeback account in just 2 minutes. Streamlined for your convenience."
  },
  {
    icon: Zap,
    title: "Cheapest rates",
    description: "We offer the cheapest Ethoca and Verifi rates in the industry."
  },
  {
    icon: Clock,
    title: "Pay per alert",
    description: "You only pay when we successfully prevent a chargeback for you."
  }
];

const comparisonFeatures = [
  { feature: "Duplicate alerts?", us: "Only useful alerts", others: "All alerts" },
  { feature: "Chargebacks coverage", us: "90-91%", others: "70-85%" },
  { feature: "Refund automation?", us: "Fully automated", others: "Poor" },
  { feature: "Alert Insights?", us: "Total insights", others: "Limited" },
  { feature: "Enrollment time?", us: "Fastest available", others: "Slow" },
  { feature: "Alert to transaction mapping?", us: "Exceptional", others: "Decent" },
  { feature: "Pricing?", us: "Lowest available", others: "Pricier" },
  { feature: "Monthly minimum?", us: "None", others: "Some providers" },
  { feature: "Contract?", us: "None", others: "Some providers" },
  { feature: "Official Ethoca & Verifi partner?", us: "Yes", others: "Some providers" },
  { feature: "AMEX and Discover coverage", us: "Yes", others: "Some providers" }
];


export default function PricingComponent() {
  const router = useRouter();
  const pathname = usePathname();
  const isPricingPage = pathname === '/pricing';

  const handleClose = () => {
    router.push('/dashboard');
  };

  const content = (
    <>
      {/* Hero Section */}
      <section className={`py-20 px-4 md:px-6 lg:py-20 ${isPricingPage ? 'pt-32' : ''}`}>
        <div className="container mx-auto text-center">
          <div className="container mx-auto flex justify-center mb-6">
            <div className="bg-transparent border border-zinc-800 rounded-xl p-3 px-4 flex items-center justify-center gap-3 text-white max-w-xl hover:border-zinc-700 transition-all duration-200">
              <div className="flex items-center gap-2">
                <EthocaLogo width={20} height={20} />
                <VerifiLogo width={20} height={20} />
              </div>
              <span className="text-lg">Ethoca and Verifi official partner</span>
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Pay per use pricing model
          </h1>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto mb-4">
            Only pay when we successfully help you prevent a chargeback.
          </p>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="pb-10 px-4 md:px-6">
        <div className="container mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {pricingPlans.map((plan, index) => (
              <div key={index} className={`relative bg-transparent border border-zinc-800 rounded-xl p-6 transition-all duration-200 hover:border-zinc-700 ${plan.popular ? 'border-white/30 scale-105' : ''}`}>
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-transparent border border-zinc-700 text-white px-4 py-1 rounded-full flex items-center">
                      <Star className="w-4 h-4 mr-1 text-white" />
                      Most Popular
                    </div>
                  </div>
                )}
                <div className="text-center pb-4">
                  <h3 className="text-2xl font-normal mb-4">{plan.name}</h3>
                  <div className="flex items-center justify-center gap-2 mb-4">
                    {plan.provider === "Mastercard" ? (
                      <MastercardLogo width={69} height={47} />
                    ) : (
                      <VisaLogo width={69} height={47} />
                    )}
                  </div>
                  <div className="text-4xl font-semibold text-white">
                    ${plan.price}
                    <span className="text-lg font-normal text-gray-400">/per alert</span>
                  </div>
                  <p className="text-sm text-gray-400 mt-4">{plan.description}</p>
                </div>
                <div className="space-y-5 mt-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Coverage:</span>
                      <span className="text-white">{plan.coverage}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Coverage %:</span>
                      <span className="text-white">{plan.coveragePercent}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Enrollment:</span>
                      <span className="text-white">{plan.enrollmentTime}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Refund:</span>
                      <span className="text-white">{plan.refundOptions}</span>
                    </div>
                  </div>

                  <div className="border-t border-zinc-800 pt-4">
                    <h4 className="font-normal mb-3 text-white">What&apos;s included?</h4>
                    <ul className="space-y-2">
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center gap-2 text-sm">
                          <Check className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-300">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="border-t border-zinc-800 pt-4 text-center">
                    <span className="text-sm text-gray-500">{plan.poweredBy}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4 md:px-6 bg-black rounded-none">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon;
              return (
                <div key={index} className="text-center bg-transparent border border-zinc-800 rounded-xl p-6 transition-all duration-200 hover:border-zinc-700">
                  <div className="w-20 h-20 bg-black flex items-center justify-center mx-auto mb-4">
                    <Icon className="w-10 h-10 text-primary" />
                  </div>
                  <h3 className="text-xl font-normal mb-2 text-white">{benefit.title}</h3>
                  <p className="text-gray-400">{benefit.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Comparison Table */}
      <section className="py-20 px-4 md:px-6">
        <div className="container mx-auto">
          <h2 className="text-3xl font-normal text-center mb-2 text-white">Quick comparison</h2>
          <p className="text-center text-gray-400 mb-12">Discover why leading brands rely on Chargeback.</p>

          <div className="max-w-4xl mx-auto">
            <div className="bg-transparent border border-zinc-800 rounded-xl overflow-hidden transition-all duration-200 hover:border-zinc-700">
              <div className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-zinc-800">
                        <th className="text-left p-4 font-normal text-white">What you pay for?</th>
                        <th className="text-center p-4 font-normal bg-zinc-900/80 text-white">Zero Chargeback Rate</th>
                        <th className="text-center p-4 font-normal text-white">Others</th>
                      </tr>
                    </thead>
                    <tbody>
                      {comparisonFeatures.map((item, index) => (
                        <tr key={index} className="border-b border-zinc-800 last:border-b-0">
                          <td className="p-4 font-normal text-white">{item.feature}</td>
                          <td className="p-4 text-center bg-zinc-900/80">
                            <div className="flex items-center justify-center gap-2">
                              <Check className="w-4 h-4 text-gray-400" />
                              <span className="font-normal text-white">{item.us}</span>
                            </div>
                          </td>
                          <td className="p-4 text-center">
                            <div className="flex items-center justify-center gap-2">
                              <X className="w-4 h-4 text-gray-400" />
                              <span className="text-gray-400">{item.others}</span>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 md:px-6 bg-black rounded-none">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl font-normal mb-2 text-white">Keep your payment gateway protected</h2>
          <p className="text-lg text-gray-400 mb-12 max-w-2xl mx-auto">
            Use our service to safeguard against costly disputes, reduce fees, and protect your payment gateway.
          </p>

          <div className="mt-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              <div className="bg-transparent border border-zinc-800 rounded-xl p-6 transition-all duration-200 hover:border-zinc-700 text-left">
                <div className="mb-6">
                  <h3 className="text-xl font-normal text-white mb-2">Pros of using Zero Chargeback Rate:</h3>
                </div>
                <div className="space-y-5">
                  <div className="flex items-start gap-3">
                    <Check className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-normal text-white">Reduce Chargeback disputes by up to 91%</div>
                      <div className="text-sm text-gray-400">Stop disputes before they happen with real-time alerts and smart transaction matching.</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Check className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-normal text-white">Prevent payment gateway shutdowns</div>
                      <div className="text-sm text-gray-400">Protect your business from account suspensions caused by high chargeback rates.</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Check className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-normal text-white">Avoid dispute monitoring programs</div>
                      <div className="text-sm text-gray-400">Stay off Visa and Mastercard&apos;s dispute monitoring lists (VDMP/MDMP) to avoid fines.</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Check className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-normal text-white">Boost your Stripe Risk Score & accept more payments</div>
                      <div className="text-sm text-gray-400">Lower dispute rates let you take on more risk and process more transactions confidently.</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Check className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-normal text-white">Improve cash flow by avoiding rolling reserves</div>
                      <div className="text-sm text-gray-400">Reduce processor-held funds and keep your revenue flowing.</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-transparent border border-zinc-800 rounded-xl p-6 transition-all duration-200 hover:border-zinc-700 text-left">
                <div className="mb-6">
                  <h3 className="text-xl font-normal text-white mb-2">Cons of NOT using Zero Chargeback Rate:</h3>
                </div>
                <div className="space-y-5">
                  <div className="flex items-start gap-3">
                    <X className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-normal text-white">Pay high dispute fees to your payment gateway</div>
                      <div className="text-sm text-gray-400">Without proactive alerts, dispute fees can exceed $100,000 annually for a $10M ARR business.</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <X className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-normal text-white">Account suspension or gateway shutdowns</div>
                      <div className="text-sm text-gray-400">High chargeback rates put your payment processing at risk.</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <X className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-normal text-white">Verifi/Ethoca alerts costs the same as the dispute fees</div>
                      <div className="text-sm text-gray-400">Without Chargeback, you pay your processor the same but miss all the other benefits.</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <X className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-normal text-white">Higher risk of being placed in VDMP/MDMP</div>
                      <div className="text-sm text-gray-400">Visa and Mastercard monitoring programs result in additional fines and penalties.</div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <X className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-normal text-white">Internal dispute management costs</div>
                      <div className="text-sm text-gray-400">Manual dispute handling wastes time and resources.</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );

  if (isPricingPage) {
    return (
      <>
        {/* Full Screen Backdrop Overlay */}
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-40" onClick={handleClose} />
        
        {/* Full Screen Content Container */}
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="min-h-screen bg-black">
            {/* Close Button */}
            <div className="fixed top-4 left-4 z-60">
              <button
                onClick={handleClose}
                className="bg-transparent border border-zinc-800 cursor-pointer text-white hover:border-zinc-700 rounded-full transition-all duration-200 w-8 h-8 p-0 flex items-center justify-center"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
            {content}
          </div>
        </div>
      </>
    );
  }

  return content;
}