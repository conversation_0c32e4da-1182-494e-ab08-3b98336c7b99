"use client";

import { useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { ShopifyService } from "@/services/shopify.service";
import { authService } from "@/services/auth.service";
import { signIn } from "next-auth/react";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "sonner";
import { Loader2, CheckCircle, CreditCard } from "lucide-react";
import { apiAxios } from "@/lib/apiAxios";
// Stripe imports removed - using Shopify Managed Pricing

// Wrapper component that uses searchParams
function ShopifyCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<
    "loading" | "registration-success" | "success" | "error" | "no-data"
  >("loading");
  const [errorMessage, setErrorMessage] = useState("");
  // User data and store ID no longer needed for card setup

  // Handle direct token-based connection (no OAuth)
  async function handleDirectConnection() {
    try {
      // Get saved data from session storage
      const pendingRegistrationData = sessionStorage.getItem(
        "pendingRegistration"
      );
      const pendingShopifyConnection = sessionStorage.getItem(
        "pendingShopifyConnection"
      );
      const pendingShopifyToken = sessionStorage.getItem("pendingShopifyToken");

      if (
        !pendingRegistrationData ||
        !pendingShopifyConnection ||
        !pendingShopifyToken
      ) {
        setStatus("no-data");
        return;
      }

      const userData = JSON.parse(pendingRegistrationData);

      // 1. Register the user
      try {
        await authService.register({
          email: userData.email,
          password: userData.password,
          fullName: userData.fullName,
          phoneNumber: userData.phoneNumber,
          address: userData.address || "", // Add address field with fallback
          gender: userData.gender,
          birthDate: new Date(userData.birthDate),
          note: "", // Add optional note field
        });

        // 2. Connect Shopify store with the token
        const connectedStore = await ShopifyService.connectShopWithToken(
          pendingShopifyConnection,
          pendingShopifyToken
        );

        // 3. Sign in the user
        await signIn("credentials", {
          email: userData.email,
          password: userData.password,
          redirect: false,
        });

        // 4. Create Stripe account for the store if it doesn't exist
        try {
          const { createStripeAccount } = await import("@/services/stripe.service");
          await createStripeAccount({
            storeId: connectedStore.id,
            stripeAccountId: null, // Will be set later when Connect account is actually created
            status: "pending"
          });
        } catch (error: any) {
          // Stripe account creation not needed for Shopify Managed Pricing
          console.log("Skipping Stripe account creation - using Shopify billing");
        }

        // Clear session storage
        sessionStorage.removeItem("pendingShopifyConnection");
        sessionStorage.removeItem("pendingRegistration");
        sessionStorage.removeItem("pendingShopifyToken");

        // User data stored for registration only

        // Skip card setup for Shopify Managed Pricing - go directly to success
        setStatus("success");
        toast.success("Account setup completed successfully!");

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          router.push("/dashboard");
        }, 2000);
      } catch (error: any) {
        setStatus("error");
        setErrorMessage(
          error?.message ||
          "An error occurred during registration and connection"
        );
        toast.error("Failed to complete registration. Please try again.");
      }
    } catch (error: any) {
      setStatus("error");
      setErrorMessage(error?.message || "An unexpected error occurred");
    }
  }

  useEffect(() => {
    async function handleCallback() {
      try {
        // Check if this is a direct token connection
        const isDirect = searchParams.get("direct") === "true";

        if (isDirect) {
          // Handle direct token connection (no OAuth callback)
          await handleDirectConnection();
          return;
        }

        // Otherwise handle OAuth callback
        const shop = searchParams.get("shop");
        const code = searchParams.get("code");

        if (!shop || !code) {
          setStatus("error");
          setErrorMessage("Missing required parameters from Shopify");
          return;
        }

        // Get the pending registration data
        const pendingShopifyConnection = sessionStorage.getItem(
          "pendingShopifyConnection"
        );
        const pendingRegistrationData = sessionStorage.getItem(
          "pendingRegistration"
        );

        if (!pendingRegistrationData || !pendingShopifyConnection) {
          setStatus("no-data");
          return;
        }

        const userData = JSON.parse(pendingRegistrationData);

        // 1. Register the user
        try {
          const registrationResponse = await authService.register({
            email: userData.email,
            password: userData.password,
            fullName: userData.fullName,
            phoneNumber: userData.phoneNumber,
            address: userData.address || "", // Add address field with fallback
            gender: userData.gender,
            birthDate: new Date(userData.birthDate),
            note: "", // Add optional note field
          });

          // 2. Exchange the code for an access token (handled by backend)
          const response = await apiAxios.get(
            `/shopify/callback?shop=${shop}&code=${code}`
          );
          const data = response.data;

          const { accessToken } = data;

          // 3. Connect Shopify store with the token
          const connectedStore = await ShopifyService.connectShopWithToken(shop, accessToken);

          // 4. Sign in the user with proper authentication data
          const authData = {
            storeId: connectedStore.id, // LinkedStore ID (primary identifier)
            shopify_store_id: connectedStore.providerStoreId, // Shopify store ID (correct field name)
            name: userData.fullName,
            email: userData.email,
            domain: connectedStore.data?.domain || shop, // Domain from data object
            myshopify_domain: connectedStore.data?.shop?.myshopify_domain || shop, // From data.shop
            access_token: accessToken,
          };

          await signIn("credentials", {
            authData: JSON.stringify(authData),
            redirect: false,
          });

          // 5. Create Stripe account for the store if it doesn't exist
          try {
            const { createStripeAccount } = await import("@/services/stripe.service");
            await createStripeAccount({
              storeId: connectedStore.id,
              stripeAccountId: null, // Will be set later when Connect account is actually created
              status: "pending"
            });
          } catch (error: any) {
            // Stripe account creation not needed for Shopify Managed Pricing
            console.log("Skipping Stripe account creation - using Shopify billing");
          }

          // Clear session storage
          sessionStorage.removeItem("pendingShopifyConnection");
          sessionStorage.removeItem("pendingRegistration");

          // User data stored for registration only

          // Skip card setup for Shopify Managed Pricing - go directly to success
          setStatus("success");
          toast.success("Account setup completed successfully!");

          // Redirect to dashboard after a short delay
          setTimeout(() => {
            router.push("/dashboard");
          }, 2000);
        } catch (error: any) {
          setStatus("error");
          setErrorMessage(
            error?.message ||
            "An error occurred during registration and connection"
          );
          toast.error("Failed to complete registration. Please try again.");
        }
      } catch (error: any) {
        setStatus("error");
        setErrorMessage(error?.message || "An unexpected error occurred");
      }
    }

    handleCallback();
  }, [searchParams, router]);

  // Card setup handlers removed - using Shopify Managed Pricing

  // Progress indicator component
  const ProgressIndicator = ({ currentStep }: { currentStep: number }) => (
    <div className="flex items-center justify-center mb-6 space-x-4">
      <div className="flex items-center">
        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${currentStep >= 1 ? 'bg-[#1D9BF0] text-white' : 'bg-[#2F3336] text-[#71767B]'
          }`}>
          {currentStep > 1 ? <CheckCircle className="w-4 h-4" /> : '1'}
        </div>
        <span className="ml-2 text-sm text-white">Account Setup</span>
      </div>

      <div className={`w-8 h-0.5 ${currentStep >= 2 ? 'bg-[#1D9BF0]' : 'bg-[#2F3336]'}`} />

      <div className="flex items-center">
        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${currentStep >= 2 ? 'bg-[#1D9BF0] text-white' : 'bg-[#2F3336] text-[#71767B]'
          }`}>
          <CheckCircle className="w-4 h-4" />
        </div>
        <span className="ml-2 text-sm text-white">Complete</span>
      </div>
    </div>
  );

  return (
    <div className="flex items-center justify-center min-h-screen bg-black">
      <div className="w-full max-w-2xl space-y-8">
        {/* Progress Indicator */}
        {(status === "loading" || status === "success") && (
          <ProgressIndicator
            currentStep={
              status === "loading" ? 1 :
                status === "success" ? 2 : 1
            }
          />
        )}

        {/* Main Content Card */}
        <Card className="w-full max-w-md mx-auto bg-[#16181C] border-[#2F3336]">
          <CardContent className="pt-6 text-center">
            {status === "loading" && (
              <>
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-[#1D9BF0]" />
                <h2 className="text-xl font-semibold mb-2 text-white">
                  Setting Up Your Account
                </h2>
                <p className="text-[#71767B]">
                  Please wait while we connect your account with Shopify...
                </p>
              </>
            )}

            {/* Card setup removed - Shopify handles all billing */}

            {status === "success" && (
              <>
                <div className="h-12 w-12 rounded-full bg-green-500/20 flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-6 w-6 text-green-400" />
                </div>
                <h2 className="text-xl font-semibold mb-2 text-white">
                  Setup Complete!
                </h2>
                <p className="text-[#71767B]">
                  Your account has been created successfully. Shopify will handle all billing automatically.
                </p>
                <p className="text-sm mt-4 text-[#1D9BF0]">Redirecting to dashboard...</p>
              </>
            )}

            {status === "error" && (
              <>
                <div className="h-12 w-12 rounded-full bg-red-500/20 flex items-center justify-center mx-auto mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-red-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </div>
                <h2 className="text-xl font-semibold mb-2 text-white">Setup Error</h2>
                <p className="text-[#71767B]">
                  {errorMessage ||
                    "An error occurred during the setup process"}
                </p>
                <button
                  onClick={() => router.push("/auth/signup")}
                  className="mt-4 text-sm text-[#1D9BF0] hover:underline"
                >
                  Back to Sign Up
                </button>
              </>
            )}

            {status === "no-data" && (
              <>
                <div className="h-12 w-12 rounded-full bg-yellow-500/20 flex items-center justify-center mx-auto mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-yellow-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    />
                  </svg>
                </div>
                <h2 className="text-xl font-semibold mb-2 text-white">
                  Missing Registration Data
                </h2>
                <p className="text-[#71767B]">
                  Registration information could not be found. Please start the
                  registration process again.
                </p>
                <button
                  onClick={() => router.push("/auth/signup")}
                  className="mt-4 text-sm text-[#1D9BF0] hover:underline"
                >
                  Back to Sign Up
                </button>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Main page component with Suspense boundary
export default function ShopifyCallbackPage() {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <Card className="w-full max-w-md">
            <CardContent className="pt-6 text-center">
              <div className="h-8 w-8 animate-spin mx-auto mb-4 border-t-2 border-primary rounded-full"></div>
              <h2 className="text-xl font-semibold mb-2">Loading</h2>
              <p className="text-muted-foreground">Please wait...</p>
            </CardContent>
          </Card>
        </div>
      }
    >
      <ShopifyCallbackContent />
    </Suspense>
  );
}
