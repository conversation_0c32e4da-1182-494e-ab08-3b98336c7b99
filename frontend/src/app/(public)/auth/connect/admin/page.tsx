"use client";

import { Suspense, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { signIn } from "next-auth/react";
import { Loader2 } from "lucide-react";
import ShopifyLogo from "@/components/svg/partners/ShopifyLogo";

const TOKEN =
  "LSJZVDIDSdwfA7y6eu0G0wERzyXmz4DwZEEzgM7xvMF1aWOp2oKRrecrGtxdZplK";

function AdminConnectContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const token = searchParams.get("token");

    if (token === TOKEN) {
      // Directly sign in with the admin credentials (fixed data structure)
      // STORE: quantchargeback-demo
      // const adminUserData = {
      //   id: "936e7770-5c3a-4e3d-b152-4915b2f4d3a7", // LinkedStore ID (primary identifier)
      //   user_id: "3f0868a2-505c-488e-9214-4bbcaba196af", // User ID
      //   shopify_store_id: "94074110225",
      //   name: "quantchargeback-demo",
      //   email: "<EMAIL>",
      //   domain: "quantchargeback-demo.myshopify.com",
      //   myshopify_domain: "quantchargeback-demo.myshopify.com",
      //   access_token: "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",
      // };

      // STORE: Makezbright Gifts (No amount data in disputes)
      const adminUserData = {
        id: "420057a5-1c6d-4e13-a08e-e156ab065d96", // LinkedStore ID (primary identifier)
        user_id: "4086c294-d97a-45e5-a4da-88c50912feee", // User ID
        shopify_store_id: "28003598418",
        name: "Makezbright Gifts",
        email: "<EMAIL>",
        domain: "makezbrightgifts.com",
        myshopify_domain: "makezbright.myshopify.com",
        access_token: "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",
      };

      // STORE: Wrap Whirl (Has complete dispute data with amounts)
      // const adminUserData = {
      //   id: "8c67011a-fb05-40d3-9794-c40eebe50ef8", // LinkedStore ID (primary identifier)
      //   user_id: "68f39528-d532-40e3-9297-cf474d7beb00", // User ID
      //   shopify_store_id: "69468356841",
      //   name: "Wrap Whirl.",
      //   email: "<EMAIL>",
      //   domain: "wrapwhirl.com",
      //   myshopify_domain: "bec4c7-4b.myshopify.com",
      //   access_token: "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",
      // };

      signIn("credentials", {
        authData: JSON.stringify(adminUserData),
        redirect: true,
        callbackUrl: "/dashboard",
      });
    } else {
      // Invalid token, redirect to 404 or connect page
      router.replace("/auth/connect");
    }
  }, [searchParams, router]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-background via-background to-green-50/10 dark:to-green-950/10">
      <div className="w-full max-w-md space-y-6 p-4">
        <div className="text-center space-y-2">
          <h1 className="text-2xl font-semibold tracking-tight">
            Admin Access
          </h1>
          <p className="text-sm text-muted-foreground">
            Authenticating admin access...
          </p>
        </div>

        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 via-green-400/10 to-green-500/10 rounded-2xl blur-xl" />

          <div className="relative bg-card rounded-2xl border shadow-lg p-8">
            <div className="flex flex-col items-center justify-center space-y-6">
              <div className="relative">
                <div className="absolute inset-0 bg-green-500/20 rounded-full blur-2xl animate-pulse" />
                <ShopifyLogo className="w-16 h-16" />
              </div>

              <div className="flex flex-col items-center space-y-3">
                <div className="flex space-x-2">
                  <div
                    className="w-2 h-2 bg-green-600 rounded-full animate-bounce"
                    style={{ animationDelay: "0ms" }}
                  />
                  <div
                    className="w-2 h-2 bg-green-600 rounded-full animate-bounce"
                    style={{ animationDelay: "150ms" }}
                  />
                  <div
                    className="w-2 h-2 bg-green-600 rounded-full animate-bounce"
                    style={{ animationDelay: "300ms" }}
                  />
                </div>
                <p className="text-sm font-medium text-muted-foreground animate-pulse">
                  Setting up admin session...
                </p>
              </div>

              <div className="w-full space-y-2 px-4">
                <div className="flex items-center space-x-3 text-sm">
                  <Loader2 className="h-4 w-4 animate-spin text-green-600" />
                  <span className="text-muted-foreground">
                    Verifying admin token
                  </span>
                </div>
                <div className="flex items-center space-x-3 text-sm opacity-50">
                  <div className="h-4 w-4 rounded-full border-2 border-muted-foreground/30" />
                  <span className="text-muted-foreground">
                    Creating session
                  </span>
                </div>
                <div className="flex items-center space-x-3 text-sm opacity-50">
                  <div className="h-4 w-4 rounded-full border-2 border-muted-foreground/30" />
                  <span className="text-muted-foreground">
                    Redirecting to dashboard
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function AdminConnectPage() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center">
          <div className="text-center space-y-3">
            <div className="w-12 h-12 border-4 border-gray-200 border-t-primary rounded-full animate-spin mx-auto"></div>
            <p className="text-sm text-muted-foreground">Loading...</p>
          </div>
        </div>
      }
    >
      <AdminConnectContent />
    </Suspense>
  );
}
