"use client";

import { AlertTriangle } from "lucide-react";
import Stripe<PERSON>ogo from "@/components/svg/partners/StripeLogo";

export default function StripeMarketplace() {

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-background via-background to-indigo-50/10 dark:to-indigo-950/10">
      <div className="w-full max-w-4xl space-y-6 p-4">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-2xl font-semibold tracking-tight">
            Stripe Marketplace Installation
          </h1>
          <p className="text-sm text-muted-foreground">
            Session information and installation parameters
          </p>
        </div>

        {/* Content Card */}
        <div className="relative">
          {/* Background blur effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/10 via-purple-400/10 to-indigo-500/10 rounded-2xl blur-xl" />

          <div className="relative bg-card rounded-2xl border shadow-lg p-8">
            <div className="flex flex-col items-center space-y-6">
              {/* Stripe Icon */}
              <div className="relative">
                <div className="absolute inset-0 bg-indigo-500/20 rounded-full blur-2xl" />
                <StripeLogo className="w-16 h-16" />
              </div>

              <div className="w-full space-y-6 text-center">
                {/* Instructions Section */}
                <div className="bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-800 rounded-lg p-6">
                  <div className="space-y-4">
                    <div className="bg-indigo-100 dark:bg-indigo-800 rounded-full p-3 w-fit mx-auto">
                      <AlertTriangle className="w-8 h-8 text-indigo-600 dark:text-indigo-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-indigo-900 dark:text-indigo-100 mb-2">
                        No Session Found
                      </h3>
                      <p className="text-sm text-indigo-700 dark:text-indigo-300 mb-4">
                        To connect Stripe with your account, please log in with Shopify first.
                      </p>
                      <div className="space-y-2 text-left max-w-md mx-auto">
                        <div className="flex items-start space-x-2">
                          <span className="text-indigo-600 font-semibold">1.</span>
                          <span className="text-sm text-indigo-700 dark:text-indigo-300">
                            Log in with your Shopify account
                          </span>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="text-indigo-600 font-semibold">2.</span>
                          <span className="text-sm text-indigo-700 dark:text-indigo-300">
                            Reinstall app from marketplace or access{" "}
                            <a href="/settings/payment-gateway" className="underline hover:text-indigo-800 dark:hover:text-indigo-200">
                              Payment gateway
                            </a>
                          </span>
                        </div>
                      </div>
                    </div>
                    <a
                      href="/auth/connect"
                      className="inline-flex items-center justify-center px-6 py-2.5 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg transition-colors"
                    >
                      Login with Shopify
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}