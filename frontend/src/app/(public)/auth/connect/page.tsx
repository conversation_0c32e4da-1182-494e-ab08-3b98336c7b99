"use client";

import { Suspense } from "react";
import { useState, useEffect, useCallback } from "react";
import { useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { ShopifyService } from "@/services/shopify.service";
import { toast } from "sonner";
import { Loader2, StoreIcon } from "lucide-react";
import Link from "next/link";
import ShopifyLogo from "@/components/svg/partners/ShopifyLogo";
import PricingComponent from "../../_components/Pricing";
import { forceResetStore } from "@/redux/store";

// Define form validation schema
const connectSchema = z.object({
  shop: z
    .string()
    .min(1, "Shop name is required")
    .regex(
      /^[a-zA-Z0-9][a-zA-Z0-9\-]*[a-zA-Z0-9]$/,
      "Shop name must be a valid Shopify store name (alphanumeric and hyphens only)"
    ),
});

type ConnectFormValues = z.infer<typeof connectSchema>;

function ConnectPageContent() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const searchParams = useSearchParams();

  // Initialize form
  const form = useForm<ConnectFormValues>({
    resolver: zodResolver(connectSchema),
    defaultValues: {
      shop: "",
    },
  });

  // Force logout cleanup on component mount
  useEffect(() => {
    const performCleanup = async () => {
      try {
        // Clear Redux store
        await forceResetStore();

        // Clear localStorage and sessionStorage
        if (typeof window !== "undefined") {
          try {
            localStorage.clear();
            sessionStorage.clear();
          } catch (e) {
            // Ignore storage errors
          }
        }
      } catch (error) {
        // Ignore cleanup errors to not interfere with the auth flow
      }
    };

    performCleanup();
  }, []); // Run only once on mount

  // Clean up force_logout parameter from URL
  useEffect(() => {
    const forceLogout = searchParams.get("force_logout");
    if (forceLogout) {
      // Remove the force_logout parameter from URL without causing a page refresh
      const url = new URL(window.location.href);
      url.searchParams.delete("force_logout");
      window.history.replaceState({}, "", url.toString());
    }
  }, [searchParams]);

  // Define onSubmit before useEffect to avoid hoisting issues
  const onSubmit = useCallback(async (values: ConnectFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Get the authorization URL from Shopify service
      const authUrl = await ShopifyService.getAuthUrl(
        `${values.shop}.myshopify.com`
      );

      // Redirect to Shopify authorization in the same tab
      window.location.href = authUrl;
    } catch (error: any) {
      setError(
        error?.message ||
          "Failed to connect to Shopify. Please check your shop name and try again."
      );
      toast.error("Failed to connect to Shopify. Please try again.");
      setIsLoading(false);
    }
  }, []);

  // Check for shop query parameter and process it
  useEffect(() => {
    const shopParam = searchParams.get("shop");

    if (shopParam) {
      // Remove .myshopify.com if present in the query parameter
      const shopName = shopParam.replace(/\.myshopify\.com$/, "");
      form.setValue("shop", shopName);

      // Automatically submit the form if shop parameter is provided
      form.handleSubmit(onSubmit)();
    }
  }, [searchParams, form, onSubmit]);

  return (
    <div className="flex-1 transition-all duration-300 flex flex-col min-h-0 overflow-y-auto">
      <div className="w-full max-w-xl space-y-8 mx-auto py-40">
        <div className="flex flex-col space-y-3 text-center">
          <h1 className="text-3xl font-semibold tracking-tight">
            Connect Your Shopify Store
          </h1>
          <p className="text-base text-muted-foreground">
            Connect your Shopify store to start managing chargebacks
          </p>
        </div>

        <Card className="relative">
          <CardContent>
            {/* Loading State with Shopify Animation */}
            {isLoading ? (
              <div className="flex items-center justify-center py-16">
                <div className="flex flex-col items-center space-y-8 animate-in fade-in-0 zoom-in-95 duration-300">
                  {/* Shopify Icon */}
                  <div className="relative">
                    <div className="absolute inset-0 bg-green-500/20 rounded-full blur-2xl animate-pulse" />
                    <ShopifyLogo className="w-20 h-20" />
                  </div>

                  {/* Store Name Display */}
                  {form.watch("shop") && (
                    <div className="text-center space-y-3 animate-in slide-in-from-bottom-3 duration-300">
                      <h3 className="text-3xl font-bold bg-gradient-to-r from-green-600 to-green-400 bg-clip-text text-transparent">
                        {form.watch("shop")}
                      </h3>
                      <p className="text-base text-muted-foreground">
                        .myshopify.com
                      </p>
                    </div>
                  )}

                  {/* Loading Text */}
                  <div className="flex items-center space-x-3">
                    <Loader2 className="h-5 w-5 animate-spin text-green-600" />
                    <p className="text-base font-medium text-muted-foreground">
                      Connecting to Shopify...
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <>
                {error && (
                  <Alert variant="destructive" className="mb-6">
                    <AlertDescription className="text-base">
                      {error}
                    </AlertDescription>
                  </Alert>
                )}

                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    <FormField
                      control={form.control}
                      name="shop"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <div className="relative group">
                              <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-400/10 rounded-lg blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                              <div className="relative flex items-center h-14 rounded-lg border border-input focus-within:border-green-500 focus-within:ring-2 focus-within:ring-green-500/20 transition-all duration-200">
                                <div className="flex items-center pl-5 pr-3">
                                  <StoreIcon className="w-5 h-5 text-muted-foreground" />
                                </div>
                                <input
                                  placeholder="your-store-name"
                                  autoComplete="off"
                                  disabled={isLoading}
                                  className="flex-1 h-full border-0 bg-transparent outline-none focus:outline-none px-2 text-base font-medium placeholder:text-muted-foreground/60"
                                  suppressHydrationWarning
                                  {...field}
                                />
                                <div className="flex items-center px-5 border-l border-input/50">
                                  <span className="text-base text-muted-foreground">
                                    .myshopify.com
                                  </span>
                                </div>
                              </div>
                            </div>
                          </FormControl>
                          <FormMessage className="text-sm" />
                          <p className="text-sm text-muted-foreground mt-2 italic">
                            * Note: Enter your store name without the full
                            domain
                          </p>
                        </FormItem>
                      )}
                    />

                    <Button
                      className="w-full h-12 text-base"
                      type="submit"
                      disabled={isLoading}
                    >
                      Connect to Shopify
                    </Button>
                  </form>
                </Form>
              </>
            )}
          </CardContent>
        </Card>
        <div className="flex flex-wrap justify-center gap-6 text-sm text-muted-foreground">
          <Link
            href="/about-us"
            className="hover:text-primary transition-colors"
          >
            About Us
          </Link>
          <Link
            href="/privacy-policy"
            className="hover:text-primary transition-colors"
          >
            Privacy Policy
          </Link>
          {/* <Link
            href="/shipping-policy"
            className="hover:text-primary transition-colors"
          >
            Shipping Policy
          </Link> */}
          <Link
            href="/terms-conditions"
            className="hover:text-primary transition-colors"
          >
            Terms & Conditions
          </Link>
          <Link
            href="/refund-policy"
            className="hover:text-primary transition-colors"
          >
            Refund Policy
          </Link>
        </div>
      </div>
      <PricingComponent />
    </div>
  );
}

export default function ConnectPage() {
  return (
    <Suspense fallback={
      <div className="flex-1 transition-all duration-300 flex flex-col min-h-0 overflow-y-auto">
        <div className="w-full max-w-xl space-y-8 mx-auto py-40">
          <div className="flex items-center justify-center">
            <div className="text-center space-y-3">
              <div className="w-12 h-12 border-4 border-gray-200 border-t-primary rounded-full animate-spin mx-auto"></div>
              <p className="text-sm text-muted-foreground">Loading...</p>
            </div>
          </div>
        </div>
      </div>
    }>
      <ConnectPageContent />
    </Suspense>
  );
}
