"use client";

import { signIn, getSession, signOut } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";
import { Loader2 } from "lucide-react";
import ShopifyLogo from "@/components/svg/partners/ShopifyLogo";
import { apiAxios } from "@/lib/apiAxios";

// Function to get user-friendly error message from error key
function getErrorMessage(errorKey: string): string {
  switch (errorKey) {
    case "missing_parameters":
      return "Missing required parameters for Shopify authentication";
    case "invalid_shop":
      return "Invalid shop domain. Please check your Shopify store URL and try again.";
    case "hmac_validation_failed":
      return "Security validation failed. Please try connecting again.";
    case "timestamp_validation_failed":
      return "Connection request expired. Please try connecting again.";
    case "access_token_failed":
      return "Failed to obtain authorization from Shopify. Please try again.";
    case "billing_failed":
      return "Failed to create subscription plan. Please try again.";
    case "billing_declined":
      return "Subscription was declined. Please try connecting again to approve the billing plan.";
    case "signin_failed":
      return "Failed to sign in with your Shopify store. Please try again.";
    case "oauth_error":
      return "An error occurred during the authentication process. Please try again.";
    default:
      return "An unexpected error occurred while connecting to Shopify";
  }
}

// Function to create subscription via backend API
async function createSubscription(linkedStoreId: string, accessToken: string) {
  try {
    const response = await apiAxios.post('/billing/create', {
      linkedStoreId: linkedStoreId
    }, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    if (!response.data?.success) {
      throw new Error(response.data?.error || 'Subscription creation failed');
    }

    return response.data;
  } catch (error: any) {
    throw error;
  }
}

function CallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(true);
  const [errorKey, setErrorKey] = useState<string | null>(
    searchParams.get("error") || null
  );
  const [authStep, setAuthStep] = useState<string>("Verifying credentials");
  const [showPaymentsConnect, setShowPaymentsConnect] = useState(false);
  const [paymentsConnected, setPaymentsConnected] = useState(false);

  // Handle Shopify Payments connection
  const handlePaymentsConnect = async () => {
    try {
      setPaymentsConnected(true);
      setShowPaymentsConnect(false);
      setAuthStep("Redirecting to dashboard");
      
      // Wait a bit for session to be established
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Verify session is established before redirecting
      const session = await getSession();
      if (session) {
        // Force a hard navigation to ensure clean state
        window.location.href = "/dashboard";
      } else {
        // If session isn't ready, try again after a longer wait
        setTimeout(async () => {
          const retrySession = await getSession();
          if (retrySession) {
            window.location.href = "/dashboard";
          } else {
            // If still no session, show error
            setErrorKey("signin_failed");
            setIsLoading(false);
          }
        }, 2000);
      }
    } catch (error) {
      setErrorKey("oauth_error");
      setIsLoading(false);
    }
  };

  // Initialize with error state from URL parameters if they exist
  useEffect(() => {
    if (errorKey) {
      setIsLoading(false);
      return;
    }

    async function handleCallback() {
      try {
        setAuthStep("Verifying credentials");
        
        // Force logout first to ensure clean authentication state
        // This handles both direct /auth/connect access and Shopify admin installation
        try {
          await signOut({ redirect: false });
          // Clear any client-side storage
          sessionStorage.clear();
          localStorage.clear();
          // Wait a moment for cleanup
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (logoutError) {
          // Continue even if logout fails
          console.log("Logout cleanup completed");
        }
        
        // Extract parameters from the URL query
        const id = searchParams.get("id"); // LinkedStore ID
        const user_id = searchParams.get("user_id"); // User ID
        const shopify_store_id = searchParams.get("shopify_store_id"); // Shopify store ID
        const name = searchParams.get("name");
        const email = searchParams.get("email");
        const domain = searchParams.get("domain");
        const myshopify_domain = searchParams.get("myshopify_domain");
        const access_token = searchParams.get("access_token");
        const billing_status = searchParams.get("billing_status");
        const charge = searchParams.get("charge"); // For billing confirmation

        // Validate required parameters
        if (!id || !shopify_store_id || !access_token || !myshopify_domain || !name || !email) {
          setErrorKey("missing_parameters");
          setIsLoading(false);
          return;
        }

        // Check if this is a billing confirmation callback
        if (charge && billing_status) {
          setAuthStep("Processing subscription confirmation");
          
          if (billing_status === 'declined') {
            setErrorKey("billing_declined");
            setIsLoading(false);
            return;
          }
          
          if (billing_status === 'active') {
            setAuthStep("Subscription confirmed - finalizing setup");
            
            // Create the user data for authentication
            const userData = {
              id: id,
              user_id: user_id,
              shopify_store_id: shopify_store_id,
              name: name,
              email: email,
              domain: domain || "",
              myshopify_domain: myshopify_domain,
              access_token: access_token,
              provider: "shopify",
            };

            // Sign in with confirmed billing
            const signInResult = await signIn("credentials", {
              authData: JSON.stringify(userData),
              redirect: false,
            });

            if (signInResult?.error) {
              setErrorKey("signin_failed");
              setIsLoading(false);
              return;
            }

            setAuthStep("Redirecting to dashboard");
            setTimeout(() => {
              window.location.href = "/dashboard";
            }, 1000);
            return;
          }
        }

        // Check if user already has active subscription (no charge parameter needed)
        if (billing_status === 'active') {
          setAuthStep("Existing subscription found - verifying");
          
          // Create the user data for authentication
          const userData = {
            id: id,
            user_id: user_id,
            shopify_store_id: shopify_store_id,
            name: name,
            email: email,
            domain: domain || "",
            myshopify_domain: myshopify_domain,
            access_token: access_token,
            provider: "shopify",
          };

          // Sign in with existing active subscription
          const signInResult = await signIn("credentials", {
            authData: JSON.stringify(userData),
            redirect: false,
          });

          if (signInResult?.error) {
            setErrorKey("signin_failed");
            setIsLoading(false);
            return;
          }

          // Wait for session to be established
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          setAuthStep("Verifying subscription in Shopify");

          try {
            // Verify/create subscription in Shopify even for existing subscriptions
            const subscriptionResult = await createSubscription(id, access_token);
            console.log('Subscription verification result:', subscriptionResult);

            // If subscription needs confirmation, redirect to Shopify
            if (subscriptionResult.confirmationUrl) {
              setAuthStep("Redirecting to billing confirmation");
              window.location.href = subscriptionResult.confirmationUrl;
              return;
            }

            // If subscription is verified or created successfully, proceed to dashboard
            setAuthStep("Redirecting to dashboard");
            setTimeout(() => {
              window.location.href = "/dashboard";
            }, 1000);
            return;
          } catch (billingError: any) {
            console.error('Billing verification error:', billingError);
            // If verification fails, still proceed to dashboard since subscription exists in database
            setAuthStep("Redirecting to dashboard");
            setTimeout(() => {
              window.location.href = "/dashboard";
            }, 1000);
            return;
          }
        }

        // Regular OAuth flow - create subscription
        setAuthStep("Setting up your account");

        // First, sign in to establish session for API calls
        const userData = {
          id: id,
          user_id: user_id,
          shopify_store_id: shopify_store_id,
          name: name,
          email: email,
          domain: domain || "",
          myshopify_domain: myshopify_domain,
          access_token: access_token,
          provider: "shopify",
        };

        const signInResult = await signIn("credentials", {
          authData: JSON.stringify(userData),
          redirect: false,
        });

        console.log('SignIn result:', signInResult);

        if (signInResult?.error) {
          console.error('SignIn error:', signInResult.error);
          setErrorKey("signin_failed");
          setIsLoading(false);
          return;
        }

        // Wait for session to be established
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setAuthStep("Creating subscription plan");

        try {
          // Create subscription using backend API
          const subscriptionResult = await createSubscription(id, access_token);
          console.log('Subscription result:', subscriptionResult);

          if (subscriptionResult.confirmationUrl) {
            setAuthStep("Redirecting to billing confirmation");
            // Redirect to Shopify's billing confirmation page
            window.location.href = subscriptionResult.confirmationUrl;
            return;
          } else {
            console.error('No confirmation URL in subscription result:', subscriptionResult);
            throw new Error('No confirmation URL received from subscription creation');
          }
        } catch (billingError: any) {
          console.error('Billing error:', billingError);
          setErrorKey("billing_failed");
          setIsLoading(false);
          return;
        }

      } catch (error: any) {
        setErrorKey("oauth_error");
        setIsLoading(false);
      }
    }

    handleCallback();
  }, [searchParams, router]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-background via-background to-green-50/10 dark:to-green-950/10">
      <div className="w-full max-w-md space-y-6 p-4">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-2xl font-semibold tracking-tight">
            {isLoading ? "Connecting to Shopify" : showPaymentsConnect ? "Connect Shopify Payments" : errorKey ? "Connection Failed" : "Processing..."}
          </h1>
          <p className="text-sm text-muted-foreground">
            {isLoading && "Please wait while we set up your subscription"}
            {showPaymentsConnect && "One more step to complete your setup"}
          </p>
        </div>

        {/* Content Card */}
        <div className="relative">
          {/* Background blur effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 via-green-400/10 to-green-500/10 rounded-2xl blur-xl" />

          <div className="relative bg-card rounded-2xl border shadow-lg p-8">
            {isLoading ? (
              <div className="flex flex-col items-center justify-center space-y-6">
                {/* Animated Shopify Icon */}
                <div className="relative">
                  <div className="absolute inset-0 bg-green-500/20 rounded-full blur-2xl animate-pulse" />
                  <ShopifyLogo className="w-16 h-16" />
                </div>

                {/* Loading indicators */}
                <div className="flex flex-col items-center space-y-3">
                  <div className="flex space-x-2">
                    <div className="w-2 h-2 bg-green-600 rounded-full animate-bounce" style={{ animationDelay: "0ms" }} />
                    <div className="w-2 h-2 bg-green-600 rounded-full animate-bounce" style={{ animationDelay: "150ms" }} />
                    <div className="w-2 h-2 bg-green-600 rounded-full animate-bounce" style={{ animationDelay: "300ms" }} />
                  </div>
                  <p className="text-sm font-medium text-muted-foreground animate-pulse">
                    {authStep === "Existing subscription found - verifying" ? "Existing subscription found - verifying..." : 
                     authStep === "Verifying subscription in Shopify" ? "Verifying subscription in Shopify..." : 
                     "Setting up your subscription..."}
                  </p>
                </div>

                {/* Progress steps */}
                <div className="w-full space-y-2 px-4">
                  <div className={`flex items-center space-x-3 text-sm ${authStep === "Verifying credentials" ? "" : "opacity-50"}`}>
                    {authStep === "Verifying credentials" ? (
                      <Loader2 className="h-4 w-4 animate-spin text-green-600" />
                    ) : (
                      <div className="h-4 w-4 rounded-full bg-green-600 flex items-center justify-center">
                        <svg className="h-2.5 w-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                    <span className="text-muted-foreground">Verifying credentials</span>
                  </div>
                  <div className={`flex items-center space-x-3 text-sm ${authStep === "Setting up your account" ? "" : (authStep === "Creating subscription plan" || authStep === "Redirecting to billing confirmation" || authStep === "Processing subscription confirmation" || authStep === "Subscription confirmed - finalizing setup" || authStep === "Existing subscription found - signing in" || authStep === "Redirecting to dashboard") ? "" : "opacity-50"}`}>
                    {authStep === "Setting up your account" ? (
                      <Loader2 className="h-4 w-4 animate-spin text-green-600" />
                    ) : authStep === "Creating subscription plan" || authStep === "Redirecting to billing confirmation" || authStep === "Processing subscription confirmation" || authStep === "Subscription confirmed - finalizing setup" || authStep === "Existing subscription found - signing in" || authStep === "Redirecting to dashboard" ? (
                      <div className="h-4 w-4 rounded-full bg-green-600 flex items-center justify-center">
                        <svg className="h-2.5 w-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    ) : (
                      <div className="h-4 w-4 rounded-full border-2 border-muted-foreground/30" />
                    )}
                    <span className="text-muted-foreground">Setting up your account</span>
                  </div>
                  <div className={`flex items-center space-x-3 text-sm ${authStep === "Creating subscription plan" ? "" : authStep === "Redirecting to billing confirmation" || authStep === "Processing subscription confirmation" || authStep === "Subscription confirmed - finalizing setup" || authStep === "Existing subscription found - signing in" || authStep === "Redirecting to dashboard" ? "" : "opacity-50"}`}>
                    {authStep === "Creating subscription plan" ? (
                      <Loader2 className="h-4 w-4 animate-spin text-green-600" />
                    ) : authStep === "Redirecting to billing confirmation" || authStep === "Processing subscription confirmation" || authStep === "Subscription confirmed - finalizing setup" || authStep === "Existing subscription found - signing in" || authStep === "Redirecting to dashboard" ? (
                      <div className="h-4 w-4 rounded-full bg-green-600 flex items-center justify-center">
                        <svg className="h-2.5 w-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    ) : (
                      <div className="h-4 w-4 rounded-full border-2 border-muted-foreground/30" />
                    )}
                    <span className="text-muted-foreground">Creating subscription plan</span>
                  </div>
                  <div className={`flex items-center space-x-3 text-sm ${authStep === "Redirecting to billing confirmation" || authStep === "Processing subscription confirmation" || authStep === "Subscription confirmed - finalizing setup" || authStep === "Existing subscription found - verifying" || authStep === "Verifying subscription in Shopify" || authStep === "Redirecting to dashboard" ? "" : "opacity-50"}`}>
                    {authStep === "Redirecting to billing confirmation" || authStep === "Processing subscription confirmation" || authStep === "Subscription confirmed - finalizing setup" || authStep === "Existing subscription found - verifying" || authStep === "Verifying subscription in Shopify" ? (
                      <Loader2 className="h-4 w-4 animate-spin text-green-600" />
                    ) : authStep === "Redirecting to dashboard" ? (
                      <div className="h-4 w-4 rounded-full bg-green-600 flex items-center justify-center">
                        <svg className="h-2.5 w-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    ) : (
                      <div className="h-4 w-4 rounded-full border-2 border-muted-foreground/30" />
                    )}
                    <span className="text-muted-foreground">Confirming subscription</span>
                  </div>
                  <div className={`flex items-center space-x-3 text-sm ${authStep === "Redirecting to dashboard" ? "" : "opacity-50"}`}>
                    {authStep === "Redirecting to dashboard" ? (
                      <Loader2 className="h-4 w-4 animate-spin text-green-600" />
                    ) : (
                      <div className="h-4 w-4 rounded-full border-2 border-muted-foreground/30" />
                    )}
                    <span className="text-muted-foreground">Redirecting to dashboard</span>
                  </div>
                </div>
              </div>
            ) : showPaymentsConnect ? (
              <div className="flex flex-col items-center justify-center space-y-6">
                {/* Shopify Payments Connection Card */}
                <div className="w-full max-w-sm bg-card border border-border rounded-xl p-6 space-y-4">
                  {/* Shopify Logo */}
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <ShopifyLogo className="w-8 h-8" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-foreground">Shopify Payments</h3>
                      <p className="text-sm text-muted-foreground">
                        Connect your Shopify Payments account to fully automate your Shopify disputes.
                      </p>
                    </div>
                  </div>
                  
                  {/* Connect Button */}
                  <button
                    onClick={handlePaymentsConnect}
                    className="w-full bg-primary text-primary-foreground hover:bg-primary/90 font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2 cursor-pointer"
                  >
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                    </svg>
                    <span>Connect</span>
                  </button>
                </div>

                {/* Status Text */}
                <p className="text-sm text-muted-foreground text-center">
                  Please connect your Shopify Payments to continue
                </p>
              </div>
            ) : errorKey ? (
              <div className="flex flex-col items-center justify-center space-y-6 text-center">
                {/* Error Icon */}
                <div className="relative">
                  <div className="absolute inset-0 bg-red-500/20 rounded-full blur-xl" />
                  <div className="relative bg-red-100 dark:bg-red-900/20 rounded-full p-4">
                    <svg className="w-12 h-12 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>

                {/* Error Message */}
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">
                    {getErrorMessage(errorKey)}
                  </p>
                </div>

                {/* Action Button */}
                <button
                  onClick={() => router.push('/auth/connect')}
                  className="flex items-center space-x-2 bg-primary text-primary-foreground px-6 py-2.5 rounded-lg font-medium hover:bg-primary/90 transition-colors cursor-pointer"
                >
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  <span>Try Again</span>
                </button>
              </div>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ShopifyCallback() {
  return (
    <Suspense fallback={<div className="flex min-h-screen items-center justify-center">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
    </div>}>
      <CallbackContent />
    </Suspense>
  );
}