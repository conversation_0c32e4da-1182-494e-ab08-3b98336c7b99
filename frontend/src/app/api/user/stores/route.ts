import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { apiAxios } from '@/lib/apiAxios';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Make real API call to backend to get actual LinkedStore records
    try {
      
      const response = await apiAxios.get('/shopify/stores', {
        headers: {
          'Authorization': `Bearer ${session.user.access_token}`,
          'Content-Type': 'application/json',
        },
      });


      return NextResponse.json(response.data);
    } catch (apiError: any) {
      
      // If backend API fails, return empty array instead of mock data
      return NextResponse.json([]);
    }

  } catch (error: any) {
    return NextResponse.json(
      { error: 'Failed to fetch stores', message: error.message },
      { status: 500 }
    );
  }
} 