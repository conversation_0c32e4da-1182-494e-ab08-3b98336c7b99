"use client";

import React from "react";
import { ReduxProvider } from "@/redux/provider";
import { DataInitializer } from "@/components/DataInitializer";
import { useSessionPersist } from "@/hooks/useSessionPersist";
import dynamic from "next/dynamic";

// Dynamically import CrispChat to ensure it only loads on client-side
const CrispChat = dynamic(() => import("@/components/CrispChat"), {
  ssr: false,
});

interface AppProvidersProps {
  children: React.ReactNode;
}

/**
 * Session persistence wrapper component
 */
function SessionPersistWrapper({ children }: { children: React.ReactNode }) {
  useSessionPersist();
  return <>{children}</>;
}

/**
 * Root provider component that wraps all application providers
 */
export function AppProviders({ children }: AppProvidersProps) {
  return (
    <ReduxProvider>
      <SessionPersistWrapper>
        <DataInitializer />
        <CrispChat />
        {children}
      </SessionPersistWrapper>
    </ReduxProvider>
  );
}
