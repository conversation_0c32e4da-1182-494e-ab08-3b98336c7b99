import { apiAxios } from "@/lib/apiAxios";

export interface LinkedStore {
  id: string; // LinkedStore ID (primary identifier)
  storeName: string;
  provider: string;
  providerStoreId: string | null;
  data: {
    shop: any;
    accessToken: string;
    domain: string;
    lastFetchedAt?: string;
    disputes?: {
      disputes: Array<{
        id: string;
        order_id: string;
        type: string;
        status: string;
        amount: string;
        currency: string;
        reason: string;
        network: string;
        created_at: string;
        evidence_due_by: string | null;
      }>;
    };
    orders?: {
      orders: Array<any>;
    };
    products?: {
      products: Array<any>;
    };
    customers?: {
      customers: Array<any>;
    };
    transactions?: {
      transactions: Array<{
        id: string;
        type: string;
        test: boolean;
        payout_id: string;
        payout_status: string;
        amount: string;
        fee: string;
        net: string;
        currency: string;
        source_id: string;
        source_type: string;
        source_order_id: string;
        processed_at: string;
      }>;
    };
    payouts?: {
      payouts: Array<{
        id: string;
        status: string;
        date: string;
        amount: string;
        currency: string;
        summary: {
          adjustments_fee_amount: string;
          adjustments_gross_amount: string;
          charges_fee_amount: string;
          charges_gross_amount: string;
          refunds_fee_amount: string;
          refunds_gross_amount: string;
          reserved_funds_fee_amount: string;
          reserved_funds_gross_amount: string;
          retried_payouts_fee_amount: string;
          retried_payouts_gross_amount: string;
        };
      }>;
    };
  };
  createdAt: string;
  updatedAt: string;
}

export class ShopifyService {
  /**
   * Get the authorization URL for Shopify OAuth
   * @param shop The shopify shop domain (e.g. your-store.myshopify.com)
   * @returns The authorization URL to redirect to
   */
  static async getAuthUrl(shop: string): Promise<string> {
    try {
      const response = await apiAxios.get(`/shopify/auth/url`, {
        params: { shop: shop },
      });

      return response.data.authUrl;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Process the OAuth callback from Shopify
   * @param params OAuth callback parameters
   * @returns The linked store information
   */
  static async processCallback(params: {
    shop: string | null;
    code: string | null;
    hmac: string | null;
    host: string | null;
    timestamp: string | null;
  }): Promise<LinkedStore> {
    try {
      const response = await apiAxios.post(
        `/shopify/callback-with-user`,
        params
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Connect a Shopify store using access token
   * @param shop The shopify shop domain (e.g. your-store.myshopify.com)
   * @param accessToken The access token for the Shopify store
   * @returns The linked store object
   */
  static async connectShopWithToken(
    shop: string,
    accessToken: string
  ): Promise<LinkedStore> {
    try {
      const response = await apiAxios.post(`/shopify/connect`, {
        shop,
        accessToken,
      });

      return response.data;
    } catch (error) {
      throw error;
    }
  }


  /**
   * Get a store by its ID
   * @param storeId The ID of the store to get
   * @returns The store with the specified ID
   */
  static async getStoreById(storeId: string): Promise<LinkedStore> {
    try {
      const response = await apiAxios.get(`/shopify/stores/${storeId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Disconnect a linked store
   * @param storeId The ID of the store to disconnect
   */
  static async disconnectStore(storeId: string): Promise<void> {
    try {
      await apiAxios.delete(`/shopify/stores/${storeId}`);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Refresh store details from Shopify
   * @param storeId The ID of the store to refresh
   * @returns Updated store data
   */
  static async refreshStoreDetails(storeId: string): Promise<LinkedStore> {
    try {
      const response = await apiAxios.get(`/shopify/stores/${storeId}/refresh`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get order transactions from Shopify
   * @param storeId The ID of the store
   * @param orderId The ID of the order
   * @returns Transactions data for the specified order
   */
  static async getOrderTransactions(
    storeId: string,
    orderId: string
  ): Promise<any> {
    try {
      const response = await apiAxios.get(
        `/shopify/stores/${storeId}/orders/${orderId}/transactions`
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Find orders related to a block alert
   * @param storeId The ID of the store
   * @param blockInfo Block information to use for searching
   * @returns Related orders that match the block info
   */
  static async findRelatedOrders(
    storeId: string,
    blockInfo: {
      descriptor?: string;
      transactionId?: string;
      amount?: string;
      cardBin?: string;
      cardNumber?: string;
    }
  ): Promise<any> {
    try {
      const response = await apiAxios.post(
        `/shopify/stores/${storeId}/find-related-orders`,
        blockInfo
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

}
