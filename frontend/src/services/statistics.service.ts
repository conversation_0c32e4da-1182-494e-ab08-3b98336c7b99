import { apiAxios } from "@/lib/apiAxios";

// TypeScript interfaces for statistics data
export interface ChargebackCountryStats {
  countryCode: string;
  countryName: string;
  count: number;
}

export interface ChargebackReasonStats {
  reasonCode: string;
  reasonName: string;
  count: number;
}

export interface ChargebackStatsOverview {
  countries: ChargebackCountryStats[];
  reasons: ChargebackReasonStats[];
}

export interface GetStatsParams {
  limit?: number;
  storeId?: string;
  provider?: 'shopify' | 'stripe';
}

export interface StatisticsResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

class StatisticsService {
  /**
   * Get top chargeback countries for the authenticated user
   */
  async getTopChargebackCountries(
    params: GetStatsParams = {}
  ): Promise<ChargebackCountryStats[]> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.limit) queryParams.append("limit", params.limit.toString());
      if (params.storeId) queryParams.append("storeId", params.storeId);
      if (params.provider) queryParams.append("provider", params.provider);
      
      const queryString = queryParams.toString();
      const url = `/statistics/countries${queryString ? `?${queryString}` : ""}`;
      
      const response = await apiAxios.get<StatisticsResponse<ChargebackCountryStats[]>>(url);
      return response.data.data || [];
    } catch (error: any) {
      console.error('Error fetching top chargeback countries:', error);
      throw new Error(
        error.response?.data?.message || 'Failed to fetch chargeback countries'
      );
    }
  }

  /**
   * Get top chargeback reasons for the authenticated user
   */
  async getTopChargebackReasons(
    params: GetStatsParams = {}
  ): Promise<ChargebackReasonStats[]> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.limit) queryParams.append("limit", params.limit.toString());
      if (params.storeId) queryParams.append("storeId", params.storeId);
      if (params.provider) queryParams.append("provider", params.provider);
      
      const queryString = queryParams.toString();
      const url = `/statistics/reasons${queryString ? `?${queryString}` : ""}`;
      
      const response = await apiAxios.get<StatisticsResponse<ChargebackReasonStats[]>>(url);
      return response.data.data || [];
    } catch (error: any) {
      console.error('Error fetching top chargeback reasons:', error);
      throw new Error(
        error.response?.data?.message || 'Failed to fetch chargeback reasons'
      );
    }
  }

  /**
   * Get combined chargeback statistics overview
   */
  async getChargebackStatsOverview(
    params: GetStatsParams = {}
  ): Promise<ChargebackStatsOverview> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.limit) queryParams.append("limit", params.limit.toString());
      if (params.storeId) queryParams.append("storeId", params.storeId);
      if (params.provider) queryParams.append("provider", params.provider);
      
      const queryString = queryParams.toString();
      const url = `/statistics/overview${queryString ? `?${queryString}` : ""}`;
      
      const response = await apiAxios.get<StatisticsResponse<ChargebackStatsOverview>>(url);
      return response.data.data || { countries: [], reasons: [] };
    } catch (error: any) {
      console.error('Error fetching chargeback stats overview:', error);
      throw new Error(
        error.response?.data?.message || 'Failed to fetch statistics overview'
      );
    }
  }

  /**
   * Reset statistics for the authenticated user (for testing purposes)
   */
  async resetUserStatistics(storeId?: string): Promise<{ deletedCountries: number; deletedReasons: number }> {
    try {
      const queryParams = new URLSearchParams();
      if (storeId) queryParams.append("storeId", storeId);
      
      const queryString = queryParams.toString();
      const url = `/statistics/reset${queryString ? `?${queryString}` : ""}`;
      
      const response = await apiAxios.delete<StatisticsResponse<{ deletedCountries: number; deletedReasons: number }>>(url);
      return response.data.data || { deletedCountries: 0, deletedReasons: 0 };
    } catch (error: any) {
      console.error('Error resetting user statistics:', error);
      throw new Error(
        error.response?.data?.message || 'Failed to reset statistics'
      );
    }
  }
}

export const statisticsService = new StatisticsService();
