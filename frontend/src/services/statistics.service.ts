import { apiAxios } from "@/lib/apiAxios";

// TypeScript interfaces for statistics data
export interface ChargebackCountryStats {
  countryCode: string;
  countryName: string;
  count: number;
}

export interface ChargebackReasonStats {
  reasonCode: string;
  reasonName: string;
  count: number;
}

export interface ChargebackStatsOverview {
  countries: ChargebackCountryStats[];
  reasons: ChargebackReasonStats[];
}

export interface GetStatsParams {
  limit?: number;
  storeId?: string;
  provider?: "shopify" | "stripe";
}

export interface StatisticsResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

class StatisticsService {
  /**
   * Get top chargeback countries for a user
   */
  async getTopChargebackCountries(
    userId: string,
    params: GetStatsParams = {}
  ): Promise<ChargebackCountryStats[]> {
    try {
      const queryParams = new URLSearchParams();

      if (params.limit) queryParams.append("limit", params.limit.toString());
      if (params.storeId) queryParams.append("storeId", params.storeId);
      if (params.provider) queryParams.append("provider", params.provider);

      const queryString = queryParams.toString();
      const url = `/statistics/${userId}/countries${
        queryString ? `?${queryString}` : ""
      }`;

      const response = await apiAxios.get<
        StatisticsResponse<ChargebackCountryStats[]>
      >(url);
      return response.data.data || [];
    } catch (error: any) {
      console.error("Error fetching top chargeback countries:", error);
      throw new Error(
        error.response?.data?.message || "Failed to fetch chargeback countries"
      );
    }
  }

  /**
   * Get top chargeback reasons for a user
   */
  async getTopChargebackReasons(
    userId: string,
    params: GetStatsParams = {}
  ): Promise<ChargebackReasonStats[]> {
    try {
      const queryParams = new URLSearchParams();

      if (params.limit) queryParams.append("limit", params.limit.toString());
      if (params.storeId) queryParams.append("storeId", params.storeId);
      if (params.provider) queryParams.append("provider", params.provider);

      const queryString = queryParams.toString();
      const url = `/statistics/${userId}/reasons${
        queryString ? `?${queryString}` : ""
      }`;

      const response = await apiAxios.get<
        StatisticsResponse<ChargebackReasonStats[]>
      >(url);
      return response.data.data || [];
    } catch (error: any) {
      console.error("Error fetching top chargeback reasons:", error);
      throw new Error(
        error.response?.data?.message || "Failed to fetch chargeback reasons"
      );
    }
  }

  /**
   * Get combined chargeback statistics overview for a user
   */
  async getChargebackStatsOverview(
    userId: string,
    params: GetStatsParams = {}
  ): Promise<ChargebackStatsOverview> {
    try {
      const queryParams = new URLSearchParams();

      if (params.limit) queryParams.append("limit", params.limit.toString());
      if (params.storeId) queryParams.append("storeId", params.storeId);
      if (params.provider) queryParams.append("provider", params.provider);

      const queryString = queryParams.toString();
      const url = `/statistics/${userId}/overview${
        queryString ? `?${queryString}` : ""
      }`;

      const response = await apiAxios.get<
        StatisticsResponse<ChargebackStatsOverview>
      >(url);
      return response.data.data || { countries: [], reasons: [] };
    } catch (error: any) {
      console.error("Error fetching chargeback stats overview:", error);
      throw new Error(
        error.response?.data?.message || "Failed to fetch statistics overview"
      );
    }
  }

  /**
   * Reset statistics for a user (for testing purposes)
   */
  async resetUserStatistics(
    userId: string,
    storeId?: string
  ): Promise<{ deletedCountries: number; deletedReasons: number }> {
    try {
      const queryParams = new URLSearchParams();
      if (storeId) queryParams.append("storeId", storeId);

      const queryString = queryParams.toString();
      const url = `/statistics/${userId}/reset${
        queryString ? `?${queryString}` : ""
      }`;

      const response = await apiAxios.delete<
        StatisticsResponse<{ deletedCountries: number; deletedReasons: number }>
      >(url);
      return response.data.data || { deletedCountries: 0, deletedReasons: 0 };
    } catch (error: any) {
      console.error("Error resetting user statistics:", error);
      throw new Error(
        error.response?.data?.message || "Failed to reset statistics"
      );
    }
  }
}

export const statisticsService = new StatisticsService();
