import { apiAxios } from "@/lib/apiAxios";
import { MATCHED_BLOCK_ENDPOINTS } from "@/constants/api-paths";
import { 
  ViewMode, 
  SortBy, 
  TimeRange, 
  BlockType, 
  FeedbackStatus 
} from "@/constants/matched-blocks";
import { AxiosError } from "axios";

// Re-export enums for backward compatibility
export { ViewMode, SortBy, TimeRange, BlockType, FeedbackStatus };

// Define base matched block interface
export interface IBaseMatchedBlock {
  id: string;
  userId: string;
  alertTime: string;
  amount: number;
  currency: string;
  descriptor?: string | null;
  authCode?: string | null;
  cardBin?: string | null;
  cardNumber?: string | null;
  chargebackCode?: string | null;
  disputeAmount?: number | null;
  disputeCurrency?: string | null;
  transactionTime?: string | null;
  provider?: string | null;
  linkedStoreId?: string | null;

  // Feedback status field
  feedbackStatus: FeedbackStatus;

  // Meta fields
  type: BlockType;
  createdAt: Date;
  updatedAt: Date;
}

// Define ETHOCA specific matched block interface
export interface IEthocaMatchedBlock extends IBaseMatchedBlock {
  type: BlockType.ETHOCA;

  // ETHOCA required fields
  age: string;

  // ETHOCA specific fields
  alertSource?: string | null;
  arn?: string | null;
  issuer?: string | null;
  initiatedBy?: string | null;
  liability?: string | null;
  merchantCategoryCode?: string | null;
  transactionId?: string | null;
  transactionType?: string | null;
}

// Define RDR specific matched block interface
export interface IRdrMatchedBlock extends IBaseMatchedBlock {
  type: BlockType.RDR;

  // ETHOCA field (optional for RDR)
  age?: string | null;

  // RDR specific fields
  acquirerBin?: string | null;
  acquirerReferenceNumber?: string | null;
  alertStatus?: string | null;
  caid?: string | null;
  descriptorContact?: string | null;
  ruleName?: string | null;
  ruleType?: string | null;
}

// Combine both matched block types
export type IMatchedBlock = IEthocaMatchedBlock | IRdrMatchedBlock;


export interface MatchedBlockListResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    items: IMatchedBlock[];
    pagination: {
      total: number;
      page: number;
      size: number;
      totalPages: number;
    };
    meta: {
      totalEthoca: number;
      totalRdr: number;
    };
  };
}

export interface MatchedBlockOverviewResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    blocks: {
      count: number;
      blocked: number;
    };
    rate: {
      actualChargeback: string;
      blocked: string;
    };
    goal: {
      actualChargeback: string;
      chargebackGoal: string;
      profitGoalProgress: string;
    };
  };
}

export interface MatchedBlockChargebackRateChartData {
  date: string;
  rate: number;
  disputeCount: number;
  transactionCount: number;
}

export interface MatchedBlockChargebackRateChartResponse {
  success: boolean;
  status: number;
  message: string;
  data: MatchedBlockChargebackRateChartData[]; // Direct array like original block service
}

class MatchedBlockService {
  /**
   * Get matched blocks with support for overview and list views
   *
   * @param userId User ID (required)
   * @param page Current page number (for pagination)
   * @param limit Number of items per page
   * @param filters Filtering options
   * @returns Response with matched blocks or overview data based on the viewMode
   */
  async getMatchedBlocks(
    userId: string,
    page: number = 1,
    limit: number = 10,
    filters?: Record<string, any>
  ): Promise<MatchedBlockListResponse | MatchedBlockOverviewResponse> {
    try {
      // Build query params
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      // Add filters to query params if provided
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            // Handle empty strings by skipping them
            if (typeof value === 'string' && value.trim() === '') {
              return;
            }
            
            if (Array.isArray(value)) {
              // Filter out empty values from arrays
              const filteredArray = value.filter(v => v !== null && v !== undefined && v !== '');
              if (filteredArray.length > 0) {
                queryParams.append(key, filteredArray.join(','));
              }
            } else {
              queryParams.append(key, value.toString().trim());
            }
          }
        });
      }

      // Use default API
      const api = apiAxios;
      const response = await api.get(
        `${MATCHED_BLOCK_ENDPOINTS.LIST}/user/${userId}?${queryParams.toString()}`
      );
      
      return response.data;
    } catch (error: AxiosError | unknown) {
      throw error;
    }
  }

  /**
   * Get chargeback rate chart data for matched blocks
   *
   * @param userId User ID (required)
   * @param filters Filtering options (timeRange, startDate, endDate, etc.)
   * @returns Response with chargeback rate chart data
   */
  async getChargebackRateChart(
    userId: string,
    filters?: Record<string, any>
  ): Promise<MatchedBlockChargebackRateChartResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            // Handle empty strings by skipping them
            if (typeof value === 'string' && value.trim() === '') {
              return;
            }
            
            if (Array.isArray(value)) {
              // Filter out empty values from arrays
              const filteredArray = value.filter(v => v !== null && v !== undefined && v !== '');
              if (filteredArray.length > 0) {
                params.append(key, filteredArray.join(','));
              }
            } else {
              params.append(key, value.toString().trim());
            }
          }
        });
      }

      const api = apiAxios;
      const response = await api.get(
        `${MATCHED_BLOCK_ENDPOINTS.LIST}/chart/chargeback-rate/user/${userId}?${params.toString()}`
      );
      
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new Error(
          error.response?.data?.message || "Failed to fetch chargeback rate chart"
        );
      }
      throw error;
    }
  }

}

export const matchedBlockService = new MatchedBlockService();