import { apiAxios, createAuthenticated<PERSON>pi } from "@/lib/apiAxios";
import { PaymentHistory } from "./payment-history.service";

export interface Bill {
  id: string;
  linkedStoreId: string;
  amount: number;
  currency: string;
  description?: string | null;
  status: string;
  dueDate: string;
  createdAt: string;
  updatedAt: string;
  paymentHistories?: PaymentHistory[];
}

export interface CreateBillData {
  linkedStoreId: string;
  amount: number;
  currency?: string;
  description?: string;
  status?: string;
  dueDate: string;
}

export interface UpdateBillData {
  amount?: number;
  currency?: string;
  description?: string;
  status?: string;
  dueDate?: string;
}

export interface BillQuery {
  page?: number;
  limit?: number;
}

export interface PaginatedBillResponse {
  items: Bill[];
  total: number;
  page: number;
  limit: number;
}

/**
 * Get all bills for a store with pagination
 * @param storeId Store ID
 * @param query Query parameters for pagination
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns Paginated bills response
 */
export const getBillsByStore = async (storeId: string, query?: BillQuery, token?: string, provider?: string): Promise<PaginatedBillResponse> => {
  const params = new URLSearchParams();
  if (query) {
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });
  }
  
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.get(`/bill/store/${storeId}?${params.toString()}`);
  // API response is wrapped in { success: true, data: { items: [...], total, page, limit }, message: "...", status: 200 }
  return response.data.data || { items: [], total: 0, page: 1, limit: 10 };
};

/**
 * Get bill by ID
 * @param id Bill ID
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns Bill
 */
export const getBillById = async (id: string, token?: string, provider?: string): Promise<Bill> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.get(`/bill/${id}`);
  return response.data.data;
};

/**
 * Create a new bill
 * @param data Bill data
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns Created bill
 */
export const createBill = async (data: CreateBillData, token?: string, provider?: string): Promise<Bill> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.post(`/bill`, data);
  return response.data.data;
};

/**
 * Update an existing bill
 * @param id Bill ID
 * @param data Bill data
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns Updated bill
 */
export const updateBill = async (id: string, data: UpdateBillData, token?: string, provider?: string): Promise<Bill> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.put(`/bill/${id}`, data);
  return response.data.data;
};

/**
 * Delete a bill
 * @param id Bill ID
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns Success message
 */
export const deleteBill = async (id: string, token?: string, provider?: string): Promise<{ success: boolean; message: string }> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.delete(`/bill/${id}`);
  return response.data;
};

/**
 * Generate payment link for a bill
 * @param billId Bill ID
 * @param params Additional parameters for payment link generation
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns Payment link data
 */
export const generatePaymentLink = async (
  billId: string, 
  params: {
    stripeAccountId: string;
    amount: number;
    currency?: string;
    description?: string;
  },
  token?: string,
  provider?: string
): Promise<{ url: string }> => {
  const queryParams = new URLSearchParams({
    stripeAccountId: params.stripeAccountId,
    amount: params.amount.toString(),
    ...(params.currency && { currency: params.currency }),
    ...(params.description && { description: params.description })
  });
  
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.get(`/bill/payment-link/${billId}?${queryParams.toString()}`);
  return response.data.data;
};

/**
 * Get payment histories for a specific bill
 * @param billId Bill ID
 * @returns Bill with payment histories
 */
export const getBillPaymentHistories = async (billId: string): Promise<{ bill: Bill; paymentHistories: PaymentHistory[] }> => {
  const response = await apiAxios.get(`/bill/${billId}/payment-histories`);
  return response.data.data;
}; 