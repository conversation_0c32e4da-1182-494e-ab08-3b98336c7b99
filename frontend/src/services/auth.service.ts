import { apiAxios } from "@/lib/apiAxios";
import { AUTH_ENDPOINTS } from "@/constants/api-paths";
import { getSession, signOut } from "next-auth/react";
import { AxiosError } from "axios";
import { signIn } from "next-auth/react";

// Hàm tính thời gian hết hạn của token dựa vào định dạng như '30m', '1h', '30d'
export const calculateExpiryTime = (duration: string): Date => {
  const now = new Date();
  const unit = duration.slice(-1);
  const value = parseInt(duration.slice(0, -1));

  switch (unit) {
    case "m":
      return new Date(now.getTime() + value * 60 * 1000);
    case "h":
      return new Date(now.getTime() + value * 60 * 60 * 1000);
    case "d":
      return new Date(now.getTime() + value * 24 * 60 * 60 * 1000);
    default:
      return new Date(now.getTime() + 30 * 60 * 1000); // Default: 30 minutes
  }
};

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  fullName: string;
  phoneNumber: string;
  code?: string;
  address?: string;
  gender: string;
  birthDate: Date;
  status?: boolean;
  note?: string;
}

export interface ForgotPasswordData {
  email: string;
}

export interface ResetPasswordData {
  token: string;
  password: string;
}

export interface TokenData {
  accessToken: string;
  refreshToken?: string;
  accessTokenExpiresAt?: string;
  refreshTokenExpiresAt?: string;
}

export interface IUser {
  id: string;
  code: string;
  fullName: string;
  phoneNumber: string;
  email: string;
  password: string;
  address?: string | null;
  gender: string;
  birthDate: Date;
  status: boolean;
  note?: string | null;
}

export interface AuthenticatedUser {
  id: string;
  code: string;
  email: string;
  phoneNumber: string;
  roles: string[];
  fullName?: string;
}

export interface AuthResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    id: string;
    code: string;
    email: string;
    phoneNumber: string;
    roles: string[];
    fullName?: string;
    token: {
      accessToken: string;
      refreshToken: string;
      accessTokenExpiresAt?: string;
      refreshTokenExpiresAt?: string;
    };
  };
}

class AuthService {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await apiAxios.post(AUTH_ENDPOINTS.SIGN_IN, credentials);


      await signIn("credentials", {
        authData: JSON.stringify(response.data.data),
        redirect: true,
        callbackUrl: "/dashboard",
      });
      return response.data;
    } catch (error: AxiosError | unknown) {
      throw error;
    }
  }

  async refreshToken(refreshToken: string): Promise<TokenData> {
    try {
      if (!refreshToken) {
        throw new Error("Refresh token không tồn tại");
      }

      const response = await apiAxios.post(AUTH_ENDPOINTS.REFRESH_TOKEN, {
        refreshToken: refreshToken,
      });

      return response.data.data;
    } catch (error) {
      // Đăng xuất người dùng khi gặp lỗi
      await signOut({ redirect: false });
      throw error;
    }
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      const response = await apiAxios.post(AUTH_ENDPOINTS.SIGN_UP, data);
      return response.data;
    } catch (error: AxiosError | unknown) {
      if (error instanceof AxiosError) {
        // Handle specific error
      }
      throw error;
    }
  }

  async forgotPassword(data: ForgotPasswordData): Promise<{ message: string }> {
    try {
      const response = await apiAxios.post(
        AUTH_ENDPOINTS.FORGOT_PASSWORD,
        data
      );
      return response.data;
    } catch (error: AxiosError | unknown) {
      if (error instanceof AxiosError) {
        // Handle specific error
      }
      throw error;
    }
  }

  async resetPassword(data: ResetPasswordData): Promise<{ message: string }> {
    try {
      const response = await apiAxios.post(AUTH_ENDPOINTS.RESET_PASSWORD, data);
      return response.data;
    } catch (error: AxiosError | unknown) {
      if (error instanceof AxiosError) {
        // Handle specific error
      }
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      // Clear localStorage first
      if (typeof window !== 'undefined') {
        localStorage.removeItem('persist:root')
        localStorage.clear()
      }
      
      await signOut({ 
        redirect: false // Handle redirect manually for better control
      });
      
      // Small delay to ensure cleanup before redirect
      setTimeout(() => {
        window.location.href = "/auth/connect";
      }, 100);
    } catch (error) {
      console.error("Logout error:", error);
      
      // Even if signOut fails, clear storage and force redirect
      if (typeof window !== 'undefined') {
        localStorage.clear()
      }
      
      setTimeout(() => {
        window.location.href = "/auth/connect";
      }, 100);
    }
  }

  async getCurrentUser(token: string): Promise<AuthenticatedUser | null> {
    try {
      if (!token) return null;


      // Sử dụng config đặc biệt để override token từ session
      const response = await apiAxios.get(AUTH_ENDPOINTS.PROFILE, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data.data;
    } catch (error: AxiosError | unknown) {
      if (error instanceof AxiosError) {
        // Handle specific error
      }
      return null;
    }
  }

  async updateProfile(
    data: Partial<AuthenticatedUser>
  ): Promise<AuthenticatedUser> {
    try {
      // Sử dụng path tương đối vì đã có baseURL trong apiAxios
      const response = await apiAxios.put(AUTH_ENDPOINTS.PROFILE, data);
      return response.data.data;
    } catch (error: AxiosError | unknown) {
      if (error instanceof AxiosError) {
        // Handle specific error
      }
      throw error;
    }
  }
}

export const authService = new AuthService();
