import { apiAxios } from "@/lib/apiAxios";

export interface Subscription {
  id: string;
  linkedStoreId: string;
  shopifySubscriptionId: string | null;
  shopifyChargeId: string | null;
  planName: string;
  planType: string;
  status: string;
  amount: number;
  currency: string;
  billingInterval: string;
  cappedAmount: number;
  currentUsage: number;
  trialDays: number | null;
  confirmationUrl: string | null;
  returnUrl: string | null;
  activatedAt: string | null;
  lastBilledAt: string | null;
  nextBillingAt: string | null;
  cancelledAt: string | null;
  metadata: any;
  webhookData: any;
  createdAt: string;
  updatedAt: string;
  usageRecords?: UsageRecord[];
}

export interface UsageRecord {
  id: string;
  subscriptionId: string;
  description: string;
  quantity: number;
  amount: number;
  currency: string;
  recordedAt: string;
  metadata: any;
  createdAt: string;
  updatedAt: string;
}

export interface CreateSubscriptionRequest {
  linkedStoreId: string;
}

export interface CreateSubscriptionResponse {
  success: boolean;
  message?: string;
  confirmationUrl?: string;
  error?: string;
}

export interface GetSubscriptionStatusResponse {
  success: boolean;
  data?: Subscription;
  error?: string;
}

class BillingService {
  
  /**
   * Create/Activate a subscription for a store
   * This is called when user clicks "Activate Subscription" on a PENDING subscription
   */
  async createSubscription(linkedStoreId: string, accessToken: string): Promise<CreateSubscriptionResponse> {
    try {
      const response = await apiAxios.post('/billing/create', {
        linkedStoreId
      }, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return response.data;
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Get subscription status for a store
   */
  async getSubscriptionStatus(linkedStoreId: string, accessToken: string): Promise<GetSubscriptionStatusResponse> {
    try {
      const response = await apiAxios.get(`/billing/status/${linkedStoreId}`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return response.data;
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(linkedStoreId: string, accessToken: string): Promise<{ success: boolean; message?: string; error?: string }> {
    try {
      const response = await apiAxios.post(`/billing/cancel/${linkedStoreId}`, {}, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return response.data;
    } catch (error: any) {
      throw error;
    }
  }
}

export const billingService = new BillingService(); 