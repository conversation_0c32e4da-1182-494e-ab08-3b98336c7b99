import { apiAxios, createAuthenticatedApi } from "@/lib/apiAxios";

export interface UsageSummary {
  period: string;
  totalBlocks: number;
  totalAmount: number;
  ethokaBlocks: number;
  rdrBlocks: number;
  planLimit: number;
  planTier: string;
  usagePercentage: number;
  isOverLimit: boolean;
  remainingBlocks: number;
}

export interface BillingPeriod {
  period: string;
  startDate: string;
  endDate: string;
  totalUsage: number;
  blockCount: number;
  status: 'current' | 'past';
}

export interface PeriodDetails {
  period: string;
  totalBlocks: number;
  totalAmount: number;
  dailyBreakdown: Array<{
    date: string;
    count: number;
    amount: number;
  }>;
}

export interface PlanInfo {
  planName: string;
  planTier: string;
  description: string;
}

export interface AccountBalance {
  balance: number;
  formattedBalance: string;
}

class UsageTrackingService {
  /**
   * Get current period usage
   */
  async getCurrentUsage(storeId: string, token?: string, provider?: string): Promise<UsageSummary> {
    const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
    const response = await api.get(`/usage/${storeId}/current`);
    return response.data.data;
  }

  /**
   * Get billing history
   */
  async getBillingHistory(storeId: string, limit: number = 12, token?: string, provider?: string): Promise<BillingPeriod[]> {
    const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
    const response = await api.get(`/usage/${storeId}/history?limit=${limit}`);
    return response.data.data;
  }

  /**
   * Get period details
   */
  async getPeriodDetails(storeId: string, period: string, token?: string, provider?: string): Promise<PeriodDetails> {
    const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
    const response = await api.get(`/usage/${storeId}/period/${period}`);
    return response.data.data;
  }

  /**
   * Get plan info
   */
  async getPlanInfo(storeId: string, token?: string, provider?: string): Promise<PlanInfo> {
    const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
    const response = await api.get(`/usage/${storeId}/plan`);
    return response.data.data;
  }

  /**
   * Get account balance
   */
  async getAccountBalance(storeId: string, token?: string, provider?: string): Promise<AccountBalance> {
    const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
    const response = await api.get(`/usage/${storeId}/balance`);
    return response.data.data;
  }

  /**
   * Format cents to currency string
   */
  formatCurrency(cents: number): string {
    return `$${(cents / 100).toFixed(2)}`;
  }

  /**
   * Format period to readable string
   */
  formatPeriod(period: string | undefined): string {
    if (!period) {
      return 'Unknown Period';
    }
    
    try {
      const [year, month] = period.split('-');
      if (!year || !month) {
        return period; // Return as-is if format is unexpected
      }
      
      const date = new Date(parseInt(year), parseInt(month) - 1);
      return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    } catch (error) {
      console.error('Error formatting period:', error);
      return period || 'Unknown Period';
    }
  }

  /**
   * Get usage percentage class for styling
   */
  getUsageClass(percentage: number): string {
    if (percentage >= 90) return 'text-red-600 bg-red-50';
    if (percentage >= 75) return 'text-yellow-600 bg-yellow-50';
    return 'text-green-600 bg-green-50';
  }

  /**
   * Calculate daily average
   */
  calculateDailyAverage(totalAmount: number, daysInPeriod: number): number {
    return Math.round(totalAmount / daysInPeriod);
  }

  /**
   * Get plan display name
   */
  getPlanDisplayName(planTier: string): string {
    return 'Free Plan';
  }

  /**
   * Start a new subscription cycle
   */
  async startNewSubscriptionCycle(): Promise<{
    success: boolean;
    message?: string;
    data?: {
      confirmationUrl: string;
      chargeId: string;
    };
  }> {
    try {
      const response = await apiAxios.post('/subscription-cycle/new');
      return response.data;
    } catch (error: any) {
      console.error('Error starting new subscription cycle:', error);
      throw error;
    }
  }

  /**
   * Check subscription cycle status
   */
  async getSubscriptionCycleStatus(): Promise<{
    currentPeriod: string;
    currentUsage: number;
    usageLimit: number;
    canStartNewCycle: boolean;
    remainingAmount: number;
    usagePercentage: number;
  }> {
    const response = await apiAxios.get<{ 
      success: boolean; 
      data: {
        currentPeriod: string;
        currentUsage: number;
        usageLimit: number;
        canStartNewCycle: boolean;
        remainingAmount: number;
        usagePercentage: number;
      }
    }>('/subscription-cycle/status');
    return response.data.data;
  }
}

export const usageTrackingService = new UsageTrackingService();