import { apiAxios, createAuthenticatedApi } from '@/lib/apiAxios';

// Interfaces
export interface StoreBalance {
  balance: number;
  currency: string;
  formatted: string;
}

export interface PaymentMethods {
  stripe: boolean;
}

export interface ManualTopupRequest {
  amount: number;
  paymentMethod: 'STRIPE';
}

export interface ManualTopupResponse {
  amount: number;
  paymentMethod: string;
  newBalance: number;
  formatted: string;
}

export interface BalanceHistoryItem {
  id: string;
  type: 'TOPUP' | 'DEDUCTION';
  amount: number;
  description: string;
  status: string;
  createdAt: string;
  paymentMethod: string | null;
}

export interface BalanceHistoryResponse {
  items: BalanceHistoryItem[];
  total: number;
  page: number;
  limit: number;
}

// API functions

/**
 * Get current balance for a store
 * @param storeId Store ID
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 */
export const getStoreBalance = async (storeId: string, token?: string, provider?: string): Promise<StoreBalance> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.get(`/stores/${storeId}/balance`);
  return response.data.data;
};

/**
 * Get available payment methods for a store
 * @param storeId Store ID
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 */
export const getPaymentMethods = async (storeId: string, token?: string, provider?: string): Promise<PaymentMethods> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.get(`/stores/${storeId}/payment-methods`);
  return response.data.data;
};

/**
 * Perform manual topup
 * @param storeId Store ID
 * @param data Manual topup request data
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 */
export const manualTopup = async (
  storeId: string,
  data: ManualTopupRequest,
  token?: string,
  provider?: string
): Promise<ManualTopupResponse> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.post(`/stores/${storeId}/topup`, data);
  return response.data.data;
};

/**
 * Get balance history
 * @param storeId Store ID
 * @param page Page number for pagination
 * @param limit Number of items per page
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 */
export const getBalanceHistory = async (
  storeId: string,
  page: number = 1,
  limit: number = 20,
  token?: string,
  provider?: string
): Promise<BalanceHistoryResponse> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.get(`/stores/${storeId}/balance-history`, {
    params: { page, limit }
  });
  return response.data.data;
};

/**
 * Trigger balance maintenance check for all stores or a specific store
 * This will check and maintain minimum balances for all active stores or a specific store
 * @param storeId Store ID (optional)
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 */
export const triggerBalanceMaintenance = async (storeId?: string, token?: string, provider?: string): Promise<{ message: string }> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  
  if (storeId) {
    // Call specific store maintenance endpoint
    const response = await api.post(`/stores/${storeId}/maintenance/check-balance`);
    return response.data;
  } else {
    // Call all stores maintenance endpoint
    const response = await api.post('/stores/maintenance/check-balances');
    return response.data;
  }
};