import { apiAxios } from "@/lib/apiAxios";
import { ALERT_ENDPOINTS } from "@/constants/api-paths";

export enum AlertType {
  ETHOCA = "ETHOCA",
  RDR = "RDR",
}

export interface AlertTimeframe {
  days?: number;
  startDate?: string;
  endDate?: string;
}

export interface AlertFilter {
  type?: AlertType;
  timeframe?: AlertTimeframe;
  source?: string;
  descriptor?: string;
  country?: string;
  bank?: string;
  outcome?: string;
  cardType?: string;
}

export interface AlertOverview {
  count: number;
  volume: number;
  currency: string;
}

export interface AlertOverviewResponse {
  total: AlertOverview;
  ethoca: AlertOverview;
  rdr: AlertOverview;
}

export interface AlertsByGroup {
  name: string;
  count: number;
  volume: number;
  currency: string;
}

export interface AlertsResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    items: any[];
    total: number;
    pageNumber: number;
    pageSize: number;
    totalPages: number;
  };
}

export class AlertService {
  /**
   * Get all alerts without filtering by type
   * @param page Page number
   * @param pageSize Items per page
   * @param filters Additional filters
   * @returns Paginated list of all alerts
   */
  static async getAllAlerts(
    page: number = 1,
    pageSize: number = 10,
    filters: Record<string, any> = {}
  ): Promise<AlertsResponse> {
    try {
      const response = await apiAxios.get(ALERT_ENDPOINTS.ALERTS, {
        params: {
          page,
          limit: pageSize,
          ...filters,
        },
      });


      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get alerts by type (All, RDR, Ethoca)
   * @param page Page number
   * @param pageSize Items per page
   * @param type Alert type (optional)
   * @param filters Additional filters
   * @returns Paginated list of alerts
   */
  static async getAlertsByType(
    page: number = 1,
    pageSize: number = 10,
    type?: AlertType,
    filters: Record<string, any> = {}
  ): Promise<AlertsResponse> {
    try {
      let url = ALERT_ENDPOINTS.ALERTS;

      // If type is specified, use type-specific endpoint
      if (type) {
        url = ALERT_ENDPOINTS.ALERTS_BY_TYPE(type);
      }

      const response = await apiAxios.get(url, {
        params: {
          page,
          limit: pageSize,
          ...filters,
        },
      });


      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get alert overview stats (count, volume)
   * @param timeframe Timeframe for overview (7days, 30days, all, custom)
   * @returns Overview stats
   */
  static async getAlertOverview(
    timeframe: AlertTimeframe = { days: 30 },
    type?: AlertType
  ): Promise<AlertOverviewResponse> {
    try {
      const params: Record<string, any> = { ...timeframe };
      if (type) {
        params.type = type;
      }

      const response = await apiAxios.get(ALERT_ENDPOINTS.ALERT_OVERVIEW, {
        params,
      });


      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get alerts grouped by source
   * @param filters Filter criteria
   * @returns Alerts grouped by source
   */
  static async getAlertsBySource(
    filters: AlertFilter = {}
  ): Promise<AlertsByGroup[]> {
    try {
      const response = await apiAxios.get(ALERT_ENDPOINTS.ALERTS_BY_SOURCE, {
        params: this.prepareFilters(filters),
      });


      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get alerts grouped by descriptor
   * @param filters Filter criteria
   * @returns Alerts grouped by descriptor
   */
  static async getAlertsByDescriptor(
    filters: AlertFilter = {}
  ): Promise<AlertsByGroup[]> {
    try {
      const response = await apiAxios.get(
        ALERT_ENDPOINTS.ALERTS_BY_DESCRIPTOR,
        {
          params: this.prepareFilters(filters),
        }
      );

      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get alerts grouped by country
   * @param filters Filter criteria
   * @returns Alerts grouped by country
   */
  static async getAlertsByCountry(
    filters: AlertFilter = {}
  ): Promise<AlertsByGroup[]> {
    try {
      const response = await apiAxios.get(ALERT_ENDPOINTS.ALERTS_BY_COUNTRY, {
        params: this.prepareFilters(filters),
      });


      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get alerts grouped by bank
   * @param filters Filter criteria
   * @returns Alerts grouped by bank
   */
  static async getAlertsByBank(
    filters: AlertFilter = {}
  ): Promise<AlertsByGroup[]> {
    try {
      const response = await apiAxios.get(ALERT_ENDPOINTS.ALERTS_BY_BANK, {
        params: this.prepareFilters(filters),
      });


      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get alerts grouped by outcome
   * @param filters Filter criteria
   * @returns Alerts grouped by outcome
   */
  static async getAlertsByOutcome(
    filters: AlertFilter = {}
  ): Promise<AlertsByGroup[]> {
    try {
      const response = await apiAxios.get(ALERT_ENDPOINTS.ALERTS_BY_OUTCOME, {
        params: this.prepareFilters(filters),
      });


      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get alerts grouped by card type
   * @param filters Filter criteria
   * @returns Alerts grouped by card type
   */
  static async getAlertsByCardType(
    filters: AlertFilter = {}
  ): Promise<AlertsByGroup[]> {
    try {
      const response = await apiAxios.get(ALERT_ENDPOINTS.ALERTS_BY_CARD_TYPE, {
        params: this.prepareFilters(filters),
      });


      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Helper method to prepare filter parameters
   * @param filters Alert filters
   * @returns Formatted filters for API
   */
  private static prepareFilters(filters: AlertFilter): Record<string, any> {
    const params: Record<string, any> = {};

    if (filters.type) {
      params.type = filters.type;
    }

    if (filters.timeframe) {
      if (filters.timeframe.days) {
        params.days = filters.timeframe.days;
      } else if (filters.timeframe.startDate && filters.timeframe.endDate) {
        params.startDate = filters.timeframe.startDate;
        params.endDate = filters.timeframe.endDate;
      }
    }

    if (filters.source) params.source = filters.source;
    if (filters.descriptor) params.descriptor = filters.descriptor;
    if (filters.country) params.country = filters.country;
    if (filters.bank) params.bank = filters.bank;
    if (filters.outcome) params.outcome = filters.outcome;
    if (filters.cardType) params.cardType = filters.cardType;

    return params;
  }
}
