import { apiAxios, createAuthenticated<PERSON>pi } from "@/lib/apiAxios";

export interface AlertInfo {
  id: string;
  alertType: 'RDR' | 'ETHOCA';
  descriptor: string;
  bin?: string | null;
  caid?: string | null;
  arn?: string | null;
  storeId: string;
  registrationStatus: 'WAITING' | 'EFFECTED' | 'CLOSING' | 'CLOSED';
  registrationMessage?: string | null;
  createdAt: Date;
  updatedAt: Date;
  registeredAt?: Date | null;
  closedAt?: Date | null;
}

export interface CreateAlertInfoData {
  alertType: 'RDR' | 'ETHOCA';
  descriptor: string;
  bin?: string | null;
  caid?: string | null;
  storeId: string;
}

export interface UpdateAlertInfoData {
  alertType?: 'RDR' | 'ETHOCA';
  descriptor?: string;
  bin?: string | null;
  caid?: string | null;
}

/**
 * Get all alert info accounts for a store
 * @param storeId Store ID
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns List of alert info accounts
 */
export const getAlertInfosByStoreId = async (storeId: string, token?: string, provider?: string): Promise<AlertInfo[]> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.get(`/alert-info/store/${storeId}`);
  return response.data.data;
};

/**
 * Get alert info by ID
 * @param id Alert info ID
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns Alert info
 */
export const getAlertInfoById = async (id: string, token?: string, provider?: string): Promise<AlertInfo> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.get(`/alert-info/${id}`);
  return response.data.data;
};

/**
 * Create a new alert info account
 * @param data Alert info data
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns Created alert info
 */
export const createAlertInfo = async (data: CreateAlertInfoData, token?: string, provider?: string): Promise<AlertInfo> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.post(`/alert-info`, data);
  return response.data.data;
};

/**
 * Update an existing alert info account
 * @param id Alert info ID
 * @param data Alert info data
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns Updated alert info
 */
export const updateAlertInfo = async (id: string, data: UpdateAlertInfoData, token?: string, provider?: string): Promise<AlertInfo> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.put(`/alert-info/${id}`, data);
  return response.data.data;
};

/**
 * Delete an alert info account
 * @param id Alert info ID
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns Success message
 */
export const deleteAlertInfo = async (id: string, token?: string, provider?: string): Promise<{ success: boolean; message: string }> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.delete(`/alert-info/${id}`);
  return response.data;
};
