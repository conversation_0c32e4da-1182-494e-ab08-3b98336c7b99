import { apiAxios } from "@/lib/apiAxios";
import { LINKED_STORE_ENDPOINTS } from "@/constants/api-paths";
import { AxiosError } from "axios";

export interface LinkedStore {
  id: string;
  userId: string;
  storeName: string;
  provider: string;
  providerStoreId?: string | null;
  data: any;
  isActive?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LinkedStoreResponse {
  success: boolean;
  status: number;
  message: string;
  data: LinkedStore[];
}

export interface ProviderIntegrationStatus {
  shopify: boolean;
  stripe: boolean;
  [key: string]: boolean;
}

class LinkedStoreService {
  /**
   * Get all linked stores for a specific user
   */
  static async getLinkedStoresByUserId(userId: string): Promise<LinkedStoreResponse> {
    try {
      console.log("🔍 [LinkedStoreService.getLinkedStoresByUserId] Fetching for userId:", userId);
      
      const response = await apiAxios.get<LinkedStoreResponse>(
        LINKED_STORE_ENDPOINTS.BY_USER_ID(userId)
      );

      console.log("✅ [LinkedStoreService.getLinkedStoresByUserId] Response:", response.data);
      return response.data;
    } catch (error) {
      console.error("❌ [LinkedStoreService.getLinkedStoresByUserId] Error:", error);
      
      if (error instanceof AxiosError) {
        throw new Error(
          error.response?.data?.message || 
          error.message || 
          "Failed to fetch linked stores"
        );
      }
      
      throw new Error("Failed to fetch linked stores");
    }
  }

}

export default LinkedStoreService;