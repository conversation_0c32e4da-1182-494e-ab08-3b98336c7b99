import { apiAxios } from "@/lib/apiAxios"

export interface ChargebackItem {
  id: string
  provider: string
  transactionId?: string
  orderId?: string
  orderName?: string
  chargeId?: string
  paymentIntentId?: string
  status: string
  amount: number
  currency: string
  reason: string
  type?: string
  initiatedAt?: string
  evidenceDueBy?: string
  evidenceSentOn?: string
  finalizedOn?: string
  networkReasonCode?: string
  evidence?: any
  evidenceDetails?: any
  metadata?: any
  createdAt: string
  updatedAt: string
}

export interface ChargebacksResponse {
  chargebacks: ChargebackItem[]
  pagination: {
    page: number
    pageSize: number
    totalCount: number
    totalPages: number
  }
}

export interface ChargebackStats {
  provider: string
  total: number
  totalAmount: number
  byStatus: Record<string, number>
}

export interface GetChargebacksParams {
  page?: number
  pageSize?: number
  status?: string
  startDate?: Date
  endDate?: Date
}

class ChargebackService {
  async getChargebacksByStore(
    storeId: string,
    params?: GetChargebacksParams
  ): Promise<ChargebacksResponse> {
    const queryParams = new URLSearchParams()
    
    if (params?.page) queryParams.append("page", params.page.toString())
    if (params?.pageSize) queryParams.append("pageSize", params.pageSize.toString())
    if (params?.status) queryParams.append("status", params.status)
    if (params?.startDate) queryParams.append("startDate", params.startDate.toISOString())
    if (params?.endDate) queryParams.append("endDate", params.endDate.toISOString())
    
    const queryString = queryParams.toString()
    const url = `/disputes/store/${storeId}${queryString ? `?${queryString}` : ""}`
    
    const response = await apiAxios.get(url)
    return response.data.data
  }

  async getChargebackStats(storeId: string): Promise<ChargebackStats> {
    const response = await apiAxios.get(`/disputes/store/${storeId}/stats`)
    return response.data.data
  }

  async submitDispute(disputeData: any): Promise<any> {
    const response = await apiAxios.post("/disputes/submit", disputeData)
    return response.data.data
  }

  async checkDisputeStatus(merchantOrderNo: string): Promise<any> {
    const response = await apiAxios.get(`/disputes/status/${merchantOrderNo}`)
    return response.data.data
  }

  async submitAdditionalMaterials(merchantOrderNo: string, materials: any): Promise<any> {
    const response = await apiAxios.post(
      `/disputes/${merchantOrderNo}/materials`,
      materials
    )
    return response.data.data
  }
}

export const chargebackService = new ChargebackService()