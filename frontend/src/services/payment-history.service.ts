import { apiAxios, createAuthenticated<PERSON><PERSON> } from "@/lib/apiAxios";
import { Bill } from "./bill.service";

export interface PaymentHistory {
  id: string;
  linkedStoreId: string;
  billId: string;
  amount: number;
  currency: string;
  description?: string | null;
  status: string;
  paymentDate: string;
  createdAt: string;
  updatedAt: string;
  bill?: Bill;
}

export interface CreatePaymentHistoryData {
  linkedStoreId: string;
  billId: string;
  amount: number;
  currency: string;
  description?: string;
  status: string;
  paymentDate: string;
}

export interface UpdatePaymentHistoryData {
  amount?: number;
  currency?: string;
  description?: string;
  status?: string;
  paymentDate?: string;
}

export interface PaymentHistoryQuery {
  id?: string;
  linkedStoreId?: string;
  billId?: string;
  status?: string;
  page?: number;
  limit?: number;
  offset?: number;
}

/**
 * Get all payment history for a store
 * @param storeId Store ID
 * @param query Optional query parameters
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns List of payment history
 */
export interface PaginatedPaymentHistoryResponse {
  items: PaymentHistory[];
  total: number;
  page: number;
  limit: number;
}

export const getPaymentHistoryByStore = async (storeId: string, query?: PaymentHistoryQuery, token?: string, provider?: string): Promise<PaginatedPaymentHistoryResponse> => {
  const params = new URLSearchParams();
  if (query) {
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });
  }
  
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.get(`/payment-history/store/${storeId}?${params.toString()}`);
  // API response is wrapped in { success: true, data: { items: [...], total, page, limit }, message: "...", status: 200 }
  return response.data.data || { items: [], total: 0, page: 1, limit: 10 };
};

/**
 * Get payment history by ID
 * @param id Payment history ID
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns Payment history
 */
export const getPaymentHistoryById = async (id: string, token?: string, provider?: string): Promise<PaymentHistory> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.get(`/payment-history/${id}`);
  return response.data.data;
};

/**
 * Create a new payment history record
 * @param data Payment history data
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns Created payment history
 */
export const createPaymentHistory = async (data: CreatePaymentHistoryData, token?: string, provider?: string): Promise<PaymentHistory> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.post(`/payment-history`, data);
  return response.data.data;
};

/**
 * Update an existing payment history record
 * @param id Payment history ID
 * @param data Payment history data
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns Updated payment history
 */
export const updatePaymentHistory = async (id: string, data: UpdatePaymentHistoryData, token?: string, provider?: string): Promise<PaymentHistory> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.put(`/payment-history/${id}`, data);
  return response.data.data;
};

/**
 * Delete a payment history record
 * @param id Payment history ID
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns Success message
 */
export const deletePaymentHistory = async (id: string, token?: string, provider?: string): Promise<{ success: boolean; message: string }> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.delete(`/payment-history/${id}`);
  return response.data;
};

/**
 * Get payment statistics for a store
 * @param storeId Store ID
 * @param token JWT token for authentication (optional)
 * @param provider Provider for the request (optional)
 * @returns Payment statistics
 */
export const getPaymentStatistics = async (storeId: string, token?: string, provider?: string): Promise<{
  totalAmount: number;
  totalTransactions: number;
  successfulTransactions: number;
  failedTransactions: number;
  averageAmount: number;
}> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.get(`/payment-history/store/${storeId}/statistics`);
  return response.data.data;
}; 