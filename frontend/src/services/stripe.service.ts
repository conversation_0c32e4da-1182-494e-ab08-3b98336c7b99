import { apiAxios } from "@/lib/apiAxios";

export interface StripeAccount {
  id: string;
  storeId: string;
  stripeAccountId: string | null;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  // Card setup properties
  setupCompleted?: boolean;
  stripeCustomerId?: string | null;
  paymentMethodId?: string | null;
  cardLast4?: string | null;
  cardBrand?: string | null;
  billingEmail?: string | null;
  cardholderName?: string | null;
  cardCountry?: string | null;
}

export interface CreateStripeAccountData {
  storeId: string;
  stripeAccountId: string | null;
  status?: string;
}

export interface UpdateStripeAccountData {
  stripeAccountId?: string;
  status?: string;
}

export interface StripeAccountInfo {
  isConnected: boolean;
  stripeAccountId?: string | null;
  status?: string;
  businessName?: string;
  email?: string;
  country?: string;
}

export class StripeService {
  /**
   * Get Stripe OAuth URL for authentication
   * @param userId Optional user ID to include in OAuth state
   * @returns OAuth URL
   */
  static async getAuthUrl(userId?: string): Promise<string> {
    try {
      const params = userId ? { user_id: userId } : {};
      const response = await apiAxios.get(`/stripe/oauth/url`, { params });
      return response.data.authUrl;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get Stripe account info for a store
   * @param storeId Store ID
   * @returns Stripe account info
   */
  static async getStripeAccountByStore(storeId: string): Promise<StripeAccount | null> {
    try {
      const response = await apiAxios.get(`/stripe/accounts/store/${storeId}`);
      return response.data.data;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Create a new Stripe account
   * @param data Stripe account data
   * @returns Created Stripe account
   */
  static async createStripeAccount(data: CreateStripeAccountData): Promise<StripeAccount> {
    const response = await apiAxios.post(`/stripe/accounts`, data);
    return response.data.data;
  }

  /**
   * Update an existing Stripe account
   * @param id Stripe account ID
   * @param data Stripe account data
   * @returns Updated Stripe account
   */
  static async updateStripeAccount(id: string, data: UpdateStripeAccountData): Promise<StripeAccount> {
    const response = await apiAxios.put(`/stripe/accounts/${id}`, data);
    return response.data.data;
  }

  /**
   * Delete/disconnect a Stripe account
   * @param id Stripe account ID
   * @returns Success message
   */
  static async deleteStripeAccount(id: string): Promise<{ success: boolean; message: string }> {
    const response = await apiAxios.delete(`/stripe/accounts/${id}`);
    return response.data;
  }

  /**
   * Get a store by its ID (similar to Shopify service)
   * @param storeId The ID of the store to get
   * @returns The store with the specified ID
   */
  static async getStoreById(storeId: string): Promise<any> {
    try {
      const response = await apiAxios.get(`/stripe/stores/${storeId}`);
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Refresh store details from Stripe
   * @param storeId The ID of the store to refresh
   * @returns Updated store data
   */
  static async refreshStoreDetails(storeId: string): Promise<any> {
    try {
      const response = await apiAxios.get(`/stripe/stores/${storeId}/refresh`);
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Disconnect a Stripe integration
   * @param storeId The ID of the linked store to disconnect
   * @returns Success message
   */
  static async disconnectIntegration(storeId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiAxios.delete(`/stripe/oauth/disconnect/${storeId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Disconnect a linked store (legacy method for backward compatibility)
   * @param storeId The ID of the store to disconnect
   */
  static async disconnectStore(storeId: string): Promise<void> {
    try {
      await apiAxios.delete(`/stripe/stores/${storeId}`);
    } catch (error) {
      throw error;
    }
  }
}

// ===== NEW CARD SETUP METHODS =====

export interface SetupIntentResponse {
  clientSecret: string;
  customerId: string;
}

export interface CardData {
  last4: string;
  brand: string;
  country: string;
  cardholderName: string;
  billingEmail: string;
}

export interface ConfirmCardSetupData {
  storeId: string;
  paymentMethodId: string;
  cardData: CardData;
}

/**
 * Create setup intent for card collection
 * @param storeId Store ID
 * @param provider Provider type (optional)
 * @returns Setup intent client secret and customer ID
 */
export const createSetupIntent = async (storeId: string, provider?: string): Promise<SetupIntentResponse> => {
  console.log('createSetupIntent called with storeId:', storeId, 'provider:', provider);
  const payload = provider ? { storeId, provider } : { storeId };
  console.log('Request payload:', payload);
  const response = await apiAxios.post(`/stripe/setup-intent`, payload);
  console.log('createSetupIntent response:', response.data);
  return response.data.data;
};

/**
 * Confirm card setup and save payment method
 * @param data Card setup confirmation data
 * @returns Success result
 */
export const confirmCardSetup = async (data: ConfirmCardSetupData): Promise<{ success: boolean; message: string }> => {
  const response = await apiAxios.post(`/stripe/confirm-setup`, data);
  return response.data;
};

/**
 * Get card setup status for a store
 * @param storeId Store ID
 * @returns Card setup status and info
 */
export const getCardSetupStatus = async (storeId: string): Promise<{
  setupCompleted: boolean;
  cardInfo?: {
    last4?: string;
    brand?: string;
    cardholderName?: string;
  };
}> => {
  const response = await apiAxios.get(`/stripe/card-status/${storeId}`);
  return response.data.data;
};

// ===== PAYMENT INTENT METHODS =====

export interface PaymentIntentResponse {
  clientSecret: string;
  paymentIntentId: string;
}

export interface CreatePaymentData {
  storeId: string;
  amount: number;
  currency: string;
  description?: string;
}

export interface ConfirmPaymentData {
  storeId: string;
  paymentIntentId: string;
  paymentMethodId: string;
}

/**
 * Create payment intent for processing payment
 * @param data Payment data
 * @returns Payment intent client secret and ID
 */
export const createPaymentIntent = async (data: CreatePaymentData): Promise<PaymentIntentResponse> => {
  const response = await apiAxios.post(`/stripe/payment-intent`, data);
  return response.data.data;
};

/**
 * Confirm payment and process transaction
 * @param data Payment confirmation data
 * @returns Success result
 */
export const confirmPayment = async (data: ConfirmPaymentData): Promise<{ success: boolean; message: string; paymentId?: string }> => {
  const response = await apiAxios.post(`/stripe/confirm-payment`, data);
  return response.data;
};

// Legacy exports for backward compatibility
export const getStripeAccountByStore = StripeService.getStripeAccountByStore;
export const createStripeAccount = StripeService.createStripeAccount;
export const updateStripeAccount = StripeService.updateStripeAccount;
export const deleteStripeAccount = StripeService.deleteStripeAccount;