import { apiAxios, createAuthenticated<PERSON><PERSON> } from "@/lib/apiAxios";
import { BLOCK_ENDPOINTS } from "@/constants/api-paths";
import { AxiosError } from "axios";

// Block types mirroring Alert types
export enum BlockType {
  ETHOCA = "ETHOCA",
  RDR = "RDR",
}

// Define feedback status enum
export enum FeedbackStatus {
  PENDING = "PENDING",
  SENT = "SENT",
  FAILED = "FAILED",
}

export type ViewMode = "dashboard" | "count" | "volume";

// Define base block interface with common fields
export interface IBaseBlock {
  id: string;
  alertId: string;
  alertTime: string;
  alertType: string;
  amount: number;
  currency: string;
  descriptor: string;
  authCode?: string | null;
  cardBin?: string | null;
  cardNumber?: string | null;
  chargebackCode?: string | null;
  disputeAmount?: number | null;
  disputeCurrency?: string | null;
  transactionTime?: string | null;

  // Feedback status field
  feedbackStatus?: FeedbackStatus;

  // Meta fields
  type: BlockType;
  createdAt: Date;
  updatedAt: Date;
}

// Define ETHOCA specific block interface
export interface IEthocaBlock extends IBaseBlock {
  type: BlockType.ETHOCA;

  // ETHOCA required fields
  age: string;

  // ETHOCA specific fields
  alertSource?: string | null;
  arn?: string | null;
  issuer?: string | null;
  initiatedBy?: string | null;
  liability?: string | null;
  merchantCategoryCode?: string | null;
  transactionId?: string | null;
  transactionType?: string | null;
}

// Define RDR specific block interface
export interface IRdrBlock extends IBaseBlock {
  type: BlockType.RDR;

  // ETHOCA field (optional for RDR)
  age?: string | null;

  // RDR specific fields
  acquirerBin?: string | null;
  acquirerReferenceNumber?: string | null;
  alertStatus?: string | null;
  caid?: string | null;
  descriptorContact?: string | null;
  ruleName?: string | null;
  ruleType?: string | null;
}

// Combine both block types
export type IBlock = IEthocaBlock | IRdrBlock;


export interface BlockResponse {
  success: boolean;
  status: number;
  message: string;
  data: IBlock;
}

export interface BlockDashboardResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    blocks: {
      count: number;
      blocked: number;
    };
    rate: {
      actualChargeback: number;
      blocked: number;
    };
    goal: {
      actualChargeback: number;
      chargebackGoal: number;
      profitGoalProgress: number;
    }
  };
}

export interface BlockListResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    items: IBlock[];
    total: number;
    page: number;
    size: number;
    totalPages: number;
  };
}

// Define specialized response types for dashboard data
export interface BlocksByGroup {
  name: string; // Group name (e.g., card type, bank name)
  count: number; // Number of blocks in the group
  volume: number; // Total amount in the group
  value: number; // The value to display (either count or volume depending on viewMode)
  currency: string; // Currency of the value
}

export interface BlocksGroupResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    items: BlocksByGroup[];
  };
}

export interface BlockOverviewResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    total: number | {
      count: number;
      volume: number;
      currency: string;
    };
    ethoca: number | {
      count: number;
      volume: number;
      currency: string;
    };
    rdr: number | {
      count: number;
      volume: number;
      currency: string;
    };
  };
}

export interface BlockTrendResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    items: { date: string; count: number; value: number }[];
  };
}


export interface ChargebackRateChartData {
  date: string;
  rate: number;
  blockedCount: number;
  transactionCount: number;
}

export interface ChargebackRateChartResponse {
  success: boolean;
  status: number;
  message: string;
  data: ChargebackRateChartData[];
}

class BlockService {
  /**
   * Get blocks with support for grouping and filtering
   *
   * @param page Current page number (for pagination)
   * @param limit Number of items per page
   * @param filters Filtering and grouping options
   * @param token JWT token for authentication (optional)
   * @param provider Provider for the request (optional)
   * @returns Response with blocks or grouped data based on the filters
   */
  async getBlocks(
    page: number = 1,
    limit: number = 10,
    filters?: Record<string, any>,
    token?: string,
    provider?: string
  ): Promise<
    | BlockListResponse
    | BlocksGroupResponse
    | BlockDashboardResponse
    | BlockOverviewResponse
    | BlockTrendResponse
  > {
    try {
      // Build query params
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      // Add filters to query params if provided
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              queryParams.append(key, value.join(','));
            } else {
              queryParams.append(key, value.toString());
            }
          }
        });
      }

      // Use authenticated API if token is provided
      const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
      const response = await api.get(
        `${BLOCK_ENDPOINTS.LIST}?${queryParams.toString()}`
      );
      
      return response.data;
    } catch (error: AxiosError | unknown) {
      throw error;
    }
  }

  async getBlockById(id: string, token?: string, provider?: string): Promise<BlockResponse> {
    try {
      const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
      const response = await api.get(BLOCK_ENDPOINTS.DETAIL(id));
      return response.data;
    } catch (error: AxiosError | unknown) {
      throw error;
    }
  }

  async createBlock(
    blockData: Omit<IBaseBlock, "id" | "createdAt" | "updatedAt">,
    token?: string,
    provider?: string
  ): Promise<BlockResponse> {
    try {
      const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
      const response = await api.post(BLOCK_ENDPOINTS.CREATE, blockData);
      return response.data;
    } catch (error: AxiosError | unknown) {
      throw error;
    }
  }

  async updateBlock(
    id: string,
    blockData: Partial<Omit<IBaseBlock, "id" | "createdAt" | "updatedAt">>,
    token?: string,
    provider?: string
  ): Promise<BlockResponse> {
    try {
      const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
      const response = await api.put(
        BLOCK_ENDPOINTS.UPDATE(id),
        blockData
      );
      return response.data;
    } catch (error: AxiosError | unknown) {
      throw error;
    }
  }

  async deleteBlock(
    id: string,
    token?: string,
    provider?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
      const response = await api.delete(BLOCK_ENDPOINTS.DELETE(id));
      return response.data;
    } catch (error: AxiosError | unknown) {
      throw error;
    }
  }



  async getChargebackRateChart(
    filters?: Record<string, any>,
    token?: string,
    provider?: string
  ): Promise<ChargebackRateChartResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              params.append(key, value.join(','));
            } else {
              params.append(key, value.toString());
            }
          }
        });
      }

      const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
      const response = await api.get(
        `${BLOCK_ENDPOINTS.CHARGEBACK_RATE_CHART}?${params.toString()}`
      );
      
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new Error(
          error.response?.data?.message || "Failed to fetch chargeback rate chart"
        );
      }
      throw error;
    }
  }
}

export const blockService = new BlockService();
