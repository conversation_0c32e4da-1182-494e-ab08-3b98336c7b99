import { apiAxios, createAuthenticatedApi } from "@/lib/apiAxios";

// Types
export interface StoreSetting {
  id: string;
  storeId: string;
  name: string;
  value: string;
  createdAt: string;
  updatedAt: string;
}

export interface StoreSettings {
  [key: string]: StoreSetting;
}

export interface UpdateStoreSettingsRequest {
  settings: { [key: string]: string };
}

// Setting names constants
export const SETTING_NAMES = {
  MONTHLY_GOAL: "monthly_goal",
  ALL_TIME_GOAL: "all_time_goal",
  CURRENCY: "currency",
  TIMEZONE: "timezone",
  NOTIFICATION_EMAIL: "notification_email",
} as const;

/**
 * Get all store settings for a store
 * @param storeId Store ID
 * @param token JWT token for authentication (optional)
 * @returns Store settings object
 */
export const getStoreSettings = async (storeId: string, token?: string, provider?: string): Promise<StoreSettings> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.get(`/store-settings/${storeId}`);
  return response.data.data;
};

/**
 * Get a specific setting by name
 * @param storeId Store ID
 * @param name Setting name
 * @param token JWT token for authentication (optional)
 * @returns Store setting
 */
export const getStoreSetting = async (storeId: string, name: string, token?: string, provider?: string): Promise<StoreSetting> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.get(`/store-settings/${storeId}/${name}`);
  return response.data.data;
};

/**
 * Create or update a single setting
 * @param storeId Store ID
 * @param name Setting name
 * @param value Setting value
 * @param token JWT token for authentication (optional)
 * @returns Updated store setting
 */
export const upsertStoreSetting = async (storeId: string, name: string, value: string, token?: string, provider?: string): Promise<StoreSetting> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.post(`/store-settings/${storeId}/${name}`, { value });
  return response.data.data;
};

/**
 * Update multiple settings at once
 * @param storeId Store ID
 * @param settings Settings object
 * @param token JWT token for authentication (optional)
 * @returns Updated store settings
 */
export const updateStoreSettings = async (storeId: string, settings: { [key: string]: string }, token?: string, provider?: string): Promise<StoreSettings> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  const response = await api.put(`/store-settings/${storeId}`, { settings });
  return response.data.data;
};

/**
 * Delete a specific setting
 * @param storeId Store ID
 * @param name Setting name
 * @param token JWT token for authentication (optional)
 */
export const deleteStoreSetting = async (storeId: string, name: string, token?: string, provider?: string): Promise<void> => {
  const api = token ? createAuthenticatedApi(token, provider) : apiAxios;
  await api.delete(`/store-settings/${storeId}/${name}`);
};

/**
 * Delete all settings for a store
 */
export const deleteAllStoreSettings = async (storeId: string): Promise<void> => {
  await apiAxios.delete(`/store-settings/${storeId}`);
};

/**
 * Get monthly goal setting
 * @param storeId Store ID
 * @param token JWT token for authentication (optional)
 * @returns Monthly goal value or 0 if not set
 */
export const getMonthlyGoal = async (storeId: string, token?: string, provider?: string): Promise<number> => {
  try {
    const setting = await getStoreSetting(storeId, SETTING_NAMES.MONTHLY_GOAL, token, provider);
    return parseFloat(setting.value) || 0;
  } catch (error) {
    return 0;
  }
};

/**
 * Get all-time goal setting
 * @param storeId Store ID
 * @param token JWT token for authentication (optional)
 * @returns All-time goal value or 0 if not set
 */
export const getAllTimeGoal = async (storeId: string, token?: string, provider?: string): Promise<number> => {
  try {
    const setting = await getStoreSetting(storeId, SETTING_NAMES.ALL_TIME_GOAL, token, provider);
    return parseFloat(setting.value) || 0;
  } catch (error) {
    return 0;
  }
};

/**
 * Get currency setting
 * @param storeId Store ID
 * @param token JWT token for authentication (optional)
 * @returns Currency code or "USD" if not set
 */
export const getCurrency = async (storeId: string, token?: string, provider?: string): Promise<string> => {
  try {
    const setting = await getStoreSetting(storeId, SETTING_NAMES.CURRENCY, token, provider);
    return setting.value || "USD";
  } catch (error) {
    return "USD";
  }
};

/**
 * Set monthly goal setting
 * @param storeId Store ID
 * @param goal Goal value
 * @param token JWT token for authentication (optional)
 * @returns Updated store setting
 */
export const setMonthlyGoal = async (storeId: string, goal: number, token?: string, provider?: string): Promise<StoreSetting> => {
  return upsertStoreSetting(storeId, SETTING_NAMES.MONTHLY_GOAL, goal.toString(), token, provider);
};

export const setAllTimeGoal = async (storeId: string, goal: number, token?: string, provider?: string): Promise<StoreSetting> => {
  return upsertStoreSetting(storeId, SETTING_NAMES.ALL_TIME_GOAL, goal.toString(), token, provider);
}; 