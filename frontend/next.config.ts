import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  compiler: {
    removeConsole: process.env.NEXT_PUBLIC_NODE === 'production'
  },
  reactStrictMode: false, // Disable double rendering in development
  async redirects() {
    return [
      {
        source: '/',
        destination: '/auth/connect',
        permanent: true,
      },
      {
        source: '/payments',
        destination: '/payments/bills',
        permanent: true,
      },
      {
        source: '/settings',
        destination: '/settings/alerts',
        permanent: true,
      },
    ]
  },
};

export default nextConfig;
