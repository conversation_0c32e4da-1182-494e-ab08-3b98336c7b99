NEXT_PUBLIC_NODE=test
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=https://api-psa-chargeback.vercel.app
NEXT_PUBLIC_API_KEY=54024e100df94c390d8e2774c4f39f5729e28549
NEXT_PUBLIC_ACCESS_TOKEN_EXPIRE=30m
NEXT_PUBLIC_REFRESH_TOKEN_EXPIRE=30d

AUTH_TRUST_HOST=true
AUTH_URL=http://localhost:3000
AUTH_SECRET=1C3E4jxqnMWnU6M7UaTL4Im6JrKjB0G2


# Stripe configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51JVUtwBC7gaMHZRUX3J4iyES5w4ZFFoLzPXrLzdCgZZEboUSk5stkWYRUbfqMMlmmeICHdlSnUuVaqD91ODbbB3o00H2m7g2KG

# Crisp Chat Configuration
# Get this from: Crisp Dashboard > Settings > Workspace Settings > Setup & Integrations
NEXT_PUBLIC_CRISP_SECRET_KEY=your_crisp_secret_key_here
