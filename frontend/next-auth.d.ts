import { JWT } from "next-auth/jwt";
import { AuthenticatedUser } from "@/services/auth.service";

// Mở rộng kiểu dữ liệu cho NextAuth với format sử dụng LinkedStore ID
declare module "next-auth" {
  interface User {
    id?: string; // LinkedStore ID (primary identifier)
    user_id?: string; // User ID
    shopify_store_id?: string; // Shopify store ID
    name?: string;
    email?: string;
    domain?: string;
    myshopify_domain?: string;
    access_token?: string;
    provider?: string; // Auth provider (shopify, stripe, credentials, etc.)
  }

  interface Session {
    user: {
      id?: string; // LinkedStore ID (primary identifier)
      user_id?: string; // User ID
      shopify_store_id?: string; // Shopify store ID
      name?: string;
      email?: string;
      domain?: string;
      myshopify_domain?: string;
      access_token?: string;
      provider?: string; // Auth provider (shopify, stripe, credentials, etc.)
    };
  }

  interface JWT {
    id?: string; // LinkedStore ID (primary identifier)
    user_id?: string; // User ID
    shopify_store_id?: string; // Shopify store ID
    name?: string;
    email?: string;
    domain?: string;
    myshopify_domain?: string;
    access_token: string;
    provider?: string; // Auth provider (shopify, stripe, credentials, etc.)
  }
}

declare module "next-auth/providers/credentials" {
  interface CredentialInput {
    authData?: string;
  }
}
