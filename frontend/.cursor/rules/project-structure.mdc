---
description: 
globs: 
alwaysApply: false
---
# Project Structure

This is a Next.js application using the App Router with TypeScript, Tailwind CSS, and Shadcn UI components.

## Main Directories

- [src/app](mdc:src/app) - Contains all pages and routes using Next.js App Router
- [src/components](mdc:src/components) - Reusable UI components 
- [src/lib](mdc:src/lib) - Utility libraries and configurations
- [src/hooks](mdc:src/hooks) - Custom React hooks
- [src/services](mdc:src/services) - API and external service integrations
- [src/types](mdc:src/types) - TypeScript type definitions
- [src/utils](mdc:src/utils) - Utility functions
- [src/constants](mdc:src/constants) - Constant values and configurations

## Routing Structure

- [src/app/layout.tsx](mdc:src/app/layout.tsx) - Root layout component
- [src/app/page.tsx](mdc:src/app/page.tsx) - Homepage component
- [src/app/(public)](mdc:src/app/(public)) - Public pages (no auth required)
- [src/app/(protected)](mdc:src/app/(protected)) - Protected pages (auth required)
- [src/app/api](mdc:src/app/api) - API routes

## Component Organization

- [src/components/ui](mdc:src/components/ui) - Shadcn UI components
- [src/components/layout](mdc:src/components/layout) - Layout components
- [src/components/system](mdc:src/components/system) - System/application specific components
- [src/components/svg](mdc:src/components/svg) - SVG icon components

## Authentication

- [src/auth.ts](mdc:src/auth.ts) - Auth configuration using NextAuth
- [src/middleware.ts](mdc:src/middleware.ts) - Middleware for authentication and route protection
