---
description:
globs:
alwaysApply: false
---
# No Console Logs Rule

## Frontend Logging Restrictions

**NEVER use console.log, console.error, console.warn, or other console methods in frontend code.** These logs are visible to users in production and can expose sensitive information or appear unprofessional.

### ❌ Prohibited Patterns

```tsx
// ❌ NEVER do this - visible to users in production
console.log("User data:", userData);
console.error("API failed:", error);
console.warn("Deprecated feature used");
console.debug("Debug info:", debugData);
console.info("Info message");

// ❌ Even conditional console logs are prohibited
if (process.env.NODE_ENV === 'development') {
  console.log("Dev only log");
}

// ❌ Console logs in any frontend component/page
function MyComponent() {
  console.log("Component rendered"); // NEVER
  return <div>Hello</div>;
}
```

### ✅ Approved Alternatives

#### For Error Handling:
```tsx
// ✅ Use proper error reporting
import { reportError } from "@/utils/error-reporting";

try {
  await apiCall();
} catch (error) {
  reportError(error); // Sends to error tracking service
  toast.error("Something went wrong");
}
```

#### For User Feedback:
```tsx
// ✅ Use toast notifications for user-visible messages
import { toast } from "sonner";

const handleAction = async () => {
  try {
    await performAction();
    toast.success("Action completed successfully");
  } catch (error) {
    toast.error("Action failed");
  }
};
```

### Development Tools

If you need debugging information during development:

1. **Use browser developer tools breakpoints**
2. **Use React Developer Tools**
3. **Use toast notifications for user feedback**

### Exception

The ONLY exception is in development-specific files that are never included in production builds:
- Development configuration files
- Development-only utilities
- Test files

### Implementation

When generating any frontend code:
1. **Never include console.log statements**
2. **Use toast notifications for user feedback**
3. **Use proper error handling with user-friendly messages**
4. **Consider if the information is actually needed by the user**

Remember: If users need to know something, show it in the UI. If developers need to know something, use proper development tools.
