---
description: 
globs: 
alwaysApply: false
---
# Authentication and Routing Architecture

This project uses Next.js App Router with NextAuth for authentication and route protection.

## Authentication

Authentication is implemented using NextAuth.js with the following setup:

- [src/auth.ts](mdc:src/auth.ts) - Main authentication configuration
- [src/middleware.ts](mdc:src/middleware.ts) - Middleware for route protection
- [next-auth.d.ts](mdc:next-auth.d.ts) - TypeScript definitions for NextAuth

## Route Structure

The application follows Next.js App Router conventions with route groups:

### Public Routes

- [src/app/(public)](mdc:src/app/(public)) - Public pages accessible without authentication
- [src/app/page.tsx](mdc:src/app/page.tsx) - Main landing page

### Protected Routes

- [src/app/(protected)](mdc:src/app/(protected)) - Protected routes requiring authentication
- [src/app/(protected)/layout.tsx](mdc:src/app/(protected)/layout.tsx) - Layout for protected routes

Protected routes include:

- [src/app/(protected)/dashboard](mdc:src/app/(protected)/dashboard) - Dashboard pages
- [src/app/(protected)/shopify](mdc:src/app/(protected)/shopify) - Shopify integration
- [src/app/(protected)/integrations](mdc:src/app/(protected)/integrations) - Integration management
- [src/app/(protected)/credits](mdc:src/app/(protected)/credits) - User credits
- [src/app/(protected)/alerts](mdc:src/app/(protected)/alerts) - User alerts

### API Routes

- [src/app/api](mdc:src/app/api) - API routes
  
## Route Guards

Route protection is implemented in the following ways:

1. Next.js Middleware - Redirects unauthenticated users to login
2. Protected Layout - Additional server-side checks in layout components
3. Client Components - useSession hook for client-side protection

## Session Management

- Server-side: Using the auth() function to get session data
- Client-side: Using the useSession() hook from next-auth/react
