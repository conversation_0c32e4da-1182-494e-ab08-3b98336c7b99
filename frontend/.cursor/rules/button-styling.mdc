---
description:
globs:
alwaysApply: false
---
# Button Styling Rules

## Cursor Pointer Requirement

All buttons in the application must include the `cursor-pointer` class to ensure proper user interaction feedback.

### Rule: Button Cursor Styling

**Always add `cursor-pointer` to button className:**

```tsx
// ✅ Correct - All buttons should have cursor-pointer
<Button 
  className="cursor-pointer bg-blue-600 hover:bg-blue-700"
  onClick={handleClick}
>
  Click Me
</Button>

// ✅ Correct - Even when combining with other classes
<Button 
  variant="outline"
  className="cursor-pointer border-red-700 text-red-400 hover:bg-red-950/50"
  onClick={handleAction}
>
  Delete
</Button>

// ❌ Incorrect - Missing cursor-pointer
<Button 
  className="bg-blue-600 hover:bg-blue-700"
  onClick={handleClick}
>
  Click Me
</Button>
```

### Application Scope

This rule applies to:
- All `<Button>` components from shadcn/ui
- Custom button components
- Clickable elements that act as buttons
- Form submit buttons
- Action buttons in dialogs, cards, and forms

### Exceptions

The only exception is when buttons are disabled:
```tsx
// Disabled buttons should not have cursor-pointer
<Button 
  disabled={isLoading}
  className={`${!isLoading ? 'cursor-pointer' : ''} bg-blue-600`}
>
  {isLoading ? 'Loading...' : 'Submit'}
</Button>
```

### Implementation

When creating or modifying any button component, always ensure `cursor-pointer` is included in the className prop to provide consistent user experience across the application.
