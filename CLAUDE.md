# Project Conventions

## Loading States

When implementing loading states:

1. Use the `<Loading />` component instead of skeletons for page-level loading
2. Import the Loading component from "@/components/ui/loading"
3. Example usage:
   ```tsx
   import { Loading } from "@/components/ui/loading";
   
   // Basic usage
   <Loading />
   
   // With custom size
   <Loading size="lg" />
   
   // With custom text
   <Loading text="Fetching data..." />
   
   // With custom className
   <Loading className="h-full" />
   ```

4. Only use skeleton loaders for inline components where a full-page loading spinner would be disruptive
5. Prefer showing loading spinners on buttons for actions rather than full-page loading states when possible