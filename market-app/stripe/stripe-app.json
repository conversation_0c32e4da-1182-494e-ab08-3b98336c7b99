{"id": "com.psa-stripe.quantchargeback", "version": "1.0.13", "name": "Quantchargeback", "icon": "./logo.ico", "permissions": [{"permission": "connected_account_read", "purpose": "Access connected account data for risk analysis"}, {"permission": "charge_read", "purpose": "Monitor charges for potential chargebacks"}, {"permission": "payment_intent_read", "purpose": "Track payment intent status and risk factors"}, {"permission": "dispute_read", "purpose": "Access dispute data for alerts"}, {"permission": "dispute_write", "purpose": "Submit dispute evidence automatically"}, {"permission": "customer_read", "purpose": "Access customer data for risk analysis"}, {"permission": "event_read", "purpose": "Read webhook events for real-time monitoring"}, {"permission": "charge_write", "purpose": "Issue refunds to customers"}, {"permission": "payment_intent_write", "purpose": "Process payment refunds"}], "connect_permissions": null, "allowed_redirect_uris": ["https://api.quantchargeback.com/stripe/oauth/callback", "https://be.vinast.com/stripe/oauth/callback", "https://v2-api.quantchargeback.com/stripe/oauth/callback", "https://powerful-loyal-glider.ngrok-free.app/stripe/oauth/callback"], "stripe_api_access_type": "o<PERSON>h", "distribution_type": "public", "sandbox_install_compatible": true}