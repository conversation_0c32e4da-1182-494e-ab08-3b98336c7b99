{"id": "com.psa-private.quantchargeback", "version": "1.0.7", "name": "Quantchargeback", "icon": "./logo.ico", "permissions": [{"permission": "connected_account_read", "purpose": "Access connected account data for risk analysis"}, {"permission": "charge_read", "purpose": "Monitor charges for potential chargebacks"}, {"permission": "payment_intent_read", "purpose": "Track payment intent status and risk factors"}, {"permission": "dispute_read", "purpose": "Access dispute data for alerts"}, {"permission": "dispute_write", "purpose": "Submit dispute evidence automatically"}, {"permission": "customer_read", "purpose": "Access customer data for risk analysis"}, {"permission": "event_read", "purpose": "Read webhook events for real-time monitoring"}, {"permission": "charge_write", "purpose": "Issue refunds to customers"}, {"permission": "payment_intent_write", "purpose": "Process payment refunds"}], "connect_permissions": null, "allowed_redirect_uris": ["https://powerful-loyal-glider.ngrok-free.app/stripe/oauth/callback", "https://api.quantchargeback.com/stripe/oauth/callback", "https://api-staging.quantchargeback.com/stripe/oauth/callback"], "stripe_api_access_type": "o<PERSON>h", "distribution_type": "private", "sandbox_install_compatible": true}