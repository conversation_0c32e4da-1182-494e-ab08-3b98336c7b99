{"name": "ledinhcuong.com-api", "version": "1.0.0", "description": "LE DINH CUONG API", "main": "src/server.ts", "scripts": {"start": "node build/server.js", "build": "yarn prisma:generate && yarn build:production", "prebuild": "npx prisma generate", "postinstall": "npx prisma generate", "ts-check": "tsc --project tsconfig.json", "server:start:staging": "yarn build:staging && cross-env NODE_ENV=staging pm2 start build/server.js --name ledinhcuong.com-api-staging", "server:start:production": "yarn build:production && cross-env NODE_ENV=production pm2 start build/server.js --name ledinhcuong.com-api-production", "server:stop:staging": "pm2 stop ledinhcuong.com-api-staging", "server:stop:production": "pm2 stop ledinhcuong.com-api-production", "server:restart:staging": "yarn build:staging && cross-env NODE_ENV=staging pm2 restart ledinhcuong.com-api-staging", "server:restart:production": "yarn build:production && cross-env NODE_ENV=production pm2 restart ledinhcuong.com-api-production", "server:delete:staging": "pm2 delete ledinhcuong.com-api-staging", "server:delete:production": "pm2 delete ledinhcuong.com-api-production", "server:logs:staging": "pm2 logs ledinhcuong.com-api-staging", "server:logs:production": "pm2 logs ledinhcuong.com-api-production", "development": "cross-env NODE_ENV=development nodemon src/server.ts", "staging": "yarn build:staging && cross-env NODE_ENV=staging node build/server.js", "production": "yarn build:production && cross-env NODE_ENV=production node build/server.js", "build:staging": "rm -rf build && tsc && tsc-alias && mkdir -p build/logs", "build:production": "rm -rf build && tsc && tsc-alias && mkdir -p build/logs", "postman:sync": "mkdir -p postman/export && rm -f postman/export/*.json && npx ts-node -r tsconfig-paths/register postman/sync-postman.ts", "test": "jest", "test:watch": "jest --watch", "test:billing": "npx ts-node -r tsconfig-paths/register src/scripts/test-billing-flows.ts all", "test:billing:logic": "npx ts-node -r tsconfig-paths/register src/scripts/test-billing-logic.ts", "test:coverage": "jest --coverage", "seed": "npx ts-node -r tsconfig-paths/register prisma/seeds/index.ts", "prisma:migrate:dev": "prisma migrate dev", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:generate": "prisma generate", "prisma:studio": "prisma studio", "prisma:format": "prisma format", "db:reset": "prisma migrate reset", "dev": "cross-env NODE_ENV=development nodemon src/server.ts", "vercel:production": "yarn build && vercel --prod"}, "repository": {"type": "git", "url": "git+https://github.com/ledhcg/ledinhcuong.com-api.git"}, "keywords": ["marathon"], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/ledhcg/ledinhcuong.com-api/issues"}, "homepage": "https://github.com/ledhcg/ledinhcuong.com-api#readme", "dependencies": {"@paypal/checkout-server-sdk": "^1.0.3", "@paypal/paypal-server-sdk": "^1.1.0", "@prisma/client": "^6.6.0", "@shopify/shopify-api": "^11.12.0", "@shopify/shopify-app-session-storage-prisma": "^6.0.6", "async-exit-hook": "^2.0.1", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cls-hooked": "^4.2.2", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "envalid": "^8.0.0", "express": "^4.21.1", "helmet": "^7.1.0", "http-status-codes": "^2.3.0", "i18next": "^24.0.5", "joi": "^17.12.2", "jsonwebtoken": "^9.0.2", "lru-cache": "^11.0.2", "module-alias": "^2.2.3", "moment": "^2.30.1", "pusher": "^5.2.0", "redis": "^4.6.14", "request-ip": "^3.3.0", "sharp": "^0.33.5", "socket.io": "^4.7.5", "stripe": "^18.2.1", "uuid": "^10.0.0", "winston": "^3.13.0", "zod": "^3.25.67"}, "devDependencies": {"@faker-js/faker": "^9.3.0", "@types/async-exit-hook": "^2.0.2", "@types/bcryptjs": "^2.4.6", "@types/cls-hooked": "^4.3.9", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/glob": "^8.1.0", "@types/helmet": "^4.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.6", "@types/lru-cache": "^7.10.9", "@types/module-alias": "^2.0.4", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.14.0", "@types/request-ip": "^0.0.41", "@types/uuid": "^10.0.0", "@types/winston": "^2.4.4", "cross-env": "^7.0.3", "glob": "^11.0.1", "jest": "^29.7.0", "morgan": "^1.10.0", "nodemon": "^3.1.0", "prisma": "^6.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsc-alias": "^1.8.8", "tsconfig-paths": "^4.2.0", "tslib": "^2.6.2", "typescript": "^5.6.3"}, "optionalDependencies": {"bufferutil": "^4.0.8", "utf-8-validate": "^6.0.4"}}