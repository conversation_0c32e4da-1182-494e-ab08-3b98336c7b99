# Step 1: List all environment variables and store them in a variable
env_vars=$(vercel env ls)

# Step 2: Parse the output to get variable names (assuming the names are in the first column)
# This step can vary based on the actual output format of `vercel env ls`
env_var_names=$(echo "$env_vars" | awk '{if(NR>2) print $1}')

# Step 3: Loop through each environment variable name and remove it
for name in $env_var_names
do
  # Step 3.1: Delete each variable with '-y' option to skip confirmation
  vercel env rm $name -y
done