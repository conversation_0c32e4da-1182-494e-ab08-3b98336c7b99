{"compilerOptions": {"strict": true, "target": "es2020", "lib": ["es2020", "dom"], "typeRoots": ["node_modules/@types"], "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "module": "commonjs", "pretty": true, "sourceMap": true, "declaration": true, "outDir": "build", "allowJs": true, "noEmit": false, "esModuleInterop": true, "resolveJsonModule": true, "importHelpers": true, "skipLibCheck": true, "baseUrl": "src", "paths": {"@/*": ["*"]}}, "include": ["src//*.ts", "src//*.json", ".env"], "exclude": ["node_modules", "src/http", "src/logs", "src/tests"]}