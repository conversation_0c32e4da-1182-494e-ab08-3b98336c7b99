import PrismaService from "../src/services/prisma.service";
import * as crypto from "crypto";

async function getOrCreateApiKey() {
  const prisma = PrismaService.getInstance().getClient();
  
  try {
    // Check if a client already exists
    const existingClient = await prisma.client.findFirst({
      where: {
        email: "<EMAIL>"
      }
    });

    if (existingClient) {
      console.log("Existing API Key found:");
      console.log(`API Key: ${existingClient.APIKey}`);
      console.log(`Client: ${existingClient.name} (${existingClient.email})`);
      return existingClient.APIKey;
    }

    // Create a new client with API key
    const APIKey = crypto.randomBytes(20).toString("hex");
    const newClient = await prisma.client.create({
      data: {
        name: "Frontend Application",
        email: "<EMAIL>",
        description: "API key for frontend application",
        APIKey
      }
    });

    console.log("New API Key created:");
    console.log(`API Key: ${newClient.APIKey}`);
    console.log(`Client: ${newClient.name} (${newClient.email})`);
    
    return newClient.APIKey;
  } catch (error) {
    console.error("Error getting/creating API key:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
getOrCreateApiKey();