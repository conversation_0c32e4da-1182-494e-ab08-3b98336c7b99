#!/bin/bash

# Schema-specific Database Restore Script
# This script restores a specific schema (chargeback) from various backup file types

# Configuration
DATABASE_URL="postgresql://postgres:<EMAIL>/adlibs?sslmode=require"
BACKUP_DIR="./backup/db"
SCHEMA_NAME="chargeback"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to show usage
usage() {
    echo "Usage: $0 [options] [backup_file]"
    echo "Options:"
    echo "  -l, --list      List all available backups"
    echo "  -d, --data-only Restore data only (preserve existing schema)"
    echo "  -f, --force     Skip confirmation prompts"
    echo "  -h, --help      Show this help message"
    echo
    echo "Examples:"
    echo "  $0                                         # List available backups"
    echo "  $0 backup.sql                              # Restore from backup file"
    echo "  $0 -d chargeback_data_backup.sql           # Restore data only"
    echo "  $0 -f backup.sql.gz                        # Force restore without prompts"
    exit 1
}

# Function to detect backup type
detect_backup_type() {
    local file="$1"
    local basename=$(basename "$file" .sql.gz)
    basename=$(basename "$basename" .sql)
    
    if [[ "$basename" =~ ${SCHEMA_NAME}_data_ ]]; then
        echo "data-only"
    elif [[ "$basename" =~ ${SCHEMA_NAME}_.*tables_ ]]; then
        echo "selected-tables"
    elif [[ "$basename" =~ ${SCHEMA_NAME}_backup_ ]]; then
        echo "full-schema"
    else
        echo "unknown"
    fi
}

# Function to list available backups
list_backups() {
    echo -e "${BLUE}Available backups in ${BACKUP_DIR}:${NC}"
    echo
    
    # Full schema backups
    echo -e "${CYAN}Full Schema Backups:${NC}"
    ls -la "${BACKUP_DIR}"/${SCHEMA_NAME}_backup_*.sql* 2>/dev/null | sort -r | head -10
    
    echo
    # Data-only backups
    echo -e "${CYAN}Data-Only Backups:${NC}"
    ls -la "${BACKUP_DIR}"/${SCHEMA_NAME}_data_*.sql* 2>/dev/null | sort -r | head -10
    
    echo
    # Selected table backups
    echo -e "${CYAN}Selected Table Backups:${NC}"
    ls -la "${BACKUP_DIR}"/${SCHEMA_NAME}_*tables_*.sql* 2>/dev/null | sort -r | head -10
    
    if [ $(ls "${BACKUP_DIR}"/${SCHEMA_NAME}*.sql* 2>/dev/null | wc -l) -eq 0 ]; then
        echo -e "${YELLOW}No backups found in ${BACKUP_DIR}${NC}"
    fi
}

# Parse command line arguments
DATA_ONLY=false
FORCE_RESTORE=false
BACKUP_FILE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -l|--list)
            list_backups
            exit 0
            ;;
        -d|--data-only)
            DATA_ONLY=true
            shift
            ;;
        -f|--force)
            FORCE_RESTORE=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            BACKUP_FILE="$1"
            shift
            ;;
    esac
done

# If no backup file provided, list available backups
if [ -z "$BACKUP_FILE" ]; then
    list_backups
    echo
    echo "Please specify a backup file to restore"
    exit 1
fi

# Check if backup file exists
if [ ! -f "${BACKUP_FILE}" ]; then
    echo -e "${RED}Error: Backup file not found: ${BACKUP_FILE}${NC}"
    echo
    list_backups
    exit 1
fi

# Detect backup type
DETECTED_TYPE=$(detect_backup_type "$BACKUP_FILE")
echo -e "${BLUE}Detected backup type: ${DETECTED_TYPE}${NC}"

# Handle data-only flag conflicts
if [ "$DATA_ONLY" = true ] && [ "$DETECTED_TYPE" != "data-only" ] && [ "$DETECTED_TYPE" != "unknown" ]; then
    echo -e "${YELLOW}Note: Using --data-only flag with a ${DETECTED_TYPE} backup${NC}"
    echo "The schema structure in the backup will be ignored"
fi

# Auto-enable data-only mode for data-only backups
if [ "$DETECTED_TYPE" = "data-only" ]; then
    DATA_ONLY=true
    echo -e "${CYAN}Auto-enabled data-only mode for data backup file${NC}"
fi

# Show restore details
echo -e "${YELLOW}=== Restore Details ===${NC}"
echo "Database URL: ${DATABASE_URL}"
echo "Schema: ${SCHEMA_NAME}"
echo "Backup file: ${BACKUP_FILE}"
echo "Backup type: ${DETECTED_TYPE}"
echo "Restore mode: $([ "$DATA_ONLY" = true ] && echo "Data only" || echo "Full restore")"

# Confirmation prompts
if [ "$FORCE_RESTORE" != true ]; then
    if [ "$DATA_ONLY" = true ]; then
        echo -e "${YELLOW}⚠️  WARNING: This will restore data to the ${SCHEMA_NAME} schema!${NC}"
        echo -e "${YELLOW}Existing data in the restored tables will be replaced!${NC}"
    else
        echo -e "${RED}⚠️  WARNING: This will drop and recreate the ${SCHEMA_NAME} schema!${NC}"
        echo -e "${RED}All existing data in the schema will be lost!${NC}"
    fi
    
    read -p "Are you sure you want to continue? (yes/no): " CONFIRM
    
    if [ "${CONFIRM}" != "yes" ]; then
        echo "Restore cancelled."
        exit 0
    fi
    
    # Double confirmation for full schema restore
    if [ "$DATA_ONLY" != true ]; then
        echo -e "${RED}This is your last chance to cancel!${NC}"
        read -p "Type 'RESTORE ${SCHEMA_NAME}' to confirm: " FINAL_CONFIRM
        
        if [ "${FINAL_CONFIRM}" != "RESTORE ${SCHEMA_NAME}" ]; then
            echo "Restore cancelled."
            exit 0
        fi
    fi
fi

echo -e "${YELLOW}Starting restore...${NC}"

# Create a temporary SQL file for the restore operation
TEMP_SQL="/tmp/restore_${SCHEMA_NAME}_$$_$(date +%s).sql"

# Prepare the restore based on mode
if [ "$DATA_ONLY" = true ]; then
    # Data-only restore
    cat > "${TEMP_SQL}" << EOF
-- Data-Only Restore Script
-- Generated on $(date)
-- Backup file: ${BACKUP_FILE}

-- Start transaction
BEGIN;

-- Set search path
SET search_path TO ${SCHEMA_NAME}, public;

-- Disable foreign key checks
SET session_replication_role = 'replica';

EOF

    # If it's not already a data-only backup, we need to filter out schema commands
    if [ "$DETECTED_TYPE" != "data-only" ]; then
        echo -e "${YELLOW}Filtering schema commands from backup...${NC}"
        
        # Check if the file is compressed
        if [[ "${BACKUP_FILE}" == *.gz ]]; then
            gunzip -c "${BACKUP_FILE}" | \
            grep -v "^CREATE SCHEMA" | \
            grep -v "^DROP SCHEMA" | \
            grep -v "^CREATE TABLE" | \
            grep -v "^ALTER TABLE.*OWNER TO" | \
            grep -v "^CREATE SEQUENCE" | \
            grep -v "^ALTER SEQUENCE.*OWNER TO" | \
            grep -v "^CREATE INDEX" | \
            grep -v "^CREATE UNIQUE INDEX" | \
            grep -v "^ALTER TABLE.*ADD CONSTRAINT" | \
            grep -v "^--" | \
            grep -v "^$" >> "${TEMP_SQL}"
        else
            cat "${BACKUP_FILE}" | \
            grep -v "^CREATE SCHEMA" | \
            grep -v "^DROP SCHEMA" | \
            grep -v "^CREATE TABLE" | \
            grep -v "^ALTER TABLE.*OWNER TO" | \
            grep -v "^CREATE SEQUENCE" | \
            grep -v "^ALTER SEQUENCE.*OWNER TO" | \
            grep -v "^CREATE INDEX" | \
            grep -v "^CREATE UNIQUE INDEX" | \
            grep -v "^ALTER TABLE.*ADD CONSTRAINT" | \
            grep -v "^--" | \
            grep -v "^$" >> "${TEMP_SQL}"
        fi
    else
        # It's already a data-only backup, just append it
        if [[ "${BACKUP_FILE}" == *.gz ]]; then
            gunzip -c "${BACKUP_FILE}" >> "${TEMP_SQL}"
        else
            cat "${BACKUP_FILE}" >> "${TEMP_SQL}"
        fi
    fi
    
    # Re-enable foreign key checks and commit
    cat >> "${TEMP_SQL}" << EOF

-- Re-enable foreign key checks
SET session_replication_role = 'origin';

-- Commit transaction
COMMIT;

-- Update sequences
EOF

    # Add sequence updates for each table
    echo -e "${YELLOW}Adding sequence updates...${NC}"
    TABLES=$(psql "${DATABASE_URL}" -t -A -c "SELECT tablename FROM pg_tables WHERE schemaname='${SCHEMA_NAME}';")
    for table in $TABLES; do
        # Check if table has id column
        HAS_ID=$(psql "${DATABASE_URL}" -t -A -c "SELECT COUNT(*) FROM information_schema.columns WHERE table_schema='${SCHEMA_NAME}' AND table_name='${table}' AND column_name='id';")
        if [ "$HAS_ID" = "1" ]; then
            echo "SELECT setval('${SCHEMA_NAME}.${table}_id_seq', COALESCE((SELECT MAX(id) FROM ${SCHEMA_NAME}.${table}), 1), true);" >> "${TEMP_SQL}"
        fi
    done

else
    # Full schema restore
    echo -e "${YELLOW}Extracting table names from backup file...${NC}"
    
    # Extract table names from the backup file
    TEMP_TABLES="/tmp/tables_${SCHEMA_NAME}_$$_$(date +%s).txt"
    
    if [[ "${BACKUP_FILE}" == *.gz ]]; then
        gunzip -c "${BACKUP_FILE}" | grep -E "^CREATE TABLE ${SCHEMA_NAME}\." | sed -E "s/CREATE TABLE ${SCHEMA_NAME}\.([^ ]+).*/\1/" | sort -u > "${TEMP_TABLES}"
    else
        grep -E "^CREATE TABLE ${SCHEMA_NAME}\." "${BACKUP_FILE}" | sed -E "s/CREATE TABLE ${SCHEMA_NAME}\.([^ ]+).*/\1/" | sort -u > "${TEMP_TABLES}"
    fi
    
    # Count tables found
    TABLE_COUNT=$(wc -l < "${TEMP_TABLES}" | tr -d ' ')
    echo -e "${BLUE}Found ${TABLE_COUNT} tables in backup file${NC}"
    
    cat > "${TEMP_SQL}" << EOF
-- Full Schema Restore Script
-- Generated on $(date)
-- Backup file: ${BACKUP_FILE}

-- Start transaction
BEGIN;

-- Create schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS ${SCHEMA_NAME};

-- Set search path
SET search_path TO ${SCHEMA_NAME}, public;

-- Drop only tables that exist in the backup
EOF

    # Add DROP TABLE statements for each table found in backup
    if [ -s "${TEMP_TABLES}" ]; then
        echo -e "${YELLOW}Dropping only tables that will be restored...${NC}"
        while IFS= read -r table; do
            echo "DROP TABLE IF EXISTS ${SCHEMA_NAME}.${table} CASCADE;" >> "${TEMP_SQL}"
        done < "${TEMP_TABLES}"
    fi
    
    # Clean up temp file
    rm -f "${TEMP_TABLES}"
    
    echo "" >> "${TEMP_SQL}"
    echo "-- Restore backup content" >> "${TEMP_SQL}"
    
    # Append the backup content
    if [[ "${BACKUP_FILE}" == *.gz ]]; then
        echo "Detected compressed backup file, decompressing and appending..."
        gunzip -c "${BACKUP_FILE}" >> "${TEMP_SQL}"
    else
        cat "${BACKUP_FILE}" >> "${TEMP_SQL}"
    fi
    
    # Add commit to the end
    echo "COMMIT;" >> "${TEMP_SQL}"
fi

# Execute the restore
echo -e "${YELLOW}Executing restore...${NC}"
psql "${DATABASE_URL}" -v ON_ERROR_STOP=1 -f "${TEMP_SQL}" 2>&1 | tee /tmp/restore_${SCHEMA_NAME}_$$.log

# Check if restore was successful
if [ ${PIPESTATUS[0]} -eq 0 ]; then
    echo -e "${GREEN}✓ Restore completed successfully!${NC}"
    
    # Clean up temporary files
    rm -f "${TEMP_SQL}"
    rm -f /tmp/restore_${SCHEMA_NAME}_$$.log
    
    # Verify the restore
    echo -e "${YELLOW}Verifying restore...${NC}"
    TABLE_COUNT=$(psql "${DATABASE_URL}" -t -c "SELECT COUNT(*) FROM pg_tables WHERE schemaname='${SCHEMA_NAME}';")
    echo -e "${GREEN}✓ Found ${TABLE_COUNT} tables in ${SCHEMA_NAME} schema${NC}"
    
    # Show row counts for verification
    echo -e "${BLUE}Table row counts:${NC}"
    TABLES=$(psql "${DATABASE_URL}" -t -A -c "SELECT tablename FROM pg_tables WHERE schemaname='${SCHEMA_NAME}' ORDER BY tablename;")
    for table in $TABLES; do
        COUNT=$(psql "${DATABASE_URL}" -t -A -c "SELECT COUNT(*) FROM ${SCHEMA_NAME}.${table};")
        printf "  %-30s %10s rows\n" "$table:" "$COUNT"
    done
    
    echo -e "${GREEN}✓ Restore process completed!${NC}"
else
    echo -e "${RED}✗ Restore failed!${NC}"
    echo -e "${RED}Check the log file: /tmp/restore_${SCHEMA_NAME}_$$.log${NC}"
    # Clean up temporary SQL file but keep log for debugging
    rm -f "${TEMP_SQL}"
    exit 1
fi

# Optional: Show schema information
if [ "$DATA_ONLY" != true ]; then
    echo
    echo -e "${BLUE}Schema information:${NC}"
    psql "${DATABASE_URL}" -c "\dn+ ${SCHEMA_NAME}"
fi