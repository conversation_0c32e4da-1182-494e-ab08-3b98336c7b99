#!/usr/bin/env python3
import csv
import sys
from datetime import datetime
from decimal import Decimal

def convert_amount_to_cents(amount_str):
    """Convert decimal amount string to integer cents"""
    if not amount_str or amount_str == '':
        return None
    try:
        # Convert to Decimal for precise calculation
        decimal_amount = Decimal(amount_str)
        # Multiply by 100 and round to get cents
        cents = int(decimal_amount * 100)
        return cents
    except:
        return None

def format_timestamp_with_tz(timestamp_str):
    """Convert timestamp string to PostgreSQL timestamp with timezone format"""
    if not timestamp_str or timestamp_str == '':
        return ''
    
    try:
        # Handle ISO format with Z suffix
        if timestamp_str.endswith('Z'):
            dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3] + '+00'
        # Handle regular timestamp format
        elif ' ' in timestamp_str:
            # Assume UTC for timestamps without timezone
            return timestamp_str.strip() + '+00'
        else:
            return timestamp_str
    except:
        return timestamp_str

def convert_row(row):
    """Convert a single row from old format to new format"""
    new_row = {}
    
    # Copy fields that don't change
    unchanged_fields = [
        'id', 'currency', 'descriptor', 'age', 'arn', 'issuer', 
        'liability', 'caid', 'type', 'created_at', 'updated_at',
        'feedback_data', 'feedback_status', 'acquirer_bin',
        'acquirer_reference_number', 'alert_id', 'alert_source',
        'alert_status', 'alert_type', 'auth_code', 'card_bin',
        'card_number', 'chargeback_code', 'descriptor_contact',
        'dispute_currency', 'initiated_by', 'merchant_category_code',
        'rule_name', 'rule_type', 'transaction_id', 'transaction_type'
    ]
    
    for field in unchanged_fields:
        new_row[field] = row.get(field, '')
    
    # Convert amount from text to integer (cents)
    new_row['amount'] = convert_amount_to_cents(row.get('amount', ''))
    
    # Convert dispute_amount from text to integer (cents)
    new_row['dispute_amount'] = convert_amount_to_cents(row.get('dispute_amount', ''))
    
    # Convert timestamps to timezone-aware format
    new_row['alert_time'] = format_timestamp_with_tz(row.get('alert_time', ''))
    new_row['feedback_time'] = format_timestamp_with_tz(row.get('feedback_time', ''))
    new_row['transaction_time'] = format_timestamp_with_tz(row.get('transaction_time', ''))
    
    return new_row

def main():
    input_file = 'blocks.csv'
    output_file = 'blocks_converted.csv'
    
    # Field order for new CSV (matching new_blocks.sql structure)
    output_fields = [
        'id', 'currency', 'descriptor', 'age', 'arn', 'issuer',
        'liability', 'caid', 'type', 'created_at', 'updated_at',
        'feedback_data', 'feedback_status', 'feedback_time',
        'acquirer_bin', 'acquirer_reference_number', 'alert_id',
        'alert_source', 'alert_status', 'alert_type', 'auth_code',
        'card_bin', 'card_number', 'chargeback_code', 'descriptor_contact',
        'dispute_currency', 'initiated_by', 'merchant_category_code',
        'rule_name', 'rule_type', 'transaction_id', 'transaction_type',
        'amount', 'alert_time', 'dispute_amount', 'transaction_time'
    ]
    
    with open(input_file, 'r', encoding='utf-8') as infile:
        reader = csv.DictReader(infile)
        
        with open(output_file, 'w', encoding='utf-8', newline='') as outfile:
            writer = csv.DictWriter(outfile, fieldnames=output_fields)
            writer.writeheader()
            
            for row in reader:
                converted_row = convert_row(row)
                writer.writerow(converted_row)
    
    print(f"Conversion complete! Output saved to {output_file}")

if __name__ == "__main__":
    main()