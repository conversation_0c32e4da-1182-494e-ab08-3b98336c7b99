-- DDL generated by Postico 2.1.2
-- Not all database features are supported. Do not use for backup.

-- Table Definition ----------------------------------------------

CREATE TABLE chargeback.blocks (
    id text PRIMARY KEY,
    amount text NOT NULL,
    currency text NOT NULL,
    descriptor text NOT NULL,
    age text,
    arn text,
    issuer text,
    liability text,
    caid text,
    type text NOT NULL DEFAULT 'ETHOCA'::text,
    created_at timestamp(3) without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp(3) without time zone NOT NULL,
    feedback_data jsonb,
    feedback_status text NOT NULL DEFAULT 'PENDING'::text,
    feedback_time timestamp(3) without time zone,
    acquirer_bin text,
    acquirer_reference_number text,
    alert_id text NOT NULL,
    alert_source text,
    alert_status text,
    alert_time text NOT NULL,
    alert_type text NOT NULL,
    auth_code text,
    card_bin text,
    card_number text,
    chargeback_code text,
    descriptor_contact text,
    dispute_amount text,
    dispute_currency text,
    initiated_by text,
    merchant_category_code text,
    rule_name text,
    rule_type text,
    transaction_id text,
    transaction_time text,
    transaction_type text
);

-- Indices -------------------------------------------------------

CREATE UNIQUE INDEX blocks_pkey ON chargeback.blocks(id text_ops);
