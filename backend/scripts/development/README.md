# Database Backup and Restore Scripts

This directory contains scripts for backing up and restoring the PostgreSQL database schema and data.

## Available Scripts

### 1. backup-schema.sh
Comprehensive backup script for the `chargeback` schema with flexible table selection options.

**Features:**
- Full schema backup (default)
- Selective table backup
- Interactive table selection mode
- Data-only backup option (no schema structure)
- SQL, JSON, and CSV export formats
- Automatic compression (.gz)
- PostgreSQL-compatible CSV format for direct import

**Usage:**
```bash
# Backup all tables (default behavior)
./backup-schema.sh

# List all available tables
./backup-schema.sh -l
./backup-schema.sh --list

# Backup specific tables
./backup-schema.sh -t users,orders,payments
./backup-schema.sh --tables users,orders,payments

# Interactive mode - select tables from a menu
./backup-schema.sh -i
./backup-schema.sh --interactive

# Data-only backup (no schema structure)
./backup-schema.sh -d
./backup-schema.sh --data-only

# Data-only backup for specific tables
./backup-schema.sh -d -t users,orders
./backup-schema.sh --data-only --tables users,orders

# SQL backup only (skip JSON and CSV export)
./backup-schema.sh -s
./backup-schema.sh --sql-only

# CSV export (includes SQL and JSON by default)
./backup-schema.sh -c
./backup-schema.sh --csv

# CSV export only with SQL (skip JSON)
./backup-schema.sh -c -s
./backup-schema.sh --csv --sql-only

# Show help
./backup-schema.sh -h
./backup-schema.sh --help
```

**Output:**
- Full backup: `./backup/db/chargeback_backup_YYYYMMDD_HHMMSS.sql`
- Data-only backup: `./backup/db/chargeback_data_backup_YYYYMMDD_HHMMSS.sql`
- Compressed: `./backup/db/[filename].gz`
- JSON exports: `./backup/db/json_YYYYMMDD_HHMMSS/`
- CSV exports: `./backup/db/csv_YYYYMMDD_HHMMSS/`

### 2. restore-schema.sh
Advanced restore script that supports all backup types created by backup-schema.sh.

**Features:**
- Auto-detects backup type (full schema, data-only, selected tables)
- Data-only restore mode preserves existing schema structure
- Lists and categorizes available backups
- Supports both .sql and .sql.gz files
- Safety confirmations with force mode option
- Transaction-based restore (all or nothing)
- Automatic sequence updates for data-only restores
- Row count verification after restore

**Usage:**
```bash
# List all available backups (categorized by type)
./restore-schema.sh
./restore-schema.sh -l
./restore-schema.sh --list

# Restore from specific backup (auto-detects type)
./restore-schema.sh ./backup/db/chargeback_backup_20240101_120000.sql
./restore-schema.sh ./backup/db/chargeback_data_backup_20240101_120000.sql.gz

# Data-only restore (preserves schema structure)
./restore-schema.sh -d backup.sql
./restore-schema.sh --data-only backup.sql

# Force restore without confirmations
./restore-schema.sh -f backup.sql
./restore-schema.sh --force backup.sql

# Combine options
./restore-schema.sh -d -f chargeback_data_backup.sql
```

**Backup Type Detection:**
- `chargeback_backup_*`: Full schema backup
- `chargeback_data_*`: Data-only backup (auto-enables data-only mode)
- `chargeback_*tables_*`: Selected tables backup

**Restore Modes:**
1. **Full Restore** (default):
   - Drops and recreates the entire schema
   - Restores structure and data
   - Requires double confirmation

2. **Data-Only Restore** (`-d`):
   - Preserves existing schema structure
   - Replaces data in existing tables
   - Updates sequences automatically
   - Single confirmation required

## Backup Directory Structure

```
backup/
└── db/
    ├── chargeback_backup_YYYYMMDD_HHMMSS.sql      # Full SQL backup
    ├── chargeback_backup_YYYYMMDD_HHMMSS.sql.gz   # Compressed backup
    ├── json_YYYYMMDD_HHMMSS/                      # JSON exports
    │   ├── users.json
    │   ├── orders.json
    │   ├── payments.json
    │   └── ... (other tables)
    └── csv_YYYYMMDD_HHMMSS/                       # CSV exports
        ├── users.csv
        ├── orders.csv
        ├── payments.csv
        └── ... (other tables)
```

## Configuration

All scripts use the following configuration:
- **Database URL**: Configured in the script (PostgreSQL connection string)
- **Schema**: `chargeback`
- **Backup Directory**: `./backup/db`

## Prerequisites

- PostgreSQL client tools (`pg_dump`, `psql`)
- Bash shell
- Write permissions to the backup directory
- Database connection credentials

## Best Practices

1. **Regular Backups**: Schedule regular backups using cron
   ```bash
   # Daily full backup at 2 AM
   0 2 * * * cd /path/to/scripts && ./backup-schema.sh
   
   # Hourly data-only backup (lighter weight)
   0 * * * * cd /path/to/scripts && ./backup-schema.sh -d
   ```

2. **Backup Before Major Changes**: Always backup before:
   - Schema migrations
   - Major data updates
   - System upgrades

3. **Use Data-Only Backups**: When you need:
   - Frequent backups (smaller file size)
   - To restore data without modifying schema
   - To migrate data between environments with different schemas

4. **CSV Export Usage**: Use CSV exports when you need:
   - Data analysis in spreadsheet applications (Excel, Google Sheets)
   - Fast data migration between different database systems
   - Lightweight backups for specific tables
   - Integration with data processing tools
   - Quick data sharing with non-technical team members

5. **Test Restores**: Periodically test restore procedures in a development environment

6. **Backup Retention**: Implement a retention policy to manage backup storage
   ```bash
   # Example: Delete backups older than 30 days
   find ./backup/db -name "*.sql.gz" -mtime +30 -delete
   find ./backup/db -name "json_*" -type d -mtime +30 -exec rm -rf {} +
   find ./backup/db -name "csv_*" -type d -mtime +30 -exec rm -rf {} +
   ```

7. **Offsite Backups**: Copy important backups to offsite storage (S3, Google Cloud Storage, etc.)

## Security Considerations

- Database credentials are stored in the scripts - ensure proper file permissions
- Backup files contain sensitive data - encrypt when storing offsite
- Use read-only database users for backup operations when possible
- Regularly audit access to backup files

## Common Use Cases

### 1. Development Environment Refresh
```bash
# Backup production data (data only)
./backup-schema.sh -d -s

# Restore to development environment
./restore-schema.sh -d -f ./backup/db/chargeback_data_backup_latest.sql
```

### 2. Selective Data Migration
```bash
# Backup specific tables
./backup-schema.sh -t users,orders,payments

# Restore selected tables to another environment
./restore-schema.sh ./backup/db/chargeback_users-orders-payments_*.sql
```

### 3. Disaster Recovery
```bash
# Full backup (scheduled via cron)
./backup-schema.sh

# Full restore from latest backup
./restore-schema.sh -f ./backup/db/chargeback_backup_latest.sql.gz
```

### 4. Data-Only Backup/Restore (Schema unchanged)
```bash
# Backup data before major update
./backup-schema.sh -d

# If something goes wrong, restore data only
./restore-schema.sh -d ./backup/db/chargeback_data_backup_*.sql
```

### 5. Data Analysis and Reporting
```bash
# Export specific tables to CSV for analysis
./backup-schema.sh -t users,orders,payments -c -s

# The CSV files can then be opened in Excel, Google Sheets, or imported into BI tools
# Files will be in: ./backup/db/csv_chargeback_users-orders-payments_YYYYMMDD_HHMMSS/
```

### 6. Cross-Platform Data Migration
```bash
# Export data to CSV for migration to different database system
./backup-schema.sh -c -s

# CSV files are universal and can be imported into MySQL, SQLite, etc.
# Import back to PostgreSQL:
# \copy table_name FROM 'file.csv' WITH (FORMAT CSV, HEADER true);
```

### 7. Quick Data Sharing
```bash
# Export specific user data for sharing with support team
./backup-schema.sh -t users -c -s

# Share the lightweight CSV file instead of full SQL dump
# CSV files are human-readable and can be opened in any text editor
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   chmod +x backup-schema.sh restore-schema.sh
   ```

2. **pg_dump: command not found**
   - Install PostgreSQL client tools
   - Add PostgreSQL bin directory to PATH

3. **Connection Failed**
   - Verify database URL and credentials
   - Check network connectivity
   - Ensure SSL certificates are valid

4. **Restore Fails**
   - Check if the backup file is corrupted
   - Verify PostgreSQL version compatibility
   - Ensure sufficient database permissions
   - For data-only restore: ensure schema exists and matches backup

5. **Data-Only Restore Issues**
   - Foreign key violations: Tables might need specific restore order
   - Missing tables: Ensure all required tables exist in the schema
   - Sequence issues: Script automatically updates sequences, but verify

6. **Wrong Backup Type Detected**
   - Check filename follows naming convention
   - Use appropriate flags to override auto-detection

## JSON Export Format

The JSON export creates one file per table with the following format:
```json
[
  {"id": 1, "name": "Example", "created_at": "2024-01-01T00:00:00Z"},
  {"id": 2, "name": "Another", "created_at": "2024-01-02T00:00:00Z"}
]
```

Empty tables are exported as:
```json
[]
```

## CSV Export Format

The CSV export creates PostgreSQL-compatible CSV files with the following features:
- **Header row**: Contains column names
- **Quoted fields**: All fields are enclosed in double quotes for safety
- **UTF-8 encoding**: Ensures proper character handling
- **PostgreSQL compatible**: Can be directly imported using `\copy` command

Example CSV format:
```csv
"id","name","email","created_at"
"c652c60f-0f16-44c6-8e9a-d9fc03351888","John Doe","<EMAIL>","2024-01-01 00:00:00+00"
"06d70a4a-bc0a-4463-948b-cb7254504ee2","Jane Smith","<EMAIL>","2024-01-02 00:00:00+00"
```

### Importing CSV into PostgreSQL

To import the CSV files back into PostgreSQL:

```sql
-- Import into existing table
\copy table_name FROM '/path/to/file.csv' WITH (FORMAT CSV, HEADER true);

-- Import into new table (create table first)
CREATE TABLE new_table (LIKE existing_table INCLUDING ALL);
\copy new_table FROM '/path/to/file.csv' WITH (FORMAT CSV, HEADER true);
```

**Benefits of CSV format:**
- Smaller file size compared to SQL dumps
- Universal format compatible with spreadsheet applications
- Fast import/export operations
- Easy data analysis and manipulation
- Direct PostgreSQL compatibility

## Integration with Prisma

After restore operations, the scripts automatically:
1. Run `npm run prisma:db:pull` to introspect the database
2. Run `npm run prisma:generate` to regenerate the Prisma client

This ensures your Prisma schema stays in sync with the restored database structure.