#!/bin/bash

# Schema-specific Database Backup Script
# This script creates backups for specific schemas (chargeback) with table selection support

# Configuration
DATABASE_URL="postgresql://postgres:<EMAIL>/adlibs?sslmode=require"
BACKUP_DIR="./backup/db"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
SCHEMA_NAME="chargeback"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Create backup directory if it doesn't exist
mkdir -p "${BACKUP_DIR}"

# Function to show usage
usage() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -a, --all       Backup all tables in the schema (default)"
    echo "  -t, --tables    Comma-separated list of tables to backup"
    echo "  -l, --list      List all available tables in the schema"
    echo "  -i, --interactive   Interactive mode to select tables"
    echo "  -d, --data-only Backup data only (no schema structure)"
    echo "  -j, --json      Export as JSON format (default: true)"
    echo "  -c, --csv       Export as CSV format (default: true)"
    echo "  -s, --sql-only  Export SQL only, skip JSON and CSV export (default: exports SQL, JSON, and CSV)"
    echo "  -h, --help      Show this help message"
    echo
    echo "Examples:"
    echo "  $0                             # Backup all tables (default)"
    echo "  $0 -l                          # List all tables"
    echo "  $0 -t users,orders,payments    # Backup specific tables"
    echo "  $0 -i                          # Interactive selection"
    echo "  $0 -d                          # Data only backup"
    echo "  $0 -d -t users,orders          # Data only for specific tables"
    echo "  $0 -s                          # SQL backup only, no JSON/CSV"
    echo "  $0 -c                          # Include CSV export"
    echo "  $0                             # Default: exports SQL, JSON, and CSV"
    echo "  $0 -j -c                       # Explicitly enable JSON and CSV export"
    exit 1
}

# Function to list all tables
list_tables() {
    echo -e "${BLUE}Tables in ${SCHEMA_NAME} schema:${NC}"
    psql "${DATABASE_URL}" -t -A -c \
        "SELECT tablename FROM pg_tables WHERE schemaname='${SCHEMA_NAME}' ORDER BY tablename;"
}

# Function to get all tables as array
get_all_tables() {
    psql "${DATABASE_URL}" -t -A -c \
        "SELECT tablename FROM pg_tables WHERE schemaname='${SCHEMA_NAME}' ORDER BY tablename;"
}

# Function for interactive table selection
interactive_selection() {
    echo -e "${CYAN}Loading tables from ${SCHEMA_NAME} schema...${NC}"
    
    # Get all tables
    TABLES=()
    while IFS= read -r table; do
        TABLES+=("$table")
    done < <(get_all_tables)
    
    if [ ${#TABLES[@]} -eq 0 ]; then
        echo -e "${RED}No tables found in schema ${SCHEMA_NAME}${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}Available tables:${NC}"
    for i in "${!TABLES[@]}"; do
        printf "%3d) %s\n" $((i+1)) "${TABLES[$i]}"
    done
    
    echo
    echo -e "${YELLOW}Enter table numbers to backup (comma-separated), 'all' for all tables, or 'q' to quit:${NC}"
    read -p "> " SELECTION
    
    if [ "${SELECTION}" = "q" ]; then
        echo "Backup cancelled."
        exit 0
    fi
    
    if [ "${SELECTION}" = "all" ]; then
        SELECTED_TABLES=("${TABLES[@]}")
        BACKUP_ALL=true
    else
        SELECTED_TABLES=()
        IFS=',' read -ra SELECTIONS <<< "${SELECTION}"
        for sel in "${SELECTIONS[@]}"; do
            # Remove spaces
            sel=$(echo "$sel" | xargs)
            # Check if valid number
            if [[ "$sel" =~ ^[0-9]+$ ]] && [ "$sel" -ge 1 ] && [ "$sel" -le ${#TABLES[@]} ]; then
                SELECTED_TABLES+=("${TABLES[$((sel-1))]}")
            else
                echo -e "${RED}Invalid selection: $sel${NC}"
            fi
        done
    fi
    
    if [ ${#SELECTED_TABLES[@]} -eq 0 ]; then
        echo -e "${RED}No valid tables selected${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}Selected tables:${NC}"
    printf '%s\n' "${SELECTED_TABLES[@]}"
}

# Parse command line arguments
EXPORT_JSON=true
EXPORT_CSV=true
INTERACTIVE=false
TABLE_LIST=""
BACKUP_ALL=false
DATA_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -a|--all)
            BACKUP_ALL=true
            shift
            ;;
        -l|--list)
            list_tables
            exit 0
            ;;
        -t|--tables)
            TABLE_LIST="$2"
            shift 2
            ;;
        -i|--interactive)
            INTERACTIVE=true
            shift
            ;;
        -d|--data-only)
            DATA_ONLY=true
            shift
            ;;
        -j|--json)
            EXPORT_JSON=true
            shift
            ;;
        -c|--csv)
            EXPORT_CSV=true
            shift
            ;;
        -s|--sql-only)
            EXPORT_JSON=false
            EXPORT_CSV=false
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            usage
            ;;
    esac
done

# Determine which tables to backup
if [ "$INTERACTIVE" = true ]; then
    interactive_selection
elif [ -n "$TABLE_LIST" ]; then
    IFS=',' read -ra SELECTED_TABLES <<< "$TABLE_LIST"
    # Trim spaces from table names
    for i in "${!SELECTED_TABLES[@]}"; do
        SELECTED_TABLES[$i]=$(echo "${SELECTED_TABLES[$i]}" | xargs)
    done
else
    # Default: backup all tables
    BACKUP_ALL=true
    SELECTED_TABLES=()
    while IFS= read -r table; do
        SELECTED_TABLES+=("$table")
    done < <(get_all_tables)
fi

# If backing up all tables, use full schema backup
if [ "$BACKUP_ALL" = true ]; then
    echo -e "${YELLOW}Starting full schema backup for: ${SCHEMA_NAME}${NC}"
    echo "Timestamp: ${TIMESTAMP}"
    
    # Backup schema structure and data
    if [ "$DATA_ONLY" = true ]; then
        SCHEMA_BACKUP="${BACKUP_DIR}/${SCHEMA_NAME}_data_backup_${TIMESTAMP}.sql"
        echo -e "${YELLOW}Backing up data only for schema: ${SCHEMA_NAME}${NC}"
    else
        SCHEMA_BACKUP="${BACKUP_DIR}/${SCHEMA_NAME}_backup_${TIMESTAMP}.sql"
        echo -e "${YELLOW}Backing up entire schema: ${SCHEMA_NAME}${NC}"
    fi
    
    if [ "$DATA_ONLY" = true ]; then
        pg_dump "${DATABASE_URL}" \
            --schema="${SCHEMA_NAME}" \
            --no-owner \
            --no-privileges \
            --data-only \
            --disable-triggers \
            --file="${SCHEMA_BACKUP}" \
            --verbose
    else
        pg_dump "${DATABASE_URL}" \
            --schema="${SCHEMA_NAME}" \
            --no-owner \
            --no-privileges \
            --clean \
            --if-exists \
            --file="${SCHEMA_BACKUP}" \
            --verbose
    fi
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Schema backup completed: ${SCHEMA_BACKUP}${NC}"
        
        # Compress the backup
        gzip -c "${SCHEMA_BACKUP}" > "${SCHEMA_BACKUP}.gz"
        echo -e "${GREEN}✓ Compressed backup created: ${SCHEMA_BACKUP}.gz${NC}"
    else
        echo -e "${RED}✗ Schema backup failed!${NC}"
        exit 1
    fi
else
    # Backup selected tables only
    # Verify selected tables exist
    echo -e "${YELLOW}Verifying selected tables...${NC}"
    VALID_TABLES=()
    for table in "${SELECTED_TABLES[@]}"; do
        EXISTS=$(psql "${DATABASE_URL}" -t -A -c \
            "SELECT EXISTS (SELECT 1 FROM pg_tables WHERE schemaname='${SCHEMA_NAME}' AND tablename='${table}');")
        
        if [ "$EXISTS" = "t" ]; then
            VALID_TABLES+=("$table")
            echo -e "${GREEN}✓ Table found: ${table}${NC}"
        else
            echo -e "${RED}✗ Table not found: ${table}${NC}"
        fi
    done
    
    if [ ${#VALID_TABLES[@]} -eq 0 ]; then
        echo -e "${RED}No valid tables to backup${NC}"
        exit 1
    fi
    
    # Create backup filename
    TABLES_STRING=$(IFS='-'; echo "${VALID_TABLES[*]}")
    if [ "$DATA_ONLY" = true ]; then
        if [ ${#VALID_TABLES[@]} -gt 3 ]; then
            BACKUP_NAME="${SCHEMA_NAME}_data_${#VALID_TABLES[@]}tables_${TIMESTAMP}"
        else
            BACKUP_NAME="${SCHEMA_NAME}_data_${TABLES_STRING}_${TIMESTAMP}"
        fi
    else
        if [ ${#VALID_TABLES[@]} -gt 3 ]; then
            BACKUP_NAME="${SCHEMA_NAME}_${#VALID_TABLES[@]}tables_${TIMESTAMP}"
        else
            BACKUP_NAME="${SCHEMA_NAME}_${TABLES_STRING}_${TIMESTAMP}"
        fi
    fi
    SCHEMA_BACKUP="${BACKUP_DIR}/${BACKUP_NAME}.sql"
    
    echo -e "${YELLOW}Starting backup of ${#VALID_TABLES[@]} tables...${NC}"
    echo "Timestamp: ${TIMESTAMP}"
    echo "Output file: ${SCHEMA_BACKUP}"
    
    # Build pg_dump command with multiple tables
    if [ "$DATA_ONLY" = true ]; then
        PG_DUMP_CMD="pg_dump \"${DATABASE_URL}\" --no-owner --no-privileges --data-only --disable-triggers"
    else
        PG_DUMP_CMD="pg_dump \"${DATABASE_URL}\" --no-owner --no-privileges --clean --if-exists"
    fi
    
    # Add each table to the command
    for table in "${VALID_TABLES[@]}"; do
        PG_DUMP_CMD="${PG_DUMP_CMD} --table=\"${SCHEMA_NAME}.${table}\""
    done
    
    # Execute backup
    echo -e "${YELLOW}Creating SQL backup...${NC}"
    eval "${PG_DUMP_CMD}" > "${SCHEMA_BACKUP}"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ SQL backup completed: ${SCHEMA_BACKUP}${NC}"
        
        # Compress the backup
        gzip -c "${SCHEMA_BACKUP}" > "${SCHEMA_BACKUP}.gz"
        echo -e "${GREEN}✓ Compressed backup created: ${SCHEMA_BACKUP}.gz${NC}"
    else
        echo -e "${RED}✗ SQL backup failed!${NC}"
        exit 1
    fi
    
    # Use VALID_TABLES for JSON export instead of SELECTED_TABLES
    SELECTED_TABLES=("${VALID_TABLES[@]}")
fi

# Export as JSON if requested
if [ "$EXPORT_JSON" = true ]; then
    echo -e "${YELLOW}Creating JSON data exports...${NC}"
    if [ "$BACKUP_ALL" = true ]; then
        JSON_BACKUP_DIR="${BACKUP_DIR}/json_${TIMESTAMP}"
    else
        JSON_BACKUP_DIR="${BACKUP_DIR}/json_${BACKUP_NAME}"
    fi
    mkdir -p "${JSON_BACKUP_DIR}"
    
    # Export each table as JSON using psql
    for TABLE in "${SELECTED_TABLES[@]}"; do
        if [ ! -z "${TABLE// }" ]; then
            echo "Exporting table: ${TABLE}"
            # Fixed JSON export with proper formatting
            psql "${DATABASE_URL}" -t -A -c \
                "SELECT COALESCE(json_agg(row_to_json(t))::text, '[]') FROM (SELECT * FROM ${SCHEMA_NAME}.\"${TABLE}\") t;" \
                | sed 's/^[[:space:]]*//;s/[[:space:]]*$//' \
                > "${JSON_BACKUP_DIR}/${TABLE}.json"
            
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}  ✓ Exported: ${TABLE}.json${NC}"
            else
                echo -e "${RED}  ✗ Failed: ${TABLE}.json${NC}"
            fi
        fi
    done
    
    echo -e "${GREEN}✓ JSON exports completed: ${JSON_BACKUP_DIR}/${NC}"
fi

# Export as CSV if requested
if [ "$EXPORT_CSV" = true ]; then
    echo -e "${YELLOW}Creating CSV data exports...${NC}"
    if [ "$BACKUP_ALL" = true ]; then
        CSV_BACKUP_DIR="${BACKUP_DIR}/csv_${TIMESTAMP}"
    else
        CSV_BACKUP_DIR="${BACKUP_DIR}/csv_${BACKUP_NAME}"
    fi
    mkdir -p "${CSV_BACKUP_DIR}"
    
    # Export each table as CSV using psql
    for TABLE in "${SELECTED_TABLES[@]}"; do
        if [ ! -z "${TABLE// }" ]; then
            echo "Exporting table: ${TABLE}"
            psql "${DATABASE_URL}" -c \
                 "\copy (SELECT * FROM ${SCHEMA_NAME}.\"${TABLE}\") TO '${CSV_BACKUP_DIR}/${TABLE}.csv' WITH (FORMAT CSV, HEADER true, FORCE_QUOTE *, ENCODING 'UTF8');"
            
            if [ $? -eq 0 ]; then
                echo -e "${GREEN}  ✓ Exported: ${TABLE}.csv${NC}"
            else
                echo -e "${RED}  ✗ Failed: ${TABLE}.csv${NC}"
            fi
        fi
    done
    
    echo -e "${GREEN}✓ CSV exports completed: ${CSV_BACKUP_DIR}/${NC}"
fi

# Summary
echo
echo -e "${GREEN}=== Backup Summary ===${NC}"
echo -e "${BLUE}Schema:${NC} ${SCHEMA_NAME}"
if [ "$BACKUP_ALL" = true ]; then
    if [ "$DATA_ONLY" = true ]; then
        echo -e "${BLUE}Backup type:${NC} Full schema data-only backup"
    else
        echo -e "${BLUE}Backup type:${NC} Full schema backup (structure + data)"
    fi
else
    if [ "$DATA_ONLY" = true ]; then
        echo -e "${BLUE}Backup type:${NC} Selected tables data-only"
    else
        echo -e "${BLUE}Backup type:${NC} Selected tables (structure + data)"
    fi
    echo -e "${BLUE}Tables backed up:${NC} ${#SELECTED_TABLES[@]}"
    printf '%s\n' "${SELECTED_TABLES[@]}" | sed 's/^/  - /'
fi
echo -e "${BLUE}SQL backup:${NC} ${SCHEMA_BACKUP}"
echo -e "${BLUE}Compressed backup:${NC} ${SCHEMA_BACKUP}.gz"
if [ "$EXPORT_JSON" = true ]; then
    echo -e "${BLUE}JSON exports:${NC} ${JSON_BACKUP_DIR}/"
fi
if [ "$EXPORT_CSV" = true ]; then
    echo -e "${BLUE}CSV exports:${NC} ${CSV_BACKUP_DIR}/"
fi
echo -e "${GREEN}=====================${NC}"