import { PrismaClient } from "@prisma/client";
import { v4 as uuidv4 } from "uuid";
import axios from "axios";

// Define some sample data for generating blocks

const issuerNames = ["VISA", "MASTERCARD", "AMEX", "DISCOVER"];
const liabilityOptions = ["Merchant", "Issuer", "Split", "Unknown"];
const ruleTypes = ["High Risk", "Velocity", "Pattern", "Geo", "Amount"];
const ruleNames = [
  "Rule_HighRiskCountry",
  "Rule_LargeAmount",
  "Rule_MultipleTransactions",
  "Rule_UnusualPattern",
  "Rule_VelocityCheck",
];

// Constants for Shopify API
const SHOPIFY_SHOP = "bec4c7-4b.myshopify.com";
const SHOPIFY_ACCESS_TOKEN = "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";
const SHOPIFY_API_VERSION = "2025-04";

// Shopify order and transaction data
interface ShopifyTransaction {
  id: string;
  order_id: string;
  kind: string;
  gateway: string;
  status: string;
  message: string;
  created_at: string;
  error_code?: string;
  payment_details: {
    credit_card_bin?: string;
    credit_card_number?: string;
    credit_card_company?: string;
    credit_card_name?: string;
  };
  amount: string;
  currency: string;
  payment_id: string;
}

interface ShopifyOrder {
  id: string;
  name: string;
  email: string;
  created_at: string;
  processed_at: string;
  total_price: string;
  currency: string;
  customer: {
    id: string;
    first_name: string;
    last_name: string;
  };
  transactions?: ShopifyTransaction[];
}

// Fetch data from Shopify API
const fetchShopifyData = async (): Promise<ShopifyOrder[]> => {
  console.log("📊 Fetching Shopify data...");

  try {
    // Step 1: Get all orders
    const ordersResponse = await axios.get(
      `https://${SHOPIFY_SHOP}/admin/api/${SHOPIFY_API_VERSION}/orders.json?status=any&limit=10`,
      {
        headers: {
          "X-Shopify-Access-Token": SHOPIFY_ACCESS_TOKEN,
          "Content-Type": "application/json",
        },
      }
    );

    const orders: ShopifyOrder[] = ordersResponse.data.orders;
    console.log(`📑 Fetched ${orders.length} orders from Shopify`);

    // Step 2: For each order, get transactions
    for (const order of orders) {
      // Get transactions for this order
      const transactionsResponse = await axios.get(
        `https://${SHOPIFY_SHOP}/admin/api/${SHOPIFY_API_VERSION}/orders/${order.id}/transactions.json`,
        {
          headers: {
            "X-Shopify-Access-Token": SHOPIFY_ACCESS_TOKEN,
            "Content-Type": "application/json",
          },
        }
      );

      // Add transactions to the order
      const transactions = transactionsResponse.data.transactions || [];
      order.transactions = transactions;
      console.log(
        `💳 Fetched ${transactions.length} transactions for order ${order.name}`
      );
    }

    return orders;
  } catch (error) {
    console.error("Error fetching Shopify data:", error);
    return [];
  }
};

// Helper function to get a random item from an array
const getRandomItem = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)];
};

// Generate blocks from Shopify orders and transactions
const generateBlocksFromShopifyOrders = (orders: ShopifyOrder[]): any[] => {
  const blocks: any[] = [];

  for (const order of orders) {
    if (!order.transactions || order.transactions.length === 0) {
      continue;
    }

    // Create an RDR block for orders with failed transactions
    const failedTransactions = order.transactions.filter(
      (t) => t.status === "failure"
    );
    if (failedTransactions.length > 0) {
      // Use the most recent failed transaction
      const transaction = failedTransactions[failedTransactions.length - 1];

      const rdrBlock = {
        id: uuidv4(),
        alertId: `RDR-SHOP-${transaction.id}`,
        alertTime: new Date(),
        alertType: "dispute",
        amount: Math.round(parseFloat(transaction.amount) * 100),
        currency: transaction.currency,
        descriptor: "SP WRAP WHIRL STORE",
        authCode: Math.floor(100000 + Math.random() * 900000).toString(),
        cardBin: transaction.payment_details.credit_card_bin || null,
        cardNumber: transaction.payment_details.credit_card_number || null,
        chargebackCode: transaction.error_code || "card_declined",
        disputeAmount: Math.round(parseFloat(transaction.amount) * 100),
        disputeCurrency: transaction.currency,
        transactionTime: new Date(transaction.created_at),

        // RDR specific fields
        acquirerBin: "401134",
        acquirerReferenceNumber: transaction.payment_id,
        alertSource: "VISA",
        alertStatus: "Open",
        caid: "GKJQJVXGFJO1SND",
        descriptorContact: `<EMAIL>`,
        ruleName: getRandomItem(ruleNames),
        ruleType: getRandomItem(ruleTypes),

        // Type field
        type: "RDR",

        // Feedback fields
        feedbackStatus: "PENDING",
        feedbackTime: null,
        feedbackData: null,
      };

      blocks.push(rdrBlock);
    }

    // Create an ETHOCA block for orders with successful transactions
    const successTransactions = order.transactions.filter(
      (t) => t.status === "success"
    );
    if (successTransactions.length > 0) {
      // Use the most recent successful transaction
      const transaction = successTransactions[successTransactions.length - 1];

      // Calculate age (days between order creation and now)
      const orderDate = new Date(order.created_at);
      const now = new Date();
      const ageInDays = Math.floor(
        (now.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24)
      ).toString();

      const ethocaBlock = {
        id: uuidv4(),
        alertId: `ETHOCA-SHOP-${transaction.id}`,
        alertTime: new Date(),
        alertType: "dispute",
        amount: Math.round(parseFloat(transaction.amount) * 100),
        currency: transaction.currency,
        descriptor: "SP WRAP WHIRL STORE",
        authCode: transaction.payment_id.substring(0, 6),
        cardBin: transaction.payment_details.credit_card_bin || null,
        cardNumber: transaction.payment_details.credit_card_number || null,
        chargebackCode: "REFUND",
        disputeAmount: Math.round(parseFloat(transaction.amount) * 100),
        disputeCurrency: transaction.currency,
        transactionTime: new Date(transaction.created_at),

        // ETHOCA required fields
        age: ageInDays,

        // ETHOCA specific fields
        alertSource: transaction.payment_details.credit_card_company || "VISA",
        arn: `ARN-${transaction.payment_id}`,
        issuer:
          transaction.payment_details.credit_card_company ||
          getRandomItem(issuerNames),
        initiatedBy: "Customer",
        liability: getRandomItem(liabilityOptions),
        merchantCategoryCode: "02918",
        transactionId: transaction.id.toString(),
        transactionType: "Online",

        // Type field
        type: "ETHOCA",

        // Feedback fields
        feedbackStatus: "PENDING",
        feedbackTime: null,
        feedbackData: null,
      };

      blocks.push(ethocaBlock);
    }
  }

  return blocks;
};

// Generate additional blocks to reach the target count
const generateAdditionalBlocks = (count: number): any[] => {
  const blocks: any[] = [];
  
  for (let i = 0; i < count; i++) {
    const isRDR = Math.random() < 0.5;
    const amount = Math.floor(Math.random() * 500000) + 1000; // $10 - $5000
    
    if (isRDR) {
      // Generate RDR block
      blocks.push({
        id: uuidv4(),
        alertId: `RDR-GEN-${Date.now()}-${i}`,
        alertTime: new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)), // Last 30 days
        alertType: "dispute",
        amount: amount,
        currency: "USD",
        descriptor: "SP WRAP WHIRL STORE",
        authCode: Math.floor(100000 + Math.random() * 900000).toString(),
        cardBin: Math.floor(400000 + Math.random() * 100000).toString(),
        cardNumber: `****${Math.floor(1000 + Math.random() * 9000)}`,
        chargebackCode: getRandomItem(["card_declined", "insufficient_funds", "fraud", "cancelled_recurring"]),
        disputeAmount: amount,
        disputeCurrency: "USD",
        transactionTime: new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)),
        
        // RDR specific fields
        acquirerBin: "401134",
        acquirerReferenceNumber: `REF-${Date.now()}-${i}`,
        alertSource: "VISA",
        alertStatus: getRandomItem(["Open", "Processing", "Closed"]),
        caid: "GKJQJVXGFJO1SND",
        descriptorContact: "<EMAIL>",
        ruleName: getRandomItem(ruleNames),
        ruleType: getRandomItem(ruleTypes),
        
        // Type field
        type: "RDR",
        
        // Feedback fields
        feedbackStatus: getRandomItem(["PENDING", "SENT", "FAILED"]),
        feedbackTime: Math.random() < 0.3 ? new Date(Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)) : null,
        feedbackData: null,
      });
    } else {
      // Generate ETHOCA block
      const ageInDays = Math.floor(Math.random() * 30) + 1;
      
      blocks.push({
        id: uuidv4(),
        alertId: `ETHOCA-GEN-${Date.now()}-${i}`,
        alertTime: new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)),
        alertType: "dispute",
        amount: amount,
        currency: "USD",
        descriptor: "SP WRAP WHIRL STORE",
        authCode: Math.floor(100000 + Math.random() * 900000).toString().substring(0, 6),
        cardBin: Math.floor(400000 + Math.random() * 100000).toString(),
        cardNumber: `****${Math.floor(1000 + Math.random() * 9000)}`,
        chargebackCode: "REFUND",
        disputeAmount: amount,
        disputeCurrency: "USD",
        transactionTime: new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)),
        
        // ETHOCA required fields
        age: ageInDays.toString(),
        
        // ETHOCA specific fields
        alertSource: getRandomItem(issuerNames),
        arn: `ARN-${Date.now()}-${i}`,
        issuer: getRandomItem(issuerNames),
        initiatedBy: getRandomItem(["Customer", "Merchant", "Issuer"]),
        liability: getRandomItem(liabilityOptions),
        merchantCategoryCode: "02918",
        transactionId: `TXN-${Date.now()}-${i}`,
        transactionType: getRandomItem(["Online", "In-Store", "Phone", "Mail"]),
        
        // Type field
        type: "ETHOCA",
        
        // Feedback fields
        feedbackStatus: getRandomItem(["PENDING", "SENT", "FAILED"]),
        feedbackTime: Math.random() < 0.3 ? new Date(Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)) : null,
        feedbackData: null,
      });
    }
  }
  
  return blocks;
};

/**
 * Seed blocks into the database
 */
export const seedBlocks = async (prisma: PrismaClient) => {
  console.log("🔒 Seeding blocks...");

  // Fetch real data from Shopify
  let shopifyOrders: ShopifyOrder[] = [];
  let shopifyBlocks: any[] = [];

  try {
    shopifyOrders = await fetchShopifyData();
    shopifyBlocks = generateBlocksFromShopifyOrders(shopifyOrders);
    console.log(`Generated ${shopifyBlocks.length} blocks from Shopify data`);
  } catch (error) {
    console.error("Error generating Shopify blocks:", error);
    console.log("Will continue with generated blocks only");
  }

  // Generate additional blocks to reach 500 total
  const additionalBlocks = generateAdditionalBlocks(500 - shopifyBlocks.length);
  const allBlocks = [...shopifyBlocks, ...additionalBlocks];
  console.log(`Generated ${allBlocks.length} total blocks (${shopifyBlocks.length} from Shopify + ${additionalBlocks.length} additional)`);
  
  // Use allBlocks instead of shopifyBlocks for seeding
  const blocksToSeed = allBlocks;

  // First delete existing blocks if needed
  const existingBlocksCount = await prisma.block.count();
  if (existingBlocksCount > 0) {
    console.log(`Deleting ${existingBlocksCount} existing blocks...`);
    await prisma.block.deleteMany({});
  }

  // Create blocks in batches for better performance
  const BATCH_SIZE = 50;

  // Insert all blocks
  if (blocksToSeed.length > 0) {
    // Insert blocks in batches for better performance
    for (let i = 0; i < blocksToSeed.length; i += BATCH_SIZE) {
      const batch = blocksToSeed.slice(i, i + BATCH_SIZE);
      await prisma.block.createMany({
        data: batch,
        skipDuplicates: true,
      });
      console.log(
        `Created blocks batch ${i / BATCH_SIZE + 1}/${Math.ceil(
          blocksToSeed.length / BATCH_SIZE
        )}`
      );
    }
  } else {
    console.log("No blocks to seed");
  }

  const totalBlocks = await prisma.block.count();
  console.log(
    `✅ Seeded ${totalBlocks} blocks (${shopifyBlocks.length} Shopify blocks + ${additionalBlocks.length} generated blocks)`
  );
};
