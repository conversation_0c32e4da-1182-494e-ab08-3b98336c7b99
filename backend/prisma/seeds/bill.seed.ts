import { PrismaClient, Bill<PERSON>tatus, Bill } from '@prisma/client';

export async function seedBills(prisma: PrismaClient): Promise<Bill[]> {
  console.log('🌱 Seeding bills...');

  try {
    // Get first linked store only
    const linkedStore = await prisma.linkedStore.findFirst();

    if (!linkedStore) {
      console.log('⚠️  No linked stores found. Skipping bill seeding.');
      return [];
    }

    // Check if bills already exist
    const existingBills = await prisma.bill.count();
    
    if (existingBills > 0) {
      console.log(`ℹ️  Bills already exist (${existingBills} records). Returning existing bills.`);
      return await prisma.bill.findMany({
        orderBy: { createdAt: 'asc' }
      });
    }

    // Create bills for the first store only
    await prisma.bill.createMany({
      data: [
        {
          linkedStoreId: linkedStore.id,
          amount: 125000, // $1250.00 in cents
          currency: 'USD',
          description: 'Monthly subscription - Premium Plan - December 2024',
          status: BillStatus.PAID,
          dueDate: new Date('2024-12-01'),
        },
        {
          linkedStoreId: linkedStore.id,
          amount: 125000, // $1250.00 in cents
          currency: 'USD',
          description: 'Monthly subscription - Premium Plan - January 2025',
          status: BillStatus.PENDING,
          dueDate: new Date('2025-01-01'),
        },
        {
          linkedStoreId: linkedStore.id,
          amount: 35000, // $350.00 in cents
          currency: 'USD',
          description: 'Add-on services - SMS notifications',
          status: BillStatus.PENDING,
          dueDate: new Date('2025-01-15'),
        },
        {
          linkedStoreId: linkedStore.id,
          amount: 250000, // $2500.00 in cents
          currency: 'USD',
          description: 'Annual subscription upgrade',
          status: BillStatus.FAILED,
          dueDate: new Date('2025-01-01'),
        },
        {
          linkedStoreId: linkedStore.id,
          amount: 15000, // $150.00 in cents
          currency: 'USD',
          description: 'Setup fee',
          status: BillStatus.CANCELLED,
          dueDate: new Date('2024-11-15'),
        },
      ]
    });

    // Fetch all created bills to return
    const allBills = await prisma.bill.findMany({
      orderBy: { createdAt: 'asc' }
    });

    console.log(`✅ Created ${allBills.length} bills`);
    return allBills;
  } catch (error) {
    console.error('❌ Error seeding bills:', error);
    throw error;
  }
}