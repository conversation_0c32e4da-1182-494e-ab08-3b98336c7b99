import { PrismaClient } from "@prisma/client";
import { seedClients } from "./client.seed";
import { seedUsers } from "./user.seed";
import { seedBlocks } from "./block.seed";
import { seedLinkedStores } from "./linked-store.seed";
import { seedBills } from "./bill.seed";
import { seedPaymentHistory } from "./payment-history.seed";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Seeding database...");

  // Seed clients
  // await seedClients(prisma);

  // Seed users
  // await seedUsers(prisma);

  // Seed blocks
  await seedBlocks(prisma);

  // Seed linked stores (depends on users)
  // await seedLinkedStores(prisma);

  // Seed bills (depends on linked stores)
  const bills = await seedBills(prisma);

  // Seed payment history (depends on bills)
  await seedPaymentHistory(prisma, bills);

  console.log("✅ Seeding completed!");
}

main()
  .catch((e) => {
    console.error("❌ Seeding failed:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
