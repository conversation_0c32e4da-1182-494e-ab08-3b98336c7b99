import { PrismaClient } from '@prisma/client';

export async function seedLinkedStores(prisma: PrismaClient) {
  console.log('🌱 Seeding linked stores...');

  try {
    // Check if linked stores already exist
    const existingLinkedStores = await prisma.linkedStore.count();
    
    if (existingLinkedStores > 0) {
      console.log(`ℹ️  Linked stores already exist (${existingLinkedStores} records). Skipping linked store seeding.`);
      return;
    }

    // Get first user to link stores to
    const user = await prisma.user.findFirst();
    
    if (!user) {
      console.log('⚠️  No users found. Skipping linked store seeding.');
      return;
    }

    // Create test linked stores
    const linkedStoresData = [
      {
        userId: user.id,
        storeName: 'Demo Store Premium',
        provider: 'manual',
        providerStoreId: 'demo-store-001',
        data: {
          domain: 'demo-premium.example.com',
          plan: 'premium',
          settings: {
            currency: 'USD',
            timezone: 'America/New_York',
            features: ['analytics', 'advanced_reports', 'sms_notifications']
          },
          metadata: {
            created_for: 'testing',
            plan_type: 'premium'
          }
        }
      },
      {
        userId: user.id,
        storeName: 'Demo Store Standard',
        provider: 'manual',
        providerStoreId: 'demo-store-002',
        data: {
          domain: 'demo-standard.example.com',
          plan: 'standard',
          settings: {
            currency: 'USD',
            timezone: 'America/Los_Angeles',
            features: ['basic_analytics', 'reports']
          },
          metadata: {
            created_for: 'testing',
            plan_type: 'standard'
          }
        }
      }
    ];

    // Create linked stores
    const linkedStores = await prisma.linkedStore.createMany({
      data: linkedStoresData,
    });

    console.log(`✅ Created ${linkedStores.count} linked stores`);
  } catch (error) {
    console.error('❌ Error seeding linked stores:', error);
    throw error;
  }
}