import { PrismaClient, PaymentStatus, <PERSON>, Prisma } from '@prisma/client';

// Generate additional payment history records
const generateAdditionalPaymentHistory = (bills: Bill[], count: number): Prisma.PaymentHistoryCreateManyInput[] => {
  const paymentHistoryData: Prisma.PaymentHistoryCreateManyInput[] = [];
  
  if (bills.length === 0) return paymentHistoryData;
  
  for (let i = 0; i < count; i++) {
    const randomBill = bills[Math.floor(Math.random() * bills.length)];
    const randomAmount = Math.floor(Math.random() * 50000) + 1000; // $10 - $500
    const randomStatus = Math.random() < 0.7 ? PaymentStatus.SUCCEEDED : 
                        Math.random() < 0.8 ? PaymentStatus.FAILED : 
                        Math.random() < 0.9 ? PaymentStatus.PENDING : PaymentStatus.REFUNDED;
    
    // Generate random payment date within the last 90 days
    const randomDate = new Date(Date.now() - Math.floor(Math.random() * 90 * 24 * 60 * 60 * 1000));
    
    const descriptions = [
      "Monthly service charge",
      "Add-on feature payment",
      "Premium upgrade fee",
      "Setup and configuration",
      "Support package",
      "Additional storage",
      "Advanced analytics",
      "Custom integration",
      "Priority support",
      "Enterprise features"
    ];
    
    paymentHistoryData.push({
      linkedStoreId: randomBill.linkedStoreId,
      billId: randomBill.id,
      amount: randomAmount,
      currency: randomBill.currency,
      description: `${descriptions[Math.floor(Math.random() * descriptions.length)]} - ${randomBill.description?.split(' - ')[0] || 'Payment'}`,
      status: randomStatus,
      paymentDate: randomDate,
    });
  }
  
  return paymentHistoryData;
};

export async function seedPaymentHistory(prisma: PrismaClient, bills: Bill[]) {
  console.log('🌱 Seeding payment history...');

  try {
    // Check if payment history already exists
    const existingPaymentHistory = await prisma.paymentHistory.count();
    
    if (existingPaymentHistory > 0) {
      console.log(`🗑️  Deleting ${existingPaymentHistory} existing payment history records...`);
      await prisma.paymentHistory.deleteMany({});
    }

    if (bills.length === 0) {
      console.log('⚠️  No bills found. Skipping payment history seeding.');
      return;
    }

    const paymentHistoryData: Prisma.PaymentHistoryCreateManyInput[] = [];

    // Create payment history for PAID bills
    const paidBills = bills.filter(bill => bill.status === 'PAID');
    
    for (const bill of paidBills) {
      // Create successful payment for each paid bill
      paymentHistoryData.push({
        linkedStoreId: bill.linkedStoreId,
        billId: bill.id,
        amount: bill.amount,
        currency: bill.currency,
        description: `Payment for: ${bill.description}`,
        status: PaymentStatus.SUCCEEDED,
        paymentDate: new Date(bill.dueDate.getTime() + Math.random() * 86400000), // Payment within 1 day of due date
      });
    }

    // Create some failed payment attempts for FAILED bills
    const failedBills = bills.filter(bill => bill.status === 'FAILED');
    
    for (const bill of failedBills) {
      // First attempt - failed
      paymentHistoryData.push({
        linkedStoreId: bill.linkedStoreId,
        billId: bill.id,
        amount: bill.amount,
        currency: bill.currency,
        description: `Payment attempt 1 for: ${bill.description}`,
        status: PaymentStatus.FAILED,
        paymentDate: new Date(bill.dueDate),
      });

      // Second attempt - also failed
      paymentHistoryData.push({
        linkedStoreId: bill.linkedStoreId,
        billId: bill.id,
        amount: bill.amount,
        currency: bill.currency,
        description: `Payment attempt 2 for: ${bill.description}`,
        status: PaymentStatus.FAILED,
        paymentDate: new Date(bill.dueDate.getTime() + 86400000), // 1 day later
      });
    }

    // Create refund for cancelled bills
    const cancelledBills = bills.filter(bill => bill.status === 'CANCELLED');
    
    for (const bill of cancelledBills) {
      // Original payment
      paymentHistoryData.push({
        linkedStoreId: bill.linkedStoreId,
        billId: bill.id,
        amount: bill.amount,
        currency: bill.currency,
        description: `Original payment for: ${bill.description}`,
        status: PaymentStatus.SUCCEEDED,
        paymentDate: new Date(bill.dueDate.getTime() - 5 * 86400000), // 5 days before due date
      });

      // Refund
      paymentHistoryData.push({
        linkedStoreId: bill.linkedStoreId,
        billId: bill.id,
        amount: bill.amount,
        currency: bill.currency,
        description: `Refund for cancelled service: ${bill.description}`,
        status: PaymentStatus.REFUNDED,
        paymentDate: new Date(bill.dueDate.getTime() + 5 * 86400000), // 5 days after due date
      });
    }

    // Create some additional payment history for PENDING bills (partial payments)
    const pendingBills = bills.filter(bill => bill.status === 'PENDING').slice(0, 2);
    
    for (const bill of pendingBills) {
      // Add a pending payment attempt
      paymentHistoryData.push({
        linkedStoreId: bill.linkedStoreId,
        billId: bill.id,
        amount: bill.amount,
        currency: bill.currency,
        description: `Pending payment - Processing for: ${bill.description}`,
        status: PaymentStatus.PENDING,
        paymentDate: new Date(),
      });
    }

    // Generate additional payment history records to reach 500 total
    const additionalPaymentHistory = generateAdditionalPaymentHistory(bills, 500 - paymentHistoryData.length);
    paymentHistoryData.push(...additionalPaymentHistory);

    // Create payment history records
    const paymentHistory = await prisma.paymentHistory.createMany({
      data: paymentHistoryData,
    });

    console.log(`✅ Created ${paymentHistory.count} payment history records (targeting 500 total)`);
  } catch (error) {
    console.error('❌ Error seeding payment history:', error);
    throw error;
  }
}