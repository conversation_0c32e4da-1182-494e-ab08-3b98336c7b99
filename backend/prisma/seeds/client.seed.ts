import { PrismaClient } from "@prisma/client";
import crypto from "crypto";

export const DEFAULT_CLIENT = {
  name: "LEDHCG",
  email: "<EMAIL>",
  description: "Owner",
  APIKey: crypto.randomBytes(20).toString("hex"),
};

export async function seedClients(prisma: PrismaClient) {
  console.log("Seeding clients...");

  // Check if client already exists
  const existingClient = await prisma.client.findUnique({
    where: { email: DEFAULT_CLIENT.email },
  });

  if (!existingClient) {
    await prisma.client.create({
      data: DEFAULT_CLIENT,
    });
    console.log(`Created client: ${DEFAULT_CLIENT.name}`);
  } else {
    console.log(`Client ${DEFAULT_CLIENT.name} already exists, skipping.`);
  }

  console.log("Clients seeding completed!");
}
