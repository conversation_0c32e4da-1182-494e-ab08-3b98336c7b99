import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";
import { env } from "../../src/config/environment";

export const DEFAULT_ADMIN = {
  code: "ADM001",
  fullName: "System Administrator",
  phoneNumber: "0123456789",
  email: `admin@${env?.SERVER_DOMAIN || "example.com"}`,
  password: "admin@123",
  address: "System Address",
  gender: "male",
  birthDate: new Date("1990-01-01"),
  status: true,
  note: "Default system administrator",
};

export async function seedUsers(prisma: PrismaClient) {
  console.log("Seeding users...");

  // Check if admin user already exists
  const existingAdmin = await prisma.user.findUnique({
    where: { email: DEFAULT_ADMIN.email },
  });

  if (!existingAdmin) {
    // Hash password before storing
    const hashedPassword = await bcrypt.hash(DEFAULT_ADMIN.password, 10);

    // Create admin user
    await prisma.user.create({
      data: {
        ...DEFAULT_ADMIN,
        password: hashedPassword,
      },
    });
    console.log(`Created admin user: ${DEFAULT_ADMIN.email}`);
  } else {
    console.log(`Admin user ${DEFAULT_ADMIN.email} already exists, skipping.`);
  }

  console.log("Users seeding completed!");
}
