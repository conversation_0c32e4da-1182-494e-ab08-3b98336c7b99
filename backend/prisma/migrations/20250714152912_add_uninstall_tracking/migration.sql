-- Add uninstallation tracking fields to users table
ALTER TABLE "chargeback"."users" ADD COLUMN "is_active" BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE "chargeback"."users" ADD COLUMN "uninstalled_at" TIMESTAMPTZ(6);

-- Add uninstallation tracking fields to linked_stores table  
ALTER TABLE "chargeback"."linked_stores" ADD COLUMN "is_active" BOOLEAN NOT NULL DEFAULT true;
ALTER TABLE "chargeback"."linked_stores" ADD COLUMN "uninstalled_at" TIMESTAMPTZ(6);

-- Create index for better query performance
CREATE INDEX "users_is_active_idx" ON "chargeback"."users" ("is_active");
CREATE INDEX "linked_stores_is_active_idx" ON "chargeback"."linked_stores" ("is_active");
CREATE INDEX "linked_stores_provider_store_id_active_idx" ON "chargeback"."linked_stores" ("provider_store_id", "is_active"); 