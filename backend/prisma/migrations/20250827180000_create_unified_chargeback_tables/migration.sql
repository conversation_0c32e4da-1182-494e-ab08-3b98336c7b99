-- Migration to create unified transaction and dispute tables for multi-provider chargeback rate calculation
-- This migration creates optimized tables for aggregating chargeback data across Shopify and Stripe

-- Create unified_transactions table
CREATE TABLE "unified_transactions" (
    "id" TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
    "user_id" TEXT NOT NULL,
    "linked_store_id" TEXT NOT NULL,
    "provider" TEXT NOT NULL, -- 'shopify' or 'stripe'
    
    -- Core transaction data
    "provider_transaction_id" TEXT NOT NULL, -- Original ID from provider
    "amount" BIGINT NOT NULL, -- Amount in cents
    "currency" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    
    -- Time tracking for chargeback rate calculations
    "processed_at" TIMESTAMPTZ NOT NULL, -- When transaction was processed
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create unified_disputes table
CREATE TABLE "unified_disputes" (
    "id" TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
    "user_id" TEXT NOT NULL,
    "linked_store_id" TEXT NOT NULL,
    "provider" TEXT NOT NULL, -- 'shopify' or 'stripe'
    
    -- Core dispute data
    "provider_dispute_id" TEXT NOT NULL, -- Original ID from provider
    "unified_transaction_id" TEXT, -- Optional reference to unified_transactions
    "amount" BIGINT NOT NULL, -- Dispute amount in cents
    "currency" TEXT NOT NULL,
    "type" TEXT NOT NULL, -- 'chargeback', 'inquiry', etc.
    "status" TEXT NOT NULL,
    "reason" TEXT,
    
    -- Time tracking for chargeback rate calculations
    "initiated_at" TIMESTAMPTZ, -- When dispute was initiated
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add foreign key constraints
ALTER TABLE "unified_transactions" ADD CONSTRAINT "unified_transactions_user_id_fkey" 
    FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "unified_transactions" ADD CONSTRAINT "unified_transactions_linked_store_id_fkey" 
    FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "unified_disputes" ADD CONSTRAINT "unified_disputes_user_id_fkey" 
    FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "unified_disputes" ADD CONSTRAINT "unified_disputes_linked_store_id_fkey" 
    FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "unified_disputes" ADD CONSTRAINT "unified_disputes_unified_transaction_id_fkey" 
    FOREIGN KEY ("unified_transaction_id") REFERENCES "unified_transactions"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Add unique constraints to ensure no duplicates per provider
ALTER TABLE "unified_transactions" ADD CONSTRAINT "unified_transactions_provider_transaction_id_key" 
    UNIQUE ("provider", "provider_transaction_id");

ALTER TABLE "unified_disputes" ADD CONSTRAINT "unified_disputes_provider_dispute_id_key" 
    UNIQUE ("provider", "provider_dispute_id");

-- Performance indexes for chargeback rate calculations
-- These indexes are specifically optimized for time-based aggregation queries

-- Unified Transactions indexes
CREATE INDEX "unified_transactions_user_id_processed_at_idx" ON "unified_transactions"("user_id", "processed_at");
CREATE INDEX "unified_transactions_linked_store_id_processed_at_idx" ON "unified_transactions"("linked_store_id", "processed_at");
CREATE INDEX "unified_transactions_provider_idx" ON "unified_transactions"("provider");
CREATE INDEX "unified_transactions_status_idx" ON "unified_transactions"("status");
CREATE INDEX "unified_transactions_processed_at_idx" ON "unified_transactions"("processed_at");

-- Unified Disputes indexes
CREATE INDEX "unified_disputes_user_id_initiated_at_idx" ON "unified_disputes"("user_id", "initiated_at");
CREATE INDEX "unified_disputes_linked_store_id_initiated_at_idx" ON "unified_disputes"("linked_store_id", "initiated_at");
CREATE INDEX "unified_disputes_type_idx" ON "unified_disputes"("type");
CREATE INDEX "unified_disputes_status_idx" ON "unified_disputes"("status");
CREATE INDEX "unified_disputes_provider_idx" ON "unified_disputes"("provider");
CREATE INDEX "unified_disputes_initiated_at_idx" ON "unified_disputes"("initiated_at");

-- Comments to document the purpose of these tables
COMMENT ON TABLE "unified_transactions" IS 'Unified transaction data from all providers (Shopify, Stripe) optimized for chargeback rate calculations';
COMMENT ON TABLE "unified_disputes" IS 'Unified dispute/chargeback data from all providers (Shopify, Stripe) for aggregated chargeback rate analysis';

-- Comments on key columns
COMMENT ON COLUMN "unified_transactions"."processed_at" IS 'Transaction processing timestamp used for time-based chargeback rate aggregation';
COMMENT ON COLUMN "unified_disputes"."initiated_at" IS 'Dispute initiation timestamp used for time-based chargeback rate aggregation';
COMMENT ON COLUMN "unified_disputes"."type" IS 'Dispute type: chargeback, inquiry, etc. Only chargebacks are counted in chargeback rate calculations';