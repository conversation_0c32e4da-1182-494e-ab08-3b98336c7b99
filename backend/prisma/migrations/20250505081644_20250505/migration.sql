/*
  Warnings:

  - You are about to drop the column `feedbackData` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `feedbackStatus` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `feedbackTime` on the `blocks` table. All the data in the column will be lost.
  - Made the column `feedback_status` on table `alerts` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "alerts" ALTER COLUMN "feedback_status" SET NOT NULL,
ALTER COLUMN "feedback_time" SET DATA TYPE TIMESTAMP(3);

-- AlterTable
ALTER TABLE "blocks" DROP COLUMN "feedbackData",
DROP COLUMN "feedbackStatus",
DROP COLUMN "feedbackTime",
ADD COLUMN     "feedback_data" JSONB,
ADD COLUMN     "feedback_status" TEXT NOT NULL DEFAULT 'PENDING',
ADD COLUMN     "feedback_time" TIMESTAMP(3),
ALTER COLUMN "updated_at" DROP DEFAULT;
