/*
  Warnings:

  - Make client_secret nullable in stripe_payment_intents table
  - Make capture_method nullable in stripe_payment_intents table  
  - Make confirmation_method nullable in stripe_payment_intents table

*/

-- Make client_secret nullable (<PERSON><PERSON> may not return this for completed payments)
ALTER TABLE "stripe_payment_intents" ALTER COLUMN "client_secret" DROP NOT NULL;

-- Make capture_method nullable (may not always be present)
ALTER TABLE "stripe_payment_intents" ALTER COLUMN "capture_method" DROP NOT NULL;

-- Make confirmation_method nullable (may not always be present)
ALTER TABLE "stripe_payment_intents" ALTER COLUMN "confirmation_method" DROP NOT NULL;