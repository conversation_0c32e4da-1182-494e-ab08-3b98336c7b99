-- CreateTable
CREATE TABLE "stripe_payment_intents" (
    "id" TEXT NOT NULL,
    "linked_store_id" TEXT NOT NULL,
    "object" TEXT NOT NULL DEFAULT 'payment_intent',
    "amount" BIGINT NOT NULL,
    "amount_capturable" BIGINT,
    "amount_received" BIGINT,
    "application" TEXT,
    "application_fee_amount" BIGINT,
    "automatic_payment_methods" JSONB,
    "canceled_at" TIMESTAMPTZ,
    "cancellation_reason" TEXT,
    "capture_method" TEXT NOT NULL,
    "client_secret" TEXT NOT NULL,
    "confirmation_method" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL,
    "currency" TEXT NOT NULL,
    "customer_id" TEXT,
    "description" TEXT,
    "invoice_id" TEXT,
    "last_payment_error" JSONB,
    "latest_charge_id" TEXT,
    "livemode" BOOLEAN NOT NULL,
    "metadata" JSONB,
    "next_action" JSONB,
    "on_behalf_of" TEXT,
    "payment_method_id" TEXT,
    "payment_method_configuration_details" JSONB,
    "payment_method_options" JSONB,
    "payment_method_types" TEXT[],
    "processing" JSONB,
    "receipt_email" TEXT,
    "review" TEXT,
    "setup_future_usage" TEXT,
    "shipping" JSONB,
    "source" TEXT,
    "statement_descriptor" TEXT,
    "statement_descriptor_suffix" TEXT,
    "status" TEXT NOT NULL,
    "transfer_data" JSONB,
    "transfer_group" TEXT,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "stripe_payment_intents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "stripe_charges" (
    "id" TEXT NOT NULL,
    "linked_store_id" TEXT NOT NULL,
    "object" TEXT NOT NULL DEFAULT 'charge',
    "amount" BIGINT NOT NULL,
    "amount_captured" BIGINT,
    "amount_refunded" BIGINT,
    "application" TEXT,
    "application_fee" TEXT,
    "application_fee_amount" BIGINT,
    "balance_transaction" TEXT,
    "billing_details" JSONB,
    "calculated_statement_descriptor" TEXT,
    "captured" BOOLEAN NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL,
    "currency" TEXT NOT NULL,
    "customer_id" TEXT,
    "description" TEXT,
    "destination" TEXT,
    "dispute_id" TEXT,
    "disputed" BOOLEAN NOT NULL DEFAULT false,
    "failure_code" TEXT,
    "failure_message" TEXT,
    "fraud_details" JSONB,
    "invoice_id" TEXT,
    "livemode" BOOLEAN NOT NULL,
    "metadata" JSONB,
    "on_behalf_of" TEXT,
    "order_id" TEXT,
    "outcome" JSONB,
    "paid" BOOLEAN NOT NULL,
    "payment_intent_id" TEXT,
    "payment_method" TEXT,
    "payment_method_details" JSONB,
    "receipt_email" TEXT,
    "receipt_number" TEXT,
    "receipt_url" TEXT,
    "refunded" BOOLEAN NOT NULL DEFAULT false,
    "refunds" JSONB,
    "review" TEXT,
    "shipping" JSONB,
    "source" JSONB,
    "source_transfer" TEXT,
    "statement_descriptor" TEXT,
    "statement_descriptor_suffix" TEXT,
    "status" TEXT NOT NULL,
    "transfer_data" JSONB,
    "transfer_group" TEXT,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "stripe_charges_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "stripe_disputes" (
    "id" TEXT NOT NULL,
    "linked_store_id" TEXT NOT NULL,
    "object" TEXT NOT NULL DEFAULT 'dispute',
    "amount" BIGINT NOT NULL,
    "balance_transactions" JSONB,
    "charge_id" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL,
    "currency" TEXT NOT NULL,
    "evidence" JSONB,
    "evidence_details" JSONB,
    "is_charge_refundable" BOOLEAN NOT NULL,
    "livemode" BOOLEAN NOT NULL,
    "metadata" JSONB,
    "network_reason_code" TEXT,
    "reason" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "stripe_disputes_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "stripe_payment_intents_linked_store_id_idx" ON "stripe_payment_intents"("linked_store_id");

-- CreateIndex
CREATE INDEX "stripe_payment_intents_status_idx" ON "stripe_payment_intents"("status");

-- CreateIndex
CREATE INDEX "stripe_payment_intents_created_at_idx" ON "stripe_payment_intents"("created_at");

-- CreateIndex
CREATE INDEX "stripe_payment_intents_customer_id_idx" ON "stripe_payment_intents"("customer_id");

-- CreateIndex
CREATE INDEX "stripe_payment_intents_latest_charge_id_idx" ON "stripe_payment_intents"("latest_charge_id");

-- CreateIndex
CREATE INDEX "stripe_charges_linked_store_id_idx" ON "stripe_charges"("linked_store_id");

-- CreateIndex
CREATE INDEX "stripe_charges_status_idx" ON "stripe_charges"("status");

-- CreateIndex
CREATE INDEX "stripe_charges_created_at_idx" ON "stripe_charges"("created_at");

-- CreateIndex
CREATE INDEX "stripe_charges_customer_id_idx" ON "stripe_charges"("customer_id");

-- CreateIndex
CREATE INDEX "stripe_charges_payment_intent_id_idx" ON "stripe_charges"("payment_intent_id");

-- CreateIndex
CREATE INDEX "stripe_charges_disputed_idx" ON "stripe_charges"("disputed");

-- CreateIndex
CREATE INDEX "stripe_charges_refunded_idx" ON "stripe_charges"("refunded");

-- CreateIndex
CREATE INDEX "stripe_disputes_linked_store_id_idx" ON "stripe_disputes"("linked_store_id");

-- CreateIndex
CREATE INDEX "stripe_disputes_status_idx" ON "stripe_disputes"("status");

-- CreateIndex
CREATE INDEX "stripe_disputes_created_at_idx" ON "stripe_disputes"("created_at");

-- CreateIndex
CREATE INDEX "stripe_disputes_charge_id_idx" ON "stripe_disputes"("charge_id");

-- CreateIndex
CREATE INDEX "stripe_disputes_reason_idx" ON "stripe_disputes"("reason");

-- AddForeignKey
ALTER TABLE "stripe_payment_intents" ADD CONSTRAINT "stripe_payment_intents_linked_store_id_fkey" FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "stripe_charges" ADD CONSTRAINT "stripe_charges_linked_store_id_fkey" FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "stripe_charges" ADD CONSTRAINT "stripe_charges_payment_intent_id_fkey" FOREIGN KEY ("payment_intent_id") REFERENCES "stripe_payment_intents"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "stripe_disputes" ADD CONSTRAINT "stripe_disputes_linked_store_id_fkey" FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "stripe_disputes" ADD CONSTRAINT "stripe_disputes_charge_id_fkey" FOREIGN KEY ("charge_id") REFERENCES "stripe_charges"("id") ON DELETE RESTRICT ON UPDATE CASCADE;