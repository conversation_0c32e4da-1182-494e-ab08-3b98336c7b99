-- CreateTable for stat_country
CREATE TABLE "stat_country" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "linked_store_id" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "country_code" TEXT NOT NULL,
    "country_name" TEXT NOT NULL,
    "count" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "stat_country_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "stat_country_user_id_idx" ON "stat_country"("user_id");

-- CreateIndex
CREATE INDEX "stat_country_linked_store_id_idx" ON "stat_country"("linked_store_id");

-- CreateIndex
CREATE INDEX "stat_country_provider_idx" ON "stat_country"("provider");

-- CreateIndex
CREATE INDEX "stat_country_country_code_idx" ON "stat_country"("country_code");

-- CreateIndex
CREATE INDEX "stat_country_count_idx" ON "stat_country"("count" DESC);

-- CreateIndex
CREATE UNIQUE INDEX "stat_country_user_id_linked_store_id_provider_country_code_key" ON "stat_country"("user_id", "linked_store_id", "provider", "country_code");

-- AddForeignKey
ALTER TABLE "stat_country" ADD CONSTRAINT "stat_country_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "stat_country" ADD CONSTRAINT "stat_country_linked_store_id_fkey" FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- CreateTable for stat_reason
CREATE TABLE "stat_reason" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "linked_store_id" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "reason_code" TEXT NOT NULL,
    "reason_name" TEXT NOT NULL,
    "count" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "stat_reason_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "stat_reason_user_id_idx" ON "stat_reason"("user_id");

-- CreateIndex
CREATE INDEX "stat_reason_linked_store_id_idx" ON "stat_reason"("linked_store_id");

-- CreateIndex
CREATE INDEX "stat_reason_provider_idx" ON "stat_reason"("provider");

-- CreateIndex
CREATE INDEX "stat_reason_reason_code_idx" ON "stat_reason"("reason_code");

-- CreateIndex
CREATE INDEX "stat_reason_count_idx" ON "stat_reason"("count" DESC);

-- CreateIndex
CREATE UNIQUE INDEX "stat_reason_user_id_linked_store_id_provider_reason_code_key" ON "stat_reason"("user_id", "linked_store_id", "provider", "reason_code");

-- AddForeignKey
ALTER TABLE "stat_reason" ADD CONSTRAINT "stat_reason_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "stat_reason" ADD CONSTRAINT "stat_reason_linked_store_id_fkey" FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;