-- CreateTable
CREATE TABLE "block_refunds" (
    "id" TEXT NOT NULL,
    "block_id" TEXT NOT NULL,
    "store_id" TEXT NOT NULL,
    "order_id" BIGINT NOT NULL,
    "refund_id" BIGINT,
    "amount" DECIMAL(15,2) NOT NULL,
    "currency" TEXT NOT NULL,
    "reason" TEXT,
    "note" TEXT,
    "notify" BOOLEAN NOT NULL DEFAULT true,
    "shipping" JSONB,
    "refund_line_items" JSONB,
    "transactions" JSONB,
    "order_adjustments" JSONB,
    "duties" JSONB,
    "gateway" TEXT,
    "parent_id" BIGINT,
    "processed_at" TIMESTAMP(3),
    "restock" BOOLEAN NOT NULL DEFAULT true,
    "user_id" BIGINT,
    "admin_graphql_api_id" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "block_refunds_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "block_refunds" ADD CONSTRAINT "block_refunds_block_id_fkey" FOREIGN KEY ("block_id") REFERENCES "blocks"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "block_refunds" ADD CONSTRAINT "block_refunds_store_id_fkey" FOREIGN KEY ("store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "block_refunds" ADD CONSTRAINT "block_refunds_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "shopify_orders"("id") ON DELETE CASCADE ON UPDATE CASCADE;
