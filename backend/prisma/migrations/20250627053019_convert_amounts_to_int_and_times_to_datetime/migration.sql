/*
  Warnings:

  - The `dispute_amount` column on the `blocks` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `transaction_time` column on the `blocks` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the `alert_feedbacks` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `alerts` table. If the table is not empty, all the data it contains will be lost.
  - Changed the type of `amount` on the `blocks` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `alert_time` on the `blocks` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- DropForeignKey
ALTER TABLE "alert_feedbacks" DROP CONSTRAINT "alert_feedbacks_predictor_id_fkey";

-- AlterTable
ALTER TABLE "blocks" DROP COLUMN "amount",
ADD COLUMN     "amount" INTEGER NOT NULL,
DROP COLUMN "alert_time",
ADD COLUMN     "alert_time" TIMESTAMP(3) NOT NULL,
DROP COLUMN "dispute_amount",
ADD COLUMN     "dispute_amount" INTEGER,
DROP COLUMN "transaction_time",
ADD COLUMN     "transaction_time" TIMESTAMP(3);

-- DropTable
DROP TABLE "alert_feedbacks";

-- DropTable
DROP TABLE "alerts";
