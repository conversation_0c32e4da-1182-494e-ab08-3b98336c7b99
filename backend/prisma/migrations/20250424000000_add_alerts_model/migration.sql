-- CreateTable
CREATE TABLE "alerts" (
    "id" TEXT NOT NULL,
    "alertId" TEXT NOT NULL,
    "alertTime" TEXT NOT NULL,
    "alertType" TEXT NOT NULL,
    "amount" TEXT NOT NULL,
    "currency" TEXT NOT NULL,
    "descriptor" TEXT NOT NULL,
    "authCode" TEXT,
    "cardBin" TEXT,
    "cardNumber" TEXT,
    "chargebackCode" TEXT,
    "disputeAmount" TEXT,
    "disputeCurrency" TEXT,
    "transactionTime" TEXT,
    "age" TEXT,
    "alertSource" TEXT,
    "arn" TEXT,
    "issuer" TEXT,
    "initiatedBy" TEXT,
    "liability" TEXT,
    "merchantCategoryCode" TEXT,
    "transactionId" TEXT,
    "transactionType" TEXT,
    "acquirerBin" TEXT,
    "acquirerReferenceNumber" TEXT,
    "alertStatus" TEXT,
    "caid" TEXT,
    "descriptorContact" TEXT,
    "ruleName" TEXT,
    "ruleType" TEXT,
    "type" TEXT NOT NULL DEFAULT 'ETHOCA',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "alerts_pkey" PRIMARY KEY ("id")
); 