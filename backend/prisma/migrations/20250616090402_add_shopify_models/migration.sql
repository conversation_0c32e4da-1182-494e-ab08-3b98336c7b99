-- CreateTable
CREATE TABLE "shopify_orders" (
    "id" BIGINT NOT NULL,
    "linked_store_id" TEXT NOT NULL,
    "name" TEXT,
    "note" TEXT,
    "tags" TEXT,
    "test" BOOLEAN,
    "email" TEXT,
    "phone" TEXT,
    "token" TEXT,
    "app_id" INTEGER,
    "number" INTEGER,
    "currency" TEXT,
    "customer_data" JSONB,
    "closed_at" TIMESTAMP(3),
    "confirmed" BOOLEAN,
    "device_id" TEXT,
    "po_number" TEXT,
    "reference" TEXT,
    "tax_data" JSONB,
    "total_tax" DECIMAL(15,2),
    "browser_ip" TEXT,
    "cart_token" TEXT,
    "created_at" TIMESTAMP(3),
    "line_items" JSONB,
    "source_url" TEXT,
    "tax_exempt" BOOLEAN,
    "updated_at" TIMESTAMP(3),
    "checkout_id" BIGINT,
    "location_id" BIGINT,
    "source_name" TEXT,
    "total_price" DECIMAL(15,2),
    "cancelled_at" TIMESTAMP(3),
    "fulfillments" JSONB,
    "landing_site" TEXT,
    "order_number" INTEGER,
    "processed_at" TIMESTAMP(3),
    "total_weight" INTEGER,
    "cancel_reason" TEXT,
    "contact_email" TEXT,
    "payment_terms" JSONB,
    "shipping_lines" JSONB,
    "subtotal_price" DECIMAL(15,2),
    "taxes_included" BOOLEAN,
    "billing_address" JSONB,
    "customer_locale" TEXT,
    "duties_included" BOOLEAN,
    "estimated_taxes" BOOLEAN,
    "note_attributes" JSONB,
    "total_discounts" DECIMAL(15,2),
    "financial_status" TEXT,
    "landing_site_ref" TEXT,
    "order_status_url" TEXT,
    "shipping_address" JSONB,
    "source_identifier" TEXT,
    "total_outstanding" DECIMAL(15,2),
    "fulfillment_status" TEXT,

    CONSTRAINT "shopify_orders_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "shopify_transactions" (
    "id" BIGINT NOT NULL,
    "linked_store_id" TEXT NOT NULL,
    "order_id" BIGINT,
    "payout_id" BIGINT,
    "fee" DECIMAL(15,2),
    "net" DECIMAL(15,2),
    "test" BOOLEAN,
    "type" TEXT,
    "amount" DECIMAL(15,2),
    "currency" TEXT,
    "source_id" BIGINT,
    "source_type" TEXT,
    "processed_at" TIMESTAMP(3),
    "payout_status" TEXT,
    "source_order_id" BIGINT,
    "adjustment_reason" TEXT,
    "source_order_transaction_id" BIGINT,
    "adjustment_order_transactions" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "shopify_transactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "shopify_payouts" (
    "id" BIGINT NOT NULL,
    "linked_store_id" TEXT NOT NULL,
    "date" TIMESTAMP(3),
    "amount" DECIMAL(15,2),
    "status" TEXT,
    "summary" JSONB,
    "currency" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "shopify_payouts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "shopify_disputes" (
    "id" BIGINT NOT NULL,
    "linked_store_id" TEXT NOT NULL,
    "transaction_id" BIGINT,
    "type" TEXT,
    "amount" DECIMAL(15,2),
    "reason" TEXT,
    "status" TEXT,
    "currency" TEXT,
    "order_id" BIGINT,
    "finalized_on" TIMESTAMP(3),
    "initiated_at" TIMESTAMP(3),
    "evidence_due_by" TIMESTAMP(3),
    "evidence_sent_on" TIMESTAMP(3),
    "network_reason_code" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "shopify_disputes_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "shopify_orders" ADD CONSTRAINT "shopify_orders_linked_store_id_fkey" FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shopify_transactions" ADD CONSTRAINT "shopify_transactions_linked_store_id_fkey" FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shopify_transactions" ADD CONSTRAINT "shopify_transactions_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "shopify_orders"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shopify_transactions" ADD CONSTRAINT "shopify_transactions_payout_id_fkey" FOREIGN KEY ("payout_id") REFERENCES "shopify_payouts"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shopify_payouts" ADD CONSTRAINT "shopify_payouts_linked_store_id_fkey" FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shopify_disputes" ADD CONSTRAINT "shopify_disputes_linked_store_id_fkey" FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shopify_disputes" ADD CONSTRAINT "shopify_disputes_transaction_id_fkey" FOREIGN KEY ("transaction_id") REFERENCES "shopify_transactions"("id") ON DELETE SET NULL ON UPDATE CASCADE;
