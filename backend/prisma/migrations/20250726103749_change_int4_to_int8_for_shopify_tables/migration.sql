/*
  Warnings:

  - You are about to alter the column `fee` on the `shopify_transactions` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `net` on the `shopify_transactions` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `amount` on the `shopify_transactions` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `amount_rounding` on the `shopify_transactions` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `reference_amount` on the `shopify_transactions` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `app_id` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `number` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `total_tax` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `total_price` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `order_number` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `total_weight` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `subtotal_price` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `total_discounts` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `total_outstanding` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `current_subtotal_price` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `current_total_discounts` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `current_total_price` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `total_line_items_price` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `total_tip_received` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `amount` on the `shopify_disputes` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `amount` on the `shopify_payouts` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `amount` on the `shopify_subscriptions` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `capped_amount` on the `shopify_subscriptions` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `current_usage` on the `shopify_subscriptions` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `trial_days` on the `shopify_subscriptions` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `quantity` on the `shopify_usage_records` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `price` on the `shopify_usage_records` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.
  - You are about to alter the column `total_amount` on the `shopify_usage_records` table. The data in that column could be lost. The data in that column will be cast from `Integer` to `BigInt`.

*/

-- AlterTable for shopify_transactions
ALTER TABLE "shopify_transactions" 
ALTER COLUMN "fee" SET DATA TYPE BIGINT,
ALTER COLUMN "net" SET DATA TYPE BIGINT,
ALTER COLUMN "amount" SET DATA TYPE BIGINT,
ALTER COLUMN "amount_rounding" SET DATA TYPE BIGINT,
ALTER COLUMN "reference_amount" SET DATA TYPE BIGINT;

-- AlterTable for shopify_orders
ALTER TABLE "shopify_orders" 
ALTER COLUMN "app_id" SET DATA TYPE BIGINT,
ALTER COLUMN "number" SET DATA TYPE BIGINT,
ALTER COLUMN "total_tax" SET DATA TYPE BIGINT,
ALTER COLUMN "total_price" SET DATA TYPE BIGINT,
ALTER COLUMN "order_number" SET DATA TYPE BIGINT,
ALTER COLUMN "total_weight" SET DATA TYPE BIGINT,
ALTER COLUMN "subtotal_price" SET DATA TYPE BIGINT,
ALTER COLUMN "total_discounts" SET DATA TYPE BIGINT,
ALTER COLUMN "total_outstanding" SET DATA TYPE BIGINT,
ALTER COLUMN "current_subtotal_price" SET DATA TYPE BIGINT,
ALTER COLUMN "current_total_discounts" SET DATA TYPE BIGINT,
ALTER COLUMN "current_total_price" SET DATA TYPE BIGINT,
ALTER COLUMN "total_line_items_price" SET DATA TYPE BIGINT,
ALTER COLUMN "total_tip_received" SET DATA TYPE BIGINT;

-- AlterTable for shopify_disputes
ALTER TABLE "shopify_disputes" 
ALTER COLUMN "amount" SET DATA TYPE BIGINT;

-- AlterTable for shopify_payouts
ALTER TABLE "shopify_payouts" 
ALTER COLUMN "amount" SET DATA TYPE BIGINT;

-- AlterTable for shopify_subscriptions
ALTER TABLE "shopify_subscriptions" 
ALTER COLUMN "amount" SET DATA TYPE BIGINT,
ALTER COLUMN "capped_amount" SET DATA TYPE BIGINT,
ALTER COLUMN "current_usage" SET DATA TYPE BIGINT,
ALTER COLUMN "trial_days" SET DATA TYPE BIGINT;

-- AlterTable for shopify_usage_records
ALTER TABLE "shopify_usage_records" 
ALTER COLUMN "quantity" SET DATA TYPE BIGINT,
ALTER COLUMN "price" SET DATA TYPE BIGINT,
ALTER COLUMN "total_amount" SET DATA TYPE BIGINT;