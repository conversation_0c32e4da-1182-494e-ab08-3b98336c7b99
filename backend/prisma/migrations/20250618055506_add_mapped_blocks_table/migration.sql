-- CreateTable
CREATE TABLE "mapped_blocks" (
    "id" TEXT NOT NULL,
    "block_id" TEXT NOT NULL,
    "linked_store_id" TEXT NOT NULL,
    "order_id" BIGINT,
    "transaction_id" BIGINT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "mapped_blocks_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "mapped_blocks_block_id_linked_store_id_order_id_transaction_key" ON "mapped_blocks"("block_id", "linked_store_id", "order_id", "transaction_id");

-- AddForeignKey
ALTER TABLE "mapped_blocks" ADD CONSTRAINT "mapped_blocks_block_id_fkey" FOREIGN KEY ("block_id") REFERENCES "blocks"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add<PERSON><PERSON><PERSON><PERSON>ey
ALTER TABLE "mapped_blocks" ADD CONSTRAINT "mapped_blocks_linked_store_id_fkey" FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "mapped_blocks" ADD CONSTRAINT "mapped_blocks_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "shopify_orders"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "mapped_blocks" ADD CONSTRAINT "mapped_blocks_transaction_id_fkey" FOREIGN KEY ("transaction_id") REFERENCES "shopify_transactions"("id") ON DELETE SET NULL ON UPDATE CASCADE;
