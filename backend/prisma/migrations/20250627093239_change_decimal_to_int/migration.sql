/*
  Warnings:

  - You are about to alter the column `amount` on the `bills` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `amount` on the `block_refunds` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `amount` on the `payment_history` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `amount` on the `shopify_disputes` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `total_tax` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `total_price` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `subtotal_price` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `total_discounts` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `total_outstanding` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `current_subtotal_price` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `current_total_discounts` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `current_total_price` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `total_line_items_price` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `total_tip_received` on the `shopify_orders` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `amount` on the `shopify_payouts` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `fee` on the `shopify_transactions` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `net` on the `shopify_transactions` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `amount` on the `shopify_transactions` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.
  - You are about to alter the column `amount_rounding` on the `shopify_transactions` table. The data in that column could be lost. The data in that column will be cast from `Decimal(15,2)` to `Integer`.

*/
-- AlterTable
ALTER TABLE "bills" ALTER COLUMN "amount" SET DATA TYPE INTEGER;

-- AlterTable
ALTER TABLE "block_refunds" ALTER COLUMN "amount" SET DATA TYPE INTEGER;

-- AlterTable
ALTER TABLE "payment_history" ALTER COLUMN "amount" SET DATA TYPE INTEGER;

-- AlterTable
ALTER TABLE "shopify_disputes" ALTER COLUMN "amount" SET DATA TYPE INTEGER;

-- AlterTable
ALTER TABLE "shopify_orders" ALTER COLUMN "total_tax" SET DATA TYPE INTEGER,
ALTER COLUMN "total_price" SET DATA TYPE INTEGER,
ALTER COLUMN "subtotal_price" SET DATA TYPE INTEGER,
ALTER COLUMN "total_discounts" SET DATA TYPE INTEGER,
ALTER COLUMN "total_outstanding" SET DATA TYPE INTEGER,
ALTER COLUMN "current_subtotal_price" SET DATA TYPE INTEGER,
ALTER COLUMN "current_total_discounts" SET DATA TYPE INTEGER,
ALTER COLUMN "current_total_price" SET DATA TYPE INTEGER,
ALTER COLUMN "total_line_items_price" SET DATA TYPE INTEGER,
ALTER COLUMN "total_tip_received" SET DATA TYPE INTEGER;

-- AlterTable
ALTER TABLE "shopify_payouts" ALTER COLUMN "amount" SET DATA TYPE INTEGER;

-- AlterTable
ALTER TABLE "shopify_transactions" ALTER COLUMN "fee" SET DATA TYPE INTEGER,
ALTER COLUMN "net" SET DATA TYPE INTEGER,
ALTER COLUMN "amount" SET DATA TYPE INTEGER,
ALTER COLUMN "amount_rounding" SET DATA TYPE INTEGER;
