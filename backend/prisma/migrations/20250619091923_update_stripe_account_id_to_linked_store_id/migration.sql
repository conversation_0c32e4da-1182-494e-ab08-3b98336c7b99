/*
  Warnings:

  - You are about to drop the column `invoice_id` on the `bills` table. All the data in the column will be lost.
  - You are about to drop the column `stripe_account_id` on the `bills` table. All the data in the column will be lost.
  - You are about to alter the column `amount` on the `bills` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `Decimal(15,2)`.
  - The `status` column on the `bills` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the `payment_histories` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `linked_store_id` to the `bills` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "BillStatus" AS ENUM ('PENDING', 'PAID', 'FAILED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "PaymentStatus" AS ENUM ('SUCCEEDED', 'FAILED', 'PENDING', 'REFUNDED');

-- DropForeignKey
ALTER TABLE "bills" DROP CONSTRAINT "bills_stripe_account_id_fkey";

-- DropForeignKey
ALTER TABLE "payment_histories" DROP CONSTRAINT "payment_histories_bill_id_fkey";

-- DropForeignKey
ALTER TABLE "payment_histories" DROP CONSTRAINT "payment_histories_stripe_account_id_fkey";

-- DropIndex
DROP INDEX "bills_invoice_id_key";

-- AlterTable
ALTER TABLE "bills" DROP COLUMN "invoice_id",
DROP COLUMN "stripe_account_id",
ADD COLUMN     "linked_store_id" TEXT NOT NULL,
ALTER COLUMN "amount" SET DATA TYPE DECIMAL(15,2),
ALTER COLUMN "currency" DROP DEFAULT,
DROP COLUMN "status",
ADD COLUMN     "status" "BillStatus" NOT NULL DEFAULT 'PENDING';

-- DropTable
DROP TABLE "payment_histories";

-- CreateTable
CREATE TABLE "payment_history" (
    "id" TEXT NOT NULL,
    "linked_store_id" TEXT NOT NULL,
    "amount" DECIMAL(15,2) NOT NULL,
    "currency" TEXT NOT NULL,
    "description" TEXT,
    "status" "PaymentStatus" NOT NULL,
    "payment_date" TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "payment_history_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "bills" ADD CONSTRAINT "bills_linked_store_id_fkey" FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payment_history" ADD CONSTRAINT "payment_history_linked_store_id_fkey" FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;
