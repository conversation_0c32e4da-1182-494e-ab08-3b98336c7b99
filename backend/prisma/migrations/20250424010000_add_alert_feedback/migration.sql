-- Tạo enum FeedbackStatus để lưu trạng thái feedback
CREATE TYPE "FeedbackStatus" AS ENUM ('PENDING', 'SENT', 'FAILED');

-- Thêm trường feedback_status và feedback_time vào bảng alerts
ALTER TABLE "alerts" ADD COLUMN "feedback_status" "FeedbackStatus" DEFAULT 'PENDING';
ALTER TABLE "alerts" ADD COLUMN "feedback_time" TIMESTAMP;

-- Tạo bảng alert_feedbacks với mối quan hệ 1-1 với bảng alerts
CREATE TABLE "alert_feedbacks" (
    "id" TEXT NOT NULL,
    "predictor_id" TEXT NOT NULL,
    "outcome" TEXT NOT NULL,
    "refunded" TEXT NOT NULL,
    "comments" TEXT,
    "is_fraud" TEXT,
    "match_order_no" TEXT,
    "refund_no" TEXT,
    "refund_date" TEXT,
    "refund_amount" TEXT,
    "refund_currency" TEXT,
    "data" JSONB,
    "error_data" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "alert_feedbacks_pkey" PRIMARY KEY ("id")
);

-- Tạo unique constraint để đảm bảo mối quan hệ 1-1
CREATE UNIQUE INDEX "alert_feedbacks_predictor_id_key" ON "alert_feedbacks"("predictor_id");

-- Tạo foreign key constraint kết nối predictor_id của AlertFeedback với id của Alert
ALTER TABLE "alert_feedbacks" ADD CONSTRAINT "alert_feedbacks_predictor_id_fkey" FOREIGN KEY ("predictor_id") REFERENCES "alerts"("id") ON DELETE CASCADE ON UPDATE CASCADE; 