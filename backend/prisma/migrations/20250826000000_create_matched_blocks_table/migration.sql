-- Migration to create new matched_blocks table
-- This table stores block data with matched transaction information across multiple providers

CREATE TABLE "matched_blocks" (
    "id" TEXT PRIMARY KEY,  -- Same as block ID
    "user_id" TEXT NOT NULL,
    "linked_store_id" TEXT NOT NULL,
    "provider" TEXT NOT NULL,  -- "shopify", "stripe", etc.
    
    -- Flexible JSON field to store matched transaction/order data
    "matched_data" JSONB,  -- Store matched transactions/orders info for any provider
    
    -- Block data fields (copied from blocks table)
    "alert_id" TEXT NOT NULL,
    "alert_time" TIMESTAMPTZ NOT NULL,
    "alert_type" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "currency" TEXT NOT NULL,
    "descriptor" TEXT NOT NULL,
    "auth_code" TEXT,
    "card_bin" TEXT,
    "card_number" TEXT,
    "chargeback_code" TEXT,
    "dispute_amount" INTEGER,
    "dispute_currency" TEXT,
    "transaction_time" TIMESTAMPTZ,
    "age" TEXT,
    "alert_source" TEXT,
    "arn" TEXT,
    "issuer" TEXT,
    "initiated_by" TEXT,
    "liability" TEXT,
    "merchant_category_code" TEXT,
    "transaction_id" TEXT,  -- Block's transaction ID
    "transaction_type" TEXT,
    "acquirer_bin" TEXT,
    "acquirer_reference_number" TEXT,
    "alert_status" TEXT,
    "caid" TEXT,
    "descriptor_contact" TEXT,
    "rule_name" TEXT,
    "rule_type" TEXT,
    "type" TEXT NOT NULL DEFAULT 'ETHOCA',
    "feedback_data" JSONB,
    "feedback_status" TEXT NOT NULL DEFAULT 'PENDING',
    "feedback_time" TIMESTAMPTZ,
    
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add foreign key constraints
ALTER TABLE "matched_blocks" ADD CONSTRAINT "matched_blocks_id_fkey" 
    FOREIGN KEY ("id") REFERENCES "blocks"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "matched_blocks" ADD CONSTRAINT "matched_blocks_user_id_fkey" 
    FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "matched_blocks" ADD CONSTRAINT "matched_blocks_linked_store_id_fkey" 
    FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add unique constraint and indexes
ALTER TABLE "matched_blocks" ADD CONSTRAINT "matched_blocks_id_linked_store_id_provider_key" 
    UNIQUE ("id", "linked_store_id", "provider");

CREATE INDEX "matched_blocks_user_id_idx" ON "matched_blocks"("user_id");
CREATE INDEX "matched_blocks_linked_store_id_idx" ON "matched_blocks"("linked_store_id");
CREATE INDEX "matched_blocks_provider_idx" ON "matched_blocks"("provider");
CREATE INDEX "matched_blocks_matched_data_idx" ON "matched_blocks" USING GIN ("matched_data");
CREATE INDEX "matched_blocks_descriptor_idx" ON "matched_blocks"("descriptor");
CREATE INDEX "matched_blocks_type_idx" ON "matched_blocks"("type");
CREATE INDEX "matched_blocks_alert_time_idx" ON "matched_blocks"("alert_time");
CREATE INDEX "matched_blocks_feedback_status_idx" ON "matched_blocks"("feedback_status");
CREATE INDEX "matched_blocks_card_bin_idx" ON "matched_blocks"("card_bin");
CREATE INDEX "matched_blocks_caid_idx" ON "matched_blocks"("caid");
CREATE INDEX "matched_blocks_amount_idx" ON "matched_blocks"("amount");

-- Add comment to document this table
COMMENT ON TABLE "matched_blocks" IS 'New table storing block data with matched transaction information across multiple providers using flexible JSON storage';