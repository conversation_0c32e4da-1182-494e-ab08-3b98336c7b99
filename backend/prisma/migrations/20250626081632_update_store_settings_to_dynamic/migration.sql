/*
  Warnings:

  - You are about to drop the column `currency` on the `store_settings` table. All the data in the column will be lost.
  - You are about to drop the column `monthly_goal` on the `store_settings` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[store_id,name]` on the table `store_settings` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `name` to the `store_settings` table without a default value. This is not possible if the table is not empty.
  - Added the required column `value` to the `store_settings` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "store_settings_store_id_key";

-- AlterTable
ALTER TABLE "store_settings" DROP COLUMN "currency",
DROP COLUMN "monthly_goal",
ADD COLUMN     "name" TEXT NOT NULL,
ADD COLUMN     "value" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "store_settings_store_id_name_key" ON "store_settings"("store_id", "name");
