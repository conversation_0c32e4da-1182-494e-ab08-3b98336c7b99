-- AlterTable
ALTER TABLE "alert_infos" ALTER COLUMN "closed_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "registered_at" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "bills" ALTER COLUMN "due_date" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "block_refunds" ALTER COLUMN "processed_at" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "block_usages" ALTER COLUMN "time" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "created_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "updated_at" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "blocks" ALTER COLUMN "feedback_time" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "alert_time" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "transaction_time" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "clients" ALTER COLUMN "created_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "updated_at" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "mapped_blocks" ALTER COLUMN "created_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "updated_at" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "orders" ALTER COLUMN "created_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "updated_at" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "payment_history" ALTER COLUMN "payment_date" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "roles" ALTER COLUMN "created_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "updated_at" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "shopify_disputes" ALTER COLUMN "finalized_on" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "initiated_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "evidence_due_by" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "evidence_sent_on" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "created_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "updated_at" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "shopify_orders" ALTER COLUMN "closed_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "created_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "updated_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "cancelled_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "processed_at" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "shopify_payouts" ALTER COLUMN "date" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "created_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "updated_at" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "shopify_transactions" ALTER COLUMN "processed_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "created_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "updated_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "authorization_expires_at" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "store_settings" ALTER COLUMN "created_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "updated_at" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "tokens" ALTER COLUMN "expires_at" SET DATA TYPE TIMESTAMPTZ;

-- AlterTable
ALTER TABLE "users" ALTER COLUMN "birth_date" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "created_at" SET DATA TYPE TIMESTAMPTZ,
ALTER COLUMN "updated_at" SET DATA TYPE TIMESTAMPTZ;
