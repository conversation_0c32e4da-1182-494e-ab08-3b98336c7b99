-- AlterTable
ALTER TABLE "shopify_transactions" ADD COLUMN     "amount_rounding" DECIMAL(15,2),
ADD COLUMN     "authorization" TEXT,
ADD COLUMN     "authorization_expires_at" TIMESTAMP(3),
ADD COLUMN     "currency_exchange_adjustment" JSONB,
ADD COLUMN     "device_id" BIGINT,
ADD COLUMN     "error_code" TEXT,
ADD COLUMN     "extended_authorization_attributes" JSONB,
ADD COLUMN     "gateway" TEXT,
ADD COLUMN     "kind" TEXT,
ADD COLUMN     "location_id" JSONB,
ADD COLUMN     "manual_payment_gateway" BOOLEAN,
ADD COLUMN     "message" TEXT,
ADD COLUMN     "parent_id" BIGINT,
ADD COLUMN     "payment_details" JSONB,
ADD COLUMN     "payments_refund_attributes" JSONB,
ADD COLUMN     "receipt" JSONB,
ADD COLUMN     "source_name" TEXT,
ADD COLUMN     "status" TEXT,
ADD COLUMN     "total_unsettled_set" J<PERSON>N<PERSON>,
ADD COLUMN     "user_id" BIGINT;
