/*
  Warnings:

  - You are about to drop the column `acquirerBin` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `acquirerReferenceNumber` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `alertId` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `alertSource` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `alertStatus` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `alertTime` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `alertType` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `authCode` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `cardBin` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `cardNumber` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `chargebackCode` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `descriptorContact` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `disputeAmount` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `disputeCurrency` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `initiatedBy` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `merchantCategoryCode` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `ruleName` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `ruleType` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `transactionId` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `transactionTime` on the `blocks` table. All the data in the column will be lost.
  - You are about to drop the column `transactionType` on the `blocks` table. All the data in the column will be lost.
  - Added the required column `alert_id` to the `blocks` table without a default value. This is not possible if the table is not empty.
  - Added the required column `alert_time` to the `blocks` table without a default value. This is not possible if the table is not empty.
  - Added the required column `alert_type` to the `blocks` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "blocks" DROP COLUMN "acquirerBin",
DROP COLUMN "acquirerReferenceNumber",
DROP COLUMN "alertId",
DROP COLUMN "alertSource",
DROP COLUMN "alertStatus",
DROP COLUMN "alertTime",
DROP COLUMN "alertType",
DROP COLUMN "authCode",
DROP COLUMN "cardBin",
DROP COLUMN "cardNumber",
DROP COLUMN "chargebackCode",
DROP COLUMN "descriptorContact",
DROP COLUMN "disputeAmount",
DROP COLUMN "disputeCurrency",
DROP COLUMN "initiatedBy",
DROP COLUMN "merchantCategoryCode",
DROP COLUMN "ruleName",
DROP COLUMN "ruleType",
DROP COLUMN "transactionId",
DROP COLUMN "transactionTime",
DROP COLUMN "transactionType",
ADD COLUMN     "acquirer_bin" TEXT,
ADD COLUMN     "acquirer_reference_number" TEXT,
ADD COLUMN     "alert_id" TEXT NOT NULL,
ADD COLUMN     "alert_source" TEXT,
ADD COLUMN     "alert_status" TEXT,
ADD COLUMN     "alert_time" TEXT NOT NULL,
ADD COLUMN     "alert_type" TEXT NOT NULL,
ADD COLUMN     "auth_code" TEXT,
ADD COLUMN     "card_bin" TEXT,
ADD COLUMN     "card_number" TEXT,
ADD COLUMN     "chargeback_code" TEXT,
ADD COLUMN     "descriptor_contact" TEXT,
ADD COLUMN     "dispute_amount" TEXT,
ADD COLUMN     "dispute_currency" TEXT,
ADD COLUMN     "initiated_by" TEXT,
ADD COLUMN     "merchant_category_code" TEXT,
ADD COLUMN     "rule_name" TEXT,
ADD COLUMN     "rule_type" TEXT,
ADD COLUMN     "transaction_id" TEXT,
ADD COLUMN     "transaction_time" TEXT,
ADD COLUMN     "transaction_type" TEXT;
