-- CreateTable
CREATE TABLE "shopify_subscriptions" (
    "id" text NOT NULL,
    "linked_store_id" text NOT NULL,
    "shopify_subscription_id" text,
    "shopify_charge_id" text,
    "plan_name" text NOT NULL DEFAULT 'Professional Plan',
    "plan_type" text NOT NULL DEFAULT 'USAGE_BASED',
    "status" text NOT NULL DEFAULT 'PENDING',
    "amount" integer NOT NULL DEFAULT 0,
    "currency" text NOT NULL DEFAULT 'USD',
    "billing_interval" text NOT NULL DEFAULT 'EVERY_30_DAYS',
    "capped_amount" integer NOT NULL DEFAULT 80000,
    "current_usage" integer NOT NULL DEFAULT 0,
    "trial_days" integer,
    "confirmation_url" text,
    "return_url" text,
    "activated_at" timestamp(3),
    "last_billed_at" timestamp(3),
    "next_billing_at" timestamp(3),
    "cancelled_at" timestamp(3),
    "metadata" jsonb,
    "webhook_data" jsonb,
    "created_at" timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp(3) NOT NULL,

    CONSTRAINT "shopify_subscriptions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "shopify_usage_records" (
    "id" text NOT NULL,
    "subscription_id" text NOT NULL,
    "shopify_record_id" text,
    "description" text NOT NULL,
    "quantity" integer NOT NULL DEFAULT 1,
    "price" integer NOT NULL,
    "total_amount" integer NOT NULL,
    "billing_date" timestamp(3) NOT NULL,
    "metadata" jsonb,
    "status" text NOT NULL DEFAULT 'PENDING',
    "created_at" timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp(3) NOT NULL,

    CONSTRAINT "shopify_usage_records_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "shopify_subscriptions_linked_store_id_key" ON "shopify_subscriptions"("linked_store_id");

-- CreateIndex
CREATE UNIQUE INDEX "shopify_subscriptions_shopify_subscription_id_key" ON "shopify_subscriptions"("shopify_subscription_id");

-- CreateIndex
CREATE UNIQUE INDEX "shopify_subscriptions_shopify_charge_id_key" ON "shopify_subscriptions"("shopify_charge_id");

-- CreateIndex
CREATE INDEX "shopify_subscriptions_shopify_subscription_id_idx" ON "shopify_subscriptions"("shopify_subscription_id");

-- CreateIndex
CREATE INDEX "shopify_subscriptions_status_idx" ON "shopify_subscriptions"("status");

-- CreateIndex
CREATE INDEX "shopify_subscriptions_linked_store_id_status_idx" ON "shopify_subscriptions"("linked_store_id", "status");

-- CreateIndex
CREATE UNIQUE INDEX "shopify_usage_records_shopify_record_id_key" ON "shopify_usage_records"("shopify_record_id");

-- CreateIndex
CREATE INDEX "shopify_usage_records_subscription_id_idx" ON "shopify_usage_records"("subscription_id");

-- CreateIndex
CREATE INDEX "shopify_usage_records_billing_date_idx" ON "shopify_usage_records"("billing_date");

-- CreateIndex
CREATE INDEX "shopify_usage_records_status_idx" ON "shopify_usage_records"("status");

-- AddForeignKey
ALTER TABLE "shopify_subscriptions" ADD CONSTRAINT "shopify_subscriptions_linked_store_id_fkey" FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shopify_usage_records" ADD CONSTRAINT "shopify_usage_records_subscription_id_fkey" FOREIGN KEY ("subscription_id") REFERENCES "shopify_subscriptions"("id") ON DELETE CASCADE ON UPDATE CASCADE; 