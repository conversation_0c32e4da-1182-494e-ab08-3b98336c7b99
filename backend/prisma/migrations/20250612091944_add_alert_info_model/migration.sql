-- CreateTable
CREATE TABLE "alert_infos" (
    "id" TEXT NOT NULL,
    "alert_type" TEXT NOT NULL,
    "descriptor" TEXT NOT NULL,
    "bin" TEXT,
    "caid" TEXT,
    "store_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "alert_infos_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "alert_infos" ADD CONSTRAINT "alert_infos_store_id_fkey" FOREIGN KEY ("store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;
