/*
  Warnings:

  - The `order_status_url` column on the `shopify_orders` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- AlterTable
ALTER TABLE "shopify_orders" ADD COLUMN     "buyer_accepts_marketing" BOOLEAN,
ADD COLUMN     "checkout_token" TEXT,
ADD COLUMN     "client_details" JSONB,
ADD COLUMN     "company" JSONB,
ADD COLUMN     "confirmation_number" TEXT,
ADD COLUMN     "current_subtotal_price" DECIMAL(15,2),
ADD COLUMN     "current_subtotal_price_set" JSONB,
ADD COLUMN     "current_total_additional_fees_set" J<PERSON><PERSON><PERSON>,
ADD COLUMN     "current_total_discounts" DECIMAL(15,2),
ADD COLUMN     "current_total_discounts_set" JSONB,
ADD COLUMN     "current_total_duties_set" JSONB,
ADD COLUMN     "current_total_price" DECIMAL(15,2),
ADD COLUMN     "current_total_price_set" J<PERSON><PERSON><PERSON>,
ADD COLUMN     "customer" JSONB,
ADD COLUMN     "discount_applications" JSONB,
ADD COLUMN     "discount_codes" JSONB,
ADD COLUMN     "merchant_business_entity_id" TEXT,
ADD COLUMN     "merchant_of_record_app_id" BIGINT,
ADD COLUMN     "original_total_additional_fees_set" JSONB,
ADD COLUMN     "original_total_duties_set" JSONB,
ADD COLUMN     "payment_gateway_names" JSONB,
ADD COLUMN     "referring_site" TEXT,
ADD COLUMN     "refunds" JSONB,
ADD COLUMN     "subtotal_price_set" JSONB,
ADD COLUMN     "tax_lines" JSONB,
ADD COLUMN     "total_cash_rounding_payment_adjustment_set" JSONB,
ADD COLUMN     "total_cash_rounding_refund_adjustment_set" JSONB,
ADD COLUMN     "total_discounts_set" JSONB,
ADD COLUMN     "total_line_items_price" DECIMAL(15,2),
ADD COLUMN     "total_line_items_price_set" JSONB,
ADD COLUMN     "total_price_set" JSONB,
ADD COLUMN     "total_shipping_price_set" JSONB,
ADD COLUMN     "total_tax_set" JSONB,
ADD COLUMN     "total_tip_received" DECIMAL(15,2),
ADD COLUMN     "user_id" BIGINT,
DROP COLUMN "order_status_url",
ADD COLUMN     "order_status_url" JSONB;
