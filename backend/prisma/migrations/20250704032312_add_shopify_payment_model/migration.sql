-- CreateTable
CREATE TABLE "store_plans" (
    "id" TEXT NOT NULL,
    "store_id" TEXT NOT NULL,
    "plan_tier" TEXT NOT NULL,
    "block_limit" INTEGER NOT NULL,
    "start_date" TIMESTAMPTZ NOT NULL,
    "end_date" TIMESTAMPTZ,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "store_plans_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "usage_tracking" (
    "id" TEXT NOT NULL,
    "store_id" TEXT NOT NULL,
    "block_type" TEXT NOT NULL,
    "period" TEXT NOT NULL,
    "timestamp" TIMESTAMPTZ NOT NULL,
    "amount" INTEGER,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "usage_tracking_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "store_plans_store_id_key" ON "store_plans"("store_id");

-- CreateIndex
CREATE INDEX "usage_tracking_store_id_period_idx" ON "usage_tracking"("store_id", "period");

-- AddForeignKey
ALTER TABLE "store_plans" ADD CONSTRAINT "store_plans_store_id_fkey" FOREIGN KEY ("store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "usage_tracking" ADD CONSTRAINT "usage_tracking_store_id_fkey" FOREIGN KEY ("store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;
