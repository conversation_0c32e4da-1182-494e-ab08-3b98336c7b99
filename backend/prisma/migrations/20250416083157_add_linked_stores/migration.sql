/*
  Warnings:

  - You are about to drop the `shopify_stores` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropTable
DROP TABLE "shopify_stores";

-- CreateTable
CREATE TABLE "linked_stores" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "store_name" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "provider_store_id" TEXT,
    "data" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "linked_stores_pkey" PRIMARY KEY ("id")
);

/*
  Warnings:

  - Made the column `data` on table `linked_stores` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "linked_stores" ALTER COLUMN "data" SET NOT NULL;
-- AddForeignKey
ALTER TABLE "linked_stores" ADD CONSTRAINT "linked_stores_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
