-- CreateTable
CREATE TABLE "paypal_agreements" (
    "id" TEXT NOT NULL,
    "linked_store_id" TEXT NOT NULL,
    "agreement_id" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL,

    CONSTRAINT "paypal_agreements_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "paypal_payments" (
    "id" TEXT NOT NULL,
    "agreement_id" TEXT NOT NULL,
    "bill_id" TEXT,
    "payment_id" TEXT NOT NULL,
    "amount" INTEGER NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'USD',
    "status" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "paypal_payments_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "paypal_agreements_agreement_id_key" ON "paypal_agreements"("agreement_id");

-- CreateIndex
CREATE UNIQUE INDEX "paypal_payments_payment_id_key" ON "paypal_payments"("payment_id");

-- AddForeignKey
ALTER TABLE "paypal_agreements" ADD CONSTRAINT "paypal_agreements_linked_store_id_fkey" FOREIGN KEY ("linked_store_id") REFERENCES "linked_stores"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "paypal_payments" ADD CONSTRAINT "paypal_payments_agreement_id_fkey" FOREIGN KEY ("agreement_id") REFERENCES "paypal_agreements"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "paypal_payments" ADD CONSTRAINT "paypal_payments_bill_id_fkey" FOREIGN KEY ("bill_id") REFERENCES "bills"("id") ON DELETE SET NULL ON UPDATE CASCADE;
