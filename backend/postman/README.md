# API Scripts

## Postman Sync Script

The `sync-postman.ts` script automatically extracts all API routes from your Express application and generates a Postman collection. This helps maintain up-to-date API documentation and testing environments.

### Features

- Automatically scans all route files in your application
- Extracts HTTP methods, paths, and middleware information
- Groups endpoints by resource type
- Detects authentication requirements
- Generates proper Postman request formats
- Adds appropriate headers and authentication placeholders
- Generates separate environments for development, staging, and production
- Configurable through a dedicated config file

### How to Use

1. Make sure all dependencies are installed:

   ```bash
   yarn install
   ```

2. Run the sync script:

   ```bash
   yarn postman:sync
   ```

3. The script will generate the following files in the `postman` directory:

   - `postman-collection.json` - The API collection
   - `postman-environment-development.json` - Development environment
   - `postman-environment-staging.json` - Staging environment
   - `postman-environment-production.json` - Production environment

4. Import the generated files into Postman:
   - Open Postman
   - Click "Import" button
   - Select the generated files
   - The collection and environments will be imported with all your API endpoints

### Postman Environments

The script generates three separate environment files for different stages:

- **Development**: For local development (default: `http://localhost:3000`)
- **Staging**: For testing on staging servers
- **Production**: For production API

Each environment contains these variables:

- `baseUrl`: The base URL of your API for that environment
- `authToken`: Authentication token (to be filled after login)

### Configuration

You can customize the script by modifying the `postman.config.ts` file:

```typescript
const config: PostmanConfig = {
  // Project information
  projectName: "Your Project Name",

  // Collection settings
  collectionName: "API Collection",
  apiVersion: "v1",

  // Output file paths
  outputDir: "../postman",
  collectionFileName: "postman-collection.json",

  // Environment configurations
  environments: [
    {
      name: "Development",
      baseUrl: "http://localhost:3000",
      variables: {
        authToken: "",
      },
    },
    // More environments...
  ],
};
```

#### API Versioning

The `apiVersion` configuration handles API version prefixing:

- When `apiVersion` is set to a value (e.g., "v1"), it's included in the `baseUrl` environment variable as `http://localhost:3000/v1`
- When `apiVersion` is set to `null` or an empty string, no version prefix is added to the baseUrl
- This allows for easy switching between versioned and non-versioned API endpoints

### Best Practices

- Run the sync script after making changes to your API routes
- Keep your Postman collection and environments in version control
- Customize the `postman.config.ts` file to match your project's requirements
- Add custom variables to environments as needed for your API
