/**
 * <PERSON>ript to automatically sync Express routes with Postman
 * This script traverses all route files, identifies endpoints, and generates a Postman collection
 */

import fs from "fs";
import path from "path";
import { Router } from "express";
import { glob } from "glob";
import config from "./postman.config";

// Types
interface RouteInfo {
  path: string;
  method: string;
  fullPath: string;
  middlewares: string[];
  controller: string;
  isPublic: boolean;
}

interface PostmanRequest {
  name: string;
  request: {
    method: string;
    url: {
      raw: string;
      host: string[];
      path: string[];
    };
    header: {
      key: string;
      value: string;
      type: string;
    }[];
    body?: {
      mode: string;
      raw?: string;
      options?: {
        raw: {
          language: string;
        };
      };
    };
    auth?: {
      type: string;
      bearer: {
        token: string;
      }[];
    };
  };
}

interface PostmanFolder {
  name: string;
  item: (PostmanRequest | PostmanFolder)[];
}

interface PostmanCollection {
  info: {
    name: string;
    schema: string;
  };
  item: (PostmanRequest | PostmanFolder)[];
}

interface PostmanEnvironmentVariable {
  key: string;
  value: string;
  enabled: boolean;
  type: string;
}

interface PostmanEnvironment {
  id: string;
  name: string;
  values: PostmanEnvironmentVariable[];
  _postman_variable_scope: string;
  _postman_exported_at: string;
  _postman_exported_using: string;
}

// Configuration
const BASE_URL = "{{baseUrl}}";
const ROUTES_DIR = path.resolve(__dirname, "../src/routes");
const POSTMAN_DIR = path.resolve(__dirname, config.outputDir);
const COLLECTION_FILE = path.resolve(POSTMAN_DIR, config.collectionFileName);
const API_VERSION = config.apiVersion;

// Helper function to extract routes from a file
function extractRoutesFromFile(filePath: string): RouteInfo[] {
  const fileContent = fs.readFileSync(filePath, "utf8");
  const routes: RouteInfo[] = [];

  // Extract route base path from file name (e.g., auth.route.ts -> /auth)
  const fileName = path.basename(filePath, path.extname(filePath));
  const basePath = `/${fileName.replace(".route", "")}`;

  // Match router definition patterns
  const routeRegex =
    /router\.(get|post|put|patch|delete)\(\s*['"]([^'"]*)['"]\s*,\s*([^)]*)\)/g;
  let match;

  while ((match = routeRegex.exec(fileContent)) !== null) {
    const method = match[1].toUpperCase();
    const routePath = match[2];
    const handlers = match[3].split(",").map((h) => h.trim());

    // Extract middleware and controller names
    const middlewares = handlers.slice(0, -1).map((h) => {
      // Extract the middleware name
      const middlewareName = h.split(".")[1] || h;
      return middlewareName;
    });

    // Extract the controller name
    const controller = handlers[handlers.length - 1];
    const controllerName = controller.includes(".")
      ? controller.split(".")[1]
      : controller;

    // Determine if route is public (no auth middleware)
    const isPublic = !middlewares.some(
      (m) =>
        m.includes("auth") ||
        m.includes("userRoute") ||
        m.includes("adminRoute")
    );

    // Build the full path
    const fullPath = path.join(basePath, routePath).replace(/\\/g, "/");

    routes.push({
      path: routePath,
      method,
      fullPath,
      middlewares,
      controller: controllerName,
      isPublic,
    });
  }

  return routes;
}

// Generate a Postman collection from routes
function generatePostmanCollection(routes: RouteInfo[]): PostmanCollection {
  // Group routes by their base path
  const routeGroups: { [key: string]: RouteInfo[] } = {};

  routes.forEach((route) => {
    const basePath = route.fullPath.split("/")[1];
    if (!routeGroups[basePath]) {
      routeGroups[basePath] = [];
    }
    routeGroups[basePath].push(route);
  });

  // Create folders for each group
  const folders: PostmanFolder[] = Object.keys(routeGroups).map((groupName) => {
    const groupRoutes = routeGroups[groupName];

    // Create requests for each route
    const requests: PostmanRequest[] = groupRoutes.map((route) => {
      // Split the path into segments and handle route parameters (with colons)
      let pathSegments = route.fullPath.split("/").filter(Boolean);
      let routeParams: string[] = [];
      
      // Find path segments with colons and convert them to Postman variables
      const processedPathSegments = pathSegments.map(segment => {
        if (segment.startsWith(":")) {
          // Remove the colon and store the parameter name
          const paramName = segment.substring(1);
          routeParams.push(paramName);
          // Return as Postman variable
          return `{{${paramName}}}`;
        }
        return segment;
      });

      // Create a processed full path with Postman variables
      const processedFullPath = "/" + processedPathSegments.join("/");

      // Create the request
      const request: PostmanRequest = {
        name: `${route.method} ${route.fullPath}`,
        request: {
          method: route.method,
          url: {
            raw: `${BASE_URL}${processedFullPath}`,
            host: [BASE_URL.replace(/^https?:\/\//, "").replace(/\/$/, "")],
            path: [...processedPathSegments],
          },
          header: [
            {
              key: "Content-Type",
              value: "application/json",
              type: "text",
            },
          ],
        },
      };

      // Add authentication for non-public routes
      if (!route.isPublic) {
        request.request.auth = {
          type: "bearer",
          bearer: [
            {
              token: "{{authToken}}",
            },
          ],
        };
      }

      // Add body for POST, PUT, PATCH requests
      if (["POST", "PUT", "PATCH"].includes(route.method)) {
        request.request.body = {
          mode: "raw",
          raw: "{}",
          options: {
            raw: {
              language: "json",
            },
          },
        };
      }

      return request;
    });

    return {
      name: groupName.charAt(0).toUpperCase() + groupName.slice(1),
      item: requests,
    };
  });

  // Create the collection
  return {
    info: {
      name: `${config.projectName} ${config.collectionName}`,
      schema:
        "https://schema.getpostman.com/json/collection/v2.1.0/collection.json",
    },
    item: folders,
  };
}

// Extract unique route parameters from all routes
function extractUniqueRouteParams(routes: RouteInfo[]): string[] {
  const params = new Set<string>();
  
  routes.forEach(route => {
    // Split the path into segments
    const pathSegments = route.fullPath.split("/").filter(Boolean);
    
    // Find path segments with colons
    pathSegments.forEach(segment => {
      if (segment.startsWith(":")) {
        // Remove the colon
        const paramName = segment.substring(1);
        params.add(paramName);
      }
    });
  });
  
  return Array.from(params);
}

// Generate Postman environment for a specific stage
function generatePostmanEnvironment(
  envConfig: (typeof config.environments)[0],
  routeParams: string[] = []
): PostmanEnvironment {
  const timestamp = new Date().toISOString();
  const id = `env-${config.projectName
    .toLowerCase()
    .replace(/\s+/g, "-")}-${envConfig.name.toLowerCase()}`;

  // Create environment variables array
  const values: PostmanEnvironmentVariable[] = [
    {
      key: "baseUrl",
      value:
        API_VERSION && API_VERSION.trim() !== ""
          ? `${envConfig.baseUrl}/${API_VERSION}`
          : envConfig.baseUrl,
      enabled: true,
      type: "default",
    },
  ];

  // Add custom variables from config
  Object.entries(envConfig.variables).forEach(([key, value]) => {
    values.push({
      key,
      value,
      enabled: true,
      type: "default",
    });
  });

  // Add route parameters as environment variables with empty values
  routeParams.forEach(param => {
    // Only add if not already in the environment
    if (!values.some(v => v.key === param)) {
      values.push({
        key: param,
        value: "", // Empty value that user can fill in
        enabled: true,
        type: "default",
      });
    }
  });

  return {
    id,
    name: `${config.projectName} (${envConfig.name})`,
    values,
    _postman_variable_scope: "environment",
    _postman_exported_at: timestamp,
    _postman_exported_using: "Postman/10.18.11",
  };
}

// Main function
async function main() {
  try {
    // Create postman directory if it doesn't exist
    if (!fs.existsSync(POSTMAN_DIR)) {
      fs.mkdirSync(POSTMAN_DIR, { recursive: true });
      console.log(`Created directory: ${POSTMAN_DIR}`);
    }

    // Find all route files
    const routeFiles = glob.sync("**/*.route.ts", {
      cwd: ROUTES_DIR,
      absolute: true,
    });

    console.log(`Found ${routeFiles.length} route files`);

    // Extract routes from each file
    let allRoutes: RouteInfo[] = [];

    routeFiles.forEach((file) => {
      const routes = extractRoutesFromFile(file);
      console.log(
        `Extracted ${routes.length} routes from ${path.basename(file)}`
      );
      allRoutes = [...allRoutes, ...routes];
    });

    console.log(`Total routes extracted: ${allRoutes.length}`);

    // Extract all unique route parameters
    const routeParams = extractUniqueRouteParams(allRoutes);
    console.log(`Found ${routeParams.length} unique route parameters: ${routeParams.join(', ')}`);

    // Generate Postman collection
    const collection = generatePostmanCollection(allRoutes);

    // Write collection to file
    fs.writeFileSync(COLLECTION_FILE, JSON.stringify(collection, null, 2));
    console.log(`Postman collection generated at ${COLLECTION_FILE}`);

    // Generate and write environments to files
    config.environments.forEach((envConfig) => {
      const environment = generatePostmanEnvironment(envConfig, routeParams);
      const envFileName = `postman-environment-${envConfig.name.toLowerCase()}.json`;
      const envFilePath = path.resolve(POSTMAN_DIR, envFileName);

      fs.writeFileSync(envFilePath, JSON.stringify(environment, null, 2));
      console.log(`Generated environment: ${envConfig.name}`);
    });

    console.log(`Postman environments generated at ${POSTMAN_DIR}`);
    console.log("You can import these files into Postman");
  } catch (error) {
    console.error("Error syncing with Postman:", error);
  }
}

// Run the script
main();
