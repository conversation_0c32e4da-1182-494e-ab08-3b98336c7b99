/**
 * Configuration file for Postman environment setup
 * This file contains the configuration for different environments used in the Postman collection
 */

export interface PostmanEnvConfig {
  name: string;
  baseUrl: string;
  variables: Record<string, string>;
}

export interface PostmanConfig {
  // Project information
  projectName: string;

  // Collection settings
  collectionName: string;
  apiVersion: string | null;

  // Output file paths
  outputDir: string;
  collectionFileName: string;

  // Environments configuration
  environments: PostmanEnvConfig[];
}

// Default configuration
const config: PostmanConfig = {
  // Project information
  projectName: "Charge Back App",

  // Collection settings
  collectionName: "API Collection",
  apiVersion: null,

  // Output file paths
  outputDir: "export",
  collectionFileName: "postman-collection.json",

  // Environment configurations
  environments: [
    {
      name: "Development",
      baseUrl: "http://localhost:29000",
      variables: {
        authToken: "",
      },
    },
    {
      name: "Staging",
      baseUrl: "https://api-staging.yourapp.com",
      variables: {
        authToken: "",
      },
    },
    {
      name: "Production",
      baseUrl: "https://api.yourapp.com",
      variables: {
        authToken: "",
      },
    },
  ],
};

export default config;
