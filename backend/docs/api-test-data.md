# API Test Data cho Hệ Thống Charge-Back

Tài liệu này cung cấp dữ liệu mẫu và hướng dẫn chi tiết để test các API của hệ thống Charge-Back, bao gồm các module:

1. Anti-Fraud (Chống <PERSON>ian lận)
2. Chargeback-Dispute (<PERSON><PERSON><PERSON> chấp hoàn tiền)
3. Compensation (Bồi thường)
4. Early-Warning (Cảnh báo sớm)

## M<PERSON><PERSON> lục

- [C<PERSON><PERSON> hình Postman](#cấu-hình-postman)
- [1. Anti-Fraud API](#1-anti-fraud-api)
- [2. Chargeback-Dispute API](#2-chargeback-dispute-api)
- [3. Compensation API](#3-compensation-api)
- [4. Early-Warning API](#4-early-warning-api)
- [Hướng dẫn sử dụng Postman Collection](#hướng-dẫn-sử-dụng-postman-collection)

## C<PERSON><PERSON> hình Postman

### Biến Môi Trường

Tạo collection và môi trường trong Postman với các biến sau:

| Biến        | Mô tả                                               |
| ----------- | --------------------------------------------------- |
| `baseUrl`   | URL gốc của API, ví dụ: `http://localhost:3000/api` |
| `authToken` | JWT Token cho xác thực                              |

### Xác thực

Các API yêu cầu xác thực bằng JWT Token trong header:

```
Authorization: Bearer {{authToken}}
```

## 1. Anti-Fraud API

API Anti-Fraud cung cấp các endpoint để đánh giá rủi ro giao dịch và thông báo kết quả giao dịch.

### 1.1. Check Transaction

- **Endpoint**: `POST {{baseUrl}}/anti-fraud/check-transaction`
- **Authentication**: Required
- **Description**: Đánh giá rủi ro cho một giao dịch

#### Sample Request:

```json
{
  "BasicInfo": {
    "AccountId": "merchant_acc_12345",
    "TransactionType": "payment",
    "MerchantId": "MER12345",
    "MerchantName": "Example E-commerce"
  },
  "UserInfo": {
    "UserId": "user_87654321",
    "UserIp": "*************",
    "UserLocation": "Vietnam",
    "UserDeviceType": "mobile",
    "UserDeviceId": "android-xyz-123"
  },
  "ClientInfo": {
    "ClientIp": "*************",
    "ClientType": "mobile_app",
    "ClientVersion": "2.3.4",
    "ClientOS": "Android 13"
  },
  "ChannelInfo": {
    "ChannelId": "mobile_app",
    "ChannelName": "Android App"
  },
  "SceneInfo": {
    "SceneId": "checkout",
    "SceneName": "Payment Checkout"
  },
  "ExtraInfo": {
    "OrderId": "ORD-********9",
    "ProductIds": ["PROD-001", "PROD-002"],
    "CardBIN": "450000",
    "PaymentMethod": "credit_card",
    "IsNewUser": false,
    "AccountAge": 187,
    "TransactionAmount": 1500.0,
    "TransactionCurrency": "VND",
    "ShippingAddress": {
      "Country": "Vietnam",
      "City": "Ho Chi Minh",
      "PostalCode": "70000"
    }
  }
}
```

#### Sample Response:

```json
{
  "success": true,
  "message": "Risk check completed successfully",
  "data": {
    "ResultCode": 0,
    "Action": "PASS",
    "ResultInfo": "Transaction passed risk check",
    "Reference": "REF********9",
    "ReferenceIdle": "IDLE*********",
    "Score": 15,
    "ScoreItems": [
      {
        "Field": "UserLocation",
        "Score": 5
      },
      {
        "Field": "TransactionAmount",
        "Score": 10
      }
    ]
  }
}
```

### 1.2. Notify Transaction Result

- **Endpoint**: `POST {{baseUrl}}/anti-fraud/notify-transaction`
- **Authentication**: Required
- **Description**: Thông báo kết quả của giao dịch sau khi đã được xử lý

#### Sample Request:

```json
{
  "BasicInfo": {
    "RcUUID": "REF********9",
    "TxnResult": "SUCCESS",
    "TxnResultReason": "Completed",
    "TxnFinalAmount": 1500.0,
    "TxnFinalCurrency": "VND",
    "TxnTime": "2023-08-15T14:30:45Z",
    "MerchantId": "MER12345"
  },
  "AdditionalInfo": {
    "OrderId": "ORD-********9",
    "PaymentMethod": "credit_card",
    "ProcessingTime": 1.5,
    "IssuingBank": "Example Bank",
    "CardCountry": "VN",
    "AuthCode": "AUTH123"
  }
}
```

#### Sample Response:

```json
{
  "success": true,
  "message": "Transaction result notification completed",
  "data": {
    "ack": true,
    "code": 0,
    "message": "Notification received and processed successfully"
  }
}
```

## 2. Chargeback-Dispute API

API Chargeback-Dispute cung cấp các endpoint để quản lý quy trình tranh chấp hoàn tiền.

### 2.1. Submit Dispute

- **Endpoint**: `POST {{baseUrl}}/chargeback-dispute/submit`
- **Authentication**: Required
- **Description**: Gửi yêu cầu tranh chấp cho một giao dịch bị hoàn tiền

#### Sample Request:

```json
{
  "Chargeback": {
    "cb_id": "CB-********",
    "cb_reason_code": "10.4",
    "cb_date": "2023-08-10T08:15:30Z",
    "cb_amount": 1500.0,
    "cb_currency": "VND",
    "issuing_bank": "Example Bank",
    "cb_description": "Customer claims transaction not authorized"
  },
  "Payment": {
    "transaction_id": "TXN-*********",
    "transaction_date": "2023-08-01T14:30:15Z",
    "transaction_amount": 1500.0,
    "transaction_currency": "VND",
    "payment_method": "credit_card",
    "card_number": "XXXX-XXXX-XXXX-4567",
    "authorization_code": "AUTH-12345"
  },
  "Seller&buyer": {
    "seller_name": "Example E-commerce",
    "seller_id": "MER12345",
    "buyer_name": "Nguyen Van A",
    "buyer_id": "user_87654321",
    "buyer_email": "<EMAIL>",
    "buyer_phone": "+84********9"
  },
  "Contract": {
    "order_id": "ORD-********9",
    "order_date": "2023-08-01T14:25:10Z",
    "terms_accepted": true,
    "terms_date": "2023-08-01T14:25:10Z",
    "policies_url": "https://example.com/policies"
  },
  "Products": {
    "items": [
      {
        "product_id": "PROD-001",
        "product_name": "Smartphone X",
        "quantity": 1,
        "unit_price": 1500.0,
        "currency": "VND"
      }
    ],
    "total_items": 1
  },
  "Quality": {
    "product_description": "Brand new, sealed in original packaging",
    "quality_issues": "None reported"
  },
  "Shipping": {
    "shipping_date": "2023-08-02T09:00:00Z",
    "delivery_date": "2023-08-04T15:30:00Z",
    "tracking_number": "TRACK-12345",
    "carrier": "Example Logistics",
    "shipping_address": {
      "recipient": "Nguyen Van A",
      "address": "123 Example Street",
      "city": "Ho Chi Minh",
      "country": "Vietnam",
      "postal_code": "70000"
    },
    "delivery_confirmation": true,
    "delivery_signature": "https://example.com/signature/12345.jpg"
  },
  "Return&Refund": {
    "return_requested": false,
    "refund_requested": false
  },
  "Aftersales": {
    "customer_communications": [
      {
        "date": "2023-08-05T10:15:00Z",
        "channel": "email",
        "summary": "Customer confirmed receipt of product"
      }
    ],
    "support_provided": true
  },
  "History": {
    "previous_transactions": 15,
    "previous_disputes": 0,
    "account_created": "2022-01-15T00:00:00Z"
  },
  "Statement": {
    "merchant_statement": "The transaction was legitimate and fulfilled according to terms. Customer received the product as evidenced by delivery confirmation and follow-up communication.",
    "evidence_files": [
      "https://example.com/evidence/delivery-confirmation.pdf",
      "https://example.com/evidence/product-description.pdf",
      "https://example.com/evidence/customer-communication.pdf"
    ]
  }
}
```

#### Sample Response:

```json
{
  "success": true,
  "message": "Dispute submitted successfully. Waiting for materials.",
  "data": {
    "merchant_order_no": "DISP-********",
    "order_status": "Waiting_Mer_materials",
    "sign": "a1b2c3d4e5f6g7h8i9j0...",
    "order_results": []
  }
}
```

### 2.2. Check Dispute Status

- **Endpoint**: `GET {{baseUrl}}/chargeback-dispute/status/:merchantOrderNo`
- **Authentication**: Required
- **Description**: Kiểm tra trạng thái của một yêu cầu tranh chấp đã gửi trước đó

#### Sample Request:

URL: `{{baseUrl}}/chargeback-dispute/status/DISP-********`

#### Sample Response:

```json
{
  "success": true,
  "message": "Dispute status retrieved successfully",
  "data": {
    "merchant_order_no": "DISP-********",
    "order_status": "SubmitDownload",
    "downPath": "https://example.com/dispute/documents/DISP-********.pdf",
    "sign": "a1b2c3d4e5f6g7h8i9j0...",
    "order_results": [
      {
        "order_no": "ORD-********9",
        "judgment_result": "WIN",
        "judgment_amount": 1500.0,
        "judgment_currency": "VND",
        "eligibility_note": "Evidence sufficient to dispute chargeback"
      }
    ]
  }
}
```

### 2.3. Submit Additional Materials

- **Endpoint**: `POST {{baseUrl}}/chargeback-dispute/:merchantOrderNo/materials`
- **Authentication**: Required
- **Description**: Gửi thêm tài liệu bổ sung cho một yêu cầu tranh chấp

#### Sample Request:

URL: `{{baseUrl}}/chargeback-dispute/DISP-********/materials`

```json
{
  "materialType": "EVIDENCE",
  "description": "Additional transaction evidence",
  "files": [
    {
      "name": "customer_ip_logs.pdf",
      "contentType": "application/pdf",
      "content": "base64_encoded_content_here..."
    },
    {
      "name": "additional_confirmation.jpg",
      "contentType": "image/jpeg",
      "content": "base64_encoded_content_here..."
    }
  ],
  "additionalNotes": "These logs demonstrate that the transaction was made from the customer's usual IP address."
}
```

#### Sample Response:

```json
{
  "success": true,
  "message": "Additional materials submitted successfully",
  "data": {
    "merchant_order_no": "DISP-********",
    "materials_received": 2,
    "materials_status": "Processing",
    "updated_at": "2023-08-20T09:15:30Z"
  }
}
```

## 3. Compensation API

API Compensation cung cấp các endpoint để quản lý quy trình yêu cầu bồi thường.

### 3.1. Submit Compensation Request

- **Endpoint**: `POST {{baseUrl}}/compensation/submit`
- **Authentication**: Required
- **Description**: Gửi yêu cầu bồi thường cho một giao dịch

#### Sample Request:

```json
{
  "transaction_id": "TXN-*********",
  "merchant_order_no": "ORD-********9",
  "compensation_amount": 1500.0,
  "compensation_currency": "VND",
  "reason": "Unauthorized transaction resulted in chargeback and fees",
  "evidence_files": [
    "https://example.com/evidence/chargeback-notice.pdf",
    "https://example.com/evidence/fee-statement.pdf"
  ],
  "additional_details": {
    "chargeback_reference": "CB-********",
    "chargeback_date": "2023-08-10T08:15:30Z",
    "chargeback_reason_code": "10.4",
    "processor_fee": 250.0,
    "bank_fee": 150.0,
    "operational_cost": 100.0,
    "total_loss": 2000.0
  },
  "contact_person": {
    "name": "Nguyen Thi B",
    "email": "<EMAIL>",
    "phone": "+***********",
    "department": "Finance"
  }
}
```

#### Sample Response:

```json
{
  "success": true,
  "message": "Compensation request submitted successfully",
  "data": {
    "request_id": "COMP-********",
    "status": "PENDING",
    "processed_amount": 0,
    "processed_currency": "VND",
    "created_at": "2023-08-25T10:20:30Z",
    "sign": "a1b2c3d4e5f6g7h8i9j0..."
  }
}
```

### 3.2. Check Compensation Status

- **Endpoint**: `GET {{baseUrl}}/compensation/status/:requestId`
- **Authentication**: Required
- **Description**: Kiểm tra trạng thái của một yêu cầu bồi thường đã gửi trước đó

#### Sample Request:

URL: `{{baseUrl}}/compensation/status/COMP-********`

#### Sample Response:

```json
{
  "success": true,
  "message": "Compensation status retrieved successfully",
  "data": {
    "request_id": "COMP-********",
    "status": "APPROVED",
    "processed_amount": 1500.0,
    "processed_currency": "VND",
    "created_at": "2023-08-25T10:20:30Z",
    "updated_at": "2023-08-28T14:35:20Z",
    "approval_date": "2023-08-28T14:35:20Z",
    "expected_payment_date": "2023-09-05T00:00:00Z",
    "notes": "Compensation approved in full amount",
    "sign": "a1b2c3d4e5f6g7h8i9j0..."
  }
}
```

### 3.3. Submit Additional Evidence

- **Endpoint**: `POST {{baseUrl}}/compensation/:requestId/evidence`
- **Authentication**: Required
- **Description**: Gửi thêm bằng chứng bổ sung cho một yêu cầu bồi thường

#### Sample Request:

URL: `{{baseUrl}}/compensation/COMP-********/evidence`

```json
{
  "evidence_files": [
    "https://example.com/evidence/additional-bank-statement.pdf",
    "https://example.com/evidence/processor-communication.pdf"
  ],
  "description": "Additional evidence showing the full impact of fees and communication with the payment processor",
  "contact_update": {
    "name": "Tran Van C",
    "email": "<EMAIL>",
    "phone": "+***********"
  }
}
```

#### Sample Response:

```json
{
  "success": true,
  "message": "Additional evidence submitted successfully",
  "data": {
    "request_id": "COMP-********",
    "evidence_received": 2,
    "status": "UNDER_REVIEW",
    "updated_at": "2023-08-27T11:40:15Z"
  }
}
```

### 3.4. List Compensation Requests

- **Endpoint**: `GET {{baseUrl}}/compensation/list`
- **Authentication**: Required
- **Description**: Lấy danh sách các yêu cầu bồi thường với phân trang và bộ lọc tùy chọn

#### Sample Request:

URL: `{{baseUrl}}/compensation/list?page=1&limit=10&status=PENDING`

#### Sample Response:

```json
{
  "success": true,
  "message": "Compensation requests retrieved successfully",
  "data": {
    "requests": [
      {
        "request_id": "COMP-********",
        "transaction_id": "TXN-*********",
        "merchant_order_no": "ORD-********9",
        "status": "PENDING",
        "compensation_amount": 1500.0,
        "compensation_currency": "VND",
        "created_at": "2023-08-25T10:20:30Z",
        "updated_at": "2023-08-25T10:20:30Z"
      },
      {
        "request_id": "COMP-12345679",
        "transaction_id": "TXN-987654322",
        "merchant_order_no": "ORD-123456790",
        "status": "PENDING",
        "compensation_amount": 2500.0,
        "compensation_currency": "VND",
        "created_at": "2023-08-26T09:15:10Z",
        "updated_at": "2023-08-26T09:15:10Z"
      }
    ],
    "pagination": {
      "total": 15,
      "page": 1,
      "limit": 10,
      "pages": 2
    }
  }
}
```

## 4. Early-Warning API

API Early-Warning cung cấp các endpoint để cấu hình và quản lý dịch vụ cảnh báo sớm về rủi ro gian lận và hoàn tiền.

### 4.1. Activate Early Warning

- **Endpoint**: `POST {{baseUrl}}/early-warning/activate`
- **Authentication**: Required (Admin)
- **Description**: Kích hoạt dịch vụ cảnh báo sớm cho tài khoản merchant

#### Sample Request:

```json
{
  // No payload required (uses configuration from environment)
}
```

#### Sample Response:

```json
{
  "success": true,
  "message": "Early warning service activated successfully",
  "data": {
    "active": true,
    "callback_url": "https://api.example.com/webhooks/early-warning",
    "activation_date": "2023-08-30T08:00:00Z",
    "service_types": ["ETHOCA", "RDR"],
    "configured_alerts": ["FRAUD", "DISPUTE"]
  }
}
```

### 4.2. Check Early Warning Status

- **Endpoint**: `GET {{baseUrl}}/early-warning/status`
- **Authentication**: Required
- **Description**: Kiểm tra trạng thái hiện tại của dịch vụ cảnh báo sớm

#### Sample Request:

URL: `{{baseUrl}}/early-warning/status`

#### Sample Response:

```json
{
  "success": true,
  "message": "Early warning status retrieved successfully",
  "data": {
    "active": true,
    "callback_url": "https://api.example.com/webhooks/early-warning",
    "activation_date": "2023-08-30T08:00:00Z",
    "last_check_date": "2023-09-01T12:30:45Z",
    "service_types": ["ETHOCA", "RDR"],
    "configured_alerts": ["FRAUD", "DISPUTE"],
    "alerts_received_24h": 5,
    "service_health": "GOOD"
  }
}
```

### 4.3. Update Callback URL

- **Endpoint**: `POST {{baseUrl}}/early-warning/update-callback`
- **Authentication**: Required (Admin)
- **Description**: Cập nhật URL callback nơi nhận thông báo cảnh báo sớm

#### Sample Request:

```json
{
  "callbackUrl": "https://api.example.com/webhooks/early-warning-new"
}
```

#### Sample Response:

```json
{
  "success": true,
  "message": "Callback URL updated successfully",
  "data": {
    "callback_url": "https://api.example.com/webhooks/early-warning-new",
    "updated_at": "2023-09-01T15:45:30Z",
    "status": "ACTIVE",
    "test_notification_sent": true
  }
}
```

### 4.4. Process Ethoca Alert Webhook

- **Endpoint**: `POST {{baseUrl}}/early-warning/webhook/ethoca`
- **Authentication**: Not Required (called by Ethoca)
- **Description**: Xử lý webhook thông báo cảnh báo từ Ethoca

#### Sample Request:

```json
{
  "alertId": "ETH-********9",
  "age": 2,
  "alertTime": "2023-09-01T10:15:30Z",
  "alertType": "fraud",
  "amount": 1500.0,
  "currency": "VND",
  "descriptor": "Example E-commerce - Smartphone X",
  "alertSource": "ETHOCA",
  "arn": "ARN********9",
  "authCode": "AUTH-12345",
  "cardBin": "450000",
  "cardNumber": "XXXX-XXXX-XXXX-4567",
  "chargebackCode": "",
  "disputeAmount": 0,
  "disputeCurrency": "",
  "initiatedBy": "ISSUER",
  "issuer": "Example Bank",
  "liability": "MERCHANT",
  "merchantCategoryCode": "5399",
  "transactionId": "TXN-*********",
  "transactionTime": "2023-08-31T14:30:15Z",
  "transactionType": "PURCHASE",
  "riskScore": 85,
  "recommendedAction": "REFUND",
  "timeToRespond": "48H"
}
```

#### Sample Response:

```json
{
  "success": true,
  "message": "Ethoca alert processed successfully",
  "data": {
    "alertId": "ETH-********9",
    "received": true,
    "processed": true,
    "notificationSent": true,
    "timestamps": {
      "received": "2023-09-01T10:15:35Z",
      "processed": "2023-09-01T10:15:36Z",
      "notification": "2023-09-01T10:15:37Z"
    }
  }
}
```

### 4.5. Process RDR Alert Webhook

- **Endpoint**: `POST {{baseUrl}}/early-warning/webhook/rdr`
- **Authentication**: Not Required (called by RDR)
- **Description**: Xử lý webhook thông báo cảnh báo từ RDR (Rapid Dispute Resolution)

#### Sample Request:

```json
{
  "alertId": "RDR-********9",
  "alertTime": "2023-09-01T11:20:45Z",
  "alertType": "dispute_prevention",
  "amount": 1500.0,
  "currency": "VND",
  "descriptor": "Example E-commerce - Smartphone X",
  "acquirerBin": "123456",
  "acquirerReferenceNumber": "ARN********9",
  "alertSource": "RDR",
  "alertStatus": "ACTIVE",
  "authCode": "AUTH-12345",
  "caid": "CAID123456",
  "cardBin": "450000",
  "cardNumber": "XXXX-XXXX-XXXX-4567",
  "chargebackCode": "10.4",
  "descriptorContact": "+84********9",
  "disputeAmount": 1500.0,
  "disputeCurrency": "VND",
  "merchantCategoryCode": "5399",
  "ruleName": "Pre-arbitration_Prevention",
  "ruleType": "CREDIT",
  "transactionTime": "2023-08-31T14:30:15Z",
  "timeToRespond": "72H",
  "optionToRefund": true,
  "refundAmount": 1500.0,
  "refundCurrency": "VND"
}
```

#### Sample Response:

```json
{
  "success": true,
  "message": "RDR alert processed successfully",
  "data": {
    "alertId": "RDR-********9",
    "received": true,
    "processed": true,
    "notificationSent": true,
    "timestamps": {
      "received": "2023-09-01T11:20:50Z",
      "processed": "2023-09-01T11:20:51Z",
      "notification": "2023-09-01T11:20:52Z"
    }
  }
}
```

### 4.6. Send Ethoca Feedback

- **Endpoint**: `POST {{baseUrl}}/early-warning/feedback/:alertId`
- **Authentication**: Required
- **Description**: Gửi phản hồi về một cảnh báo Ethoca

#### Sample Request:

URL: `{{baseUrl}}/early-warning/feedback/ETH-********9`

```json
{
  "predictorId": "PRED-********",
  "outcome": "stopped",
  "refunded": "refunded",
  "actionTimestamp": "2023-09-01T16:30:00Z",
  "amountStopped": 1500.0,
  "comments": "Transaction refunded to avoid chargeback",
  "isFraud": true,
  "matchOrderNo": "ORD-********9",
  "actionTaken": "FULL_REFUND",
  "additionalEvidence": "Customer confirmed unauthorized transaction"
}
```

#### Sample Response:

```json
{
  "success": true,
  "message": "Ethoca feedback sent successfully",
  "data": {
    "alertId": "ETH-********9",
    "feedbackReceived": true,
    "feedbackProcessed": true,
    "acknowledgement": {
      "id": "ACK-********9",
      "status": "ACCEPTED",
      "timestamp": "2023-09-01T16:30:05Z"
    }
  }
}
```

### 4.7. List Alerts

- **Endpoint**: `GET {{baseUrl}}/early-warning/alerts`
- **Authentication**: Required
- **Description**: Lấy danh sách các cảnh báo sớm với phân trang và bộ lọc tùy chọn

#### Sample Request:

URL: `{{baseUrl}}/early-warning/alerts?page=1&limit=10&alertType=fraud&startDate=2023-09-01&endDate=2023-09-02`

#### Sample Response:

```json
{
  "success": true,
  "message": "Alerts retrieved successfully",
  "data": {
    "alerts": [
      {
        "id": "ALERT-********",
        "alertId": "ETH-********9",
        "alertSource": "ETHOCA",
        "alertType": "fraud",
        "transactionId": "TXN-*********",
        "merchantOrderNo": "ORD-********9",
        "amount": 1500.0,
        "currency": "VND",
        "alertTime": "2023-09-01T10:15:30Z",
        "status": "PENDING_ACTION",
        "riskLevel": "HIGH"
      },
      {
        "id": "ALERT-12345679",
        "alertId": "RDR-********9",
        "alertSource": "RDR",
        "alertType": "dispute_prevention",
        "transactionId": "TXN-987654322",
        "merchantOrderNo": "ORD-123456790",
        "amount": 2500.0,
        "currency": "VND",
        "alertTime": "2023-09-01T11:20:45Z",
        "status": "PENDING_ACTION",
        "riskLevel": "MEDIUM"
      }
    ],
    "pagination": {
      "total": 5,
      "page": 1,
      "limit": 10,
      "pages": 1
    }
  }
}
```

## Hướng dẫn sử dụng Postman Collection

### 1. Cài đặt Collection

1. Tải file Postman Collection và Environment từ thư mục `docs/postman`
2. Import vào Postman
3. Cấu hình biến môi trường:
   - `baseUrl`: URL gốc của API backend
   - `authToken`: JWT token xác thực (có thể lấy bằng cách gọi API đăng nhập)

### 2. Xác thực

1. Trước khi test các API, sử dụng endpoint đăng nhập để lấy JWT token:
   - `POST {{baseUrl}}/auth/login`
   - Body: `{ "email": "<EMAIL>", "password": "your_password" }`
2. Lưu token nhận được vào biến môi trường `authToken`

### 3. Thứ tự test

1. **Anti-Fraud**:

   - Check Transaction
   - Notify Transaction Result

2. **Early-Warning**:

   - Activate Early Warning
   - Check Early Warning Status
   - Update Callback URL
   - List Alerts
   - Webhooks có thể được test bằng công cụ bên ngoài (như webhook.site)

3. **Chargeback-Dispute**:

   - Submit Dispute
   - Check Dispute Status
   - Submit Additional Materials

4. **Compensation**:
   - Submit Compensation Request
   - Check Compensation Status
   - Submit Additional Evidence
   - List Compensation Requests

### 4. Môi trường test

Trong môi trường development, một số API có thể hoạt động trong chế độ mô phỏng và không thực sự gọi đến các dịch vụ bên ngoài. Hãy kiểm tra biến môi trường `.env.development` để xác nhận cấu hình.

### 5. Phân tích lỗi

Nếu nhận được lỗi khi gọi API:

1. Kiểm tra mã trạng thái HTTP và thông báo lỗi
2. Đảm bảo JWT token còn hiệu lực
3. Xác minh dữ liệu đầu vào phù hợp với yêu cầu
4. Kiểm tra các giá trị signature nếu API yêu cầu
5. Xem logs của ứng dụng để biết thêm chi tiết về lỗi

### 6. Best Practices

1. Sử dụng dữ liệu test trong môi trường non-production
2. Tạo các biến môi trường Postman cho các giá trị thường dùng
3. Tạo các test script Postman để xác minh response
4. Sử dụng pre-request scripts để tạo dữ liệu động khi cần thiết
5. Lưu trữ ID và token từ response để sử dụng trong các request tiếp theo
