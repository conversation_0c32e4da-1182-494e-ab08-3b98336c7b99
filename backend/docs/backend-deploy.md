# BACKEND CONFIGURATION AND DEPLOYMENT GUIDE

## Deploying Backend to Vercel Serverless

### System Requirements

- Node.js (recommended version: 18.x or higher)
- npm or yarn
- Vercel account

### Understanding Vercel and Serverless

Vercel is a cloud platform that enables rapid and easy deployment of web applications, particularly well-suited for modern JavaScript applications. When deploying a Node.js backend on Vercel, your application runs as a serverless function, offering numerous benefits:

- **Automatic Scaling**: Serverless automatically scales according to usage demands
- **Pay-per-execution**: Cost savings when there's no traffic
- **Rapid Deployment**: Built-in CI/CD with Vercel
- **Zero Downtime Deployments**: Ensures your application is always available
- **Global CDN**: Distributes your application across a global network, reducing latency

For backend APIs, the serverless model is particularly suitable for applications with varying traffic patterns over time.

### Purpose of Prebuild Setup

Prebuild setup is the process of preparing and configuring the project before Vercel performs the build and deployment. The main purposes include:

1. **Build Time Optimization**: Preparing necessary resources before building
2. **Ensuring Consistency**: Build environment matches local environment
3. **Prisma Integration**: Generating Prisma Client and running migrations when necessary
4. **Process Automation**: Minimizing human errors

In this project, the prebuild setup is configured through scripts in `package.json` and pre-commit hooks, ensuring code is always checked and successfully built before committing.

### Deployment Steps

1. **Install Vercel CLI** (if not already installed)

   ```bash
   npm install -g vercel
   # or
   yarn global add vercel
   ```

2. **Log in to Vercel** (if not already logged in)

   ```bash
   vercel login
   ```

3. **Check Vercel Configuration**

   Ensure the `vercel.json` file is correctly configured in the backend directory:

   ```json
   {
     "version": 2,
     "builds": [
       {
         "src": "build/server.js",
         "use": "@vercel/node",
         "config": { "includeFiles": ["build/**", "node_modules/.prisma/**"] }
       }
     ],
     "routes": [
       {
         "src": "/(.*)",
         "dest": "build/server.js"
       }
     ],
     "env": {
       "NODE_ENV": "vercel"
     }
   }
   ```

4. **Prepare Environment Variables**

   Create an `.env` file in Vercel with the necessary environment variables:

   - Navigate to the backend directory
   - Run the following command and follow the prompts to add environment variables:
     ```bash
     vercel env add
     ```
   - Or configure environment variables directly in the Vercel Dashboard

5. **Package and Deploy**

   ```bash
   # Navigate to the backend directory
   cd backend

   # Build the project
   yarn build
   # or
   npm run build

   # Deploy to Vercel
   vercel
   ```

   To deploy to the production environment:

   ```bash
   vercel --prod
   ```

6. **Verify the Deployed Application**

   After deployment, Vercel will provide a URL to access your application.

## Setting Up Pre-commit Hooks

### Installing Necessary Tools

1. **Install the pre-commit library**

   ```bash
   # In the project root directory
   npm install pre-commit --save-dev
   # or
   yarn add pre-commit --dev
   ```

2. **Configure pre-commit hooks**

   Add the following configuration to the `package.json` file in the root directory:

   ```json
   {
     "scripts": {
       "be-ts-check": "cd backend && yarn ts.check && cd ..",
       "be-build": "cd backend && yarn build && cd ..",
       "be-add-build": "git add backend/build"
     },
     "pre-commit": ["be-ts-check", "be-build", "be-add-build"]
   }
   ```

   > **Important Note**: The `package.json` file containing the pre-commit configuration must be at the same level as the `.git` directory (root of the git repository). This ensures Git hooks can find and execute the pre-commit scripts. If the `package.json` file is in a subdirectory, Git will not be able to automatically find the pre-commit configuration.

3. **Check Backend Configuration**

   Ensure the `package.json` file in the backend directory has the necessary scripts:

   ```json
   {
     "scripts": {
       "build": "yarn prisma:generate && yarn build:production",
       "ts-check": "tsc --project tsconfig.json"
     }
   }
   ```

### How Pre-commit Works

When you run the `git commit` command, pre-commit automatically executes the configured scripts:

1. **be-ts-check**: Checks for TypeScript errors in the backend source code
2. **be-build**: Builds the backend from TypeScript source to JavaScript
3. **be-add-build**: Adds the build directory to staged files for commit

If any step fails, the commit process will be aborted, allowing you to fix errors before committing.

## Troubleshooting Common Issues

### Vercel Deployment Errors

1. **Prisma Client Errors**

   - Ensure you've added `"includeFiles": ["build/**", "node_modules/.prisma/**"]` to the builds configuration in `vercel.json`
   - Confirm that `prisma generate` runs before the build

2. **Environment Variable Errors**

   - Check that all necessary environment variables are configured in Vercel

3. **Build Errors**
   - Check the build logs on Vercel to identify specific errors
   - Try building locally before deploying: `yarn build`

### Pre-commit Hook Issues

1. **Hooks Not Running**

   - Check pre-commit installation: `npm list pre-commit`
   - Ensure you've committed the package.json file with pre-commit configuration

2. **TypeScript Check Errors**
   - Fix the reported TypeScript errors
   - Check if the tsconfig.json file has appropriate configuration
