# Shopify App Uninstall/Reinstall Testing Guide

## Overview
This guide outlines how to test the complete Shopify app uninstall/reinstall workflow that handles immediate uninstall detection, temporary deactivation, permanent data cleanup, and user reactivation.

## Test Environment Setup

### Prerequisites
- Shopify development store with the app installed
- User account with active subscription
- Access to server logs
- Database access for verification
- Webhook endpoints configured

### Required Environment Variables
```bash
SHOPIFY_API_KEY=your_api_key
SHOPIFY_API_SECRET=your_api_secret
SHOPIFY_API_VERSION=2025-04
```

## Test Scenarios

### 1. Immediate Uninstall Detection (API Error Method)

**Test Steps:**
1. Install app and create active user with subscription
2. Go to Shopify admin → Apps → Delete app
3. Immediately try to access the app or make API calls
4. Verify uninstall detection happens within seconds/minutes

**Expected Results:**
- API calls return 401 Unauthorized errors
- `ShopifyUninstallService.detectUninstallFromApiError()` is triggered
- User marked as `isActive: false` and `uninstalledAt: timestamp`
- Store marked as `isActive: false` and `uninstalledAt: timestamp`
- Subscription status changed to `CANCELLED`
- All user tokens invalidated from database and Redis
- Logs show "Potential uninstall detected" messages

**Database Verification:**
```sql
-- Check user status
SELECT id, email, isActive, uninstalledAt FROM users WHERE id = 'user_id';

-- Check store status
SELECT id, storeName, isActive, uninstalledAt FROM linked_stores WHERE id = 'store_id';

-- Check subscription status
SELECT id, status, cancelledAt FROM shopify_subscriptions WHERE linkedStoreId = 'store_id';

-- Check tokens are cleared
SELECT COUNT(*) FROM tokens WHERE userId = 'user_id';
```

### 2. Permanent Data Cleanup (48-Hour Webhook)

**Test Steps:**
1. Wait 48 hours after uninstall (or simulate webhook)
2. Trigger `shop/redact` webhook manually:
   ```bash
   curl -X POST http://localhost:4000/api/shopify/compliance/shop/redact \
   -H "Content-Type: application/json" \
   -H "X-Shopify-Hmac-Sha256: valid_hmac_signature" \
   -d '{
     "shop_id": "12345",
     "shop_domain": "test-store.myshopify.com"
   }'
   ```

**Expected Results:**
- `ShopifyUninstallService.handlePermanentUninstall()` is triggered
- All related data permanently deleted:
  - `shopify_usage_records`
  - `shopify_subscriptions`
  - `block_refunds`
  - `block_usage`
  - `mapped_blocks`
  - `shopify_disputes`
  - `shopify_payouts`
  - `shopify_transactions`
  - `shopify_orders`
  - `bills`
  - `payment_history`
  - `usage_tracking`
  - `store_settings`
  - `linked_stores`
  - `users` (if no other stores)
  - `tokens`
- Logs show "Permanent uninstall completed" messages

### 3. Authentication Middleware Testing

**Test Steps:**
1. After uninstall, try to access protected endpoints
2. Use both JWT tokens and Shopify access tokens
3. Test different API endpoints

**Expected Results:**
- JWT token validation fails with appropriate error:
  ```json
  {
    "success": false,
    "message": "App appears to be uninstalled. Please reinstall the app to continue.",
    "data": {
      "requiresReinstall": true,
      "uninstalledAt": "2025-01-20T...",
      "isTemporaryUninstall": true
    }
  }
  ```
- Shopify token validation fails with similar error
- HTTP 401 status codes returned

### 4. Reinstall Detection and Reactivation

**Test Steps:**
1. After uninstall, reinstall the app from Shopify admin
2. Complete OAuth flow
3. Verify user and store reactivation
4. Test that subscription can be reactivated

**Expected Results:**
- `ShopifyUninstallService.detectAndHandleReinstall()` triggered
- User reactivated: `isActive: true`, `uninstalledAt: null`
- Store reactivated: `isActive: true`, `uninstalledAt: null`
- Subscription status reset to `PENDING`
- New access token stored
- Background sync job started
- Logs show "Reinstall detected and handled"

### 5. Webhook Verification Testing

**Test Steps:**
1. Send webhook requests with invalid HMAC signatures
2. Send webhook requests with valid HMAC signatures
3. Send webhook requests without signatures

**Expected Results:**
- Invalid signatures return 401 Unauthorized
- Valid signatures are processed successfully
- Missing signatures return 401 Unauthorized
- Logs show signature verification details

## Manual Testing Commands

### Simulate Uninstall Detection
```bash
# Test API error detection
curl -X GET http://localhost:4000/api/shopify/stores/store_id \
-H "Authorization: Bearer invalid_token" \
-H "x-api-key: your_api_key"
```

### Simulate Webhook
```bash
# Generate HMAC signature
node -e "
const crypto = require('crypto');
const body = JSON.stringify({shop_id: '12345', shop_domain: 'test.myshopify.com'});
const hmac = crypto.createHmac('sha256', 'your_shopify_secret').update(body).digest('base64');
console.log('HMAC:', hmac);
"

# Send webhook
curl -X POST http://localhost:4000/api/shopify/compliance/shop/redact \
-H "Content-Type: application/json" \
-H "X-Shopify-Hmac-Sha256: generated_hmac" \
-d '{"shop_id": "12345", "shop_domain": "test.myshopify.com"}'
```

### Check Database State
```sql
-- Check all user statuses
SELECT id, email, isActive, uninstalledAt, 
       CASE WHEN uninstalledAt IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (NOW() - uninstalledAt))/3600 
            ELSE NULL 
       END as hours_since_uninstall
FROM users;

-- Check subscription statuses
SELECT ls.storeName, ss.status, ss.cancelledAt, ss.linkedStoreId
FROM shopify_subscriptions ss
JOIN linked_stores ls ON ss.linkedStoreId = ls.id;
```

## Log Monitoring

### Key Log Messages to Watch For
- "Potential uninstall detected for shop: {domain}"
- "Marked store {domain} as temporarily uninstalled"
- "Successfully processed app uninstallation via API error"
- "Reinstall detected and handled for shop: {domain}"
- "Permanent uninstall completed for {count} stores"
- "Invalid Shopify webhook signature"

### Log Locations
- Application logs: Check for uninstall detection and reactivation
- Database logs: Monitor for data cleanup operations
- Redis logs: Check for token invalidation
- Webhook logs: Verify signature verification

## Edge Cases to Test

### 1. Accidental Uninstall
- Test scenario where user uninstalls and immediately reinstalls
- Verify data is preserved and user experience is smooth

### 2. Multiple Stores
- Test user with multiple Shopify stores
- Verify only the uninstalled store is affected
- User should remain active if other stores exist

### 3. Webhook Delivery Issues
- Test webhook retry scenarios
- Test webhook ordering (uninstall detection vs shop/redact)
- Test duplicate webhook handling

### 4. Concurrent Operations
- Test simultaneous uninstall detection and API calls
- Test race conditions between temporary and permanent cleanup

## Success Criteria

✅ **Immediate Response (< 1 minute)**
- Uninstall detected through API errors
- User sessions invalidated
- App access blocked

✅ **Temporary Deactivation (< 48 hours)**
- Data preserved but inaccessible
- User marked as uninstalled
- Clear error messages about reinstallation

✅ **Permanent Cleanup (48+ hours)**
- All shop data deleted according to GDPR
- Audit trail maintained
- Database cleaned up

✅ **Smooth Reinstallation**
- User can reinstall without issues
- Data reactivated appropriately
- Subscription can be renewed

✅ **Security**
- Webhook signatures properly verified
- No unauthorized access to uninstalled stores
- Tokens properly invalidated

## Troubleshooting

### Common Issues
1. **Webhook signature verification fails**
   - Check `SHOPIFY_API_SECRET` environment variable
   - Verify webhook endpoint configuration
   - Check raw body capture middleware

2. **Uninstall not detected**
   - Verify API error wrapping is in place
   - Check if store exists in database
   - Verify shop domain matching

3. **Data not cleaned up**
   - Check foreign key constraints
   - Verify transaction handling
   - Check for orphaned records

### Debug Commands
```bash
# Check environment variables
echo $SHOPIFY_API_SECRET

# Check webhook configuration
curl -X GET "https://admin.shopify.com/admin/api/2025-04/webhooks.json" \
-H "X-Shopify-Access-Token: your_token"

# Check app installation status
curl -X GET "https://admin.shopify.com/admin/api/2025-04/shop.json" \
-H "X-Shopify-Access-Token: your_token"
```

## Conclusion

This comprehensive testing approach ensures that the Shopify uninstall/reinstall workflow handles all scenarios correctly, providing users with a clean slate when they reinstall the app while maintaining GDPR compliance and security standards. 