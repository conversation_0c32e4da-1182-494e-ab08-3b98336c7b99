# Vercel Environment Variables Sync Tool

A shell script tool to sync environment variables from `.env` files to Vercel.

## 🚀 Features

- ✅ Read and parse any `.env` file
- ✅ Support for environments: `production`, `preview`, `development`
- ✅ Preview mode to see what will be added before execution
- ✅ Auto-update if variable already exists
- ✅ Colored output and progress indicators
- ✅ Detailed error handling and logging
- ✅ Confirmation prompts to prevent mistakes

## 📋 Requirements

1. **Vercel CLI** must be installed:
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel**:
   ```bash
   vercel login
   ```

## 🔧 Usage

### Basic syntax:
```bash
./sync-env-to-vercel.sh [OPTIONS]
```

### Options:

| Option | Description | Default |
|--------|-------------|---------|
| `-f, --file FILE` | Specify env file | `.env.production` |
| `-e, --environment ENV` | Target environment | `production` |
| `-p, --preview` | Preview mode (no execution) | `false` |
| `-y, --yes` | Auto-confirm all prompts | `false` |
| `-h, --help` | Show help message | - |

### Supported environments:
- `production` - Production environment
- `preview` - Preview/staging environment  
- `development` - Development environment

## 📖 Usage Examples

### 1. Default usage (.env.production → production):
```bash
./sync-env-to-vercel.sh
```

### 2. Specify different env file:
```bash
./sync-env-to-vercel.sh -f .env.development -e development
```

### 3. Sync staging file to preview environment:
```bash
./sync-env-to-vercel.sh -f .env.staging -e preview
```

### 4. Preview mode (dry run without execution):
```bash
./sync-env-to-vercel.sh -p
```

### 5. Auto-confirm (no confirmation prompts):
```bash
./sync-env-to-vercel.sh -y
```

### 6. Combine multiple options:
```bash
./sync-env-to-vercel.sh -f .env.staging -e preview -p
```

## 📝 .env File Format

Script supports standard `.env` file format:

```bash
# Comments are ignored
NODE_ENV=production
DATABASE_URL="********************************/db"
API_KEY='your-api-key-here'
EMPTY_VALUE=
# Inline comments are also supported
DEBUG=true # This is debug mode
```

### Rules:
- ✅ Comments (`#`) are ignored
- ✅ Empty lines are ignored  
- ✅ Quotes (`"` or `'`) are automatically removed
- ✅ Leading/trailing whitespace is trimmed
- ❌ Variables with empty values will be skipped

## 🎯 Example Output

```bash
🚀 Vercel Environment Variables Sync Tool

[INFO] Logged in as: <EMAIL>
[INFO] Parsing environment file: .env.production
[INFO] Found 15 environment variables

[INFO] Preview mode - showing variables that would be added:
Target environment: production

  NODE_ENV = production
  DATABASE_URL = ********************************/db
  API_KEY = your-api-key-here
  ...

[INFO] Total: 15 variables

Are you sure you want to add 15 variables to Vercel environment 'production'? (y/N) y

[INFO] Starting to add variables to Vercel...

Adding NODE_ENV... ✓
Adding DATABASE_URL... exists, updating... ✓
Adding API_KEY... ✓
...

[INFO] Summary:
  - Successfully added/updated: 15
  - Errors: 0
  - Skipped: 0
  - Total processed: 15

[INFO] All variables successfully synced to Vercel!
```

## ⚠️ Important Notes

1. **Backup before running**: Script will update existing variables
2. **Check .env file**: Ensure no unwanted secrets are included
3. **Preview mode**: Always run with `-p` first to check
4. **Permissions**: Ensure you have access to the Vercel project

## 🔒 Security

- Script does not log secret values
- Only displays key names in output
- Uses official Vercel CLI, no local credential storage

## 🐛 Troubleshooting

### Error: "Vercel CLI is not installed"
```bash
npm install -g vercel
```

### Error: "You are not logged in to Vercel"
```bash
vercel login
```

### Error: "Environment file not found"
- Check the `.env` file path
- Use `-f` option to specify the correct file

### Error: "Some variables failed to be added"
- Check permissions on the Vercel project
- Verify the environment name is correct
- Run again with preview mode to debug