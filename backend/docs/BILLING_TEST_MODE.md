# Shopify Billing Test Mode

This document explains how to configure and use Shopify billing test mode for development and testing purposes.

## Overview

In development mode, the Shopify subscription billing system automatically operates in test mode to prevent actual charges from being made during development and testing.

## Configuration

### Automatic Test Mode
- **Development Environment**: Test mode is automatically enabled when `NODE_ENV=development`
- **Manual Override**: Set `SHOPIFY_BILLING_TEST_MODE=true` in environment variables

### Environment Variables

```bash
# Automatic test mode in development
NODE_ENV=development

# Manual test mode override
SHOPIFY_BILLING_TEST_MODE=true
```

## How It Works

### Test Mode Features
- ✅ **No Real Charges**: Subscriptions are created but no actual billing occurs
- ✅ **Full Functionality**: All billing flows work exactly as in production
- ✅ **Shopify Approval**: Users still need to approve subscriptions in Shopify admin
- ✅ **Usage Tracking**: Usage records are created but marked as test mode
- ✅ **UI Indicators**: Frontend displays test mode indicators

### Backend Changes
- GraphQL `appSubscriptionCreate` mutation includes `test: true` parameter
- Database records include `testMode: true` in metadata
- Logs prefixed with `[TEST]` or `[LIVE]` indicators
- Usage records marked with test mode metadata

### Frontend Changes
- Subscription activation button shows "Activate Test Subscription"
- Test mode indicators throughout the billing UI
- Clear messaging that no charges will be made
- Different step descriptions for test vs live mode

## Development Workflow

### 1. Setup
```bash
# Ensure development environment
NODE_ENV=development

# Or manually enable test mode
SHOPIFY_BILLING_TEST_MODE=true
```

### 2. Testing Subscription Flow
1. Connect Shopify store via OAuth
2. Navigate to billing page
3. Click "Activate Test Subscription"
4. Approve in Shopify admin (no charges made)
5. Verify active subscription status

### 3. Testing Usage Records
- Block processing creates test usage records
- Records marked with `testMode: true` in metadata
- No actual billing occurs

## Database Schema

### Test Mode Metadata
```json
{
  "testMode": true,
  "createdAt": "2025-01-12T10:00:00Z"
}
```

### Usage Records
```json
{
  "metadata": {
    "testMode": true,
    "createdAt": "2025-01-12T10:00:00Z"
  }
}
```

## Production Deployment

### Disable Test Mode
```bash
# Remove or set to false
SHOPIFY_BILLING_TEST_MODE=false

# Or use production environment
NODE_ENV=production
```

### Verification
- Check logs for `[LIVE]` prefixes
- Verify UI shows live billing messages
- Confirm no test mode indicators

## Troubleshooting

### Common Issues

1. **Test Mode Not Working**
   - Verify `NODE_ENV=development` or `SHOPIFY_BILLING_TEST_MODE=true`
   - Check backend logs for test mode indicators
   - Restart server after environment changes

2. **Subscriptions Still Showing Live**
   - Clear browser cache
   - Verify frontend environment variables
   - Check network requests for test parameter

3. **Usage Records Not Marked as Test**
   - Verify subscription metadata includes `testMode: true`
   - Check usage record creation logs
   - Ensure environment variables are loaded

### Debug Commands

```bash
# Check current environment
echo $NODE_ENV
echo $SHOPIFY_BILLING_TEST_MODE

# View test mode status in logs
grep -i "test\|live" logs/app.log
```

## Best Practices

1. **Always Use Test Mode in Development**
   - Never test with live billing
   - Verify test mode is enabled before development

2. **Clear Test Data**
   - Regularly clean test subscriptions
   - Monitor test usage records

3. **Production Checklist**
   - Disable test mode before deployment
   - Verify billing indicators show live mode
   - Test with small amounts initially

## Related Files

- `backend/src/config/environment.ts` - Environment configuration
- `backend/src/services/shopify.service.ts` - Subscription creation
- `backend/src/constants/billing.ts` - Billing configuration
- `backend/src/utils/billing.util.ts` - Billing utilities
- `frontend/src/components/billing/SubscriptionActivation.tsx` - Test mode UI 