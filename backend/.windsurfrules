## Tech Stack
- Backend Framework: Node.js với Express.js
- <PERSON>ôn ngữ lập trình: TypeScript
- C<PERSON> sở dữ liệu: PostgreSQL với Prisma ORM
- Bộ nhớ đệm: Redis
- <PERSON><PERSON><PERSON> thực: JSON Web Tokens (JWT)
- Email: Nodemailer
- <PERSON>hi log: <PERSON>
<PERSON> <PERSON><PERSON><PERSON><PERSON> lý tệp: <PERSON><PERSON>, <PERSON>
- <PERSON><PERSON><PERSON> mật: <PERSON><PERSON><PERSON>, CORS
- Thời gian thực: Socket.io, Pusher
- Kiểm thử: Jest
- Dịch vụ đám mây: Firebase
- Quốc tế hóa: i18next

## Cấu trúc dự án
- <PERSON>ô hình MVC (Model-View-Controller)
- `src/config/`: Ch<PERSON>a cấu hình hệ thống
- `src/controllers/`: <PERSON><PERSON> lý logic nghiệp vụ
- `src/routes/`: Định nghĩa API endpoints
- `src/services/`: <PERSON><PERSON> lý logic phức tạp
- `src/prisma/`: Chứa schema Prisma và migration
- `src/middlewares/`: <PERSON><PERSON> lý middleware
- `src/utils/`: <PERSON>ông cụ tiện ích
- `src/constants/`: Các hằng số
- `src/translations/`: File ngôn ngữ

## Quy tắc coding
- Sử dụng TypeScript nghiêm ngặt với typings rõ ràng
- Sử dụng async/await cho xử lý bất đồng bộ
- Xử lý lỗi triệt để với try/catch
- Chuẩn hóa response API
- Tên endpoints phải tuân theo kebab-case
- Sử dụng Joi để validate dữ liệu đầu vào
- Sử dụng abstraction và DRY (Don't Repeat Yourself)
- Sử dụng dependency injection khi thích hợp
- Tài liệu hóa API với định dạng chuẩn
- Tất cả các route đều phải qua middleware xác thực trừ khi đặc biệt cho phép

## Cấu hình môi trường
- Sử dụng dotenv để quản lý biến môi trường
- Có ba môi trường: development, staging, production
- File cấu hình riêng cho từng môi trường (.env.development, .env.staging, .env.production)

## Prisma Schema Guidelines
- Sử dụng created_at và updated_at cho tất cả model
- Xác định kiểu dữ liệu cụ thể cho mỗi trường
- Sử dụng relations để định nghĩa mối quan hệ giữa các model
- Định nghĩa index rõ ràng cho các trường thường xuyên truy vấn
- Sử dụng enums cho các trường có giá trị cố định
- Viết rõ các ràng buộc và mối quan hệ trong schema
- Sử dụng Prisma Middleware khi cần xử lý logic trước/sau khi tương tác với database

## Kiểm thử
- Viết unit tests cho các hàm quan trọng
- Viết integration tests cho endpoints API
- Sử dụng Prisma với SQLite cho kiểm thử database

## API Design
- RESTful API design
- Versioning cho API endpoints
- Sử dụng HTTP status codes thích hợp
- Chuẩn hóa response format
- Rate limiting cho bảo mật

## Bảo mật
- Băm mật khẩu với bcryptjs
- JWT cho xác thực
- Helmet cho HTTP headers bảo mật
- CORS được cấu hình cẩn thận
- Xác thực và phân quyền rõ ràng

## Quy tắc đặt tên
- camelCase cho biến và hàm
- PascalCase cho class và model Prisma
- kebab-case cho tên file
- UPPER_CASE cho hằng số
- Tên file controller kết thúc bằng .controller.ts
- Tên file route kết thúc bằng .route.ts
- Tên file prisma client service kết thúc bằng .prisma.ts

## Cài đặt file mới
- Bao gồm tất cả các imports cần thiết
- Thêm comment header mô tả tệp
- Sắp xếp imports theo thứ tự: external libraries, sau đó internal modules
- Sử dụng path aliases (@/) để tham chiếu đến thư mục nguồn