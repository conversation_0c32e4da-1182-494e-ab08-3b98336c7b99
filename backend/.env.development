ENV=development
SERVER_GREETING="Welcome to API"
SERVER_CODE=api-quantchargeback
SERVER_PUBLIC_URL=https://be.vinast.com
SERVER_DOMAIN=quantchargeback.com
SERVER_VERSION=1
SERVER_PORT=9000

# AUTHENTICATION
JWT_SECRET=mXx99d
ACCESS_TOKEN_TTL=1y
REFRESH_TOKEN_TTL=1y

# REDIS
REDIS_HOST=*************
REDIS_PORT=6379
REDIS_PASSWORD="6QHYXBOuL6"

# DATABASE
# dev
# DATABASE_URL="postgresql://postgres:<EMAIL>/adlibs?schema=chargeback&sslmode=require"
# SHADOW_DATABASE_URL="postgresql://postgres:<EMAIL>/adlibs?schema=chargeback_shadow&sslmode=require"

# production
DATABASE_URL="postgresql://postgres:<EMAIL>/adlibs?schema=chargeback&sslmode=require"
SHADOW_DATABASE_URL="postgresql://postgres:<EMAIL>/adlibs?schema=chargeback_shadow&sslmode=require"

# Anti-Fraud Service
ANTI_FRAUD_SECRET_ID="your-prod-secret-id"
ANTI_FRAUD_SECRET_KEY="your-prod-secret-key"
ANTI_FRAUD_DOMAIN="rce.na-siliconvalley.wetech-rc.com"
ANTI_FRAUD_REGION="na-siliconvalley"
ANTI_FRAUD_CLIENT_ID="your-prod-client-id"

# Chargeback Dispute Service
CHARGEBACK_MERCHANT_NO="your-prod-merchant-no"
CHARGEBACK_SIGN_KEY="your-prod-sign-key"
CHARGEBACK_API_ENDPOINT="https://mer.tradefensor.com"

# Compensation Service
COMPENSATION_MERCHANT_NO="your-prod-merchant-no"
COMPENSATION_SIGN_KEY="your-prod-sign-key"
COMPENSATION_API_ENDPOINT="https://mer.tradefensor.com"

# Early Warning Service
EARLY_WARNING_MERCHANT_NO=SGB001736
EARLY_WARNING_SIGN_KEY="YhgpK3ATsj0HhN0mSYY6Nw=="
EARLY_WARNING_API_ENDPOINT=https://mer.tradefensor.com
EARLY_WARNING_CALLBACK_URL="https://be.vinast.com/blocks/webhook"

# Webhook log
WEBHOOK_LOG_URL=https://logger.ledinhcuong.com/api/webhook
WEBHOOK_LOG_TOKEN=04c0fabd-f6da-42ea-8f97-1dbee8797457

# Shopify Integration
SHOPIFY_API_KEY=b4d3f857ed7cc383c53852f436eefec3
SHOPIFY_API_SECRET=72f6e2ef37b89a680e16c291da62740a
SHOPIFY_SCOPES=read_orders,read_all_orders,read_custom_pixels,read_pixels,write_orders,read_shopify_payments_disputes
SHOPIFY_HOST_NAME=https://be.vinast.com
SHOPIFY_API_VERSION=2025-04
SHOPIFY_BILLING_TEST_MODE=true
FRONTEND_URL=https://fe.vinast.com

# Stripe
STRIPE_SECRET_KEY=sk_test_51JVUtwBC7gaMHZRU2ICF3MuLt7CAOoh6gsTfDEAu3KAwLoeCuUlZRLlD77UNWd1qD5lg6ISQK7xoD7IePSAc5npY00WwQw1xLP
STRIPE_PUBLISHABLE_KEY=pk_test_51JVUtwBC7gaMHZRUX3J4iyES5w4ZFFoLzPXrLzdCgZZEboUSk5stkWYRUbfqMMlmmeICHdlSnUuVaqD91ODbbB3o00H2m7g2KG
STRIPE_WEBHOOK_SECRET=whsec_qvk5wbcP71kCvqjg1DPrIv0KDdLyFoMW
STRIPE_API_VERSION=2025-05-28.basil
STRIPE_CLIENT_ID=ca_SS6TISpIykFexT6V72wOzQwPGzYfWiAb
STRIPE_OAUTH_REDIRECT_URI=https://be.vinast.com/stripe/oauth/callback

SYNC_SERVICE_URL=http://ec2-54-234-93-109.compute-1.amazonaws.com

PAYPAL_CLIENT_ID=AWGNwJ0HMGC-fUZaTVFEpnS75Cc6Arcgs6gmS5WidaX2-q-qSsCWCGHfHv8-bNT5pCIYSGrcIk9eW1X8
PAYPAL_CLIENT_SECRET=EPP9fd5KoNnNr--iv3fUhTtS0eTHlVHyZjabXhovFAfW0LH7d3F1gUmtxjRjQ24WdVC_CvU9xDhqEfF2
PAYPAL_MODE="sandbox"
PAYPAL_WEBHOOK_ID=40U54066P0894213H

# Stripe App Platform OAuth (từ stripe apps upload)
STRIPE_APP_CLIENT_ID=ca_SS6TISpIykFexT6V72wOzQwPGzYfWiAb
STRIPE_APP_SECRET_KEY=*********************************************************************************************************** #LIVE MODE
# STRIPE_APP_SECRET_KEY=sk_test_51RxKQAQqSSLBVMW8NFOIEqzstcxhkVUu5jzX0CZWLlLEz5NMYbBNI3C3V545st7WPjvSNPXtiDn5gRBE0nHj5Z0K00YPWeLJaq #TEST MODE
# STRIPE_APP_SECRET_KEY=sk_test_51JVUtwBC7gaMHZRU2ICF3MuLt7CAOoh6gsTfDEAu3KAwLoeCuUlZRLlD77UNWd1qD5lg6ISQK7xoD7IePSAc5npY00WwQw1xLP
STRIPE_APP_REDIRECT_URI=https://be.vinast.com/stripe/oauth/callback
STRIPE_APP_WEBHOOK_SECRET=whsec_...
STRIPE_APP_MODE=test
STRIPE_APP_DISTRIBUTION=public
STRIPE_APP_API_VERSION=2025-05-28.basil
STRIPE_APP_MARKETPLACE_URL=https://marketplace.stripe.com/oauth/v2/chnlink_61T8stwwUSFM4ajdt41QqSSLBVMW8TJI/authorize?client_id=ca_StsCBElVT5r702He0lg216ARWuhj3lQc