#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
DEFAULT_ENV_FILE=".env.production"
TARGET_ENV="production"
PREVIEW_MODE=false
AUTO_CONFIRM=false

# Function to display usage
usage() {
    echo -e "${BLUE}Usage: $0 [OPTIONS]${NC}"
    echo
    echo -e "${YELLOW}OPTIONS:${NC}"
    echo "  -f, --file FILE         Specify env file (default: .env.production)"
    echo "  -e, --environment ENV   Target environment: production, preview, development (default: production)"
    echo "  -p, --preview          Preview mode - show what would be added without executing"
    echo "  -y, --yes              Auto-confirm all prompts"
    echo "  -h, --help             Show this help message"
    echo
    echo -e "${YELLOW}EXAMPLES:${NC}"
    echo "  $0                                          # Use .env.production for production"
    echo "  $0 -f .env.development -e development      # Use custom file for development"
    echo "  $0 -f .env.staging -e preview             # Use staging file for preview"
    echo "  $0 -p                                      # Preview mode only"
    echo "  $0 -y                                      # Auto-confirm all"
}

# Function to log messages
log() {
    local level=$1
    shift
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} $*"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $*"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $*"
            ;;
        "DEBUG")
            echo -e "${BLUE}[DEBUG]${NC} $*"
            ;;
    esac
}

# Function to check if vercel CLI is installed
check_vercel_cli() {
    if ! command -v vercel &> /dev/null; then
        log "ERROR" "Vercel CLI is not installed. Please install it first:"
        echo "  npm install -g vercel"
        exit 1
    fi
}

# Function to check if user is logged in to Vercel
check_vercel_auth() {
    if ! vercel whoami &> /dev/null; then
        log "ERROR" "You are not logged in to Vercel. Please login first:"
        echo "  vercel login"
        exit 1
    fi
}

# Function to parse env file and extract variables
parse_env_file() {
    local env_file=$1
    local -a env_vars=()
    
    if [[ ! -f "$env_file" ]]; then
        log "ERROR" "Environment file '$env_file' not found!"
        exit 1
    fi
    
    log "INFO" "Parsing environment file: $env_file"
    
    # Read file line by line
    while IFS= read -r line || [[ -n "$line" ]]; do
        # Skip empty lines and comments
        if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
            continue
        fi
        
        # Check if line contains = and is a valid env var
        if [[ "$line" =~ ^[[:space:]]*([^=]+)=(.*)$ ]]; then
            local key="${BASH_REMATCH[1]}"
            local value="${BASH_REMATCH[2]}"
            
            # Clean up key (remove leading/trailing whitespace)
            key=$(echo "$key" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
            
            # Clean up value (remove quotes if present and remove newlines)
            value=$(echo "$value" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
            if [[ "$value" =~ ^\"(.*)\"$ ]] || [[ "$value" =~ ^\'(.*)\'$ ]]; then
                value="${BASH_REMATCH[1]}"
            fi
            # Remove any newline characters from the value
            value=$(echo "$value" | tr -d '\n')
            
            # Skip if key is empty
            if [[ -n "$key" ]]; then
                env_vars+=("$key=$value")
            fi
        fi
    done < "$env_file"
    
    # Return the array (using global variable)
    ENV_VARIABLES=("${env_vars[@]}")
    
    log "INFO" "Found ${#ENV_VARIABLES[@]} environment variables"
}

# Function to preview what will be added
preview_variables() {
    echo
    log "INFO" "Preview mode - showing variables that would be added:"
    echo -e "${YELLOW}Target environment: $TARGET_ENV${NC}"
    echo
    
    for var in "${ENV_VARIABLES[@]}"; do
        IFS='=' read -r key value <<< "$var"
        echo -e "  ${GREEN}$key${NC} = ${BLUE}$value${NC}"
    done
    
    echo
    log "INFO" "Total: ${#ENV_VARIABLES[@]} variables"
}

# Function to confirm before proceeding
confirm_action() {
    if [[ "$AUTO_CONFIRM" == true ]]; then
        return 0
    fi
    
    echo
    echo -e "${YELLOW}Are you sure you want to add ${#ENV_VARIABLES[@]} variables to Vercel environment '$TARGET_ENV'? (y/N)${NC}"
    read -r response
    case "$response" in
        [yY][eE][sS]|[yY])
            return 0
            ;;
        *)
            log "INFO" "Operation cancelled by user"
            exit 0
            ;;
    esac
}

# Function to add variables to Vercel
add_variables_to_vercel() {
    local success_count=0
    local error_count=0
    local skipped_count=0
    
    log "INFO" "Starting to add variables to Vercel..."
    echo
    
    for var in "${ENV_VARIABLES[@]}"; do
        IFS='=' read -r key value <<< "$var"
        
        # Skip empty values
        if [[ -z "$value" ]]; then
            log "WARN" "Skipping $key (empty value)"
            ((skipped_count++))
            continue
        fi
        
        echo -n "Adding $key... "
        
        # Add environment variable to Vercel (ensure no newlines)
        if printf '%s' "$value" | vercel env add "$key" "$TARGET_ENV" &> /dev/null; then
            echo -e "${GREEN}✓${NC}"
            ((success_count++))
        else
            # If it fails, it might already exist, try to remove and add again
            echo -n -e "${YELLOW}exists, updating...${NC} "
            if vercel env rm "$key" "$TARGET_ENV" --yes &> /dev/null && printf '%s' "$value" | vercel env add "$key" "$TARGET_ENV" &> /dev/null; then
                echo -e "${GREEN}✓${NC}"
                ((success_count++))
            else
                echo -e "${RED}✗${NC}"
                ((error_count++))
            fi
        fi
    done
    
    echo
    log "INFO" "Summary:"
    echo "  - Successfully added/updated: $success_count"
    echo "  - Errors: $error_count"
    echo "  - Skipped: $skipped_count"
    echo "  - Total processed: ${#ENV_VARIABLES[@]}"
    
    if [[ $error_count -gt 0 ]]; then
        log "WARN" "Some variables failed to be added. Please check manually."
        exit 1
    else
        log "INFO" "All variables successfully synced to Vercel!"
    fi
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--file)
                DEFAULT_ENV_FILE="$2"
                shift 2
                ;;
            -e|--environment)
                TARGET_ENV="$2"
                shift 2
                ;;
            -p|--preview)
                PREVIEW_MODE=true
                shift
                ;;
            -y|--yes)
                AUTO_CONFIRM=true
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                log "ERROR" "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Validate target environment
    if [[ ! "$TARGET_ENV" =~ ^(production|preview|development)$ ]]; then
        log "ERROR" "Invalid environment: $TARGET_ENV"
        log "ERROR" "Valid environments: production, preview, development"
        exit 1
    fi
}

# Main function
main() {
    echo -e "${BLUE}🚀 Vercel Environment Variables Sync Tool${NC}"
    echo
    
    # Parse arguments
    parse_arguments "$@"
    
    # Check prerequisites
    check_vercel_cli
    check_vercel_auth
    
    # Show current user
    local current_user=$(vercel whoami 2>/dev/null)
    log "INFO" "Logged in as: $current_user"
    
    # Parse environment file
    parse_env_file "$DEFAULT_ENV_FILE"
    
    if [[ ${#ENV_VARIABLES[@]} -eq 0 ]]; then
        log "WARN" "No environment variables found in $DEFAULT_ENV_FILE"
        exit 0
    fi
    
    # Show preview
    preview_variables
    
    # If preview mode, exit here
    if [[ "$PREVIEW_MODE" == true ]]; then
        log "INFO" "Preview mode completed"
        exit 0
    fi
    
    # Confirm action
    confirm_action
    
    # Add variables to Vercel
    add_variables_to_vercel
}

# Declare global array for environment variables
declare -a ENV_VARIABLES

# Run main function with all arguments
main "$@"