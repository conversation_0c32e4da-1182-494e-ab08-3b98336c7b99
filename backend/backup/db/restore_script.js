const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

// Initialize Prisma client
const prisma = new PrismaClient();

// Path to the backup data
const backupFile = path.join(__dirname, 'data_backup.json');

async function restoreData() {
  try {
    // Check if backup file exists
    if (!fs.existsSync(backupFile)) {
      console.error('Backup file not found:', backupFile);
      return;
    }

    // Read backup data
    const backupData = JSON.parse(fs.readFileSync(backupFile, 'utf8'));
    
    // Start a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Restore roles first (no dependencies)
      if (backupData.roles && backupData.roles.length > 0) {
        console.log(`Restoring ${backupData.roles.length} roles...`);
        for (const role of backupData.roles) {
          await tx.role.upsert({
            where: { id: role.id },
            update: role,
            create: role,
          });
        }
      }
      
      // Restore users
      if (backupData.users && backupData.users.length > 0) {
        console.log(`Restoring ${backupData.users.length} users...`);
        for (const user of backupData.users) {
          // Extract roles to handle the many-to-many relationship
          const { roles, ...userData } = user;
          
          // Create user without roles first
          await tx.user.upsert({
            where: { id: user.id },
            update: userData,
            create: userData,
          });
          
          // Then connect roles if they exist
          if (roles && roles.length > 0) {
            await tx.user.update({
              where: { id: user.id },
              data: {
                roles: {
                  connect: roles.map(role => ({ id: role.id })),
                },
              },
            });
          }
        }
      }
      
      // Restore clients
      if (backupData.clients && backupData.clients.length > 0) {
        console.log(`Restoring ${backupData.clients.length} clients...`);
        for (const client of backupData.clients) {
          await tx.client.upsert({
            where: { id: client.id },
            update: client,
            create: client,
          });
        }
      }
      
      // Restore tokens (depends on users)
      if (backupData.tokens && backupData.tokens.length > 0) {
        console.log(`Restoring ${backupData.tokens.length} tokens...`);
        for (const token of backupData.tokens) {
          await tx.token.upsert({
            where: { id: token.id },
            update: token,
            create: token,
          });
        }
      }
      
      // Restore linked stores (depends on users)
      if (backupData.linkedStores && backupData.linkedStores.length > 0) {
        console.log(`Restoring ${backupData.linkedStores.length} linked stores...`);
        for (const store of backupData.linkedStores) {
          await tx.linkedStore.upsert({
            where: { id: store.id },
            update: store,
            create: store,
          });
        }
      }
      
      return { success: true };
    });
    
    console.log('Restoration completed successfully!');
  } catch (error) {
    console.error('Error during restoration:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the restoration
restoreData(); 