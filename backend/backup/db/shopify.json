{"orders": {"id": 6273889304809, "name": "#14492", "note": null, "tags": "", "test": false, "email": "<EMAIL>", "phone": null, "token": "8175e3a143e934b312e840682a54c86b", "app_id": 580111, "number": 13492, "refunds": [], "user_id": null, "currency": "USD", "customer": {"id": 8189555704041, "note": null, "tags": "", "email": "<EMAIL>", "phone": "+819010103700", "state": "disabled", "currency": "USD", "last_name": "<PERSON><PERSON><PERSON>", "created_at": "2025-06-16T00:25:49-07:00", "first_name": "<PERSON><PERSON><PERSON>", "tax_exempt": false, "updated_at": "2025-06-16T00:26:16-07:00", "tax_exemptions": [], "verified_email": true, "default_address": {"id": 9698088222953, "zip": "690-0015", "city": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "phone": "+819010103700", "company": null, "country": "Japan", "default": true, "address1": "Agenogi9-4-25", "address2": "Fukuda Internalmedicine clinic", "province": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "customer_id": 8189555704041, "country_code": "JP", "country_name": "Japan", "province_code": "JP-32"}, "admin_graphql_api_id": "gid://shopify/Customer/8189555704041", "multipass_identifier": null, "sms_marketing_consent": {"state": "not_subscribed", "opt_in_level": "single_opt_in", "consent_updated_at": null, "consent_collected_from": "OTHER"}, "email_marketing_consent": {"state": "not_subscribed", "opt_in_level": "single_opt_in", "consent_updated_at": null}}, "closed_at": null, "confirmed": true, "device_id": null, "po_number": null, "reference": null, "tax_lines": [{"rate": 0.2, "price": "21.59", "title": "CT", "price_set": {"shop_money": {"amount": "21.59", "currency_code": "USD"}, "presentment_money": {"amount": "21.59", "currency_code": "USD"}}, "channel_liable": false}], "total_tax": "21.59", "browser_ip": "************", "cart_token": "Z2NwLWFzaWEtc291dGhlYXN0MTowMUpYSDIxMkhGNVBNV1gzQlJNMkQ0RlFUUQ", "created_at": "2025-06-16T00:26:07-07:00", "line_items": [{"id": 15158616293609, "sku": "PK-LionsMane-12", "name": "Wrap Whirl Pure Lion's Mane Caps: Powered with <PERSON><PERSON><PERSON>, Tongkat Ali & Cod Liver Oil (60 N) - 1", "grams": 81, "price": "29.99", "title": "Wrap Whirl Pure Lion's Mane Caps: Powered with <PERSON><PERSON><PERSON>, Tongkat Ali & Cod Liver Oil (60 N)", "duties": [], "vendor": "Wrap Whirl", "taxable": true, "quantity": 6, "gift_card": false, "price_set": {"shop_money": {"amount": "29.99", "currency_code": "USD"}, "presentment_money": {"amount": "29.99", "currency_code": "USD"}}, "tax_lines": [{"rate": 0.2, "price": "21.59", "title": "CT", "price_set": {"shop_money": {"amount": "21.59", "currency_code": "USD"}, "presentment_money": {"amount": "21.59", "currency_code": "USD"}}, "channel_liable": false}], "product_id": 8760195383529, "properties": [{"name": "__kaching_bundles", "value": "{\"deal\":\"QmpV\",\"main\":true,\"ab\":\"A\"}"}, {"name": "__kaching_session_id", "value": "2b125b89-0192-4dba-8ee5-422f0952fe38"}], "variant_id": **************, "variant_title": "1", "product_exists": true, "total_discount": "0.00", "current_quantity": 6, "attributed_staffs": [], "requires_shipping": true, "fulfillment_status": null, "total_discount_set": {"shop_money": {"amount": "0.00", "currency_code": "USD"}, "presentment_money": {"amount": "0.00", "currency_code": "USD"}}, "fulfillment_service": "manual", "admin_graphql_api_id": "gid://shopify/LineItem/15158616293609", "discount_allocations": [{"amount": "71.97", "amount_set": {"shop_money": {"amount": "71.97", "currency_code": "USD"}, "presentment_money": {"amount": "71.97", "currency_code": "USD"}}, "discount_application_index": 0}], "fulfillable_quantity": 6, "variant_inventory_management": null}], "source_url": null, "tax_exempt": false, "updated_at": "2025-06-16T00:26:50-07:00", "checkout_id": 36172671025385, "location_id": null, "source_name": "web", "total_price": "129.56", "cancelled_at": null, "fulfillments": [], "landing_site": "/products/mars-surge-max-powered-with-ashwagandha-shilajit-safed-musli-2?utm_source=120219426329820708&utm_medium=120219426332660708&utm_campaign=120219429552370708&utm_content=120219429552370708&fbclid=IwZXh0bgNhZW0BMABhZGlkAasa7zPvXAQBHhxsm40TtgaasiSc1M6acM_OkIWOxIdFDSg2Lr6TNC8qBi8Iti-AazTwwIlE_aem_BF1da-58qsgDsfyYCgxzvg&campaign_id=120219426329820708&ad_id=120219429552370708&utm_id=120219426329820708&utm_term=120219426332660708", "order_number": 14492, "processed_at": "2025-06-16T00:26:03-07:00", "total_weight": 486, "cancel_reason": null, "contact_email": "<EMAIL>", "payment_terms": null, "total_tax_set": {"shop_money": {"amount": "21.59", "currency_code": "USD"}, "presentment_money": {"amount": "21.59", "currency_code": "USD"}}, "checkout_token": "157061465df1472b6ea473f8f0d7cf72", "client_details": {"browser_ip": "************", "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) WebKit/8621 (KHTML, like Gecko) Mobile/22F76 [FBAN/FBIOS;FBDV/iPhone16,1;FBMD/iPhone;FBSN/iOS;FBSV/18.5;FBSS/3;FBID/phone;FBLC/ja_JP;FBOP/5]", "session_hash": null, "browser_width": null, "browser_height": null, "accept_language": "en"}, "discount_codes": [], "referring_site": "http://m.facebook.com", "shipping_lines": [{"id": 5054594679017, "code": "UPS (Incl. track & trace)", "phone": null, "price": "0.00", "title": "UPS (Incl. track & trace)", "source": "shopify", "price_set": {"shop_money": {"amount": "0.00", "currency_code": "USD"}, "presentment_money": {"amount": "0.00", "currency_code": "USD"}}, "tax_lines": [], "is_removed": false, "discounted_price": "0.00", "carrier_identifier": null, "discount_allocations": [], "discounted_price_set": {"shop_money": {"amount": "0.00", "currency_code": "USD"}, "presentment_money": {"amount": "0.00", "currency_code": "USD"}}, "requested_fulfillment_service_id": null}, {"id": 5054594711785, "code": "UPS (Incl. track & trace)", "phone": null, "price": "0.00", "title": "UPS (Incl. track & trace)", "source": "shopify", "price_set": {"shop_money": {"amount": "0.00", "currency_code": "USD"}, "presentment_money": {"amount": "0.00", "currency_code": "USD"}}, "tax_lines": [], "is_removed": false, "discounted_price": "0.00", "carrier_identifier": null, "discount_allocations": [], "discounted_price_set": {"shop_money": {"amount": "0.00", "currency_code": "USD"}, "presentment_money": {"amount": "0.00", "currency_code": "USD"}}, "requested_fulfillment_service_id": null}], "subtotal_price": "107.97", "taxes_included": false, "billing_address": {"zip": "690-0015", "city": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "phone": "+819010103700", "company": null, "country": "Japan", "address1": "Agenogi9-4-25", "address2": "Fukuda Internalmedicine clinic", "latitude": 35.4434811, "province": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "longitude": 133.0630436, "first_name": "<PERSON><PERSON><PERSON>", "country_code": "JP", "province_code": "JP-32"}, "customer_locale": "en-JP", "duties_included": false, "estimated_taxes": false, "note_attributes": [{"name": "_kaching_session_id", "value": "2b125b89-0192-4dba-8ee5-422f0952fe38"}, {"name": "fbc", "value": "fb.1.1750058600900.IwZXh0bgNhZW0BMABhZGlkAasa8m1eZQQBHphykS2kdtZGlQNKyooFwPdndot1DWi6EKAaqk0JXXYaDYI8NiClwCrmmcI9_aem_LyOvTdVTYNJzhgV-bz4ukQ"}, {"name": "fbp", "value": "fb.1.1750058600900.1770374147"}, {"name": "host", "value": "https://wrapwhirl.com"}, {"name": "sh", "value": "844"}, {"name": "sw", "value": "390"}, {"name": "ttp", "value": "1gXMa19wxF705ey1iVUfnz7mB3S"}, {"name": "utm_campaign", "value": "120219443511740708"}, {"name": "utm_content", "value": "120219443511740708"}, {"name": "utm_id", "value": "120219443491860708"}, {"name": "utm_medium", "value": "120219443495190708"}, {"name": "utm_source", "value": "120219443491860708"}, {"name": "utm_term", "value": "120219443495190708"}], "total_discounts": "71.97", "total_price_set": {"shop_money": {"amount": "129.56", "currency_code": "USD"}, "presentment_money": {"amount": "129.56", "currency_code": "USD"}}, "financial_status": "paid", "landing_site_ref": null, "order_status_url": "https://wrapwhirl.com/69468356841/orders/8175e3a143e934b312e840682a54c86b/authenticate?key=c120f40f24b253dcb11cce6bb8e7a736", "shipping_address": {"zip": "690-0015", "city": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "phone": "+819010103700", "company": null, "country": "Japan", "address1": "Agenogi9-4-25", "address2": "Fukuda Internalmedicine clinic", "latitude": 35.4434811, "province": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "longitude": 133.0630436, "first_name": "<PERSON><PERSON><PERSON>", "country_code": "JP", "province_code": "JP-32"}, "current_total_tax": "21.59", "source_identifier": null, "total_outstanding": "0.00", "fulfillment_status": null, "subtotal_price_set": {"shop_money": {"amount": "107.97", "currency_code": "USD"}, "presentment_money": {"amount": "107.97", "currency_code": "USD"}}, "total_tip_received": "0.00", "confirmation_number": "QE4571V5J", "current_total_price": "129.56", "total_discounts_set": {"shop_money": {"amount": "71.97", "currency_code": "USD"}, "presentment_money": {"amount": "71.97", "currency_code": "USD"}}, "admin_graphql_api_id": "gid://shopify/Order/6273889304809", "presentment_currency": "USD", "current_total_tax_set": {"shop_money": {"amount": "21.59", "currency_code": "USD"}, "presentment_money": {"amount": "21.59", "currency_code": "USD"}}, "discount_applications": [{"type": "automatic", "title": "Buy 6", "value": "40.0", "value_type": "percentage", "target_type": "line_item", "target_selection": "entitled", "allocation_method": "across"}], "payment_gateway_names": ["paypal"], "current_subtotal_price": "107.97", "total_line_items_price": "179.94", "buyer_accepts_marketing": false, "current_total_discounts": "71.97", "current_total_price_set": {"shop_money": {"amount": "129.56", "currency_code": "USD"}, "presentment_money": {"amount": "129.56", "currency_code": "USD"}}, "current_total_duties_set": null, "total_shipping_price_set": {"shop_money": {"amount": "0.00", "currency_code": "USD"}, "presentment_money": {"amount": "0.00", "currency_code": "USD"}}, "merchant_of_record_app_id": null, "original_total_duties_set": null, "current_subtotal_price_set": {"shop_money": {"amount": "107.97", "currency_code": "USD"}, "presentment_money": {"amount": "107.97", "currency_code": "USD"}}, "total_line_items_price_set": {"shop_money": {"amount": "179.94", "currency_code": "USD"}, "presentment_money": {"amount": "179.94", "currency_code": "USD"}}, "current_total_discounts_set": {"shop_money": {"amount": "71.97", "currency_code": "USD"}, "presentment_money": {"amount": "71.97", "currency_code": "USD"}}, "merchant_business_entity_id": "MTY5NDY4MzU2ODQx", "current_total_additional_fees_set": null, "original_total_additional_fees_set": null, "total_cash_rounding_refund_adjustment_set": {"shop_money": {"amount": "0.00", "currency_code": "USD"}, "presentment_money": {"amount": "0.00", "currency_code": "USD"}}, "total_cash_rounding_payment_adjustment_set": {"shop_money": {"amount": "0.00", "currency_code": "USD"}, "presentment_money": {"amount": "0.00", "currency_code": "USD"}}}, "payouts": {"id": 125761650921, "date": "2025-06-17", "amount": "2313.82", "status": "in_transit", "summary": {"charges_fee_amount": "122.46", "refunds_fee_amount": "0.00", "charges_gross_amount": "3049.39", "refunds_gross_amount": "-114.17", "adjustments_fee_amount": "-15.00", "adjustments_gross_amount": "49.64", "reserved_funds_fee_amount": "0.00", "retried_payouts_fee_amount": "0.00", "reserved_funds_gross_amount": "-563.58", "retried_payouts_gross_amount": "0.00"}, "currency": "USD"}, "disputes": {"id": ***********, "type": "chargeback", "amount": "34.80", "reason": "product_not_received", "status": "needs_response", "currency": "USD", "order_id": 6164666056937, "finalized_on": null, "initiated_at": "2025-06-12T08:29:54-07:00", "evidence_due_by": "2025-06-27T16:00:00-07:00", "evidence_sent_on": null, "network_reason_code": "13.1"}, "transactions": {"id": 2641806721257, "fee": "1.12", "net": "29.12", "test": false, "type": "charge", "amount": "30.24", "currency": "USD", "payout_id": null, "source_id": 3005021978857, "source_type": "charge", "processed_at": "2025-06-16T00:17:24-07:00", "payout_status": "pending", "source_order_id": 6273885405417, "adjustment_reason": null, "source_order_transaction_id": 7442113822953, "adjustment_order_transactions": null}}