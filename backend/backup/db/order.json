{"order": {"id": "6270439653609", "linkedStoreId": "2c02968d-fc8d-4374-9c27-08c3785524f2", "name": "#14251", "note": null, "tags": "AfterSell Upsell", "test": false, "email": "<EMAIL>", "phone": null, "token": "69c2465e46eeee2ae59e5721698afb5d", "appId": 580111, "number": 13251, "currency": "USD", "customerData": {"id": 8184811913449, "note": null, "tags": "Login with Shop, Shop", "email": "<EMAIL>", "phone": "+***********", "state": "enabled", "currency": "USD", "last_name": "<PERSON><PERSON><PERSON>", "created_at": "2025-06-13T08:41:06-07:00", "first_name": "<PERSON><PERSON>", "tax_exempt": false, "updated_at": "2025-06-15T06:38:53-07:00", "tax_exemptions": [], "verified_email": true, "default_address": {"id": 9693802725609, "zip": "2165", "city": "Fairfield East", "name": "<PERSON><PERSON>", "phone": "+***********", "company": null, "country": "Australia", "default": true, "address1": "84 Tangerine Street", "address2": null, "province": "New South Wales", "last_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON>", "customer_id": 8184811913449, "country_code": "AU", "country_name": "Australia", "province_code": "NSW"}, "admin_graphql_api_id": "gid://shopify/Customer/8184811913449", "multipass_identifier": null, "sms_marketing_consent": {"state": "not_subscribed", "opt_in_level": "single_opt_in", "consent_updated_at": null, "consent_collected_from": "OTHER"}, "email_marketing_consent": {"state": "not_subscribed", "opt_in_level": "single_opt_in", "consent_updated_at": null}}, "closedAt": "2025-06-14T09:08:28.000Z", "confirmed": true, "deviceId": null, "poNumber": null, "reference": null, "taxData": [], "totalTax": "0", "browserIp": "*************", "cartToken": "Z2NwLWFzaWEtc291dGhlYXN0MTowMUpYTVo1RzZNSE01WTVENjBFUlY2WFQyUw", "createdAt": null, "lineItems": [{"id": 15151987523817, "sku": "PK-Surge-03", "name": "Mars Surge Max: Powered With <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & Safed Musli - 1", "grams": 200, "price": "24.00", "title": "Mars Surge Max: Powered With <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON><PERSON>li", "duties": [], "vendor": "Wrap Whirl.", "taxable": true, "quantity": 1, "gift_card": false, "price_set": {"shop_money": {"amount": "24.00", "currency_code": "USD"}, "presentment_money": {"amount": "24.00", "currency_code": "USD"}}, "tax_lines": [], "product_id": 8892908044521, "properties": [{"name": "__kaching_bundles", "value": "{\"deal\":\"QmpV\",\"main\":true,\"ab\":\"A\"}"}, {"name": "__kaching_session_id", "value": "1c3b73ff-84da-42d9-85bf-9b13aca1c63a"}], "variant_id": **************, "variant_title": "1", "product_exists": true, "total_discount": "0.00", "current_quantity": 1, "attributed_staffs": [], "requires_shipping": true, "fulfillment_status": "fulfilled", "total_discount_set": {"shop_money": {"amount": "0.00", "currency_code": "USD"}, "presentment_money": {"amount": "0.00", "currency_code": "USD"}}, "fulfillment_service": "manual", "admin_graphql_api_id": "gid://shopify/LineItem/15151987523817", "discount_allocations": [], "fulfillable_quantity": 0, "variant_inventory_management": null}, {"id": 15151989260521, "sku": "PK-Surge-03", "name": "Mars Surge Max: Powered With <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & Safed Musli - 1", "grams": 200, "price": "24.00", "title": "Mars Surge Max: Powered With <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON><PERSON>li", "duties": [], "vendor": "Wrap Whirl.", "taxable": true, "quantity": 1, "gift_card": false, "price_set": {"shop_money": {"amount": "24.00", "currency_code": "USD"}, "presentment_money": {"amount": "24.00", "currency_code": "USD"}}, "tax_lines": [], "product_id": 8892908044521, "properties": [], "variant_id": **************, "variant_title": "1", "product_exists": true, "total_discount": "3.60", "current_quantity": 1, "attributed_staffs": [], "requires_shipping": true, "fulfillment_status": "fulfilled", "total_discount_set": {"shop_money": {"amount": "3.60", "currency_code": "USD"}, "presentment_money": {"amount": "3.60", "currency_code": "USD"}}, "fulfillment_service": "manual", "admin_graphql_api_id": "gid://shopify/LineItem/15151989260521", "discount_allocations": [{"amount": "3.60", "amount_set": {"shop_money": {"amount": "3.60", "currency_code": "USD"}, "presentment_money": {"amount": "3.60", "currency_code": "USD"}}, "discount_application_index": 0}], "fulfillable_quantity": 0, "variant_inventory_management": null}, {"id": 15151991161065, "sku": "PK-Surge-03", "name": "Mars Surge Max: Powered With <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & Safed Musli - 1", "grams": 200, "price": "29.99", "title": "Mars Surge Max: Powered With <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON><PERSON>li", "duties": [], "vendor": "Wrap Whirl.", "taxable": true, "quantity": 1, "gift_card": false, "price_set": {"shop_money": {"amount": "29.99", "currency_code": "USD"}, "presentment_money": {"amount": "29.99", "currency_code": "USD"}}, "tax_lines": [], "product_id": 8901569413353, "properties": [], "variant_id": **************, "variant_title": "1", "product_exists": true, "total_discount": "8.99", "current_quantity": 1, "attributed_staffs": [], "requires_shipping": true, "fulfillment_status": "fulfilled", "total_discount_set": {"shop_money": {"amount": "8.99", "currency_code": "USD"}, "presentment_money": {"amount": "8.99", "currency_code": "USD"}}, "fulfillment_service": "manual", "admin_graphql_api_id": "gid://shopify/LineItem/15151991161065", "discount_allocations": [{"amount": "8.99", "amount_set": {"shop_money": {"amount": "8.99", "currency_code": "USD"}, "presentment_money": {"amount": "8.99", "currency_code": "USD"}}, "discount_application_index": 1}], "fulfillable_quantity": 0, "variant_inventory_management": null}], "sourceUrl": null, "taxExempt": false, "updatedAt": null, "checkoutId": "36159051432169", "sourceName": "web", "totalPrice": "65.4", "cancelledAt": null, "fulfillments": [{"id": 5661382574313, "name": "#14251.1", "status": "success", "receipt": {}, "service": "manual", "order_id": 6270439653609, "created_at": "2025-06-14T02:08:28-07:00", "line_items": [{"id": 15151987523817, "sku": "PK-Surge-03", "name": "Mars Surge Max: Powered With <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & Safed Musli - 1", "grams": 200, "price": "24.00", "title": "Mars Surge Max: Powered With <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON><PERSON>li", "duties": [], "vendor": "Wrap Whirl.", "taxable": true, "quantity": 1, "gift_card": false, "price_set": {"shop_money": {"amount": "24.00", "currency_code": "USD"}, "presentment_money": {"amount": "24.00", "currency_code": "USD"}}, "tax_lines": [], "product_id": 8892908044521, "properties": [{"name": "__kaching_bundles", "value": "{\"deal\":\"QmpV\",\"main\":true,\"ab\":\"A\"}"}, {"name": "__kaching_session_id", "value": "1c3b73ff-84da-42d9-85bf-9b13aca1c63a"}], "variant_id": **************, "variant_title": "1", "product_exists": true, "total_discount": "0.00", "current_quantity": 1, "attributed_staffs": [], "requires_shipping": true, "fulfillment_status": "fulfilled", "total_discount_set": {"shop_money": {"amount": "0.00", "currency_code": "USD"}, "presentment_money": {"amount": "0.00", "currency_code": "USD"}}, "fulfillment_service": "manual", "admin_graphql_api_id": "gid://shopify/LineItem/15151987523817", "discount_allocations": [], "fulfillable_quantity": 0, "variant_inventory_management": null}, {"id": 15151989260521, "sku": "PK-Surge-03", "name": "Mars Surge Max: Powered With <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & Safed Musli - 1", "grams": 200, "price": "24.00", "title": "Mars Surge Max: Powered With <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON><PERSON>li", "duties": [], "vendor": "Wrap Whirl.", "taxable": true, "quantity": 1, "gift_card": false, "price_set": {"shop_money": {"amount": "24.00", "currency_code": "USD"}, "presentment_money": {"amount": "24.00", "currency_code": "USD"}}, "tax_lines": [], "product_id": 8892908044521, "properties": [], "variant_id": **************, "variant_title": "1", "product_exists": true, "total_discount": "3.60", "current_quantity": 1, "attributed_staffs": [], "requires_shipping": true, "fulfillment_status": "fulfilled", "total_discount_set": {"shop_money": {"amount": "3.60", "currency_code": "USD"}, "presentment_money": {"amount": "3.60", "currency_code": "USD"}}, "fulfillment_service": "manual", "admin_graphql_api_id": "gid://shopify/LineItem/15151989260521", "discount_allocations": [{"amount": "3.60", "amount_set": {"shop_money": {"amount": "3.60", "currency_code": "USD"}, "presentment_money": {"amount": "3.60", "currency_code": "USD"}}, "discount_application_index": 0}], "fulfillable_quantity": 0, "variant_inventory_management": null}, {"id": 15151991161065, "sku": "PK-Surge-03", "name": "Mars Surge Max: Powered With <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & Safed Musli - 1", "grams": 200, "price": "29.99", "title": "Mars Surge Max: Powered With <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON><PERSON>li", "duties": [], "vendor": "Wrap Whirl.", "taxable": true, "quantity": 1, "gift_card": false, "price_set": {"shop_money": {"amount": "29.99", "currency_code": "USD"}, "presentment_money": {"amount": "29.99", "currency_code": "USD"}}, "tax_lines": [], "product_id": 8901569413353, "properties": [], "variant_id": **************, "variant_title": "1", "product_exists": true, "total_discount": "8.99", "current_quantity": 1, "attributed_staffs": [], "requires_shipping": true, "fulfillment_status": "fulfilled", "total_discount_set": {"shop_money": {"amount": "8.99", "currency_code": "USD"}, "presentment_money": {"amount": "8.99", "currency_code": "USD"}}, "fulfillment_service": "manual", "admin_graphql_api_id": "gid://shopify/LineItem/15151991161065", "discount_allocations": [{"amount": "8.99", "amount_set": {"shop_money": {"amount": "8.99", "currency_code": "USD"}, "presentment_money": {"amount": "8.99", "currency_code": "USD"}}, "discount_application_index": 1}], "fulfillable_quantity": 0, "variant_inventory_management": null}], "updated_at": "2025-06-14T19:21:48-07:00", "location_id": 75215929577, "tracking_url": "https://t.17track.net/en#nums=YSD400248639YQ", "tracking_urls": ["https://t.17track.net/en#nums=YSD400248639YQ"], "origin_address": {}, "shipment_status": null, "tracking_number": "YSD400248639YQ", "tracking_company": "Other", "tracking_numbers": ["YSD400248639YQ"], "admin_graphql_api_id": "gid://shopify/Fulfillment/5661382574313"}], "landingSite": "/products/mars-surge-max-powered-with-ashwagandha-shilajit-safed-musli-2?utm_source=*********329820708&utm_medium=*********332660708&utm_campaign=120219430095610708&utm_content=120219430095610708&fbclid=IwZXh0bgNhZW0BMABhZGlkAasa70yAbAQBHns5KY5cHeUQgdCeUN7kP8jNNytQDJd8rx2buOLfeZ2xGWsS3R9sti-yqlmw_aem_uXnD7e8TQ_1doCgt68FRPQ&campaign_id=*********329820708&ad_id=120219430095610708&utm_id=*********329820708&utm_term=*********332660708", "orderNumber": 14251, "processedAt": "2025-06-13T15:45:20.000Z", "totalWeight": 600, "cancelReason": null, "contactEmail": "<EMAIL>", "paymentTerms": null, "shippingLines": [{"id": 5052022751465, "code": "UPS (Incl. track & trace)", "phone": null, "price": "0.00", "title": "UPS (Incl. track & trace)", "source": "shopify", "price_set": {"shop_money": {"amount": "0.00", "currency_code": "USD"}, "presentment_money": {"amount": "0.00", "currency_code": "USD"}}, "tax_lines": [], "is_removed": false, "discounted_price": "0.00", "carrier_identifier": null, "discount_allocations": [], "discounted_price_set": {"shop_money": {"amount": "0.00", "currency_code": "USD"}, "presentment_money": {"amount": "0.00", "currency_code": "USD"}}, "requested_fulfillment_service_id": null}, {"id": 5052022784233, "code": "UPS (Incl. track & trace)", "phone": null, "price": "0.00", "title": "UPS (Incl. track & trace)", "source": "shopify", "price_set": {"shop_money": {"amount": "0.00", "currency_code": "USD"}, "presentment_money": {"amount": "0.00", "currency_code": "USD"}}, "tax_lines": [], "is_removed": false, "discounted_price": "0.00", "carrier_identifier": null, "discount_allocations": [], "discounted_price_set": {"shop_money": {"amount": "0.00", "currency_code": "USD"}, "presentment_money": {"amount": "0.00", "currency_code": "USD"}}, "requested_fulfillment_service_id": null}], "subtotalPrice": "65.4", "taxesIncluded": false, "billingAddress": {"zip": "2165", "city": "Fairfield East", "name": "<PERSON><PERSON>", "phone": "+***********", "company": null, "country": "Australia", "address1": "84 Tangerine Street", "address2": null, "latitude": -33.8762571, "province": "New South Wales", "last_name": "<PERSON><PERSON><PERSON>", "longitude": 150.9715912, "first_name": "<PERSON><PERSON>", "country_code": "AU", "province_code": "NSW"}, "customerLocale": "en-AU", "dutiesIncluded": false, "estimatedTaxes": false, "noteAttributes": [{"name": "_kaching_session_id", "value": "1c3b73ff-84da-42d9-85bf-9b13aca1c63a"}, {"name": "fbc", "value": "fb.1.1749829207816.IwZXh0bgNhZW0BMABhZGlkAasa70yAbAQBHns5KY5cHeUQgdCeUN7kP8jNNytQDJd8rx2buOLfeZ2xGWsS3R9sti-yqlmw_aem_uXnD7e8TQ_1doCgt68FRPQ"}, {"name": "fbp", "value": "fb.1.1749829207816.1022015035"}, {"name": "host", "value": "https://wrapwhirl.com"}, {"name": "sh", "value": "926"}, {"name": "sw", "value": "428"}, {"name": "ttp", "value": "WViICcUlEmO3CxU70AyZVt5dpcK"}, {"name": "utm_campaign", "value": "120219430095610708"}, {"name": "utm_content", "value": "120219430095610708"}, {"name": "utm_id", "value": "*********329820708"}, {"name": "utm_medium", "value": "*********332660708"}, {"name": "utm_source", "value": "*********329820708"}, {"name": "utm_term", "value": "*********332660708"}], "totalDiscounts": "12.59", "financialStatus": "paid", "landingSiteRef": null, "orderStatusUrl": "https://wrapwhirl.com/***********/orders/69c2465e46eeee2ae59e5721698afb5d/authenticate?key=72c3e969a31766fe579db822ad7b352b", "shippingAddress": {"zip": "2165", "city": "Fairfield East", "name": "<PERSON><PERSON>", "phone": "+***********", "company": null, "country": "Australia", "address1": "84 Tangerine Street", "address2": null, "latitude": -33.8762571, "province": "New South Wales", "last_name": "<PERSON><PERSON><PERSON>", "longitude": 150.9715912, "first_name": "<PERSON><PERSON>", "country_code": "AU", "province_code": "NSW"}, "sourceIdentifier": null, "totalOutstanding": "0", "fulfillmentStatus": "fulfilled", "linkedStore": {"id": "2c02968d-fc8d-4374-9c27-08c3785524f2", "userId": "b3873a3b-986a-4475-a2dc-8f4a1151e5b3", "storeName": "Wrap Whirl.", "provider": "shopify", "providerStoreId": "***********", "createdAt": "2025-06-16T07:31:44.909Z", "updatedAt": "2025-06-16T09:31:12.880Z"}, "transactions": []}, "transactions": [], "payouts": [], "disputes": [], "summary": {"order": {"totalPrice": "65.4", "currency": "USD", "financialStatus": "paid", "fulfillmentStatus": "fulfilled", "customerEmail": "<EMAIL>", "customerName": "<PERSON><PERSON>"}, "transactions": {"count": 0, "totalAmount": "0.00", "totalFees": "0.00", "totalNet": "0.00", "byType": []}, "payouts": {"count": 0, "totalAmount": "0.00"}, "disputes": {"count": 0, "totalAmount": "0.00", "byStatus": []}}}