npx prisma migrate reset
node backup/db/restore_script.js

# Backup
pg_dump "postgresql://postgres:<EMAIL>/adlibs?sslmode=require" \
  --schema=chargeback \
  --no-owner \
  --no-privileges \
  > chargeback_schema_dump.sql

# Restore
sed 's/chargeback\./chargeback_shadow./g' chargeback_schema_dump.sql > chargeback_shadow_schema_dump.sql

psql "postgresql://postgres:<EMAIL>/adlibs?sslmode=require" \
  -f chargeback_shadow_schema_dump.sql
