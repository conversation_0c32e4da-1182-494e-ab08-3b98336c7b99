import { shopify } from '../src/config/shopify';
import path from 'path';
import fs from 'fs';
// Test configuration
const TEST_CONFIG = {
  shop: 'bec4c7-4b.myshopify.com',
  accessToken: '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',
  outputFile: 'results/shopify-orders-test-result.json'
};

async function getOrdersByShopAndAccessToken(shop: string, accessToken: string): Promise<{ orders: any[], total: number }> {
    try {
      const client = new shopify.clients.Rest({
        session: {
          shop,
          accessToken,
        } as any,
      });

      let allOrders: any[] = [];
      let sinceId: string | undefined = undefined;
      const limit = 250; // Shopify's max limit per request

      // Fetch all orders using pagination
      while (true) {
        const query: any = {
          status: 'any',
          limit: limit
        };

        if (sinceId) {
          query.since_id = sinceId;
        } else {
          query.order = 'id asc'; // Only use order when not using since_id
        }

        const ordersResponse = await client.get({
          path: 'orders',
          query: query
        });

        const orders = ordersResponse.body.orders || [];
        
        if (orders.length === 0) {
          break; // No more orders
        }

        allOrders = allOrders.concat(orders);
        
        // If we got less than the limit, we've reached the end
        if (orders.length < limit) {
          break;
        }

        // Set since_id to the last order's id for next iteration
        sinceId = orders[orders.length - 1].id;
      }

      return {
        orders: allOrders,
        total: allOrders.length
      };
    } catch (error) {
      console.error('Error fetching orders from Shopify:', error);
      throw new Error(`Failed to fetch orders from Shopify: ${(error as Error).message}`);
    }
  }

async function testGetAllOrders() {
  console.log('🚀 Starting Shopify Orders Test...');
  console.log(`📍 Shop: ${TEST_CONFIG.shop}`);
  console.log(`🔑 Access Token: ${TEST_CONFIG.accessToken.substring(0, 10)}...`);
  console.log('');

  const startTime = Date.now();
  
  try {
    // Make API request
    const params = {
      shop: TEST_CONFIG.shop,
      accessToken: TEST_CONFIG.accessToken
    };

    console.log(`📋 Parameters:`, params);
    console.log('');

    const response = await getOrdersByShopAndAccessToken(TEST_CONFIG.shop, TEST_CONFIG.accessToken);
    const endTime = Date.now();
    const duration = endTime - startTime;

    // Extract data
    const { total, orders } = response;

    // Save to JSON file
    const outputPath = path.join(__dirname, TEST_CONFIG.outputFile);
    fs.writeFileSync(outputPath, JSON.stringify({ total, orders }, null, 2), 'utf8');

    console.log(`⏱️  Duration: ${duration}ms`);
    console.log(`📦 Total Orders: ${total}`);
    console.log(`💾 Data Saved to: ${outputPath}`);
    console.log('✅ TEST COMPLETED SUCCESSFULLY!');

  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    console.error(`⏱️  Duration: ${duration}ms`);
    console.error(`❌ TEST FAILED!`);
    console.error(`💥 ERROR DETAILS:`, error);
  }
}
testGetAllOrders();