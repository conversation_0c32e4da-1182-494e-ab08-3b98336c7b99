import Stripe from 'stripe';
import { env } from "./environment";

export interface StripeConfig {
  secretKey: string;
  webhookSecret: string;
  apiVersion: string;
  publishableKey?: string;
}

// Stripe configuration with environment variables
const stripeConfig: StripeConfig = {
  secretKey: env.STRIPE_SECRET_KEY || "",
  webhookSecret: env.STRIPE_WEBHOOK_SECRET || "",
  apiVersion: env.STRIPE_API_VERSION || "2023-11-15",
  publishableKey: env.STRIPE_PUBLISHABLE_KEY || "",
};

// Validate required configuration
if (!stripeConfig.secretKey) {
  console.error("Missing required environment variable: STRIPE_SECRET_KEY");
  process.exit(1);
}

if (!stripeConfig.webhookSecret) {
  console.warn("Warning: Missing environment variable: STRIPE_WEBHOOK_SECRET. Webhook signature verification will be disabled.");
}

// Initialize Stripe instance with configuration
let stripeInstance: Stripe | null = null;

export const getStripeInstance = (): Stripe => {
  if (!stripeInstance) {
    stripeInstance = new Stripe(stripeConfig.secretKey, {
      apiVersion: stripeConfig.apiVersion as Stripe.LatestApiVersion,
      typescript: true,
    });
  }
  return stripeInstance;
};

// Export configuration
export default stripeConfig;
