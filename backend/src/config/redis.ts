import { createClient } from "redis";
import { env } from "./environment";
import { createLogger } from "./logger";

const { REDIS_PASSWORD, REDIS_HOST, REDIS_PORT } = env;

const logger = createLogger("logs/redis/error.log", "logs/redis/combined.log");

const redisClient = createClient({
  legacyMode: false,
  password: REDIS_PASSWORD,
  socket: {
    host: REDIS_HOST,
    port: Number(REDIS_PORT),
    connectTimeout: 10000,
  },
});

export const connectRedis = async () => {
  redisClient.on("error", (err) =>
    logger.error(`Redis Client Error. [${JSON.stringify(err)}]`)
  );
  await redisClient
    .connect()
    .then((res) => {
      if (res) {
        logger.info(`Redis connection successfully.`);
      }
    })
    .catch((err) => {
      logger.error(`Redis connection failed. [${JSON.stringify(err)}]`);
    });
};

export const disconnectRedis = async () => {
  await redisClient.disconnect();
};

export enum PrefixRedisKey {
  ACCESS_TOKEN = "ACCESS_TOKEN",
  API_KEY = "API_KEY",
}

export default redisClient;
