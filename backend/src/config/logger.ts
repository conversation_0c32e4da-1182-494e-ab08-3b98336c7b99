import winston from "winston";

export const createLogger = (
  errorFile = "error.log",
  combinedFile = "combined.log"
) => {
  const logger = winston.createLogger({
    level: "info",
    format: winston.format.combine(
      winston.format.timestamp({
        format: "YYYY-MM-DD HH:mm:ss.SSS",
      }),
      // Add stack trace for errors
      winston.format.errors({ stack: true }),
      winston.format.printf(
        ({ timestamp, level, message, stack, ...metadata }) => {
          let log = `${timestamp} | ${level
            .toUpperCase()
            .padEnd(7)} | ${message}`;

          // Add metadata if exists
          if (Object.keys(metadata).length > 0) {
            log += ` | ${JSON.stringify(metadata)}`;
          }

          // Add stack trace if exists
          if (stack) {
            log += `\n${stack}`;
          }

          return log;
        }
      )
    ),
    transports: [
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.simple()
        ),
      }),
      // Only add file transports when not running on Vercel
      ...(process.env.NODE_ENV === "vercel"
        ? []
        : [
            new winston.transports.File({ filename: combinedFile }),
            new winston.transports.File({
              filename: errorFile,
              level: "error",
            }),
          ]),
    ],
  });

  return logger;
};
