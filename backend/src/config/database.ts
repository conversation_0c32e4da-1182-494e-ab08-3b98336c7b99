import { createLogger } from "./logger";
import PrismaService from "@/services/prisma.service";

const logger = createLogger(
  "logs/database/error.log",
  "logs/database/combined.log"
);

// Database collections (for reference only)
export const TABLE_CLIENT = "clients";
export const TABLE_USER = "users";
export const TABLE_ROLE = "roles";
export const TABLE_TOKEN = "tokens";

export const connectDB = async () => {
  try {
    const prismaService = PrismaService.getInstance();
    await prismaService.connect();
    logger.info("Database connection successfully.");
    return prismaService.getClient();
  } catch (err) {
    logger.error(`Database connection failed. [${JSON.stringify(err)}]`);
    throw err;
  }
};

export const disconnectDB = async () => {
  try {
    const prismaService = PrismaService.getInstance();
    await prismaService.disconnect();
    logger.info("Database disconnection successfully.");
  } catch (err) {
    logger.error(`Database disconnection failed. [${JSON.stringify(err)}]`);
    throw err;
  }
};
