import * as dotenv from "dotenv";
import path from "path";

// Load environment variables based on NODE_ENV
if (!process.env.NODE_ENV || process.env.NODE_ENV === "vercel") {
  // Load from default .env file if NODE_ENV is empty or "vercel"
  dotenv.config({
    path: path.resolve(__dirname, "..", "..", ".env"),
  });
} else {
  // Load from environment-specific file (e.g., .env.development, .env.production)
  dotenv.config({
    path: path.resolve(__dirname, "..", "..", `.env.${process.env.NODE_ENV}`),
  });
}

export const env = {
  ENV: process.env.ENV || "development",
  SERVER_GREETING: process.env.SERVER_GREETING || "Welcome to API",
  SERVER_CODE: process.env.SERVER_CODE || "server-code",
  SERVER_PUBLIC_URL: process.env.SERVER_PUBLIC_URL || "http://localhost",
  SERVER_DOMAIN: process.env.SERVER_DOMAIN || "localhost",
  SERVER_VERSION: process.env.SERVER_VERSION || "1",
  SERVER_PORT: process.env.SERVER_PORT || 9000,

  // Authentication
  JWT_SECRET: process.env.JWT_SECRET || "secret",
  ACCESS_TOKEN_TTL: process.env.ACCESS_TOKEN_TTL || "24h",
  REFRESH_TOKEN_TTL: process.env.REFRESH_TOKEN_TTL || "90d",

  // Redis
  REDIS_HOST: process.env.REDIS_HOST || "localhost",
  REDIS_PORT: process.env.REDIS_PORT || 6379,
  REDIS_PASSWORD: process.env.REDIS_PASSWORD || "root",

  // Database
  DATABASE_URL:
    process.env.DATABASE_URL ||
    "postgresql://postgres:postgres@localhost:5432/postgres",

  // Anti-Fraud Service
  ANTI_FRAUD_SECRET_ID: process.env.ANTI_FRAUD_SECRET_ID || "",
  ANTI_FRAUD_SECRET_KEY: process.env.ANTI_FRAUD_SECRET_KEY || "",
  ANTI_FRAUD_DOMAIN:
    process.env.ANTI_FRAUD_DOMAIN || "rce.na-siliconvalley.wetech-rc.com",
  ANTI_FRAUD_REGION: process.env.ANTI_FRAUD_REGION || "na-siliconvalley",
  ANTI_FRAUD_CLIENT_ID: process.env.ANTI_FRAUD_CLIENT_ID || "",

  // Chargeback Dispute Service
  CHARGEBACK_MERCHANT_NO: process.env.CHARGEBACK_MERCHANT_NO || "",
  CHARGEBACK_SIGN_KEY: process.env.CHARGEBACK_SIGN_KEY || "",
  CHARGEBACK_API_ENDPOINT:
    process.env.CHARGEBACK_API_ENDPOINT || "https://mer.tradefensor.com",

  // Compensation Service
  COMPENSATION_MERCHANT_NO: process.env.COMPENSATION_MERCHANT_NO || "",
  COMPENSATION_SIGN_KEY: process.env.COMPENSATION_SIGN_KEY || "",
  COMPENSATION_API_ENDPOINT:
    process.env.COMPENSATION_API_ENDPOINT || "https://mer.tradefensor.com",

  // Early Warning Service
  EARLY_WARNING_MERCHANT_NO: process.env.EARLY_WARNING_MERCHANT_NO || "",
  EARLY_WARNING_SIGN_KEY: process.env.EARLY_WARNING_SIGN_KEY || "",
  EARLY_WARNING_API_ENDPOINT:
    process.env.EARLY_WARNING_API_ENDPOINT || "https://mer.tradefensor.com",
  EARLY_WARNING_CALLBACK_URL: process.env.EARLY_WARNING_CALLBACK_URL || "",

  // Shopify Integration
  SHOPIFY_API_KEY: process.env.SHOPIFY_API_KEY || "",
  SHOPIFY_API_SECRET: process.env.SHOPIFY_API_SECRET || "",
  SHOPIFY_SCOPES:
    process.env.SHOPIFY_SCOPES ||
    "read_products,write_products,read_orders,write_orders,read_customers,read_fulfillments,read_shopify_payments_disputes",
  SHOPIFY_HOST_NAME: process.env.SHOPIFY_HOST_NAME || "",
  SHOPIFY_API_VERSION: process.env.SHOPIFY_API_VERSION || "",
  SHOPIFY_BILLING_TEST_MODE: process.env.SHOPIFY_BILLING_TEST_MODE === "true" || process.env.NODE_ENV === "development",
  FRONTEND_URL: process.env.FRONTEND_URL || "http://localhost:3000",

  // Webhook log
  WEBHOOK_LOG_URL: process.env.WEBHOOK_LOG_URL || "",
  WEBHOOK_LOG_TOKEN: process.env.WEBHOOK_LOG_TOKEN || "",

  // Stripe
  STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY || "",
  STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY || "",
  STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET || "",
  STRIPE_API_VERSION: process.env.STRIPE_API_VERSION || "2023-11-15",
  STRIPE_CLIENT_ID: process.env.STRIPE_CLIENT_ID || "",
  STRIPE_OAUTH_REDIRECT_URI: process.env.STRIPE_OAUTH_REDIRECT_URI || "",
  
  // Stripe App Platform
  STRIPE_APP_CLIENT_ID: process.env.STRIPE_APP_CLIENT_ID || "",
  STRIPE_APP_REDIRECT_URI: process.env.STRIPE_APP_REDIRECT_URI || "",
  STRIPE_APP_SECRET_KEY: process.env.STRIPE_APP_SECRET_KEY || "",
  STRIPE_APP_WEBHOOK_SECRET: process.env.STRIPE_APP_WEBHOOK_SECRET || "",
  STRIPE_APP_MODE: process.env.STRIPE_APP_MODE || "live",
  STRIPE_APP_API_VERSION: process.env.STRIPE_APP_API_VERSION || "2025-07-30.basil",
  STRIPE_APP_DISTRIBUTION: process.env.STRIPE_APP_DISTRIBUTION || "private",
  STRIPE_APP_MARKETPLACE_URL: process.env.STRIPE_APP_MARKETPLACE_URL || "",

  // AWS EventBridge
  AWS_REGION: process.env.AWS_REGION || "us-east-1",
  AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID || "",
  AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY || "",
  AWS_EVENTBRIDGE_BUS_NAME: process.env.AWS_EVENTBRIDGE_BUS_NAME || "default",
  AWS_EVENTBRIDGE_SOURCE: process.env.AWS_EVENTBRIDGE_SOURCE || "QuantChargeBack-EventBridge-CustomApp",
  AWS_EVENTBRIDGE_ENABLED: process.env.AWS_EVENTBRIDGE_ENABLED === "true",

  // Sync Service
  SYNC_SERVICE_URL: process.env.SYNC_SERVICE_URL || "",
};
