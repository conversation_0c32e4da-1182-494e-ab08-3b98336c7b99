import "@shopify/shopify-api/adapters/node";
import {
  shopifyApi,
  LATEST_API_VERSION,
  ApiVersion,
} from "@shopify/shopify-api";
import { env } from "./environment";

// Initialize the Shopify API
export const shopify = shopifyApi({
  apiKey: env.SHOPIFY_API_KEY,
  apiSecretKey: env.SHOPIFY_API_SECRET,
  scopes: env.SHOPIFY_SCOPES.split(","),
  hostName: env.SHOPIFY_HOST_NAME,
  isEmbeddedApp: false,
  apiVersion: (env.SHOPIFY_API_VERSION as ApiVersion) || LATEST_API_VERSION,
});
