import { Router } from "express";
import BlockRefundController from "@/controllers/block-refund.controller";
import { adminRoute } from "@/middlewares/auth.middleware";

export const router: Router = Router();

// Block Refund API routes

// Create a refund
router.post("/", BlockRefundController.create);

// Get all refunds with filters
router.get("/", BlockRefundController.findAll);

// Get refund by ID
router.get("/:id", BlockRefundController.findById);

// Update refund
router.put("/:id", adminRoute, BlockRefundController.update);

// Delete refund
router.delete("/:id", adminRoute, BlockRefundController.delete);

export default router;