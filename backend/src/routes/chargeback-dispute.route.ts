import { Router } from "express";
import ChargebackDisputeController from "@/controllers/chargeback-dispute.controller";

export const router: Router = Router();

// Chargeback Dispute API routes
// Submit dispute
router.post(
  "/submit",
  ChargebackDisputeController.submitDispute
);

// Check dispute status
router.get(
  "/status/:merchantOrderNo",
  ChargebackDisputeController.checkDisputeStatus
);

// Submit additional materials
router.post(
  "/:merchantOrderNo/materials",
  ChargebackDisputeController.submitAdditionalMaterials
);

// Get chargebacks by store
router.get(
  "/store/:storeId",
  ChargebackDisputeController.getChargebacksByStore
);

// Get chargeback statistics by store
router.get(
  "/store/:storeId/stats",
  ChargebackDisputeController.getChargebackStats
);
