import { Router } from 'express';
import { publicRoute } from '../middlewares/auth.middleware';
import { providerAuthRoute } from '../middlewares/provider-auth.middleware';
import PaymentHistoryController from '../controllers/payment-history.controller';

const router = Router();

// Apply API key validation first, then provider authentication for user-specific routes
router.use(publicRoute);
router.use(providerAuthRoute);

// POST /payment-history - Create a new payment history record
router.post('/', PaymentHistoryController.createPaymentHistory);

// GET /payment-history - Get all payment history
router.get('/', PaymentHistoryController.getAllPaymentHistory);

// GET /payment-history/store/:storeId - Get payment history by store ID
router.get('/store/:storeId', PaymentHistoryController.getPaymentHistoryByStoreId);

// GET /payment-history/:id - Get payment history by ID
router.get('/:id', PaymentHistoryController.getPaymentHistory);

// PATCH /payment-history/:id - Update payment history
router.patch('/:id', PaymentHistoryController.updatePaymentHistory);

// DELETE /payment-history/:id - Delete payment history
router.delete('/:id', PaymentHistoryController.deletePaymentHistory);

export default router;