import { Router } from "express";
import BlockController from "@/controllers/block.controller";
import { publicRoute } from "@/middlewares/auth.middleware";
import { providerAuthRoute } from "@/middlewares/provider-auth.middleware";
import {
  handleMerchantRegistrationWebhook,
} from '../controllers/alert-info.controller';

export const router: Router = Router();

// Block API routes

// Process Ethoca block webhook (public - for external services)
router.post("/webhook/ethoca", publicRoute, BlockController.processEthocaBlock);

// Process RDR block webhook (public - for external services)
router.post("/webhook/rdr", publicRoute, BlockController.processRdrBlock);

// Process Alert info webhook (public - for external services)
router.post("/webhook/alert-info/:alertInfoId", publicRoute, handleMerchantRegistrationWebhook);

// Apply API key validation first, then provider authentication for user-specific routes
router.use(publicRoute);
router.use(providerAuthRoute);

// Send Block feedback
router.post(
  "/feedback/:id",
  BlockController.sendBlockFeedback
);

// Clear block cache
router.delete("/cache/clear", BlockController.clearCache);

// Get chargeback rate chart data
router.get("/chart/chargeback-rate", BlockController.getChargebackRateChart);

// List blocks
router.get("/",
   BlockController.getBlocks);

// Find matched orders for a block
router.get(
  "/:id/matched-orders/:linkedStoreId",
  BlockController.findMatchedOrder
);

// Get a single block by ID
router.get(
  "/:id",
  BlockController.getBlock
);

// Delete a block
router.delete(
  "/:id",
  BlockController.deleteBlock
); 