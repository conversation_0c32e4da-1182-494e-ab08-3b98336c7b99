import { Router } from "express";
import UserController from "@/controllers/user.controller";
import { adminRoute } from "@/middlewares/auth.middleware";
export const router: Router = Router();

// Public routes
router.get("/:id", UserController.getUser);
router.put("/:id", UserController.updateUser);

// Admin routes
router.get("/", adminRoute, UserController.getUsers);
router.post("/", adminRoute, UserController.createUser);
router.delete("/:id", adminRoute, UserController.deleteUser);
