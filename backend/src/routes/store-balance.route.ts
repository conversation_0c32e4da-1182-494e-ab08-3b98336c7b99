import { Router } from 'express';
import { publicRoute } from '../middlewares/auth.middleware';
import { providerAuthRoute } from '../middlewares/provider-auth.middleware';
import { StoreBalanceController } from '@/controllers/store-balance.controller';

const router = Router();

// Apply API key validation first, then provider authentication for user-specific routes
router.use(publicRoute);
router.use(providerAuthRoute);

// Get current balance for a store
router.get(
  '/:storeId/balance',
  StoreBalanceController.getCurrentBalance
);

// Get available payment methods for a store
router.get(
  '/:storeId/payment-methods',
  StoreBalanceController.getPaymentMethods
);

// Get balance history for a store
router.get(
  '/:storeId/balance-history',
  StoreBalanceController.getBalanceHistory
);

// Perform manual topup
router.post(
  '/:storeId/topup',
  StoreBalanceController.manualTopup
);

// Check and maintain minimum balances for all stores
router.post(
  '/maintenance/check-balances',
  StoreBalanceController.checkAndMaintainBalances
);

// Check and maintain minimum balance for a specific store
router.post(
  '/:storeId/maintenance/check-balance',
  StoreBalanceController.checkAndMaintainBalances
);

export default router;