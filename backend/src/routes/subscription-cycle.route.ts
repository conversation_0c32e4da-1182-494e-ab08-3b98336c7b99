import { Router } from 'express';
import { Request, Response } from 'express';
import { publicRoute } from '@/middlewares/auth.middleware';
import { providerAuthRoute } from '@/middlewares/provider-auth.middleware';
import ShopifyService from '@/services/shopify.service';
import { usageTrackingService } from '@/services/usage-tracking.service';
import logger from '@/utils/logger.util';
import PrismaService from '@/services/prisma.service';

const router = Router();
const prisma = PrismaService.getInstance().getClient();
const shopifyService = new ShopifyService();

// Apply API key validation first, then provider authentication for user-specific routes
router.use(publicRoute);
router.use(providerAuthRoute);

/**
 * POST /api/subscription-cycle/new
 * Start a new subscription cycle when the monthly limit is reached
 */
router.post('/new', async (req: Request, res: Response) => {
  const storeId = req.headers['x-store-id'] as string;
  
  if (!storeId) {
    return res.status(400).json({
      success: false,
      message: 'Store ID is required'
    });
  }

  try {
    logger.info(`Starting new subscription cycle for store ${storeId}`);
    
    // Get current usage to verify limit is reached
    const currentUsage = await usageTrackingService.getCurrentPeriodUsage(storeId);
    
    // Check if the store has actually reached the limit
    if (currentUsage.totalAmount < 79999) { // $799.99 in cents
      return res.status(400).json({
        success: false,
        message: 'Monthly usage limit not yet reached'
      });
    }
    
    // Get store info for Shopify shop domain
    const store = await shopifyService.getStoreInfo(storeId);
    
    if (!store || !store.shopifyShop) {
      return res.status(400).json({
        success: false,
        message: 'Store not connected to Shopify'
      });
    }
    
    // Create a new app charge via Shopify
    const result = await shopifyService.createNewSubscriptionCycle(
      store.shopifyShop,
      store.shopifyAccessToken!,
      currentUsage.totalAmount
    );
    
    if (result.success) {
      logger.info(`Successfully created new subscription cycle for store ${storeId}`);
      
      return res.json({
        success: true,
        message: 'New subscription cycle started successfully',
        data: {
          confirmationUrl: result.confirmationUrl,
          chargeId: result.chargeId
        }
      });
    } else {
      throw new Error(result.error || 'Failed to create new subscription cycle');
    }
  } catch (error: any) {
    logger.error('Error starting new subscription cycle:', error);
    
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to start new subscription cycle'
    });
  }
});

/**
 * GET /api/subscription-cycle/status
 * Check the current subscription cycle status
 */
router.get('/status', async (req: Request, res: Response) => {
  const storeId = req.headers['x-store-id'] as string;
  
  if (!storeId) {
    return res.status(400).json({
      success: false,
      message: 'Store ID is required'
    });
  }

  try {
    // Get current usage
    const currentUsage = await usageTrackingService.getCurrentPeriodUsage(storeId);
    
    // Calculate if new cycle is available
    const canStartNewCycle = currentUsage.totalAmount >= 79999; // $799.99 in cents
    const remainingAmount = Math.max(0, 79999 - currentUsage.totalAmount);
    
    return res.json({
      success: true,
      data: {
        currentPeriod: currentUsage.period,
        currentUsage: currentUsage.totalAmount,
        usageLimit: 79999,
        canStartNewCycle,
        remainingAmount,
        usagePercentage: (currentUsage.totalAmount / 79999) * 100
      }
    });
  } catch (error: any) {
    logger.error('Error checking subscription cycle status:', error);
    
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to check subscription cycle status'
    });
  }
});

export default router;