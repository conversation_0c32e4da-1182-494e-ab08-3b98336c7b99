import { Router } from "express";
import { router as auth } from "./auth.route";
import { router as user } from "./user.route";
import { router as client } from "./client.route";
import { router as antiFraud } from "./anti-fraud.route";
import { router as chargebackDispute } from "./chargeback-dispute.route";
import { router as compensation } from "./compensation.route";
import { router as shopifyRouter } from "./shopify.routes";
import shopifySyncRouter from "./shopify-sync.routes";
import shopifyOrderDetailRouter from "./shopify-order-detail.routes";
import shopifyComplianceRouter from "./shopify-compliance.routes";
import { router as block } from "./block.route";
import { router as matchedBlocks } from "./matched-blocks.route";
import alertInfoRouter from "./alert-info.route";
import stripeRoutes from "./stripe";
import billingRoutes from "./billing.routes";
import billRoutes from "./bill.route";
import paymentHistoryRoutes from "./payment-history.route";
import blockRefundRoutes from "./block-refund.route";
import storeSettingsRoutes from "./store-settings.route";
// import { paypalRoutes } from "./paypal.route";
import storeBalanceRoutes from "./store-balance.route";
import usageTrackingRoutes from "./usage-tracking.route";
import subscriptionCycleRoutes from "./subscription-cycle.route";
import shopifyWebhooksRoutes from "./shopify-webhooks.route";
import { router as test } from "./test.route";
import linkedStoreRoutes from "./linked-store.route";
import statisticsRoutes from "./statistics.route";

const router = Router();

router.use("/clients", client);
router.use("/auth", auth);
router.use("/users", user);
router.use("/anti-fraud", antiFraud);
router.use("/disputes", chargebackDispute);
router.use("/compensation", compensation);
router.use("/shopify", shopifyRouter);
router.use("/shopify-sync", shopifySyncRouter);
router.use("/shopify", shopifyOrderDetailRouter);
router.use("/shopify/compliance", shopifyComplianceRouter);
router.use("/blocks", block);
router.use("/matched-blocks", matchedBlocks);
router.use("/alert-info", alertInfoRouter);
router.use("/stripe", stripeRoutes);
router.use("/billing", billingRoutes);
router.use("/bill", billRoutes);
router.use("/payment-history", paymentHistoryRoutes);
router.use("/block-refunds", blockRefundRoutes);
router.use("/store-settings", storeSettingsRoutes);
// router.use("/paypal", paypalRoutes);
router.use("/stores", storeBalanceRoutes);
router.use("/usage", usageTrackingRoutes);
router.use("/subscription-cycle", subscriptionCycleRoutes);
router.use("/api/shopify-webhooks", shopifyWebhooksRoutes);
router.use("/linked-stores", linkedStoreRoutes);
router.use("/statistics", statisticsRoutes);

router.use("/test", test);

export default router;
