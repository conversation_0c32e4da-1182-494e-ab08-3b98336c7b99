import express from 'express';
import {
  createAlertInfoController,
  getAlertInfosByStoreIdController,
  getAlertInfoByIdController,
  updateAlertInfoController,
  deleteAlertInfoController,
  getRegistrationStatusController,
  handleMerchantRegistrationWebhook,
  handleTradeDefensorWebhook
} from '../controllers/alert-info.controller';

const router = express.Router();

// Create a new alert info
router.post('/', createAlertInfoController);

// Get all alert info for a store
router.get('/store/:storeId', getAlertInfosByStoreIdController);

// Get alert info by id
router.get('/:id', getAlertInfoByIdController);

// Update alert info
router.put('/:id', updateAlertInfoController);

// Delete alert info
router.delete('/:id', deleteAlertInfoController);


// Get registration status
router.get('/:id/status', getRegistrationStatusController);

// Webhook endpoints
// Handle general TradeDefensor webhooks
router.post('/webhooks/tradefensor', handleTradeDefensorWebhook);

// Handle merchant registration status notifications (商户卡账单状态结果通知)
router.post('/webhooks/merchant-registration', handleMerchantRegistrationWebhook);

export default router;
