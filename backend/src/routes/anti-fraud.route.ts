import { Router } from "express";
import AntiFraudController from "@/controllers/anti-fraud.controller";
import { adminRoute, userRoute } from "@/middlewares/auth.middleware";

export const router: Router = Router();

// Anti-Fraud API routes
// Transaction risk check
router.post(
  "/check-transaction",
  userRoute,
  AntiFraudController.checkTransaction
);

// Transaction result notification
router.post(
  "/notify-transaction",
  userRoute,
  AntiFraudController.notifyTransactionResult
);
