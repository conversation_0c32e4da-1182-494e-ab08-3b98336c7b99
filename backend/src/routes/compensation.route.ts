import { Router } from "express";
import CompensationController from "@/controllers/compensation.controller";
import { adminRoute, userRoute } from "@/middlewares/auth.middleware";

export const router: Router = Router();

// Compensation API routes
// Submit compensation request
router.post(
  "/submit",
  userRoute,
  CompensationController.submitCompensationRequest
);

// Check compensation status
router.get(
  "/status/:requestId",
  userRoute,
  CompensationController.checkCompensationStatus
);

// Submit additional evidence
router.post(
  "/:requestId/evidence",
  userRoute,
  CompensationController.submitAdditionalEvidence
);

// List compensation requests
router.get(
  "/list",
  userRoute,
  CompensationController.listCompensationRequests
);
