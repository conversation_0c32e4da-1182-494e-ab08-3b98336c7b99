import { Router } from "express";
import { statisticsController } from "../controllers/statistics.controller";
// import { userRoute } from "../middlewares/auth.middleware";

const router = Router();

// Apply authentication middleware to all routes
// router.use(userRoute);

/**
 * @swagger
 * components:
 *   schemas:
 *     ChargebackCountryStats:
 *       type: object
 *       properties:
 *         countryCode:
 *           type: string
 *           description: ISO 2-letter country code
 *           example: "US"
 *         countryName:
 *           type: string
 *           description: Full country name
 *           example: "United States"
 *         count:
 *           type: number
 *           description: Number of chargebacks from this country
 *           example: 1247
 *
 *     ChargebackReasonStats:
 *       type: object
 *       properties:
 *         reasonCode:
 *           type: string
 *           description: Chargeback reason code
 *           example: "FRAUD"
 *         reasonName:
 *           type: string
 *           description: Human-readable reason name
 *           example: "Fraud"
 *         count:
 *           type: number
 *           description: Number of chargebacks with this reason
 *           example: 892
 *
 *     ChargebackStatsOverview:
 *       type: object
 *       properties:
 *         countries:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/ChargebackCountryStats'
 *         reasons:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/ChargebackReasonStats'
 */

/**
 * @swagger
 * /api/statistics/{id}/countries:
 *   get:
 *     summary: Get top chargeback countries for user
 *     tags: [Statistics]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Maximum number of countries to return
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by specific store
 *       - in: query
 *         name: provider
 *         schema:
 *           type: string
 *           enum: [shopify, stripe]
 *         description: Filter by payment provider
 *     responses:
 *       200:
 *         description: Top chargeback countries retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Top chargeback countries retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ChargebackCountryStats'
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Failed to retrieve chargeback countries
 */
router.get("/:id/countries", statisticsController.getTopChargebackCountries);

/**
 * @swagger
 * /api/statistics/{id}/reasons:
 *   get:
 *     summary: Get top chargeback reasons for user
 *     tags: [Statistics]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Maximum number of reasons to return
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by specific store
 *       - in: query
 *         name: provider
 *         schema:
 *           type: string
 *           enum: [shopify, stripe]
 *         description: Filter by payment provider
 *     responses:
 *       200:
 *         description: Top chargeback reasons retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Top chargeback reasons retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ChargebackReasonStats'
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Failed to retrieve chargeback reasons
 */
router.get("/:id/reasons", statisticsController.getTopChargebackReasons);

/**
 * @swagger
 * /api/statistics/{id}/overview:
 *   get:
 *     summary: Get combined chargeback statistics overview
 *     tags: [Statistics]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Maximum number of items per category to return
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by specific store
 *       - in: query
 *         name: provider
 *         schema:
 *           type: string
 *           enum: [shopify, stripe]
 *         description: Filter by payment provider
 *     responses:
 *       200:
 *         description: Chargeback statistics overview retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Statistics overview retrieved successfully"
 *                 data:
 *                   $ref: '#/components/schemas/ChargebackStatsOverview'
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Failed to retrieve chargeback statistics overview
 */
router.get("/:id/overview", statisticsController.getChargebackStatsOverview);

/**
 * @swagger
 * /api/statistics/{id}/reset:
 *   delete:
 *     summary: Reset statistics for the user
 *     description: Deletes all chargeback statistics for the user (for testing purposes)
 *     tags: [Statistics]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Optional store ID to reset stats for specific store only
 *     responses:
 *       200:
 *         description: User statistics reset successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Statistics reset successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     deletedCountries:
 *                       type: number
 *                       example: 15
 *                     deletedReasons:
 *                       type: number
 *                       example: 8
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Failed to reset statistics
 */
router.delete("/:id/reset", statisticsController.resetUserStatistics);

export default router;
