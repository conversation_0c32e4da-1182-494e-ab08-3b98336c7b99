import { Router } from 'express';
import { Request, Response } from 'express';
import crypto from 'crypto';
import { env } from '@/config/environment';
import logger from '@/utils/logger.util';
import ShopifyService from '@/services/shopify.service';
import { usageTrackingService } from '@/services/usage-tracking.service';
import StoreBalanceService from '@/services/store-balance.service';
import PrismaService from '@/services/prisma.service';

const router = Router();
const prisma = PrismaService.getInstance().getClient();
const shopifyService = new ShopifyService();

/**
 * Verify Shopify webhook signature
 * tmp bypass all
 */
function verifyWebhook(rawBody: string, signature: string): boolean {
  const hmac = crypto
    .createHmac('sha256', env.SHOPIFY_API_SECRET || '')
    .update(rawBody, 'utf8')
    .digest('base64');

  return true;
  
  // return hmac === signature;
}

/**
 * POST /api/shopify-webhooks/app_purchases/one_time_update
 * Handle app purchase updates (charge accepted, declined, etc.)
 */
router.post('/app_purchases/one_time_update', async (req: Request, res: Response) => {
  try {
    const signature = req.get('X-Shopify-Hmac-Sha256');
    const rawBody = JSON.stringify(req.body);
    
    if (!signature || !verifyWebhook(rawBody, signature)) {
      logger.warn('Invalid webhook signature for app_purchases/one_time_update');
      return res.status(401).send('Unauthorized');
    }
    
    const data = req.body;
    logger.info('Received app purchase update webhook:', {
      id: data.id,
      status: data.status,
      shop: data.shop_domain
    });
    
    // Handle different charge statuses
    switch (data.status) {
      case 'accepted':
        // User accepted the charge - activate it
        const linkedStore = await (prisma as any).linkedStore.findFirst({
          where: {
            provider: 'shopify',
            data: {
              path: '$.domain',
              equals: data.shop_domain
            }
          }
        });
        
        if (linkedStore) {
          const storeData = linkedStore.data as any;
          await shopifyService.activateAppCharge(
            data.shop_domain,
            storeData.accessToken,
            data.id
          );
          
          logger.info(`Activated app charge ${data.id} for shop ${data.shop_domain}`);
        }
        break;
        
      case 'declined':
        logger.warn(`App charge ${data.id} was declined by shop ${data.shop_domain}`);
        break;
        
      case 'expired':
        logger.warn(`App charge ${data.id} expired for shop ${data.shop_domain}`);
        break;
    }
    
    res.status(200).send('OK');
  } catch (error: any) {
    logger.error('Error processing app purchase webhook:', error);
    res.status(500).send('Internal Server Error');
  }
});

/**
 * POST /api/shopify-webhooks/app_subscriptions/update
 * Handle subscription updates (for future recurring billing if needed)
 */
router.post('/app_subscriptions/update', async (req: Request, res: Response) => {
  try {
    const signature = req.get('X-Shopify-Hmac-Sha256');
    const rawBody = JSON.stringify(req.body);
    
    if (!signature || !verifyWebhook(rawBody, signature)) {
      logger.warn('Invalid webhook signature for app_subscriptions/update');
      return res.status(401).send('Unauthorized');
    }
    
    const data = req.body;
    logger.info('Received app subscription update webhook:', {
      id: data.id,
      status: data.status,
      shop: data.shop_domain
    });
    
    // Handle subscription status changes
    // Currently we use one-time charges, but this is here for future use
    
    res.status(200).send('OK');
  } catch (error: any) {
    logger.error('Error processing app subscription webhook:', error);
    res.status(500).send('Internal Server Error');
  }
});

/**
 * POST /api/shopify-webhooks/app/uninstalled
 * Handle app uninstallation webhook
 */
router.post('/app/uninstalled', async (req: Request, res: Response) => {
  try {
    const signature = req.get('X-Shopify-Hmac-Sha256');
    const rawBody = JSON.stringify(req.body);
    
    if (!signature || !verifyWebhook(rawBody, signature)) {
      logger.warn('Invalid webhook signature for app/uninstalled');
      return res.status(401).send('Unauthorized');
    }
    
    const { domain } = req.body;
    logger.info(`Received app uninstalled webhook for shop ${domain}`);
    
    // Find the linked store by domain/shop name
    const linkedStore = await (prisma as any).linkedStore.findFirst({
      where: {
        provider: 'shopify',
        OR: [
          { storeName: { contains: domain } },
          { data: { path: ['domain'], equals: domain } },
          { data: { path: ['shop', 'domain'], equals: domain } },
          { data: { path: ['shop', 'myshopify_domain'], equals: domain } }
        ]
      },
      include: {
        user: true
      }
    });
    
    if (linkedStore) {
      const uninstalledAt = new Date();
      
      // Mark linked store as inactive
      await (prisma as any).linkedStore.update({
        where: { id: linkedStore.id },
        data: {
          isActive: false,
          uninstalledAt: uninstalledAt
        }
      });
      
      // Mark user as inactive (only if they have no other active stores)
      const userActiveStores = await (prisma as any).linkedStore.count({
        where: {
          userId: linkedStore.userId,
          isActive: true,
          id: { not: linkedStore.id } // Exclude the store we just deactivated
        }
      });
      
      if (userActiveStores === 0) {
        await (prisma as any).user.update({
          where: { id: linkedStore.userId },
          data: {
            isActive: false,
            uninstalledAt: uninstalledAt
          }
        });
        
        // Clean up user tokens
        await (prisma as any).token.deleteMany({
          where: { userId: linkedStore.userId }
        });
        
        logger.info(`User ${linkedStore.userId} marked as inactive (no active stores remaining)`);
      }
      
      logger.info(`Store ${linkedStore.id} marked as inactive due to app uninstallation from ${domain}`);
    } else {
      logger.warn(`No linked store found for uninstalled shop: ${domain}`);
    }
    
    res.status(200).send('OK');
  } catch (error: any) {
    logger.error('Error processing app uninstalled webhook:', error);
    res.status(500).send('Internal Server Error');
  }
});

/**
 * POST /api/shopify-webhooks/shop/redact
 * Handle GDPR shop data redaction requests
 */
router.post('/shop/redact', async (req: Request, res: Response) => {
  try {
    const signature = req.get('X-Shopify-Hmac-Sha256');
    const rawBody = JSON.stringify(req.body);
    
    if (!signature || !verifyWebhook(rawBody, signature)) {
      logger.warn('Invalid webhook signature for shop/redact');
      return res.status(401).send('Unauthorized');
    }
    
    const { shop_domain } = req.body;
    logger.info(`Received shop redact request for ${shop_domain}`);
    
    // TODO: Implement data redaction logic
    // This would involve removing all shop data from the database
    // while maintaining audit logs as required by regulations
    
    res.status(200).send('OK');
  } catch (error: any) {
    logger.error('Error processing shop redact webhook:', error);
    res.status(500).send('Internal Server Error');
  }
});

/**
 * POST /api/shopify-webhooks/customers/redact
 * Handle GDPR customer data redaction requests
 */
router.post('/customers/redact', async (req: Request, res: Response) => {
  try {
    const signature = req.get('X-Shopify-Hmac-Sha256');
    const rawBody = JSON.stringify(req.body);
    
    if (!signature || !verifyWebhook(rawBody, signature)) {
      logger.warn('Invalid webhook signature for customers/redact');
      return res.status(401).send('Unauthorized');
    }
    
    const { shop_domain, customer } = req.body;
    logger.info(`Received customer redact request for shop ${shop_domain}, customer ${customer.id}`);
    
    // TODO: Implement customer data redaction logic
    // This would involve removing/anonymizing customer data
    // while maintaining necessary records for legal compliance
    
    res.status(200).send('OK');
  } catch (error: any) {
    logger.error('Error processing customer redact webhook:', error);
    res.status(500).send('Internal Server Error');
  }
});

/**
 * POST /api/shopify-webhooks/customers/data_request
 * Handle GDPR customer data export requests
 */
router.post('/customers/data_request', async (req: Request, res: Response) => {
  try {
    const signature = req.get('X-Shopify-Hmac-Sha256');
    const rawBody = JSON.stringify(req.body);
    
    if (!signature || !verifyWebhook(rawBody, signature)) {
      logger.warn('Invalid webhook signature for customers/data_request');
      return res.status(401).send('Unauthorized');
    }
    
    const { shop_domain, customer } = req.body;
    logger.info(`Received customer data request for shop ${shop_domain}, customer ${customer.id}`);
    
    // TODO: Implement customer data export logic
    // This would involve gathering all customer data and
    // providing it in a format suitable for GDPR compliance
    
    res.status(200).send('OK');
  } catch (error: any) {
    logger.error('Error processing customer data request webhook:', error);
    res.status(500).send('Internal Server Error');
  }
});

export default router;