import { Router } from 'express';
import { paypalController } from '../controllers/paypal.controller';

const router = Router();

// Routes without authMiddleware
router.get('/agreements', (req, res) => paypalController.getAgreements(req, res));
router.post('/agreement/create', (req, res) => paypalController.createAgreement(req, res));
router.post('/agreement/execute', (req, res) => paypalController.executeAgreement(req, res));
router.post('/agreement/charge', (req, res) => paypalController.processPayment(req, res));
router.delete('/agreement/:agreementId', (req, res) => paypalController.cancelAgreement(req, res));

export { router as paypalRoutes };