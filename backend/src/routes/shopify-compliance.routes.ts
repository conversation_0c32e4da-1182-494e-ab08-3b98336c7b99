import { Router } from "express";
import { json } from "express";
import ShopifyComplianceController from "../controllers/shopify-compliance.controller";

const router = Router();

// Use express.json() middleware with raw body capture for webhook verification
router.use(json({ 
  verify: (req: any, res, buf) => {
    req.rawBody = buf;
  }
}));

/**
 * Customer Data Request Endpoint
 * POST /shopify/compliance/customers/data_request
 * 
 * Required by Shopify for public apps to handle customer data requests
 * under GDPR and other privacy laws.
 */
router.post('/customers/data_request', ShopifyComplianceController.handleCustomerDataRequest);

/**
 * Customer Data Erasure Endpoint  
 * POST /shopify/compliance/customers/redact
 * 
 * Required by Shopify for public apps to handle customer data erasure
 * requests under GDPR "right to be forgotten".
 */
router.post('/customers/redact', ShopifyComplianceController.handleCustomerDataErasure);

/**
 * Shop Data Erasure Endpoint
 * POST /shopify/compliance/shop/redact
 * 
 * Required by Shopify for public apps to handle shop data erasure
 * when a shop uninstalls the app permanently.
 */
router.post('/shop/redact', ShopifyComplianceController.handleShopDataErasure);

export default router; 