import { Router } from 'express';
import { StripeStoreController } from '../../controllers/stripe/store.controller';

const router = Router();
const stripeStoreController = new StripeStoreController();

// GET /stripe/stores/:id - Get store by ID
router.get('/:id', stripeStoreController.getStoreById.bind(stripeStoreController));

// GET /stripe/stores/:id/refresh - Refresh store details
router.get('/:id/refresh', stripeStoreController.refreshStoreDetails.bind(stripeStoreController));

// DELETE /stripe/stores/:id - Disconnect store
router.delete('/:id', stripeStoreController.disconnectStore.bind(stripeStoreController));

export default router;