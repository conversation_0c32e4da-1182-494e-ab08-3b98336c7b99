import { Router } from "express";
import StripeOAuthController from "../../controllers/stripe/oauth.controller";

export const router: Router = Router();
const stripeOAuthController = new StripeOAuthController();

// Get OAuth URL for Stripe Connect
router.get(
  "/url",
  stripeOAuthController.getOAuthUrl.bind(stripeOAuthController)
);

// Handle OAuth callback from Stripe App Platform
router.get(
  "/callback",
  stripeOAuthController.handleCallback.bind(stripeOAuthController)
);

// Disconnect Stripe integration
router.delete(
  "/disconnect/:storeId",
  stripeOAuthController.disconnect.bind(stripeOAuthController)
);

export default router;