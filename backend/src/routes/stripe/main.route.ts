import { Router } from 'express';
import StripeController from '../../controllers/stripe.controller';

const router = Router();

// ===== Stripe Account Routes =====

// POST /stripe/accounts - Create a new Stripe account
router.post('/accounts', StripeController.createStripeAccount);


// GET /stripe/accounts - Get all Stripe accounts
router.get('/accounts', StripeController.getAllStripeAccounts);

// GET /stripe/accounts/:id - Get Stripe account by ID
router.get('/accounts/:id', StripeController.getStripeAccount);

// GET /stripe/accounts/store/:storeId - Get Stripe account by store ID
router.get('/accounts/store/:storeId', StripeController.getStripeAccountByStoreId);

// GET /stripe/info/store/:storeId - Get comprehensive Stripe information by store ID
router.get('/info/store/:storeId', StripeController.getStripeInfoByStoreId);

// PATCH /stripe/accounts/:id - Update a Stripe account
router.patch('/accounts/:id', StripeController.updateStripeAccount);

// DELETE /stripe/accounts/:id - Delete a Stripe account
router.delete('/accounts/:id', StripeController.deleteStripeAccount);

// GET /stripe/accounts/:id/onboarding - Generate onboarding link for Stripe account
router.get('/accounts/:id/onboarding', StripeController.generateOnboardingLink);

// GET /stripe/accounts/:id/details - Get Stripe account details
router.get('/accounts/:id/details', StripeController.getStripeAccountDetails);

// POST /stripe/accounts/:id/sync - Sync Stripe account status
router.post('/accounts/:id/sync', StripeController.syncAccountStatus);

// GET /stripe/accounts/:id/dashboard - Create dashboard link for Stripe account
router.get('/accounts/:id/dashboard', StripeController.createDashboardLink);

// GET /stripe/accounts/:id/requirements - Get account requirements
router.get('/accounts/:id/requirements', StripeController.getAccountRequirements);


// ===== Card Setup Routes =====

// POST /stripe/setup-intent - Create setup intent for card collection
router.post('/setup-intent', StripeController.createSetupIntent);

// POST /stripe/confirm-setup - Confirm card setup and save payment method
router.post('/confirm-setup', StripeController.confirmCardSetup);

// GET /stripe/card-status/:storeId - Get card setup status for a store
router.get('/card-status/:storeId', StripeController.getCardSetupStatus);



export default router;