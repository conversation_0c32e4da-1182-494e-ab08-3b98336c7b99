import { Router } from "express";
import MatchedBlocksController from "@/controllers/matched-blocks.controller";
import { publicRoute } from "@/middlewares/auth.middleware";

export const router: Router = Router();

// Apply API key validation first, then provider authentication for user-specific routes
router.use(publicRoute);

// Get chargeback rate chart data for matched blocks
router.get(
  "/chart/chargeback-rate/user/:userId",
  MatchedBlocksController.getChargebackRateChart
);

// Get matched blocks by user ID (main endpoint for simplified block retrieval)
router.get("/user/:userId", MatchedBlocksController.getBlocksByUserId);
