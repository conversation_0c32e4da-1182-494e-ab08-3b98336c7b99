import { Router } from "express";
import ShopifySyncController from "../controllers/shopify-sync.controller";
import { userRoute } from "../middlewares/auth.middleware";

const router = Router();
const shopifySyncController = new ShopifySyncController();

router.post(
  "/stores/:storeId/sync",
  userRoute,
  shopifySyncController.triggerStoreSync.bind(shopifySyncController)
);

router.post(
  "/bulk-sync",
  userRoute,
  shopifySyncController.triggerBulkSync.bind(shopifySyncController)
);

router.get(
  "/status",
  userRoute,
  shopifySyncController.getSyncStatus.bind(shopifySyncController)
);

router.get(
  "/jobs/:jobId",
  userRoute,
  shopifySyncController.getJobDetails.bind(shopifySyncController)
);

router.post(
  "/jobs/clear",
  userRoute,
  shopifySyncController.clearJobs.bind(shopifySyncController)
);

export default router;