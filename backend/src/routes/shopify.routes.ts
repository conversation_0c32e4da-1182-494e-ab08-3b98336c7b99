import { Router } from "express";
import ShopifyController from "../controllers/shopify.controller";
import ShopifyOrderController from "../controllers/shopify-order.controller";
import ShopifyTransactionController from "../controllers/shopify-transaction.controller";
import ShopifyPayoutController from "../controllers/shopify-payout.controller";
import ShopifyDisputeController from "../controllers/shopify-dispute.controller";
import { userRoute, storeRoute, storeBillingRoute } from "../middlewares/auth.middleware";

export const router: Router = Router();
const shopifyController = new ShopifyController();

// Route for starting the OAuth flow
router.get(
  "/auth/url",
  shopifyController.generateAuthUrl.bind(shopifyController)
);

// Callback route for OAuth
router.get(
  "/auth/callback",
  shopifyController.handleCallback.bind(shopifyController)
);

// Callback route for OAuth with userId (POST only for security)
router.post(
  "/callback-with-user",
  shopifyController.handleCallbackWithUserId.bind(shopifyController)
);

// Connect store with access token
router.post(
  "/connect",
  userRoute,
  shopifyController.connectWithToken.bind(shopifyController)
);

// Get user's stores
router.get(
  "/stores",
  userRoute,
  shopifyController.getUserStores.bind(shopifyController)
);

// Get store by ID
router.get(
  "/stores/:id",
  // userRoute,
  shopifyController.getStoreById.bind(shopifyController)
);

// Delete a linked store
router.delete(
  "/stores/:id",
  // userRoute,
  shopifyController.deleteLinkedStore.bind(shopifyController)
);

// Refresh store details
router.get(
  "/stores/:id/refresh",
  // userRoute,
  shopifyController.refreshStoreDetails.bind(shopifyController)
);

// Get order transactions
router.get(
  "/stores/:storeId/orders/:orderId/transactions",
  // userRoute,
  shopifyController.getOrderTransactions.bind(shopifyController)
);

router.post(
  "/stores/:storeId/find-related-orders",
  // userRoute,
  shopifyController.findRelatedOrders.bind(shopifyController)
);

// Refund order
router.post(
  "/stores/:storeId/orders/:orderId/refunds",
  shopifyController.createRefund.bind(shopifyController)
);

// Calculate refund
router.post(
  "/stores/:storeId/orders/:orderId/refunds/calculate",
  shopifyController.calculateRefund.bind(shopifyController)
);

// Cancel order
router.post(
  "/stores/:storeId/orders/:orderId/cancel",
  shopifyController.cancelOrder.bind(shopifyController)
);

// =============================================================
// Shopify Orders Routes
// =============================================================

// Get all orders with optional filtering (query parameters)
router.get(
  "/orders",
  userRoute,
  ShopifyOrderController.getAllOrders
);

// Get orders for a specific store
router.get(
  "/orders/store/:linkedStoreId",
  userRoute,
  ShopifyOrderController.getOrdersByLinkedStoreId
);

// Get a specific order by ID
router.get(
  "/orders/:id",
  userRoute,
  ShopifyOrderController.getOrderById
);

// Create a new order
router.post(
  "/orders",
  userRoute,
  ShopifyOrderController.createOrder
);

// Create multiple orders
router.post(
  "/orders/bulk",
  userRoute,
  ShopifyOrderController.createManyOrders
);

// Update an existing order
router.put(
  "/orders/:id",
  userRoute,
  ShopifyOrderController.updateOrder
);

// Delete an order
router.delete(
  "/orders/:id",
  userRoute,
  ShopifyOrderController.deleteOrder
);

// =============================================================
// Shopify Transactions Routes
// =============================================================

// Get all transactions with optional filtering (query parameters)
router.get(
  "/transactions",
  userRoute,
  ShopifyTransactionController.getAllTransactions
);

// Get transactions for a specific store
router.get(
  "/transactions/store/:linkedStoreId",
  userRoute,
  ShopifyTransactionController.getTransactionsByLinkedStoreId
);

// Get transactions for a specific order
router.get(
  "/transactions/order/:orderId",
  userRoute,
  ShopifyTransactionController.getTransactionsByOrderId
);

// Get transactions for a specific payout
router.get(
  "/transactions/payout/:payoutId",
  userRoute,
  ShopifyTransactionController.getTransactionsByPayoutId
);

// Get a specific transaction by ID
router.get(
  "/transactions/:id",
  userRoute,
  ShopifyTransactionController.getTransactionById
);

// Create a new transaction
router.post(
  "/transactions",
  userRoute,
  ShopifyTransactionController.createTransaction
);

// Create multiple transactions
router.post(
  "/transactions/bulk",
  userRoute,
  ShopifyTransactionController.createManyTransactions
);

// Update an existing transaction
router.put(
  "/transactions/:id",
  userRoute,
  ShopifyTransactionController.updateTransaction
);

// Delete a transaction
router.delete(
  "/transactions/:id",
  userRoute,
  ShopifyTransactionController.deleteTransaction
);

// =============================================================
// Shopify Payouts Routes
// =============================================================

// Get all payouts with optional filtering (query parameters)
router.get(
  "/payouts",
  userRoute,
  ShopifyPayoutController.getAllPayouts
);

// Get payouts for a specific store
router.get(
  "/payouts/store/:linkedStoreId",
  userRoute,
  ShopifyPayoutController.getPayoutsByLinkedStoreId
);

// Get a specific payout by ID
router.get(
  "/payouts/:id",
  userRoute,
  ShopifyPayoutController.getPayoutById
);

// Create a new payout
router.post(
  "/payouts",
  userRoute,
  ShopifyPayoutController.createPayout
);

// Create multiple payouts
router.post(
  "/payouts/bulk",
  userRoute,
  ShopifyPayoutController.createManyPayouts
);

// Update an existing payout
router.put(
  "/payouts/:id",
  userRoute,
  ShopifyPayoutController.updatePayout
);

// Delete a payout
router.delete(
  "/payouts/:id",
  userRoute,
  ShopifyPayoutController.deletePayout
);

// =============================================================
// Shopify Disputes Routes
// =============================================================

// Get all disputes with optional filtering (query parameters)
router.get(
  "/disputes",
  userRoute,
  ShopifyDisputeController.getAllDisputes
);

// Get disputes for a specific store
router.get(
  "/disputes/store/:linkedStoreId",
  userRoute,
  ShopifyDisputeController.getDisputesByLinkedStoreId
);

// Get disputes for a specific transaction
router.get(
  "/disputes/transaction/:transactionId",
  userRoute,
  ShopifyDisputeController.getDisputesByTransactionId
);

// Get a specific dispute by ID
router.get(
  "/disputes/:id",
  userRoute,
  ShopifyDisputeController.getDisputeById
);

// Create a new dispute
router.post(
  "/disputes",
  userRoute,
  ShopifyDisputeController.createDispute
);

// Create multiple disputes
router.post(
  "/disputes/bulk",
  userRoute,
  ShopifyDisputeController.createManyDisputes
);

// Update an existing dispute
router.put(
  "/disputes/:id",
  userRoute,
  ShopifyDisputeController.updateDispute
);

// Delete a dispute
router.delete(
  "/disputes/:id",
  userRoute,
  ShopifyDisputeController.deleteDispute
);
