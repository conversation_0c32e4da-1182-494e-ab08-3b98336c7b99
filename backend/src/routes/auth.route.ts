import { Router } from "express";
import AuthController from "@/controllers/auth.controller";
import ValidationMiddleware from "@/middlewares/validation.middleware";
import {
  adminRoute,
  publicRoute,
  userRoute,
} from "@/middlewares/auth.middleware";
export const router: Router = Router();

// Public routes
router.post(
  "/register",
  ValidationMiddleware.register,
  AuthController.register
);
router.post("/login", AuthController.login);
router.post("/token", AuthController.getAccessToken);

// User routes
router.post("/logout", userRoute, AuthController.logout);

// Admin routes
router.post(
  "/create",
  adminRoute,
  ValidationMiddleware.create,
  AuthController.create
);
