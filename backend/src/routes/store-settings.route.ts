import { Router } from "express";
import StoreSettingsController from "../controllers/store-settings.controller";
import { publicRoute } from "../middlewares/auth.middleware";
import { providerAuthRoute } from "../middlewares/provider-auth.middleware";

const router = Router();
const storeSettingsController = new StoreSettingsController();

// Apply API key validation first, then provider authentication for user-specific routes
router.use(publicRoute);
router.use(providerAuthRoute);

// Routes for multiple settings
router.get("/:storeId", storeSettingsController.getByStoreId);
router.put("/:storeId", storeSettingsController.updateSettings);
router.delete("/:storeId", storeSettingsController.deleteAllSettings);

// Routes for individual settings
router.get("/:storeId/:name", storeSettingsController.getSetting);
router.post("/:storeId/:name", storeSettingsController.upsertSetting);
router.put("/:storeId/:name", storeSettingsController.upsertSetting);
router.delete("/:storeId/:name", storeSettingsController.deleteSetting);

export default router; 