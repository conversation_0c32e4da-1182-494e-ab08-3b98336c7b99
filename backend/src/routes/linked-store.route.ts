import { Router } from "express";
import LinkedStoreController from "../controllers/linked-store.controller";

export const router: Router = Router();
const linkedStoreController = new LinkedStoreController();

// Get all linked stores for a specific user by ID (all providers)
router.get(
  "/user/:id",
  linkedStoreController.getLinkedStoresByUserId.bind(linkedStoreController)
);

export default router;