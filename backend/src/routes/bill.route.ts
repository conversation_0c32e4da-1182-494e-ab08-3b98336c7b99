import { Router } from 'express';
import { publicRoute } from '../middlewares/auth.middleware';
import { providerAuthRoute } from '../middlewares/provider-auth.middleware';
import BillController from '../controllers/bill.controller';

const router = Router();

// Apply API key validation first, then provider authentication for user-specific routes
router.use(publicRoute);
router.use(providerAuthRoute);

// POST /bill - Create a new bill
router.post('/', BillController.createBill);

// GET /bill - Get all bills
router.get('/', BillController.getAllBills);

// GET /bill/store/:storeId - Get bills by store ID
router.get('/store/:storeId', BillController.getBillsByStoreId);

// GET /bill/:id - Get bill by ID
router.get('/:id', BillController.getBill);

// PATCH /bill/:id - Update a bill
router.patch('/:id', BillController.updateBill);

// DELETE /bill/:id - Delete a bill
router.delete('/:id', BillController.deleteBill);

// GET /bill/payment-link/:billId - Generate payment link for bill
router.get('/payment-link/:billId', BillController.generatePaymentLink);

// GET /bill/:billId/payment-histories - Get payment histories for a bill
router.get('/:billId/payment-histories', BillController.getBillPaymentHistories);

export default router;