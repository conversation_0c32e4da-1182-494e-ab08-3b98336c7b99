import { Router } from "express";
import { UsageTrackingController } from "../controllers/usage-tracking.controller";
import { publicRoute } from "../middlewares/auth.middleware";
import { providerAuthRoute } from "../middlewares/provider-auth.middleware";

const router = Router();

// Apply API key validation first, then provider authentication for user-specific routes
router.use(publicRoute);
router.use(providerAuthRoute);

// Current usage
router.get("/:storeId/current", UsageTrackingController.getCurrentUsage);

// Billing history
router.get("/:storeId/history", UsageTrackingController.getBillingHistory);

// Period details
router.get("/:storeId/period/:period", UsageTrackingController.getPeriodDetails);

// Plan info
router.get("/:storeId/plan", UsageTrackingController.getPlanInfo);

// Account balance
router.get("/:storeId/balance", UsageTrackingController.getAccountBalance);

export default router;