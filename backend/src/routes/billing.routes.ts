import { Router } from 'express';
import BillingController from '../controllers/billing.controller';
import { shopifyRoute } from '../middlewares/auth.middleware';

const router = Router();
const billingController = new BillingController();

// Public routes (no authentication required)
router.get('/plans', billingController.getBillingPlans.bind(billingController));

// Shopify callback routes (no authentication required)
router.get('/callback', billingController.handleBillingCallback.bind(billingController));
router.post('/webhook', billingController.handleBillingWebhook.bind(billingController));

// Protected routes (API key + Shopify token authentication required)
router.post('/create', shopifyRoute, billingController.createSubscription.bind(billingController));
router.get('/status/:linkedStoreId', shopifyRoute, billingController.getSubscriptionStatus.bind(billingController));
router.post('/cancel/:linkedStoreId', shopifyRoute, billingController.cancelSubscription.bind(billingController));
router.post('/usage', shopifyRoute, billingController.createUsageRecord.bind(billingController));

export default router; 