import { StatusCodes } from "http-status-codes";
import { getI18n } from "@/middlewares/language.middleware";
import PrismaService from "./prisma.service";
import { ServiceResponse } from "@/constants/type";
import { IBlock, IEthocaBlock, IRdrBlock, BlockType, IBlockFeedback, FeedbackStatus, blockSchema } from "@/models/block.model";
import logger from "@/utils/logger.util";
import { sign } from "@/utils/signature.utils";
import { env } from "@/config/environment";
import axios from "axios";
import { Prisma } from "@prisma/client";
import { subDays, startOfDay, endOfDay, startOfMonth, startOfYear } from 'date-fns';
import redisClient from "@/config/redis";
import { RedisPrefix } from "@/constants/prefix";
import StoreBalanceService from "./store-balance.service";
import CacheService from "./cache.service";
import ShopifyService from "./shopify.service";
import StoreSettingsService from "./store-settings.service";
enum TimeRange {
  TODAY = 'today',
  YESTERDAY = 'yesterday',
  THIS_MONTH = 'month',
  THIS_YEAR = 'year',
  CUSTOM = 'custom',
}


const prisma = PrismaService.getInstance().getClient();

// In-memory lock to prevent race conditions for saveMatchedOrderResult
const saveMatchedOrderLocks = new Set<string>();

const saveMappedBlocks = async (mappedBlocks: Array<{
  blockId: string;
  linkedStoreId: string;
  orderId?: bigint | null;
  transactionId?: bigint | null;
}>): Promise<void> => {
  try {
    const mappedBlockPromises = mappedBlocks.map(async (mapping) => {
      if (!mapping.orderId || !mapping.transactionId) {
        logger.warn(`Skipping mapping with missing orderId or transactionId:`, mapping);
        return;
      }

      try {
        await prisma.mappedBlock.upsert({
          where: {
            blockId_linkedStoreId_orderId_transactionId: {
              blockId: mapping.blockId,
              linkedStoreId: mapping.linkedStoreId,
              orderId: mapping.orderId,
              transactionId: mapping.transactionId,
            }
          },
          update: {},
          create: mapping
        });
      } catch (error: any) {
        if (error.code === 'P2002') {
          logger.info(`Duplicate mappedBlock ignored for block ${mapping.blockId}, transaction ${mapping.transactionId}`);
        } else {
          logger.error(`Error upserting mappedBlock:`, error);
          throw error;
        }
      }
    });

    await Promise.all(mappedBlockPromises.filter(Boolean));
    logger.info(`Successfully processed ${mappedBlocks.length} mapped blocks`);
  } catch (error: any) {
    logger.error("Error saving mapped blocks:", error.message);
    throw error;
  }
};

// Function to save findMatchedOrder result asynchronously with race condition protection
const saveFindMatchedOrderResult = async (
  blockId: string,
  linkedStoreId: string,
  finalMatchedTransactions: any[],
): Promise<void> => {
  const lockKey = `${blockId}_${linkedStoreId}`;

  // Check if already processing this block+store combination
  if (saveMatchedOrderLocks.has(lockKey)) {
    logger.info(`Save operation already in progress for block ${blockId} and store ${linkedStoreId}, skipping`);
    return;
  }

  // Acquire lock
  saveMatchedOrderLocks.add(lockKey);

  try {
    logger.info(`Starting to save find matched order result for block ${blockId}`);

    // Prepare mapped blocks data
    const mappedBlocksToSave = finalMatchedTransactions.map(transaction => ({
      blockId: blockId,
      linkedStoreId: linkedStoreId,
      orderId: transaction.orderId ? BigInt(transaction.orderId) : null,
      transactionId: BigInt(transaction.id),
    }));

    // Remove duplicates within the same batch using a Map
    const uniqueMappings = new Map();
    mappedBlocksToSave.forEach(mapping => {
      const key = `${mapping.blockId}_${mapping.linkedStoreId}_${mapping.orderId}_${mapping.transactionId}`;
      if (!uniqueMappings.has(key)) {
        uniqueMappings.set(key, mapping);
      }
    });

    const uniqueMappedBlocksToSave = Array.from(uniqueMappings.values());

    if (uniqueMappedBlocksToSave.length !== mappedBlocksToSave.length) {
      logger.warn(`Removed ${mappedBlocksToSave.length - uniqueMappedBlocksToSave.length} duplicate mappings in batch for block ${blockId}`);
    }

    if (uniqueMappedBlocksToSave.length > 0) {
      await saveMappedBlocks(uniqueMappedBlocksToSave);
      logger.info(`Successfully saved ${uniqueMappedBlocksToSave.length} unique mapped blocks for block ${blockId}`);
    }

    logger.info(`Successfully completed saving find matched order result for block ${blockId}`);

  } catch (error: any) {
    logger.error(`Error in saveFindMatchedOrderResult for block ${blockId}:`, error.message);
  } finally {
    // Always release lock
    saveMatchedOrderLocks.delete(lockKey);
  }
};

// Block service interfaces
interface EthocaBlock {
  id: string;
  alertId: string;
  age: string;
  alertTime: string;
  alertType: string;
  amount: string;
  currency: string;
  descriptor: string;
  alertSource: string;
  arn: string;
  authCode: string;
  cardBin: string;
  cardNumber: string;
  chargebackCode: string;
  disputeAmount: string;
  disputeCurrency: string;
  initiatedBy: string;
  issuer: string;
  liability: string;
  merchantCategoryCode: string;
  transactionId: string;
  transactionTime: string;
  transactionType: string;
  [key: string]: any;
}

interface RdrBlock {
  id: string;
  alertId: string;
  alertTime: string;
  alertType: string;
  amount: string;
  currency: string;
  descriptor: string;
  acquirerBin: string;
  acquirerReferenceNumber: string;
  alertSource: string;
  alertStatus: string;
  authCode: string;
  caid: string;
  cardBin: string;
  cardNumber: string;
  chargebackCode: string;
  descriptorContact: string;
  disputeAmount: string;
  disputeCurrency: string;
  merchantCategoryCode: string;
  ruleName: string;
  ruleType: string;
  transactionTime: string;
  [key: string]: any;
}

// Type definition for viewMode
type ViewMode = "dashboard" | "count" | "volume";

interface EarlyWarningConfig {
  merchantNo: string;
  signKey: string;
  apiEndpoint: string;
}

class BlockService {
  /**
   * Get all_time_goal setting with default value of 1
   */
  private static async getAllTimeGoalSetting(storeId: string): Promise<{ value: string }> {
    try {
      const storeSettingsService = new StoreSettingsService();
      const setting = await storeSettingsService.getSetting(storeId, "all_time_goal");
      
      if (!setting) {
        // Return default value for all_time_goal if not found
        return { value: "1" };
      }
      
      return setting;
    } catch (error: any) {
      logger.error("Error getting all_time_goal setting:", error);
      // Return default value on error
      return { value: "1" };
    }
  }

  /**
   * Clear all block-related cache entries
   */
  private static async clearBlockCache(): Promise<void> {
    try {
      // Clear all block-related cache patterns using CacheService
      await Promise.all([
        CacheService.deletePattern('blocks:*'),
        CacheService.deletePattern('dashboard:chart:*'),
        CacheService.deletePattern('dashboard:metrics:*')
      ]);

      logger.info('Cleared all block-related cache');
    } catch (error: any) {
      logger.error("Error clearing block cache:", error.message);
    }
  }

  /**
   * Clear specific cache by prefix and filters
   */
  static async clearSpecificCache(prefix: string, filters?: Record<string, any>): Promise<void> {
    try {
      let pattern = `${prefix}_*`;
      if (filters) {
        const cacheKey = `${prefix}_${JSON.stringify(filters)}`;
        await redisClient.del(cacheKey);
        logger.info(`Cleared specific cache: ${cacheKey}`);
      } else {
        const keys = await redisClient.keys(pattern);
        if (keys.length > 0) {
          await redisClient.del(keys);
          logger.info(`Cleared ${keys.length} cache entries for pattern: ${pattern}`);
        }
      }
    } catch (error: any) {
      logger.error("Error clearing specific cache:", error.message);
    }
  }

  private static config: EarlyWarningConfig = {
    merchantNo: env.EARLY_WARNING_MERCHANT_NO || "",
    signKey: env.EARLY_WARNING_SIGN_KEY || "",
    apiEndpoint:
      env.EARLY_WARNING_API_ENDPOINT || "https://mer.tradefensor.com",
  };

  /**
   * Create a new block
   */
  static createBlock = async (blockData: Partial<IBlock>): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Validate block data
      const { error, value } = blockSchema.validate(blockData);
      if (error) {
        return {
          success: false,
          status: StatusCodes.BAD_REQUEST,
          message: error.message,
        };
      }

      // Check if block already exists
      const existingBlock = await prisma.block.findUnique({
        where: { id: blockData.id as string },
      });

      if (existingBlock) {
        return {
          success: false,
          status: StatusCodes.CONFLICT,
          message: i18n.t("error.block.alreadyExists") || "Block already exists",
        };
      }

      // Create block using Prisma
      const block = await prisma.block.create({
        data: value,
      });

      logger.info("Block created:", { block });

      // Clear cache when new block is created
      await this.clearBlockCache();

      return {
        success: true,
        status: StatusCodes.CREATED,
        message: i18n.t("success.block.created") || "Block created successfully",
        data: block,
      };
    } catch (error: any) {
      logger.error("Error creating block:", error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.block.creation") || "Failed to create block",
      };
    }
  };

  /**
   * Get a block by ID
   */
  static getBlock = async (id: string): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      const block = await prisma.block.findUnique({
        where: { id },
      });

      if (!block) {
        return {
          success: false,
          status: StatusCodes.NOT_FOUND,
          message: i18n.t("error.block.notFound") || "Block not found",
          data: undefined,
        };
      }

      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t("success.block.retrieved") || "Block retrieved successfully",
        data: block,
      };
    } catch (error: any) {
      logger.error("Error retrieving block:", error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.block.retrieval") || "Failed to retrieve block",
        data: undefined,
      };
    }
  };

  /**
   * Update a block
   */
  static updateBlock = async (id: string, updateData: Partial<IBlock>): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Check if block exists
      const blockResult = await this.getBlock(id);
      if (!blockResult.success) {
        return blockResult;
      }

      // Update block using Prisma
      const updatedBlock = await prisma.block.update({
        where: { id },
        data: updateData,
      });

      // Clear cache when block is updated
      await this.clearBlockCache();

      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t("success.block.updated") || "Block updated successfully",
        data: updatedBlock,
      };
    } catch (error: any) {
      logger.error("Error updating block:", error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.block.update") || "Failed to update block",
        data: undefined,
      };
    }
  };

  /**
   * Delete a block
   */
  static deleteBlock = async (id: string): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Check if block exists
      const blockResult = await this.getBlock(id);
      if (!blockResult.success) {
        return blockResult;
      }

      // Delete block using Prisma
      await prisma.block.delete({
        where: { id },
      });

      // Clear cache when block is deleted
      await this.clearBlockCache();

      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t("success.block.deleted") || "Block deleted successfully",
        data: { id },
      };
    } catch (error: any) {
      logger.error("Error deleting block:", error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.block.deletion") || "Failed to delete block",
        data: undefined,
      };
    }
  };

  /**
   * Get blocks with pagination and filters
   * @param page - Current page number (default: 1)
   * @param limit - Number of items per page (default: 10)
   * @param filters - Filter criteria for blocks
   * @returns ServiceResponse with paginated blocks or grouped data
   */
  static getBlocks = async (
    page: number = 1,
    limit: number = 10,
    filters: Record<string, any> = {}
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Create cache key from filters and pagination
      const cacheKey = `blocks:${JSON.stringify({ page, limit, filters })}`;

      // Check cache first
      const cached = await CacheService.get<ServiceResponse<any>>(cacheKey);
      if (cached) {
        return cached;
      }

      // Build optimized where conditions for Prisma
      const where = this.buildWhereConditions(filters);

      // Handle data grouping if requested
      const groupByConfig = this.buildGroupByConditions(filters);
      if (groupByConfig) {
        return await this.getGroupedBlocks(where, groupByConfig);
      }

      // Use transaction for efficient querying
      // Get total count and data with a single transaction if no grouping
      const [blocks, total] = await Promise.all([
        prisma.block.findMany({
          where,
          orderBy: { alertTime: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
          // Add select to optimize by retrieving only needed fields
          // Uncomment and modify if needed for specific use cases
          // select: {
          //   id: true,
          //   alertId: true,
          //   alertTime: true,
          //   // ... other needed fields
          // }
        }),
        prisma.block.count({ where })
      ]);

      const totalPages = Math.ceil(total / limit);

      const result = {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t("success.block.retrieved") || "Blocks retrieved successfully",
        data: {
          items: blocks,
          pagination: {
            total,
            page,
            limit,
            totalPages,
          },
        },
      };

      // Cache the result for 2 minutes
      await CacheService.set(cacheKey, result, 2 * 60);

      return result;
    } catch (error: any) {
      logger.error("Error listing blocks:", error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.block.retrieval") || "Failed to retrieve blocks",
        data: undefined,
      };
    }
  };

  /**
   * Process Ethoca block
   * Handles and saves Ethoca block data
   */
  static processEthocaBlock = async (blockData: EthocaBlock): Promise<ServiceResponse<any>> => {
    // Map block data to the Block model structure
    const blockRecord: Partial<IEthocaBlock> = {
      id: blockData.id,
      alertId: blockData.alertId,
      alertTime: new Date(blockData.alertTime),
      alertType: blockData.alertType,
      amount: Math.round(parseFloat(String(blockData.amount)) * 100),
      currency: blockData.currency,
      descriptor: blockData.descriptor,
      authCode: blockData.authCode || null,
      cardBin: blockData.cardBin || null,
      cardNumber: blockData.cardNumber || null,
      chargebackCode: blockData.chargebackCode || null,
      disputeAmount: blockData.disputeAmount ? Math.round(parseFloat(String(blockData.disputeAmount)) * 100) : null,
      disputeCurrency: blockData.disputeCurrency || null,
      transactionTime: blockData.transactionTime ? new Date(blockData.transactionTime) : null,

      // ETHOCA required fields
      age: String(blockData.age),

      // ETHOCA specific fields
      alertSource: blockData.alertSource || null,
      arn: blockData.arn || null,
      issuer: blockData.issuer || null,
      initiatedBy: blockData.initiatedBy || null,
      liability: blockData.liability || null,
      merchantCategoryCode: blockData.merchantCategoryCode || null,
      transactionId: blockData.transactionId || null,
      transactionType: blockData.transactionType || null,

      // Set the type to ETHOCA
      type: BlockType.ETHOCA,

      // Set default feedback status
      feedbackStatus: FeedbackStatus.PENDING,
    };

    // Create the block first
    logger.info(`[ETHOCA] Creating block with ID: ${blockData.id}`);
    const blockResult = await this.createBlock(blockRecord);
    logger.info(`[ETHOCA] Block creation result: success=${blockResult.success}, status=${blockResult.status}, message=${blockResult.message}`);

    // Step 2: Process billing and usage tracking synchronously
    if (blockResult.success && blockData.descriptor) {
      logger.info(`[ETHOCA] Starting synchronous billing process for block ${blockData.id} with descriptor: ${blockData.descriptor}`);
      try {
        await this.processEthocaBlockBilling(
          blockData.id,
          blockData.descriptor
        );
        logger.info(`[ETHOCA] ✅ Synchronous billing process completed successfully for block ${blockData.id}`);
      } catch (error: any) {
        logger.error(`[ETHOCA] ❌ Synchronous billing process failed for block ${blockData.id}:`, {
          message: error.message,
          name: error.name,
          code: error.code,
          status: error.response?.status
        });
        // Don't throw error - we want to return success for block creation even if billing fails
      }
    } else if (blockResult.status === 409 && blockData.descriptor) { // 409 = CONFLICT = block already exists
      logger.info(`[ETHOCA] Block already exists, running billing process anyway for block ${blockData.id}`);
      try {
        await this.processEthocaBlockBilling(
          blockData.id,
          blockData.descriptor
        );
        logger.info(`[ETHOCA] ✅ Billing process completed for existing block ${blockData.id}`);
      } catch (error: any) {
        logger.error(`[ETHOCA] ❌ Billing process failed for existing block ${blockData.id}:`, {
          message: error.message,
          name: error.name,
          code: error.code,
          status: error.response?.status
        });
        // Don't throw error - we want to return success for block creation even if billing fails
      }
    } else {
      logger.warn(`[ETHOCA] ⚠️ Skipping billing: blockResult.success=${blockResult.success}, descriptor=${blockData.descriptor || 'null'}, message=${blockResult.message}`);
    }

    return blockResult;
  };

  /**
   * Process RDR block
   * Handles and saves RDR block data
   */
  static processRdrBlock = async (blockData: RdrBlock): Promise<ServiceResponse<any>> => {
    // Map block data to the Block model structure
    const blockRecord: Partial<IRdrBlock> = {
      id: blockData.id,
      alertId: blockData.alertId,
      alertTime: new Date(blockData.alertTime),
      alertType: blockData.alertType,
      amount: Math.round(parseFloat(String(blockData.amount)) * 100),
      currency: blockData.currency,
      descriptor: blockData.descriptor,
      authCode: blockData.authCode || null,
      cardBin: blockData.cardBin || null,
      cardNumber: blockData.cardNumber || null,
      chargebackCode: blockData.chargebackCode || null,
      disputeAmount: blockData.disputeAmount ? Math.round(parseFloat(String(blockData.disputeAmount)) * 100) : null,
      disputeCurrency: blockData.disputeCurrency || null,
      transactionTime: blockData.transactionTime ? new Date(blockData.transactionTime) : null,

      // RDR specific fields
      acquirerBin: blockData.acquirerBin || null,
      acquirerReferenceNumber: blockData.acquirerReferenceNumber || null,
      alertStatus: blockData.alertStatus || null,
      caid: blockData.caid || null,
      descriptorContact: blockData.descriptorContact || null,
      ruleName: blockData.ruleName || null,
      ruleType: blockData.ruleType || null,

      // Set the type to RDR
      type: BlockType.RDR,

      // For RDR blocks, automatically set feedback status to SENT
      feedbackStatus: FeedbackStatus.SENT,
      feedbackTime: new Date(),
      feedbackData: {
        message: "RDR alerts are automatically refunded without requiring feedback"
      }
    };

    // Create the block first
    const blockResult = await this.createBlock(blockRecord);

    // Step 2: Process billing and usage tracking synchronously
    if (blockResult.success && blockData.cardBin && blockData.caid) {
      try {
        await this.processRdrBlockBilling(
          blockData.id,
          blockData.cardBin,
          blockData.caid
        );
        logger.info(`[RDR] ✅ Synchronous billing process completed successfully for block ${blockData.id}`);
      } catch (error: any) {
        logger.error(`[RDR] ❌ Synchronous billing process failed for block ${blockData.id}:`, {
          message: error.message,
          name: error.name,
          code: error.code,
          status: error.response?.status
        });
        // Don't throw error - we want to return success for block creation even if billing fails
      }
    }

    return blockResult;
  };

  /**
   * Send feedback for a block
   * Updates a block with feedback data
   */
  static sendBlockFeedback = async (
    id: string,
    feedbackData: IBlockFeedback
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Check if block exists
      const blockResult = await this.getBlock(id);
      if (!blockResult.success) {
        return {
          success: false,
          status: blockResult.status,
          message: blockResult.message,
          data: undefined,
        };
      }

      const date = new Date(feedbackData.refundDate || Date.now());
      const refundDate = date.toISOString().slice(0, 19).replace("T", " ");

      const ethocaPayload = {
        predictorId: id,
        outcome: feedbackData.outcome,
        refunded: feedbackData.refunded,
        comments: feedbackData.comments || "My feedback",
        isFraud: feedbackData.isFraud || "false",
        matchOrderNo: feedbackData.matchOrderNo || "1234567890",
        refundNo: feedbackData.refundNo || "",
        refundDate: refundDate || "",
        refundAmount: feedbackData.refundAmount || "",
        refundCurrency: feedbackData.refundCurrency || "USD",
      };

      const signKey = sign(ethocaPayload, this.config.signKey);

      try {
        // Send data to Ethoca API
        const response = await axios.post(
          `${this.config.apiEndpoint}/rest/third/predictor/merchant/outcome`,
          ethocaPayload,
          {
            headers: {
              "Content-Type": "application/json",
              MerchantNo: this.config.merchantNo,
              Sign: signKey,
            },
          }
        );
        console.log("Ethoca API response:", {
          status: response.status,
          statusText: response.statusText,
          data: response.data
        });
        logger.info("Ethoca API response:", { 
          status: response.status, 
          statusText: response.statusText, 
          data: response.data,
          headers: response.headers 
        });

        const result = response.data;
        console.log("Result feedback from Ethoca:", result);

        // Check status in API result
        const isSuccess =
          result.status === true ||
          result.status === "true" ||
          result.success === true;

        // Update or create feedback in database and update alert status simultaneously

        // Return immediate response
        const immediateResponse = {
          success: isSuccess,
          status: isSuccess ? StatusCodes.OK : StatusCodes.BAD_REQUEST,
          message: isSuccess
            ? i18n.t("success.earlyWarning.feedbackSent") ||
            "Feedback sent successfully"
            : i18n.t("error.earlyWarning.feedbackFailed") ||
            "Feedback sending failed from API",
          data: {
            apiResponse: result,
            apiSuccess: isSuccess,
          },
        };

        // Process step 2 in background without blocking response
        const processStep2 = async () => {
          try {
            // Update block status based on API result
            const updateAlertResult = await this.updateBlock(id, {
              feedbackStatus: isSuccess ? FeedbackStatus.SENT : FeedbackStatus.FAILED,
              feedbackTime: new Date(),
              feedbackData: feedbackData,
            });

            if (!updateAlertResult.success) {
              console.log(
                "Failed to update alert status:",
                updateAlertResult.message
              );
            }

            // Clear cache when feedback is sent
            await this.clearBlockCache();
          } catch (error) {
            console.error('Background step 2 processing failed:', error);
          }
        };

        // Run step 2 in background
        processStep2();

        return immediateResponse;
      } catch (apiError: any) {
        console.error("API Error:", apiError);

        // If API error, still create feedback but with error status and save error to errorData
        const feedbackSent = {
          ...feedbackData,
          predictorId: id,
          data: ethocaPayload, // Save data sent to API
          errorData: {
            // Save error information from API
            message: apiError.message,
            response: apiError.response?.data,
            status: apiError.response?.status,
          },
        };

        // Use Promise.all to perform both tasks simultaneously
        const updateAlertResult = await this.updateBlock(id, {
          feedbackStatus: FeedbackStatus.FAILED,
          feedbackTime: new Date(),
          feedbackData: {
            ...feedbackSent,
            errorData: apiError.message || "Unknown error"
          },
        })


        return {
          success: false,
          status: StatusCodes.BAD_GATEWAY,
          message:
            apiError.message ||
            i18n.t("error.earlyWarning.apiError") ||
            "API Error",
          data: {
            error: apiError.message,
            savedFeedback: updateAlertResult.data.success ? updateAlertResult.data.feedbackData : null,
            updatedAlert: updateAlertResult.success
              ? updateAlertResult.data
              : null,
          },
        };
      }
    } catch (error: any) {
      logger.error(`Error sending feedback for block ${id}:`, {
        message: error.message,
        name: error.name,
        code: error.code,
        status: error.response?.status,
        statusText: error.response?.statusText
      });

      const feedbackSent = {
        ...feedbackData,
        predictorId: id,
        data: null,
        errorData: {
          // Save error information from API
          message: error.message,
          response: error.response?.data,
          status: error.response?.status,
        },
      };

      // Update block with FAILED status in case of error
      await this.updateBlock(id, {
        feedbackStatus: FeedbackStatus.FAILED,
        feedbackTime: new Date(),
        feedbackData: {
          ...feedbackSent,
          errorData: error.message || "Unknown error"
        },
      }).catch(e => logger.error("Failed to update block feedback status", e));

      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.block.feedback") || "Failed to send block feedback",
        data: undefined,
      };
    }
  };



  /**
   * Get blocks grouped by a specified field
   * This is a generic method for all grouping operations
   */
  static getGroupedBlocks = async (
    where: Record<string, any>,
    groupByConfig: {
      groupBy: string;
      field: string;
      viewMode: ViewMode;
      linkedStoreId?: string;
      isSpecialCase: boolean;
    }
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Generate cache key based on where conditions and groupByConfig
      const cacheKey = `${RedisPrefix.BLOCK_GROUPED}_${JSON.stringify(where)}_${JSON.stringify(groupByConfig)}`;

      // Try to get from cache first
      // const cachedData = await redisClient.get(cacheKey);
      // if (cachedData) {
        // logger.info(`Cache hit for grouped blocks: ${groupByConfig.groupBy}`);
        // return JSON.parse(cachedData);
      // }

      // Handle special cases
      if (groupByConfig.isSpecialCase) {
        if (groupByConfig.groupBy === 'overview') {
          logger.info(`getGroupedBlocks - Processing overview with viewMode: ${groupByConfig.viewMode}`);

          switch (groupByConfig.viewMode) {
            case 'count': {
              // Create base where clause without type constraints
              const baseWhere = { ...where };
              delete baseWhere.type;
              if (baseWhere.AND) {
                baseWhere.AND = baseWhere.AND.filter((condition: any) => 
                  !condition.type && !condition.OR?.some((orCondition: any) => orCondition.type)
                );
              }

              const [totalBlocks, ethocaBlocks, rdrBlocks] = await Promise.all([
                prisma.block.count({
                  where,
                }),
                prisma.block.count({
                  where: {
                    ...baseWhere,
                    type: BlockType.ETHOCA,
                  },
                }),
                prisma.block.count({
                  where: {
                    ...baseWhere,
                    type: BlockType.RDR,
                  },
                })
              ]);
              const countResult = {
                success: true,
                status: StatusCodes.OK,
                message: i18n.t("success.block.overviewCount") || "Block overview count retrieved successfully",
                data: {
                  total: totalBlocks,
                  ethoca: ethocaBlocks,
                  rdr: rdrBlocks,
                },
              };

              // Cache for 5 minutes
              await redisClient.setEx(cacheKey, 300, JSON.stringify(countResult));
              return countResult;
            }
            case 'dashboard': {
              const createdAtCondition = where?.AND?.find((c: any) => 'createdAt' in c)?.createdAt;
              const linkedStoreId = groupByConfig.linkedStoreId as string;
              
              // Optimize: group related queries for better performance
              const transactionWhere = {
                createdAt: createdAtCondition,
                linkedStoreId: linkedStoreId,
              };
              
              const disputeWhere = {
                createdAt: createdAtCondition,
                type: "chargeback" as const,
                linkedStoreId: linkedStoreId,
              };

              const [blockStats, transactionStats, allTimeGoal] = await Promise.all([
                // Group block queries together
                prisma.$transaction([
                  prisma.block.count({ where }),
                  prisma.block.count({
                    where: {
                      feedbackStatus: FeedbackStatus.SENT,
                      ...where,
                    },
                  })
                ]),
                // Group transaction/dispute queries together
                prisma.$transaction([
                  prisma.shopifyTransaction.count({ where: transactionWhere }),
                  prisma.shopifyDispute.count({ where: disputeWhere })
                ]),
                this.getAllTimeGoalSetting(linkedStoreId)
              ]);
              
              const [totalBlocks, blockedBlocks] = blockStats;
              const [transactions, disputes] = transactionStats;
              const dashboardResult = {
                success: true,
                status: StatusCodes.OK,
                message: i18n.t("success.block.overviewVolume") || "Block overview volume retrieved successfully",
                data: {
                  blocks: {
                    count: totalBlocks,
                    blocked: blockedBlocks,
                  },
                  //
                  // actual = num CBs / trans
                  // projected = (num CBs + Alerts )/ trans
                  rate: {
                    actualChargeback: transactions > 0 ? (disputes / transactions * 100).toFixed(2) : "0.00",
                    blocked: transactions > 0 ? ((disputes + totalBlocks) / transactions * 100).toFixed(2) : "0.00",
                  },
                  goal: {
                    actualChargeback: transactions > 0 ? (disputes / transactions * 100).toFixed(2) : "0.00",
                    chargebackGoal: Number(allTimeGoal?.value || 1).toFixed(2),
                    profitGoalProgress: Number(allTimeGoal?.value || 0) > 0 && transactions > 0 ? ((disputes / transactions * 100) / Number(allTimeGoal?.value || 0) * 100).toFixed(2) : "0.00",
                  }
                },
              };

              // Cache for 5 minutes
              await redisClient.setEx(cacheKey, 300, JSON.stringify(dashboardResult));
              return dashboardResult;
            }
          }

          const [totalBlocks, blocks] = await Promise.all([
            prisma.block.count({
              where,
            }),
            prisma.block.findMany({
              where,
              select: {
                type: true,
                amount: true
              }
            }),
          ]);

          const totalVolume = blocks.reduce((sum, b) => sum + ((b.amount || 0) / 100), 0);

          const chartItems = getEvenlyDistributedItems(blocks, 10).map((b) => ({
            type: b.type,
            amount: (b.amount || 0) / 100,
          }));
          const overviewResult = {
            success: true,
            status: StatusCodes.OK,
            message: i18n.t("success.block.overviewRetrieved") || "Block overview retrieved successfully",
            data: {
              total: {
                count: totalBlocks,
                volume: Number(totalVolume.toFixed(2)),
              },
              chart: {
                points: chartItems
              }
            },
          };

          // Cache for 5 minutes
          await redisClient.setEx(cacheKey, 300, JSON.stringify(overviewResult));
          return overviewResult;
        }
      }

      // Use Prisma groupBy for better performance      
      const groupedBlocks = await prisma.block.groupBy({
        by: [groupByConfig.field as Prisma.BlockScalarFieldEnum],
        where,
        _count: {
          _all: true
        },
      });

      // Format the grouped data and calculate volumes manually
      const formattedData = await Promise.all(groupedBlocks.map(async (group: any) => {
        const name = group[groupByConfig.field] || "Unknown";
        const count = group._count._all;

        // Calculate volume by fetching amounts for this group
        let volume = 0;
        if (groupByConfig.viewMode === "volume") {
          const blocks = await prisma.block.findMany({
            where: {
              ...where,
              [groupByConfig.field]: group[groupByConfig.field]
            },
            select: {
              amount: true
            }
          });

          volume = blocks.reduce((sum, block) => {
            return sum + ((block.amount || 0) / 100);
          }, 0);
        }

        return {
          name,
          count,
          volume: Number(volume.toFixed(2)),
          value: groupByConfig.viewMode === "volume" ? Number(volume.toFixed(2)) : count
        };
      }));

      // Sort data by count/volume based on viewMode
      const sortedData = formattedData.sort((a: any, b: any) => {
        if (groupByConfig.viewMode === "volume") {
          return b.volume - a.volume;
        }
        return b.count - a.count;
      });

      const result = {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t("success.block.retrieved") || `Blocks grouped by ${groupByConfig.field} retrieved successfully`,
        data: {
          items: sortedData
        },
      };

      // Cache for 5 minutes
      await redisClient.setEx(cacheKey, 300, JSON.stringify(result));
      return result;
    } catch (error: any) {
      logger.error(`Error retrieving blocks grouped by ${groupByConfig.groupBy}:`, error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.block.retrieval") || `Failed to retrieve blocks grouped by ${groupByConfig.groupBy}`,
        data: undefined,
      };
    }
  };


  /**
   * Builds standardized where conditions for Prisma queries
   * @param filters - Object containing various filter criteria
   * @returns Prisma where conditions object
   */
  private static buildWhereConditions(filters: Record<string, any> = {}): Record<string, any> {
    const queryConditions: Record<string, any> = {};
    const andClause: any[] = [];

    const { type, descriptor, cardBin, caid } = filters;

    // Require descriptor parameter - return empty if not provided
    if (descriptor === undefined) {
      return { id: { equals: 'NO_MATCHING_RECORDS' } };
    }

    // Handle case when no type is specified but descriptor exists
    if (type === undefined) {
      const typeFilters: any[] = [];
      
      // ETHOCA with descriptor
      const ethocaFilter: any = { type: 'ETHOCA' };
      if (Array.isArray(descriptor)) {
        ethocaFilter.descriptor = { in: descriptor };
      } else {
        ethocaFilter.descriptor = descriptor;
      }
      typeFilters.push(ethocaFilter);
      
      // RDR with descriptor
      const rdrFilter: any = { type: 'RDR' };
      if (Array.isArray(descriptor)) {
        rdrFilter.descriptor = { in: descriptor };
      } else {
        rdrFilter.descriptor = descriptor;
      }
      typeFilters.push(rdrFilter);
      
      andClause.push({ OR: typeFilters });
    }

    // BASE FILTERS
    if (type !== undefined) {
      if (Array.isArray(type)) {
        const typeFilters: any[] = [];

        if (type.includes('ETHOCA')) {
          const ethocaFilter: any = { type: 'ETHOCA' };
          if (descriptor !== undefined) {
            if (Array.isArray(descriptor)) {
              ethocaFilter.descriptor = { in: descriptor };
            } else {
              ethocaFilter.descriptor = descriptor;
            }
          }
          typeFilters.push(ethocaFilter);
        }

        if (type.includes('RDR')) {
          const rdrFilter: any = { type: 'RDR' };
          if (descriptor !== undefined) {
            if (Array.isArray(descriptor)) {
              rdrFilter.descriptor = { in: descriptor };
            } else {
              rdrFilter.descriptor = descriptor;
            }
          }
          if (cardBin !== undefined) {
            if (Array.isArray(cardBin)) {
              rdrFilter.cardBin = { in: cardBin };
            } else {
              rdrFilter.cardBin = cardBin;
            }
          }
          if (caid !== undefined) {
            if (Array.isArray(caid)) {
              rdrFilter.caid = { in: caid };
            } else {
              rdrFilter.caid = caid;
            }
          }
          typeFilters.push(rdrFilter);
        }

        if (typeFilters.length > 1) {
          andClause.push({ OR: typeFilters });
        } else if (typeFilters.length === 1) {
          andClause.push(typeFilters[0]);
        }
      } else {
        if (type === 'ETHOCA') {
          const ethocaFilter: any = { type: 'ETHOCA' };

          if (descriptor !== undefined) {
            if (Array.isArray(descriptor)) {
              ethocaFilter.descriptor = { in: descriptor };
            } else {
              ethocaFilter.descriptor = descriptor;
            }
          }

          andClause.push(ethocaFilter);

        } else if (type === 'RDR') {
          const rdrFilter: any = { type: 'RDR' };

          if (descriptor !== undefined) {
            if (Array.isArray(descriptor)) {
              rdrFilter.descriptor = { in: descriptor };
            } else {
              rdrFilter.descriptor = descriptor;
            }
          }

          if (cardBin !== undefined) {
            if (Array.isArray(cardBin)) {
              rdrFilter.cardBin = { in: cardBin };
            } else {
              rdrFilter.cardBin = cardBin;
            }
          }

          if (caid !== undefined) {
            if (Array.isArray(caid)) {
              rdrFilter.caid = { in: caid };
            } else {
              rdrFilter.caid = caid;
            }
          }

          andClause.push(rdrFilter);
        }
      }
    }

    // OPTIONS FILTERS
    const { recentDays, feedbackStatus, timeRange, startDate, endDate } = filters;

    if (timeRange !== undefined) {
      const now = new Date();
      let gte: Date | undefined;
      let lte: Date = endOfDay(now);

      switch (timeRange) {
        case TimeRange.TODAY:
          gte = startOfDay(now);
          break;
        case TimeRange.YESTERDAY:
          gte = startOfDay(subDays(now, 1));
          lte = endOfDay(subDays(now, 1));
          break;
        case TimeRange.THIS_MONTH:
          gte = startOfMonth(now);
          break;
        case TimeRange.THIS_YEAR:
          gte = startOfYear(now);
          break;
        case TimeRange.CUSTOM:
          if (startDate && endDate) {
            gte = startOfDay(new Date(startDate));
            lte = endOfDay(new Date(endDate));
          }
          break;
      }

      if (gte) {
        andClause.push({
          createdAt: {
            gte,
            lte,
          },
        });
      }
    }

    if (recentDays !== undefined) {
      andClause.push({ recentDays });
    }

    if (feedbackStatus !== undefined) {
      if (Array.isArray(feedbackStatus)) {
        andClause.push({ feedbackStatus: { in: feedbackStatus } });
      } else {
        andClause.push({ feedbackStatus });
      }
    }

    // CONSTRUCT QUERY CONDITIONS
    if (andClause.length > 0) {
      if (andClause.length === 1) {
        return andClause[0];
      } else {
        queryConditions.AND = andClause;
      }
    }

    return queryConditions;
  }

  /**
   * Builds group by configuration from filters
   * @param filters - Object containing groupBy and other parameters
   * @returns Group by configuration object or null
   */
  private static buildGroupByConditions(filters: Record<string, any> = {}): {
    groupBy: string;
    field: string;
    viewMode: ViewMode;
    linkedStoreId?: string;
    isSpecialCase: boolean;
  } | null {
    const { groupBy, viewMode = 'count', linkedStoreId } = filters;

    if (!groupBy) {
      return null;
    }

    const groupByMappings: Record<string, { field: string; isSpecialCase?: boolean }> = {
      'overview': { field: '', isSpecialCase: true },
      'type': { field: 'type' },
      'descriptor': { field: 'descriptor' },
      'country': { field: 'issuer' },
      'bank': { field: 'initiatedBy' },
      'status': { field: 'feedbackStatus' },
      'cardType': { field: 'issuer' }
    };

    const groupConfig = groupByMappings[groupBy];
    if (!groupConfig) {
      return null;
    }

    return {
      groupBy,
      field: groupConfig.field,
      viewMode,
      linkedStoreId,
      isSpecialCase: groupConfig.isSpecialCase || false
    };
  }



  /**
   * Find matched orders for a block
   * @param blockId - Block ID to find matches for
   * @param linkedStoreId - LinkedStore ID (internal ID, not providerStoreId)
   * @returns ServiceResponse with matched orders and transactions
   */
  static findMatchedOrder = async (blockId: string, linkedStoreId: string): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Check Redis cache first (before any heavy database queries)
      const cachedResult = await CacheService.getMatchedOrder(blockId, linkedStoreId);
      if (cachedResult) {
        return {
          success: true,
          status: StatusCodes.OK,
          message: i18n.t("success.block.matchedOrdersFoundFromCache") || "Matched orders found from cache",
          data: {
            ...cachedResult,
            matchingSummary: {
              ...cachedResult.matchingSummary,
              fromCache: true
            }
          },
        };
      }
      // Step 0: Check if we have cached results for this block and store
      logger.info(`Step 0: Checking cache for block ${blockId} and linkedStore ${linkedStoreId}`);
      const block = await prisma.block.findUnique({
        where: { id: blockId },
        select: {
          amount: true,
          currency: true,
          descriptor: true,
          cardBin: true,
          caid: true,
          cardNumber: true,
          authCode: true,
          arn: true,
          transactionTime: true,
          alertType: true,
          type: true,
        }
      })

      if (!block) {
        return {
          success: false,
          status: StatusCodes.NOT_FOUND,
          message: i18n.t("error.block.notFound") || "Block not found",
        };
      }

      // Check database cache (mappedBlocks) as fallback
      const mappedBlocks = await prisma.mappedBlock.findMany({
        where: {
          blockId: blockId,
          linkedStoreId: linkedStoreId
        },
        include: {
          order: {
            select: {
              id: true,
              orderNumber: true,
              totalPrice: true,
              currency: true,
            }
          },
          transaction: {
            select: {
              id: true,
              orderId: true,
              amount: true,
              currency: true,
              paymentDetails: true,
              processedAt: true,
              status: true,
              gateway: true,
              kind: true,
              authorization: true,
              receipt: true,
            }
          }
        }
      });

      if (mappedBlocks.length > 0) {
        // Convert cached results to the expected format
        // Remove duplicate orders
        const cachedOrders = Array.from(
          new Map(
            mappedBlocks
              .filter(mapping => mapping.order)
              .map(mapping => [mapping.order!.id.toString(), {
                id: mapping.order!.id.toString(),
                orderNumber: mapping.order!.orderNumber,
                totalPrice: mapping.order!.totalPrice,
                currency: mapping.order!.currency,
              }])
          ).values()
        );

        const cachedTransactions = mappedBlocks
          .filter(mapping => mapping.transaction)
          .map(mapping => ({
            id: mapping.transaction!.id.toString(),
            orderId: mapping.orderId?.toString(),
            amount: mapping.transaction!.amount,
            currency: mapping.transaction!.currency,
            paymentDetails: mapping.transaction!.paymentDetails,
            status: mapping.transaction!.status,
            gateway: mapping.transaction!.gateway,
            kind: mapping.transaction!.kind
          }));

        const responseData = {
          block,
          matchedTransactions: cachedTransactions,
          matchedOrders: cachedOrders,
          matchingSummary: {
            transactionsFound: cachedTransactions.length,
            ordersFound: cachedOrders.length,
            matchingCriteria: {
              amount: block.amount / 100,
              currency: block.currency,
              cardLast4: block.cardNumber ? block.cardNumber.slice(-4) : null,
              descriptor: block.descriptor,
              authCode: block.authCode,
              arn: block.arn,
            },
            fromCache: true
          }
        };

        logger.info(`Found database cached results for block ${blockId} and linkedStore ${linkedStoreId}: ${cachedOrders.length} orders, ${cachedTransactions.length} transactions`);

        // Cache the result in Redis for future requests
        await CacheService.setMatchedOrder(blockId, linkedStoreId, responseData);

        return {
          success: true,
          status: StatusCodes.OK,
          message: i18n.t("success.block.matchedOrdersFoundFromCache") || "Matched orders found from cache",
          data: responseData,
        };
      }

      logger.info("Step 0 completed: No cached results found, proceeding with calculation");

      // NOTE: Matching Ethoca alerts to internal transactions/orders should NOT rely on the Ethoca timestamp alone,
      //       as it can differ by up to 48 hours (and in rare cases, 72 hours).
      //
      //       The recommended priority for matching is:
      //       1. If available, match using `ARN` (Acquirer Reference Number) or `Auth Code` — these are unique identifiers.
      //       2. If neither ARN nor Auth Code is present, apply a fuzzy match using the following fields:
      //           · Billing Name (full or partial match)
      //           · Card Number (last 4 digits or masked match)
      //           · Transaction Amount (allowing up to $2 USD variance)
      //           · Approximate Transaction Time (within ±48h or ±72h in edge cases)
      //           · Currency (accounting for up to 5% variance if settlement currency differs from original)
      //
      //       CAUTION: Timestamp mismatches are common due to timezone issues and data processing delays.
      //       => Always allow a time window tolerance and fallback to fuzzy logic when no strict identifiers are available.

      // Step 1: Two-tier matching strategy - Exact Match and Fuzzy Match
      logger.info("Step 1: Implementing two-tier matching strategy");

      const exactMatchConditions: any = {
        linkedStoreId: linkedStoreId,
        AND: []
      };
      const fuzzyMatchConditions: any = {
        linkedStoreId: linkedStoreId,
        AND: []
      };

      // EXACT MATCH - ARN or Auth Code
      if (block.authCode) {
        exactMatchConditions.AND.push({
          referenceAuthorizationCode: {
            equals: block.authCode,
            mode: 'insensitive'
          }
        });
      }
      if (block.cardNumber) {
        exactMatchConditions.AND.push({
          referenceCardNumber: {
            endsWith: block.cardNumber.slice(-4)
          }
        });
      }

      // FUZZY MATCH -  Card Number, Amount, and Time

      // Match reference_amount (allowing up to $2 USD variance)
      if (block.amount) {
        const targetAmount = block.amount;
        fuzzyMatchConditions.AND.push({
          referenceAmount: {
            gte: targetAmount - 200, // $2
            lte: targetAmount + 200,
          }
        });
      }

      // Match reference_currency
      if (block.currency) {
        fuzzyMatchConditions.AND.push({
          referenceCurrency: {
            equals: block.currency,
            mode: 'insensitive'
          }
        });
      }

      // Match reference_card_number (last 4 digits)
      if (block.cardNumber) {
        fuzzyMatchConditions.AND.push({
          referenceCardNumber: {
            endsWith: block.cardNumber.slice(-4)
          }
        });
      }

      // Match approximate transaction time
      if (block.transactionTime) {
        const transactionTime = new Date(block.transactionTime);

        // Use fixed ±48h time variance for all cases
        const timeVarianceMs = 48 * 60 * 60 * 1000;

        fuzzyMatchConditions.AND.push({
          referenceTransactionTime: {
            gte: new Date(transactionTime.getTime() - timeVarianceMs),
            lte: new Date(transactionTime.getTime() + timeVarianceMs)
          }
        });
      }

      // Execute database queries for exact and fuzzy matching
      let exactMatchTransactions: any[] = [];
      let fuzzyMatchTransactions: any[] = [];
      
      try {
        logger.info(`[BILLING] Step 1.1: Executing exact match query`);
        logger.info(`[BILLING] Exact match conditions: ${JSON.stringify(exactMatchConditions)}`);
        
        exactMatchTransactions = await prisma.shopifyTransaction.findMany({
          where: exactMatchConditions,
          select: {
            id: true,
            orderId: true,
            amount: true,
            currency: true,
            paymentDetails: true,
            processedAt: true,
            status: true,
            gateway: true,
            kind: true,
            referenceAmount: true,
            referenceCurrency: true,
            referenceCardNumber: true,
            referenceArn: true,
            referenceAuthorizationCode: true,
            referenceTransactionTime: true,
          },
          take: 100 // Limit results for performance
        });
        
        logger.info(`[BILLING] Step 1.2: Exact match found ${exactMatchTransactions.length} transactions`);
        logger.info(`[BILLING] Step 1.3: Executing fuzzy match query`);
        logger.info(`[BILLING] Fuzzy match conditions: ${JSON.stringify(fuzzyMatchConditions)}`);
        
        fuzzyMatchTransactions = await prisma.shopifyTransaction.findMany({
          where: fuzzyMatchConditions,
          select: {
            id: true,
            orderId: true,
            amount: true,
            currency: true,
            paymentDetails: true,
            processedAt: true,
            status: true,
            gateway: true,
            kind: true,
            authorization: true,
            receipt: true,
            // Include reference fields for response
            referenceAmount: true,
            referenceCurrency: true,
            referenceCardNumber: true,
            referenceArn: true,
            referenceAuthorizationCode: true,
            referenceTransactionTime: true,
          },
          take: 100 // Limit results for performance
        });
        
        logger.info(`[BILLING] Step 1.4: Fuzzy match found ${fuzzyMatchTransactions.length} transactions`);
        
      } catch (error: any) {
        logger.error(`Database query error in findMatchedOrder:`, error);
        // Throw the error to be caught by the higher-level try/catch in processEthocaBlockBilling
        throw error;
      }

      let finalMatchedTransactions: any[] = [];

      // COMBINE RESULTS BASED ON STRATEGY
      if (exactMatchTransactions.length > 0 && fuzzyMatchTransactions.length > 0) {
        // Case 1: Both Exact Match and Fuzzy Match have results - find duplicates
        logger.info("Both Exact Match and Fuzzy Match have results, finding duplicates");

        const exactIds = new Set(exactMatchTransactions.map(t => t.id.toString()));
        const duplicateTransactions = fuzzyMatchTransactions.filter(transaction =>
          exactIds.has(transaction.id.toString())
        );

        finalMatchedTransactions = duplicateTransactions;
        logger.info(`Found ${finalMatchedTransactions.length} duplicate transactions between exact and fuzzy matches`);
      } else if (exactMatchTransactions.length > 0 && fuzzyMatchTransactions.length === 0) {
        // Case 2: Only Exact Match has results
        logger.info("Only Exact Match has results, using exact match transactions");
        finalMatchedTransactions = exactMatchTransactions;
      } else if (exactMatchTransactions.length === 0 && fuzzyMatchTransactions.length > 0) {
        // Case 3: Only Fuzzy Match has results
        logger.info("Only Fuzzy Match has results, using fuzzy match transactions");
        finalMatchedTransactions = fuzzyMatchTransactions;
      } else {
        // Case 4: No results from either match
        logger.info("No results from either Exact Match or Fuzzy Match");
        finalMatchedTransactions = [];
      }

      logger.info(`Step 1 completed: Final result contains ${finalMatchedTransactions.length} transactions`);

      if (finalMatchedTransactions.length === 0) {
        return {
          success: true,
          status: StatusCodes.OK,
          message: i18n.t("info.block.noMatchedTransactions") || "No transactions found matching block criteria",
          data: {
            block,
            matchedOrders: [],
            matchedTransactions: [],
            matchingSummary: {
              linkedStoresFound: 1,
              transactionsFound: 0,
              ordersFound: 0,
              matchingStrategy: {
                exactMatchUsed: (block.arn || block.authCode) ? true : false,
                exactMatchResults: 0,
                fuzzyMatchResults: 0,
                combinedResults: 0,
                strategy: "No matches found"
              },
              matchingCriteria: {
                // Exact Match criteria (ARN/Auth Code)
                exactMatch: {
                  referenceArn: block.arn,
                  referenceAuthCode: block.authCode,
                },
                // Fuzzy Match criteria
                fuzzyMatch: {
                  referenceAmount: block.amount / 100,
                  referenceCurrency: block.currency,
                  referenceCardLast4: block.cardNumber ? block.cardNumber.slice(-4) : null,
                  referenceTransactionTime: block.transactionTime,
                },
                // Keep original fields for backward compatibility
                amount: block.amount,
                currency: block.currency,
                cardLast4: block.cardNumber ? block.cardNumber.slice(-4) : null,
                descriptor: block.descriptor
              }
            }
          },
        };
      }

      // Step 2: Get order IDs from matched transactions
      logger.info("Step 2: Getting order IDs from matched transactions");

      const matchedOrderIds: Set<string> = new Set();

      for (const transaction of finalMatchedTransactions) {
        if (transaction.orderId) {
          matchedOrderIds.add(transaction.orderId.toString());
        }
      }

      logger.info(`Step 2 completed: Found ${matchedOrderIds.size} matched order IDs`);

      // Step 3: Get orders based on matched transaction order IDs
      logger.info("Step 3: Getting orders based on matched transaction order IDs");

      let finalMatchedOrders: any[] = [];

      if (matchedOrderIds.size > 0) {
        finalMatchedOrders = await prisma.shopifyOrder.findMany({
          where: {
            id: {
              in: Array.from(matchedOrderIds).map(id => BigInt(id))
            },
            linkedStoreId: linkedStoreId
          },
          select: {
            id: true,
            orderNumber: true,
            totalPrice: true,
            currency: true,
          }
        });
      }

      logger.info(`Step 3 completed: Found ${finalMatchedOrders.length} orders for matched transactions`);

      // Step 4: Save matched results to mapped_blocks table (async for performance)
      logger.info("Step 4: Save matched results to mapped_blocks table");

      // Run in background to avoid blocking the response
      saveFindMatchedOrderResult(
        blockId,
        linkedStoreId,
        finalMatchedTransactions,
      ).catch((error: any) => {
        logger.error(`Background save failed for block ${blockId}:`, error.message);
      });
      // Prepare response data
      const responseData = {
        block,
        matchedTransactions: finalMatchedTransactions.map((transaction: any) => ({
          id: transaction.id.toString(),
          orderId: transaction.orderId?.toString(),
          amount: transaction.amount,
          currency: transaction.currency,
          paymentDetails: transaction.paymentDetails,
          processedAt: transaction.processedAt,
          status: transaction.status,
          gateway: transaction.gateway,
          kind: transaction.kind,
          // Include reference fields in response
          referenceAmount: transaction.referenceAmount,
          referenceCurrency: transaction.referenceCurrency,
          referenceCardNumber: transaction.referenceCardNumber,
          referenceArn: transaction.referenceArn,
          referenceAuthorizationCode: transaction.referenceAuthorizationCode,
          referenceTransactionTime: transaction.referenceTransactionTime,
        })),
        matchedOrders: finalMatchedOrders.map((order: any) => ({
          id: order.id.toString(),
          orderNumber: order.orderNumber,
          totalPrice: order.totalPrice,
          currency: order.currency,
        })),
        matchingSummary: {
          transactionsFound: finalMatchedTransactions.length,
          ordersFound: finalMatchedOrders.length,
          matchingStrategy: {
            exactMatchUsed: exactMatchTransactions.length > 0,
            exactMatchResults: exactMatchTransactions.length,
            fuzzyMatchResults: fuzzyMatchTransactions.length,
            combinedResults: finalMatchedTransactions.length,
            strategy: (() => {
              if (exactMatchTransactions.length > 0 && fuzzyMatchTransactions.length > 0) {
                return "Duplicate Match (Exact ∩ Fuzzy)";
              } else if (exactMatchTransactions.length > 0 && fuzzyMatchTransactions.length === 0) {
                return "Exact Match Only";
              } else if (exactMatchTransactions.length === 0 && fuzzyMatchTransactions.length > 0) {
                return "Fuzzy Match Only";
              } else {
                return "No Matches Found";
              }
            })()
          },
          matchingCriteria: {
            // Exact Match criteria (ARN/Auth Code)
            exactMatch: {
              referenceArn: block.arn,
              referenceAuthCode: block.authCode,
            },
            // Fuzzy Match criteria
            fuzzyMatch: {
              referenceAmount: block.amount / 100,
              referenceCurrency: block.currency,
              referenceCardLast4: block.cardNumber ? block.cardNumber.slice(-4) : null,
              referenceTransactionTime: block.transactionTime,
            },
            // Keep original fields for backward compatibility
            amount: block.amount / 100,
            currency: block.currency,
            cardLast4: block.cardNumber ? block.cardNumber.slice(-4) : null,
            descriptor: block.descriptor,
            authCode: block.authCode,
            arn: block.arn,
          },
          fromCache: false
        }
      };

      // Cache the result in Redis for future requests
      await CacheService.setMatchedOrder(blockId, linkedStoreId, responseData);
      logger.info(`Cached matched order result for block ${blockId} and linkedStore ${linkedStoreId}`);

      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t("success.block.matchedOrdersFound") || "Matched orders found successfully",
        data: responseData,
      };

    } catch (error: any) {
      logger.error(`Error finding matched orders for block ${blockId}:`, error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.block.findMatchedOrders") || "Failed to find matched orders",
        data: undefined,
      };
    }
  };

  /**
   * Get chargeback rate chart data with time range support
   * Returns aggregated chargeback rates based on time range
   */
  static getChargebackRateChart = async (
    filters: Record<string, any> = {}
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Generate cache key based on filters
      const cacheKey = `dashboard:chart:${JSON.stringify(filters)}`;

      // Try to get from cache first  
      const cachedData = await CacheService.get<ServiceResponse<any>>(cacheKey);
      if (cachedData) {
        logger.info(`Cache hit for chargeback rate chart`);
        return cachedData;
      }

      // Determine date range based on timeRange filter
      const now = new Date();
      let startDate: Date;
      let endDate: Date = new Date();
      const timeRange = filters.timeRange || 'month'; // Default to 'This Month'

      switch (timeRange) {
        case 'month':
          startDate = startOfMonth(now);
          endDate = now;
          break;
        case 'year':
          startDate = startOfYear(now);
          endDate = now;
          break;
        case 'all_time':
          // Optimize: Use a single query to get min date with better performance
          const earliestDateResult = await prisma.$queryRaw<[{min_date: Date | null}]>`
            SELECT LEAST(
              (SELECT MIN("created_at") FROM "shopify_transactions" WHERE "linked_store_id" = ${filters.linkedStoreId}),
              (SELECT MIN("created_at") FROM "shopify_disputes" WHERE "linked_store_id" = ${filters.linkedStoreId})
            ) as min_date
          `;
          
          const minDate = earliestDateResult[0]?.min_date;
          startDate = minDate || new Date('2023-01-01'); // Fallback date
          endDate = now;
          break;
        case 'custom':
          if (filters.startDate && filters.endDate) {
            startDate = new Date(filters.startDate);
            endDate = new Date(filters.endDate);
          } else {
            // Fallback to this month if custom range not provided
            startDate = startOfMonth(now);
            endDate = now;
          }
          break;
        default:
          startDate = startOfMonth(now);
          endDate = now;
      }

      const transactionWhere = {
        createdAt: {
          gte: startDate,
          lte: endDate
        },
        linkedStoreId: filters.linkedStoreId,
      };

      const disputeWhere = {
        createdAt: {
          gte: startDate,
          lte: endDate
        },
        linkedStoreId: filters.linkedStoreId,
        type: "chargeback", // Only count chargebacks, not inquiries
      };

      // Log the where conditions for debugging
      logger.info(`getChargebackRateChart - transactionWhere:`, transactionWhere);
      logger.info(`getChargebackRateChart - disputeWhere:`, disputeWhere);

      let chartData: any[] = [];

      if (timeRange === 'month') {
        // Optimize: Use database aggregation for daily data
        const dailyStats = await prisma.$queryRaw<Array<{
          date_key: string;
          transaction_count: bigint;
          dispute_count: bigint;
        }>>`
          WITH date_series AS (
            SELECT generate_series(
              ${startDate}::date,
              ${endDate}::date,
              '1 day'::interval
            )::date as date_key
          ),
          daily_transactions AS (
            SELECT 
              DATE("created_at") as date_key,
              COUNT(*) as transaction_count
            FROM "shopify_transactions"
            WHERE "linked_store_id" = ${filters.linkedStoreId}
              AND "created_at" >= ${startDate}::timestamp
              AND "created_at" <= ${endDate}::timestamp
            GROUP BY DATE("created_at")
          ),
          daily_disputes AS (
            SELECT 
              DATE("created_at") as date_key,
              COUNT(*) as dispute_count
            FROM "shopify_disputes"
            WHERE "linked_store_id" = ${filters.linkedStoreId}
              AND "type" = 'chargeback'
              AND "created_at" >= ${startDate}::timestamp
              AND "created_at" <= ${endDate}::timestamp
            GROUP BY DATE("created_at")
          )
          SELECT 
            ds.date_key::text,
            COALESCE(dt.transaction_count, 0) as transaction_count,
            COALESCE(dd.dispute_count, 0) as dispute_count
          FROM date_series ds
          LEFT JOIN daily_transactions dt ON ds.date_key = dt.date_key
          LEFT JOIN daily_disputes dd ON ds.date_key = dd.date_key
          ORDER BY ds.date_key
        `;
        
        chartData = dailyStats.map(stat => {
          const transactionCount = Number(stat.transaction_count);
          const disputeCount = Number(stat.dispute_count);
          const rate = transactionCount > 0 
            ? parseFloat(((disputeCount / transactionCount) * 100).toFixed(3))
            : 0;

          return {
            date: new Date(stat.date_key).toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric'
            }),
            rate: rate,
            disputeCount: disputeCount,
            transactionCount: transactionCount
          };
        });

      } else if (timeRange === 'year') {
        // Optimized yearly data using database aggregation - group by month
        const monthlyStats = await prisma.$queryRaw<Array<{
          date_key: string;
          transaction_count: bigint;
          dispute_count: bigint;
        }>>`
          WITH date_series AS (
            SELECT generate_series(
              date_trunc('month', ${startDate}::timestamp),
              date_trunc('month', ${endDate}::timestamp),
              '1 month'::interval
            )::date as date_key
          ),
          monthly_transactions AS (
            SELECT 
              date_trunc('month', "created_at") as date_key,
              COUNT(*) as transaction_count
            FROM "shopify_transactions"
            WHERE "linked_store_id" = ${filters.linkedStoreId}
              AND "created_at" >= ${startDate}::timestamp
              AND "created_at" <= ${endDate}::timestamp
            GROUP BY date_trunc('month', "created_at")
          ),
          monthly_disputes AS (
            SELECT 
              date_trunc('month', "created_at") as date_key,
              COUNT(*) as dispute_count
            FROM "shopify_disputes"
            WHERE "linked_store_id" = ${filters.linkedStoreId}
              AND "type" = 'chargeback'
              AND "created_at" >= ${startDate}::timestamp
              AND "created_at" <= ${endDate}::timestamp
            GROUP BY date_trunc('month', "created_at")
          )
          SELECT 
            ds.date_key::text,
            COALESCE(mt.transaction_count, 0) as transaction_count,
            COALESCE(md.dispute_count, 0) as dispute_count
          FROM date_series ds
          LEFT JOIN monthly_transactions mt ON ds.date_key = mt.date_key
          LEFT JOIN monthly_disputes md ON ds.date_key = md.date_key
          ORDER BY ds.date_key
        `;
        
        chartData = monthlyStats.map(stat => {
          const transactionCount = Number(stat.transaction_count);
          const disputeCount = Number(stat.dispute_count);
          const rate = transactionCount > 0 
            ? parseFloat(((disputeCount / transactionCount) * 100).toFixed(3))
            : 0;

          return {
            date: new Date(stat.date_key).toLocaleDateString('en-US', {
              month: 'short',
              year: 'numeric'
            }),
            rate: rate,
            disputeCount: disputeCount,
            transactionCount: transactionCount
          };
        });

      } else {
        // Optimized all_time/custom data using database aggregation
        // For all_time and custom ranges, use monthly aggregation for better performance
        const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
        
        if (totalDays <= 365) {
          // For ranges <= 1 year, group by month for better granularity
          const monthlyStats = await prisma.$queryRaw<Array<{
          date_key: string;
          transaction_count: bigint;
          dispute_count: bigint;
        }>>`
            WITH date_series AS (
              SELECT generate_series(
                date_trunc('month', ${startDate}::timestamp),
                date_trunc('month', ${endDate}::timestamp),
                '1 month'::interval
              )::date as date_key
            ),
            monthly_transactions AS (
              SELECT 
                date_trunc('month', "created_at") as date_key,
                COUNT(*) as transaction_count
              FROM "shopify_transactions"
              WHERE "linked_store_id" = ${filters.linkedStoreId}
                AND "created_at" >= ${startDate}::timestamp
                AND "created_at" <= ${endDate}::timestamp
              GROUP BY date_trunc('month', "created_at")
            ),
            monthly_disputes AS (
              SELECT 
                date_trunc('month', "created_at") as date_key,
                COUNT(*) as dispute_count
              FROM "shopify_disputes"
              WHERE "linked_store_id" = ${filters.linkedStoreId}
                AND "type" = 'chargeback'
                AND "created_at" >= ${startDate}::timestamp
                AND "created_at" <= ${endDate}::timestamp
              GROUP BY date_trunc('month', "created_at")
            )
            SELECT 
              ds.date_key::text,
              COALESCE(mt.transaction_count, 0) as transaction_count,
              COALESCE(md.dispute_count, 0) as dispute_count
            FROM date_series ds
            LEFT JOIN monthly_transactions mt ON ds.date_key = mt.date_key
            LEFT JOIN monthly_disputes md ON ds.date_key = md.date_key
            ORDER BY ds.date_key
          `;
          
          chartData = monthlyStats.map(stat => {
            const transactionCount = Number(stat.transaction_count);
            const disputeCount = Number(stat.dispute_count);
            const rate = transactionCount > 0 
              ? parseFloat(((disputeCount / transactionCount) * 100).toFixed(3))
              : 0;

            return {
              date: new Date(stat.date_key).toLocaleDateString('en-US', {
                month: 'short',
                year: timeRange === 'all_time' ? '2-digit' : 'numeric'
              }),
              rate: rate,
              disputeCount: disputeCount,
              transactionCount: transactionCount
            };
          });
        } else {
          // For ranges > 1 year, group by quarter for manageable data points
          const quarterlyStats = await prisma.$queryRaw<Array<{
            date_key: string;
            transaction_count: bigint;
            dispute_count: bigint;
          }>>`
            WITH date_series AS (
              SELECT generate_series(
                date_trunc('quarter', ${startDate}::timestamp),
                date_trunc('quarter', ${endDate}::timestamp),
                '3 months'::interval
              )::date as date_key
            ),
            quarterly_transactions AS (
              SELECT 
                date_trunc('quarter', "created_at") as date_key,
                COUNT(*) as transaction_count
              FROM "shopify_transactions"
              WHERE "linked_store_id" = ${filters.linkedStoreId}
                AND "created_at" >= ${startDate}::timestamp
                AND "created_at" <= ${endDate}::timestamp
              GROUP BY date_trunc('quarter', "created_at")
            ),
            quarterly_disputes AS (
              SELECT 
                date_trunc('quarter', "created_at") as date_key,
                COUNT(*) as dispute_count
              FROM "shopify_disputes"
              WHERE "linked_store_id" = ${filters.linkedStoreId}
                AND "type" = 'chargeback'
                AND "created_at" >= ${startDate}::timestamp
                AND "created_at" <= ${endDate}::timestamp
              GROUP BY date_trunc('quarter', "created_at")
            )
            SELECT 
              ds.date_key::text,
              COALESCE(qt.transaction_count, 0) as transaction_count,
              COALESCE(qd.dispute_count, 0) as dispute_count
            FROM date_series ds
            LEFT JOIN quarterly_transactions qt ON ds.date_key = qt.date_key
            LEFT JOIN quarterly_disputes qd ON ds.date_key = qd.date_key
            ORDER BY ds.date_key
          `;
          
          chartData = quarterlyStats.map(stat => {
            const transactionCount = Number(stat.transaction_count);
            const disputeCount = Number(stat.dispute_count);
            const rate = transactionCount > 0 
              ? parseFloat(((disputeCount / transactionCount) * 100).toFixed(3))
              : 0;

            const date = new Date(stat.date_key);
            const quarter = Math.floor(date.getMonth() / 3) + 1;
            
            return {
              date: `Q${quarter} ${date.getFullYear()}`,
              rate: rate,
              disputeCount: disputeCount,
              transactionCount: transactionCount
            };
          });
        }
      }

      const result = {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t("success.block.chartDataRetrieved") || "Chart data retrieved successfully",
        data: chartData,
      };

      // Cache for 3 minutes using CacheService
      await CacheService.set(cacheKey, result, 3 * 60);
      return result;
    } catch (error: any) {
      logger.error("Error retrieving chargeback rate chart data:", error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.block.chartDataRetrieval") || "Failed to retrieve chart data",
        data: [],
      };
    }
  };
  /**
   * Find linked store for ETHOCA block based on descriptor
   */
  static findLinkedStoreForEthocaBlock = async (descriptor: string): Promise<{linkedStoreId: string, provider: string} | null> => {
    try {
      logger.info(`[BILLING] Searching for AlertInfo with descriptor: "${descriptor}"`);
      console.log(`[BILLING] Searching for AlertInfo with descriptor: "${descriptor}"`);
      
      // Add timeout to the database query - reduced for faster response
      const queryTimeout = new Promise<null>((_, reject) => {
        setTimeout(() => reject(new Error('AlertInfo query timeout after 5 seconds')), 5000);
      });
      
      const queryPromise = prisma.alertInfo.findFirst({
        where: {
          alertType: 'ETHOCA',
          descriptor: descriptor,
          registrationStatus: 'EFFECTED'
        },
        select: {
          storeId: true,
          store: {
            select: {
              provider: true
            }
          }
        }
      });

      logger.info(`[BILLING] Executing AlertInfo database query...`);
      console.log(`[BILLING] Executing AlertInfo database query...`);
      
      const alertInfo = await Promise.race([queryPromise, queryTimeout]);

      logger.info(`[BILLING] AlertInfo query completed successfully`);
      console.log(`[BILLING] AlertInfo query completed successfully`);
      
      logger.info(`[BILLING] AlertInfo query result: ${alertInfo ? `Found storeId: ${alertInfo.storeId}, provider: ${alertInfo.store.provider}` : 'No matching AlertInfo found'}`);
      console.log(`[BILLING] AlertInfo query result: ${alertInfo ? `Found storeId: ${alertInfo.storeId}, provider: ${alertInfo.store.provider}` : 'No matching AlertInfo found'}`);

      return alertInfo ? {linkedStoreId: alertInfo.storeId, provider: alertInfo.store.provider} : null;
    } catch (error: any) {
      logger.error('Error finding linked store for ETHOCA block:', error);
      console.error('Error finding linked store for ETHOCA block:', error);
      console.error('Error stack:', error.stack);
      
      // Skip fallback query for faster response - we already know it's a DB issue
      
      return null;
    }
  };

  /**
   * Find linked store for RDR block based on BIN and CAID
   */
  static findLinkedStoreForRdrBlock = async (cardBin: string, caid: string): Promise<string | null> => {
    try {
      const alertInfo = await prisma.alertInfo.findFirst({
        where: {
          alertType: 'RDR',
          bin: cardBin,
          caid: caid,
          registrationStatus: 'EFFECTED'
        },
        select: {
          storeId: true
        }
      });

      return alertInfo?.storeId || null;
    } catch (error: any) {
      logger.error('Error finding linked store for RDR block:', error);
      return null;
    }
  };

  /**
   * Create block usage record
   */
  static createBlockUsage = async (storeId: string, blockId: string, blockType: string): Promise<void> => {
    try {
      await prisma.blockUsage.create({
        data: {
          storeId,
          blockId,
          type: blockType,
          status: FeedbackStatus.PENDING
        }
      });

      logger.info(`Created block usage for store ${storeId}, block ${blockId}`);
    } catch (error: any) {
      logger.error('Error creating block usage:', error);
      throw error;
    }
  };

  /**
   * Create bill for block processing
   * For webhook routes: Only deducts from current_balance, no Shopify usage records
   * (Usage records are created during top-up, not during block processing)
   */
  static createBillForBlock = async (
    storeId: string,
    blockId: string,
    blockType: 'RDR' | 'ETHOCA'
  ): Promise<void> => {
    try {
      // Get fixed fee based on block type
      const blockCost = StoreBalanceService.getBlockFee(blockType);
      const description = `${blockType} block processing fee for Block ID: ${blockId}`;

      // Log the fee for debugging
      logger.info(`Block fee for ${blockType} block ${blockId}: ${blockCost} cents ($${(blockCost / 100).toFixed(2)})`);
      logger.info(`[BILLING] Processing block billing - deducting from current_balance only (no Shopify usage record)`);

      // For block processing: Only deduct from balance (current_balance in store_settings)
      // Skip Shopify usage records since they're created during top-up operations
      const deductResult = await StoreBalanceService.deductBalance(
        storeId,
        blockCost,
        description
      );

      if (!deductResult.success) {
        logger.error(`Failed to deduct balance for block ${blockId}, store ${storeId}: ${deductResult.error}`);
        // Update block usage status to indicate payment issue
        await prisma.blockUsage.updateMany({
          where: {
            blockId,
            storeId
          },
          data: {
            status: FeedbackStatus.FAILED
          }
        });
      } else {
        logger.info(`[BILLING] ✅ Successfully deducted ${blockCost} cents from current_balance for store ${storeId}`);
      }
    } catch (error: any) {
      logger.error('Error creating bill for block:', error);
    }
  };

  /**
   * Create Shopify usage record for billing
   * NOTE: This method is NOT used for block processing webhooks
   * It's kept for other operations like balance maintenance/top-up
   */
  static createShopifyUsageRecord = async (
    storeId: string,
    blockId: string,
    blockType: 'RDR' | 'ETHOCA',
    amount: number
  ): Promise<void> => {
    try {
      const shopifyService = new ShopifyService();
      const description = `${blockType} block processing - Block ID: ${blockId}`;

      await shopifyService.createUsageRecord(
        storeId,
        description,
        1, // quantity
        amount // price in cents
      );

      logger.info(`Created Shopify usage record for ${blockType} block ${blockId} - $${(amount / 100).toFixed(2)}`);
    } catch (error: any) {
      logger.error('Error creating Shopify usage record:', {
        message: error.message,
        name: error.name,
        code: error.code,
        status: error.response?.status
      });
      // Don't throw here - billing should continue even if Shopify usage tracking fails
    }
  };

  /**
   * Process billing and usage tracking for ETHOCA blocks
   * Creates block usage record and deducts fees from store balance
   */
  static async processEthocaBlockBilling(
    blockId: string,
    descriptor: string
  ): Promise<void> {
    try {
      logger.info(`[BILLING] Starting processEthocaBlockBilling for block ${blockId} with descriptor: ${descriptor}`);
      console.log(`[BILLING] Starting processEthocaBlockBilling for block ${blockId} with descriptor: ${descriptor}`);
      
      // Step 1: Find linked store and matched order
      logger.info(`[BILLING] Step 1: Finding linked store for descriptor: ${descriptor}`);
      console.log(`[BILLING] Step 1: Finding linked store for descriptor: ${descriptor}`);
      
      const storeResult = await this.findLinkedStoreForEthocaBlock(descriptor);
      
      logger.info(`[BILLING] Found store result: ${storeResult ? `linkedStoreId: ${storeResult.linkedStoreId}, provider: ${storeResult.provider}` : 'null'}`);
      console.log(`[BILLING] Found store result: ${storeResult ? `linkedStoreId: ${storeResult.linkedStoreId}, provider: ${storeResult.provider}` : 'null'}`);
      
      if (!storeResult) {
        logger.warn(`[BILLING] No linked store found for ETHOCA block ${blockId} with descriptor ${descriptor}`);
        console.log(`[BILLING] No linked store found for ETHOCA block ${blockId} with descriptor ${descriptor}`);
        // Step 2: Send "notfound" feedback and exit
        logger.info(`[BILLING] Sending 'notfound' feedback for block ${blockId}`);
        await this.sendBlockFeedback(blockId, {
          predictorId: blockId,
          refunded: 'notfound',
          outcome: 'N',
          isFraud: 'false',
          comments: 'No matched store found'
        });
        logger.info(`[BILLING] Completed: No linked store found, sent notfound feedback`);
        return;
      }

      const linkedStoreId = storeResult.linkedStoreId;
      const provider = storeResult.provider;
      
      // Skip processing if provider is stripe
      if (provider === 'stripe') {
        logger.info(`[BILLING] Skipping processing for block ${blockId} - provider is stripe`);
        console.log(`[BILLING] Skipping processing for block ${blockId} - provider is stripe`);
        return;
      }
      
      logger.info(`[BILLING] Step 2: Finding matched orders for block ${blockId} in store ${linkedStoreId} (provider: ${provider})`);
      let matchedOrderResult;
      try {
        matchedOrderResult = await this.findMatchedOrder(blockId, linkedStoreId);
        logger.info(`[BILLING] findMatchedOrder result - success: ${matchedOrderResult.success}, orders found: ${matchedOrderResult.data?.matchedOrders?.length || 0}`);
      } catch (error: any) {
        logger.error(`[BILLING] ❌ findMatchedOrder failed with error: ${error.message}`);
        // Create a failed result object
        matchedOrderResult = {
          success: false,
          message: error.message,
          data: { matchedOrders: [] }
        };
      }

      // Check if we have matched orders after initial attempt or retry
      const hasMatchedOrders = matchedOrderResult.success && matchedOrderResult.data && matchedOrderResult.data.matchedOrders.length > 0;
      
      if (hasMatchedOrders) {
        logger.info(`[BILLING] Step 3: Found ${matchedOrderResult.data.matchedOrders.length} matched orders, proceeding with refund`);
        
        // Step 2: Create refund for the first matched order
        let refundSuccess = false;
        let refundError = null;
        
        const firstMatchedOrder = matchedOrderResult.data.matchedOrders[0];
        const orderId = firstMatchedOrder.id;
        
        logger.info(`[BILLING] Attempting to create refund for order ${orderId} (orderNumber: ${firstMatchedOrder.orderNumber}) in store ${linkedStoreId}`);
        
        try {
          const shopifyService = new ShopifyService();
          logger.info(`[BILLING] Calling shopifyService.createRefund with params: storeId=${linkedStoreId}, orderId=${orderId}, blockId=${blockId}`);
          
          const refundResult = await shopifyService.createRefund(
            linkedStoreId,
            orderId.toString(),
            blockId
          );
          
          logger.info(`[BILLING] Refund result: success=${refundResult.success}, message=${refundResult.message || 'no message'}`);
          
          if (refundResult.success) {
            refundSuccess = true;
            logger.info(`[BILLING] ✅ Successfully created refund for order ${orderId} in store ${linkedStoreId}`);
          } else {
            refundError = refundResult.message || 'Failed to create refund';
            logger.error(`[BILLING] ❌ Failed to create refund for order ${orderId}: ${refundError}`);
          }
        } catch (error: any) {
          refundError = error.message || 'Unknown error during refund creation';
          logger.error(`[BILLING] ❌ Exception creating refund for order ${orderId}:`, {
            message: error.message,
            name: error.name,
            code: error.code,
            status: error.response?.status
          });
        }

        // Step 3: Send feedback based on refund result
        logger.info(`[BILLING] Step 4: Sending feedback - refunded: ${refundSuccess ? 'refunded' : 'failed'}, outcome: ${refundSuccess ? 'Y' : 'N'}`);
        await this.sendBlockFeedback(blockId, {
          predictorId: blockId,
          refunded: refundSuccess ? 'refunded' : 'failed',
          outcome: refundSuccess ? 'Y' : 'N',
          isFraud: 'false',
          comments: refundSuccess 
            ? 'Order found and refunded successfully'
            : `Order found but refund failed: ${refundError}`
        });

        // Step 4: Create block usage record (only if refund was successful)
        if (refundSuccess) {
          logger.info(`[BILLING] Step 5: Creating block usage record for successful refund`);
          await this.createBlockUsage(linkedStoreId, blockId, BlockType.ETHOCA);
          
          // Step 5: Create bill for this block with ETHOCA pricing
          logger.info(`[BILLING] Step 6: Creating bill for block with ETHOCA pricing`);
          await this.createBillForBlock(linkedStoreId, blockId, 'ETHOCA');
        } else {
          logger.warn(`[BILLING] Skipping usage and billing records due to failed refund`);
        }

        logger.info(`[BILLING] ✅ Completed processing for ETHOCA block ${blockId} for store ${linkedStoreId}. Refund: ${refundSuccess ? 'Success' : 'Failed'}`);
      } else {
        logger.info(`[BILLING] Step 3: No matched orders found or findMatchedOrder failed`);
        if (!matchedOrderResult.success) {
          logger.error(`[BILLING] findMatchedOrder failed with message: ${matchedOrderResult.message}`);
          
          // Check if it's a database connection issue and retry
          if (matchedOrderResult.message?.includes('connection pool') || matchedOrderResult.message?.includes('Timed out')) {
            logger.warn(`[BILLING] Database connection issue detected, attempting to retry database connection`);
            
            // Retry the findMatchedOrder operation up to 3 times with exponential backoff
            let retryCount = 0;
            const maxRetries = 3;
            let retryResult = null;
            
            while (retryCount < maxRetries && !retryResult?.success) {
              retryCount++;
              const backoffMs = Math.pow(2, retryCount) * 1000; // 2s, 4s, 8s
              
              logger.info(`[BILLING] Retry attempt ${retryCount}/${maxRetries} after ${backoffMs}ms delay`);
              await new Promise(resolve => setTimeout(resolve, backoffMs));
              
              try {
                retryResult = await this.findMatchedOrder(blockId, linkedStoreId);
                logger.info(`[BILLING] Retry ${retryCount} result - success: ${retryResult.success}, orders found: ${retryResult.data?.matchedOrders?.length || 0}`);
                
                if (retryResult.success && retryResult.data?.matchedOrders?.length > 0) {
                  logger.info(`[BILLING] ✅ Database retry ${retryCount} succeeded, proceeding with refund`);
                  matchedOrderResult = retryResult;
                  break;
                }
              } catch (retryError: any) {
                logger.warn(`[BILLING] Retry ${retryCount} failed: ${retryError.message}`);
                if (retryCount === maxRetries) {
                  logger.error(`[BILLING] All ${maxRetries} retry attempts failed`);
                }
              }
            }
            
            // If retries succeeded, continue with normal refund process
            if (retryResult?.success && retryResult.data?.matchedOrders?.length > 0) {
              logger.info(`[BILLING] Database retry succeeded, processing refund normally`);
              // The code will continue to the refund processing section below
            } else {
              logger.error(`[BILLING] Database retries exhausted, unable to find matched orders`);
            }
          }
        }
        
        // Step 2: Send "notfound" feedback and exit
        logger.info(`[BILLING] Sending 'notfound' feedback for no matched orders`);
        await this.sendBlockFeedback(blockId, {
          predictorId: blockId,
          refunded: 'notfound',
          outcome: 'N',
          isFraud: 'false',
          comments: matchedOrderResult.success ? 'No matched order found' : `Database error: ${matchedOrderResult.message}`
        });
        logger.info(`[BILLING] ✅ Completed: No matched order found for ETHOCA block ${blockId} in store ${linkedStoreId}`);
      }
    } catch (error: any) {
      logger.error(`[BILLING] ❌ Critical error processing billing for ETHOCA block ${blockId}:`, {
        message: error.message,
        name: error.name,
        code: error.code,
        status: error.response?.status,
        stack: error.stack
      });
      console.error(`[BILLING] ❌ Critical error processing billing for ETHOCA block ${blockId}:`, {
        message: error.message,
        name: error.name,
        stack: error.stack
      });
      console.error(`[BILLING] ❌ Error stack:`, error.stack);
      
      // For synchronous processing, we don't want to fail the entire webhook
      // Instead, we log the error and continue - the block was already created successfully
      logger.info(`[BILLING] Continuing with webhook response despite billing error for block ${blockId}`);
      throw error; // Still throw to be caught by the processEthocaBlock method
    }
  }


  /**
   * Process billing and usage tracking for RDR blocks
   * Creates block usage record and deducts fees from store balance
   */
  static async processRdrBlockBilling(
    blockId: string,
    cardBin: string,
    caid: string
  ): Promise<void> {
    // Find linked store based on BIN and CAID
    const linkedStoreId = await this.findLinkedStoreForRdrBlock(cardBin, caid);

    if (linkedStoreId) {
      try {
        // Create block usage record
        await this.createBlockUsage(linkedStoreId, blockId, BlockType.RDR);

        // Create bill for this block with RDR pricing
        await this.createBillForBlock(linkedStoreId, blockId, 'RDR');

        logger.info(`Successfully processed billing for RDR block ${blockId} for store ${linkedStoreId}`);
      } catch (error: any) {
        logger.error(`Error processing billing for RDR block ${blockId}:`, {
          message: error.message,
          name: error.name,
          code: error.code,
          status: error.response?.status,
          stack: error.stack
        });
      }
    } else {
      logger.warn(`No linked store found for RDR block ${blockId} with BIN ${cardBin} and CAID ${caid}`);
    }
  }
}

function getEvenlyDistributedItems<T>(items: T[], count: number): T[] {
  const total = items.length;
  if (count <= 0) return [];
  if (count === 1) return [items[0]];
  const step = (total - 1) / (count - 1);
  const result: T[] = [];
  for (let i = 0; i < count; i++) {
    const index = Math.round(i * step);
    result.push(items[index]);
  }
  return result;
}

export default BlockService;