import axios from "axios";
import crypto from "crypto";
import { ServiceResponse } from "@/constants/type";
import { StatusCodes } from "http-status-codes";
import { getI18n } from "@/middlewares/language.middleware";
import PrismaService from "./prisma.service";
import { env } from "@/config/environment";

const prisma = PrismaService.getInstance().getClient();

// Types for Anti-Fraud Service
interface AntiFraudConfig {
  secretId: string;
  secretKey: string;
  domain: string;
  region: string;
  clientId: string;
}

interface InputDescribeRiskControl {
  BasicInfo: {
    AccountId: string;
    TransactionType: string;
    MerchantId: string;
    MerchantName: string;
    [key: string]: any;
  };
  UserInfo: {
    UserId: string;
    UserIp: string;
    [key: string]: any;
  };
  ClientInfo: {
    ClientIp: string;
    [key: string]: any;
  };
  ChannelInfo: {
    ChannelId: string;
    ChannelName: string;
    [key: string]: any;
  };
  SceneInfo: {
    SceneId: string;
    SceneName: string;
    [key: string]: any;
  };
  ExtraInfo?: {
    [key: string]: any;
  };
}

interface RiskControlResult {
  ResultCode: number;
  Action?: string;
  ResultInfo?: string;
  Reference?: string;
  ReferenceIdle?: string;
  Score?: number;
  ScoreItems?: {
    Field: string;
    Score: number;
  }[];
}

interface InputRiskNotifyData {
  BasicInfo: {
    RcUUID: string;
    TxnResult: string;
    [key: string]: any;
  };
  [key: string]: any;
}

class AntiFraudService {
  private static config: AntiFraudConfig = {
    secretId: env.ANTI_FRAUD_SECRET_ID || "",
    secretKey: env.ANTI_FRAUD_SECRET_KEY || "",
    domain: env.ANTI_FRAUD_DOMAIN || "",
    region: env.ANTI_FRAUD_REGION || "",
    clientId: env.ANTI_FRAUD_CLIENT_ID || "",
  };

  /**
   * Transaction Check API - Performs risk assessment on a transaction
   */
  static checkTransaction = async (
    data: InputDescribeRiskControl
  ): Promise<ServiceResponse<RiskControlResult>> => {
    const i18n = getI18n();
    try {
      const result = await this.callAntiFraudAPI("DescribeRiskControl", data);

      if (result.Data?.Code !== 0) {
        return {
          success: false,
          status: StatusCodes.BAD_REQUEST,
          message:
            result.Data?.Message || i18n.t("error.antiFraud.requestFailed"),
          data: undefined,
        };
      }

      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t("success.antiFraud.riskCheckCompleted"),
        data: result.Data.Value,
      };
    } catch (error: any) {
      console.log("error from api", error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.antiFraud.internalError"),
        data: undefined,
      };
    }
  };

  /**
   * Transaction Notify API - Notifies the result of a transaction
   */
  static notifyTransactionResult = async (
    data: InputRiskNotifyData
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      const result = await this.callAntiFraudAPI("DescribeRiskNotify", data);

      if (result.Data?.Code !== 0) {
        return {
          success: false,
          status: StatusCodes.BAD_REQUEST,
          message:
            result.Data?.Message || i18n.t("error.antiFraud.notifyFailed"),
          data: undefined,
        };
      }

      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t("success.antiFraud.notifyCompleted"),
        data: result.Data.Value,
      };
    } catch (error: any) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.antiFraud.internalError"),
        data: undefined,
      };
    }
  };

  /**
   * Call Anti-Fraud API with proper authorization and encryption
   */
  private static callAntiFraudAPI = async (action: string, data: any) => {
    // Prepare timestamp and date string for credential scope
    const timestamp = Math.floor(Date.now() / 1000);
    const date = new Date(timestamp * 1000)
      .toISOString()
      .slice(0, 10)
      .replace(/-/g, "");

    // Encrypt the data using AES/ECB/PKCS7Padding
    const bizCryptoData = this.encryptBusinessData(data);

    // Create the request payload
    const payload = JSON.stringify({ BizCryptoData: bizCryptoData });

    // Calculate authorization header
    const authorization = this.calculateAuthorization(
      payload,
      timestamp,
      date,
      action
    );

    // Prepare API request
    const headers = {
      "Content-Type": "application/json",
      "X-TC-Action": action,
      "X-TC-Version": "2020-02-26",
      "X-TC-Region": this.config.region,
      "X-TC-Timestamp": timestamp.toString(),
      Host: this.config.domain,
      Authorization: authorization,
    };

    // Call the API
    const response = await axios.post(
      `https://${this.config.domain}/`,
      payload,
      { headers }
    );
    // console.log("response", JSON.stringify(response.data));
    return response.data;
  };

  /**
   * Encrypts business data using AES/ECB/PKCS7Padding
   */
  private static encryptBusinessData = (data: any) => {
    // Prepare the AES key from clientId
    const clientIdBuffer = Buffer.from(this.config.clientId, "base64");

    // For AES-256, we need exactly 32 bytes
    // Create a fixed-size buffer and ensure proper key length
    const keyBuffer = Buffer.alloc(32); // Create a 32-byte buffer filled with zeros

    // Copy bytes from clientIdBuffer to keyBuffer, up to the minimum of their lengths
    clientIdBuffer.copy(
      keyBuffer,
      0,
      0,
      Math.min(clientIdBuffer.length, keyBuffer.length)
    );

    // If clientIdBuffer was too short, keyBuffer will have zero padding
    // If it was too long, we only used the first 32 bytes

    // Convert data to JSON string
    const plaintext = JSON.stringify(data);

    // Create cipher and encrypt
    const cipher = crypto.createCipheriv("aes-256-ecb", keyBuffer, null);
    let encrypted = cipher.update(plaintext, "utf8", "base64");
    encrypted += cipher.final("base64");

    return {
      IsAuthorized: "1",
      CryptoType: "1", // AES encryption
      CryptoContent: encrypted,
    };
  };

  /**
   * Calculates the authorization header value following TC3-HMAC-SHA256 algorithm
   */
  private static calculateAuthorization = (
    payload: string,
    timestamp: number,
    date: string,
    action: string
  ) => {
    // Step 1: Create Canonical Request
    const hashedPayload = crypto
      .createHash("sha256")
      .update(payload)
      .digest("hex")
      .toLowerCase();
    const canonicalHeaders =
      "content-type:application/json\nhost:" + this.config.domain + "\n";
    const signedHeaders = "content-type;host";

    const canonicalRequest = [
      "POST",
      "/",
      "",
      canonicalHeaders,
      signedHeaders,
      hashedPayload,
    ].join("\n");

    // Step 2: Create String to Sign
    const service = "ra"; // Assuming 'ra' is the service code for anti-fraud service
    const credentialScope = `${date}/${service}/tc3_request`;

    const hashedCanonicalRequest = crypto
      .createHash("sha256")
      .update(canonicalRequest)
      .digest("hex")
      .toLowerCase();

    const stringToSign = [
      "TC3-HMAC-SHA256",
      timestamp,
      credentialScope,
      hashedCanonicalRequest,
    ].join("\n");

    // Step 3: Calculate Signature
    const secretKey = "TC3" + this.config.secretKey;
    const secretDate = crypto
      .createHmac("sha256", secretKey)
      .update(date)
      .digest();

    const secretService = crypto
      .createHmac("sha256", secretDate)
      .update(service)
      .digest();

    const secretSigning = crypto
      .createHmac("sha256", secretService)
      .update("tc3_request")
      .digest();

    const signature = crypto
      .createHmac("sha256", secretSigning)
      .update(stringToSign)
      .digest("hex")
      .toLowerCase();

    // Step 4: Create Authorization Header
    return `TC3-HMAC-SHA256 Credential=${this.config.secretId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;
  };
}

export default AntiFraudService;
