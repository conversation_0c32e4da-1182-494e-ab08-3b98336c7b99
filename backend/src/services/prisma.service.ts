import { PrismaClient } from "@prisma/client";

class PrismaService {
  private static instance: PrismaService;
  private client: PrismaClient;

  private constructor() {
    this.client = new PrismaClient({
      log:
        process.env.NODE_ENV === "development"
          ? ["query", "error", "warn"]
          : ["error"],
    });
  }

  public static getInstance(): PrismaService {
    if (!PrismaService.instance) {
      PrismaService.instance = new PrismaService();
    }
    return PrismaService.instance;
  }

  public getClient(): PrismaClient {
    return this.client;
  }

  public async connect(): Promise<void> {
    try {
      await this.client.$connect();
      console.log("Connected to PostgreSQL database");
    } catch (error) {
      console.error("Failed to connect to PostgreSQL database:", error);
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    try {
      await this.client.$disconnect();
      console.log("Disconnected from PostgreSQL database");
    } catch (error) {
      console.error("Failed to disconnect from PostgreSQL database:", error);
      throw error;
    }
  }
}

export default PrismaService;
