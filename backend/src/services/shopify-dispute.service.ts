import PrismaService from './prisma.service';
import {
  CreateShopifyDispute,
  ShopifyDisputeModel,
  ShopifyDisputeQuery
} from '../models/shopify-dispute.model';

class ShopifyDisputeService {
  private static prisma: any;
  
  static initialize() {
    this.prisma = PrismaService.getInstance().getClient();
  }

  /**
   * Create a new Shopify dispute
   * @param data Shopify dispute data
   * @returns Created dispute
   */
  static async createDispute(data: CreateShopifyDispute): Promise<ShopifyDisputeModel> {
    return this.prisma.shopifyDispute.create({
      data
    });
  }

  /**
   * Create multiple Shopify disputes
   * @param data Array of Shopify dispute data
   * @returns Number of created disputes
   */
  static async createManyDisputes(data: CreateShopifyDispute[]): Promise<number> {
    const result = await this.prisma.shopifyDispute.createMany({
      data,
      skipDuplicates: true
    });
    return result.count;
  }

  /**
   * Get all Shopify disputes with optional filtering
   * @param query Optional query parameters
   * @returns Array of disputes
   */
  static async getAllDisputes(query: ShopifyDisputeQuery = {}): Promise<ShopifyDisputeModel[]> {
    // Handle date range queries
    const whereClause: any = {...query};
    
    if (whereClause.initiatedFrom || whereClause.initiatedTo) {
      whereClause.initiatedAt = {};
      if (whereClause.initiatedFrom) {
        whereClause.initiatedAt.gte = whereClause.initiatedFrom;
        delete whereClause.initiatedFrom;
      }
      if (whereClause.initiatedTo) {
        whereClause.initiatedAt.lte = whereClause.initiatedTo;
        delete whereClause.initiatedTo;
      }
    }
    
    if (whereClause.evidenceDueFrom || whereClause.evidenceDueTo) {
      whereClause.evidenceDueBy = {};
      if (whereClause.evidenceDueFrom) {
        whereClause.evidenceDueBy.gte = whereClause.evidenceDueFrom;
        delete whereClause.evidenceDueFrom;
      }
      if (whereClause.evidenceDueTo) {
        whereClause.evidenceDueBy.lte = whereClause.evidenceDueTo;
        delete whereClause.evidenceDueTo;
      }
    }

    return this.prisma.shopifyDispute.findMany({
      where: whereClause,
      orderBy: {
        initiatedAt: 'desc'
      },
      include: {
        transaction: true
      }
    });
  }

  /**
   * Get Shopify disputes for a specific linked store
   * @param linkedStoreId Linked store ID
   * @returns Array of disputes for the store
   */
  static async getDisputesByLinkedStoreId(linkedStoreId: string): Promise<ShopifyDisputeModel[]> {
    return this.prisma.shopifyDispute.findMany({
      where: { linkedStoreId },
      orderBy: {
        initiatedAt: 'desc'
      },
      include: {
        transaction: true
      }
    });
  }

  /**
   * Get Shopify disputes for a specific transaction
   * @param transactionId Transaction ID
   * @returns Array of disputes for the transaction
   */
  static async getDisputesByTransactionId(transactionId: bigint): Promise<ShopifyDisputeModel[]> {
    return this.prisma.shopifyDispute.findMany({
      where: { transactionId },
      orderBy: {
        initiatedAt: 'desc'
      }
    });
  }

  /**
   * Get a specific Shopify dispute by ID
   * @param id Dispute ID
   * @returns Dispute or null if not found
   */
  static async getDisputeById(id: bigint): Promise<ShopifyDisputeModel | null> {
    return this.prisma.shopifyDispute.findUnique({
      where: { id },
      include: {
        transaction: {
          include: {
            order: true
          }
        }
      }
    });
  }

  /**
   * Update an existing Shopify dispute
   * @param id Dispute ID
   * @param data Updated dispute data
   * @returns Updated dispute
   */
  static async updateDispute(id: bigint, data: Partial<CreateShopifyDispute>): Promise<ShopifyDisputeModel> {
    return this.prisma.shopifyDispute.update({
      where: { id },
      data
    });
  }

  /**
   * Delete a Shopify dispute
   * @param id Dispute ID
   * @returns void
   */
  static async deleteDispute(id: bigint): Promise<void> {
    await this.prisma.shopifyDispute.delete({
      where: { id }
    });
  }

  /**
   * Count Shopify disputes that match the given criteria
   * @param query Query parameters
   * @returns Count of matching disputes
   */
  static async countDisputes(query: ShopifyDisputeQuery = {}): Promise<number> {
    // Handle date range queries
    const whereClause: any = {...query};
    
    if (whereClause.initiatedFrom || whereClause.initiatedTo) {
      whereClause.initiatedAt = {};
      if (whereClause.initiatedFrom) {
        whereClause.initiatedAt.gte = whereClause.initiatedFrom;
        delete whereClause.initiatedFrom;
      }
      if (whereClause.initiatedTo) {
        whereClause.initiatedAt.lte = whereClause.initiatedTo;
        delete whereClause.initiatedTo;
      }
    }
    
    if (whereClause.evidenceDueFrom || whereClause.evidenceDueTo) {
      whereClause.evidenceDueBy = {};
      if (whereClause.evidenceDueFrom) {
        whereClause.evidenceDueBy.gte = whereClause.evidenceDueFrom;
        delete whereClause.evidenceDueFrom;
      }
      if (whereClause.evidenceDueTo) {
        whereClause.evidenceDueBy.lte = whereClause.evidenceDueTo;
        delete whereClause.evidenceDueTo;
      }
    }

    return this.prisma.shopifyDispute.count({
      where: whereClause
    });
  }

  /**
   * Get disputes with pagination
   * @param page Page number (1-based)
   * @param limit Items per page
   * @param query Optional query parameters
   * @returns Paginated disputes
   */
  static async getPaginatedDisputes(
    page: number = 1, 
    limit: number = 10,
    query: ShopifyDisputeQuery = {}
  ): Promise<{ data: ShopifyDisputeModel[], total: number, page: number, limit: number }> {
    const skip = (page - 1) * limit;
    
    // Handle date range queries
    const whereClause: any = {...query};
    
    if (whereClause.initiatedFrom || whereClause.initiatedTo) {
      whereClause.initiatedAt = {};
      if (whereClause.initiatedFrom) {
        whereClause.initiatedAt.gte = whereClause.initiatedFrom;
        delete whereClause.initiatedFrom;
      }
      if (whereClause.initiatedTo) {
        whereClause.initiatedAt.lte = whereClause.initiatedTo;
        delete whereClause.initiatedTo;
      }
    }
    
    if (whereClause.evidenceDueFrom || whereClause.evidenceDueTo) {
      whereClause.evidenceDueBy = {};
      if (whereClause.evidenceDueFrom) {
        whereClause.evidenceDueBy.gte = whereClause.evidenceDueFrom;
        delete whereClause.evidenceDueFrom;
      }
      if (whereClause.evidenceDueTo) {
        whereClause.evidenceDueBy.lte = whereClause.evidenceDueTo;
        delete whereClause.evidenceDueTo;
      }
    }

    const [data, total] = await Promise.all([
      this.prisma.shopifyDispute.findMany({
        where: whereClause,
        skip,
        take: limit,
        orderBy: {
          initiatedAt: 'desc'
        },
        include: {
          transaction: {
            include: {
              order: true
            }
          }
        }
      }),
      this.prisma.shopifyDispute.count({ where: whereClause })
    ]);

    return {
      data,
      total,
      page,
      limit
    };
  }
}

// Initialize when module is loaded
ShopifyDisputeService.initialize();

export default ShopifyDisputeService;
