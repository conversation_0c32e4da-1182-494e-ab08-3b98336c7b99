import PrismaService from './prisma.service';
import {
  CreateStripeAccount,
  StripeAccountModel,
  UpdateStripeAccount,
  StripeAccountQuery,
  StripeAccountCreationOptions
} from '../models/stripe.model';
import Stripe from 'stripe';
import { getStripeInstance } from '../config/stripe';
import { env } from '../config/environment';
import CacheService from './cache.service';

class StripeService {
  private static prisma: any;
  
  static initialize() {
    this.prisma = PrismaService.getInstance().getClient();
  }

  // ===== Stripe Account Methods =====
  
  static async createStripeAccount(data: CreateStripeAccount): Promise<StripeAccountModel> {
    return this.prisma.stripeAccount.create({
      data,
      include: {
        store: true,
      },
    });
  }

  static async getAllStripeAccounts(query: StripeAccountQuery = {}): Promise<StripeAccountModel[]> {
    return this.prisma.stripeAccount.findMany({
      where: query,
      include: {
        store: true,
      },
    });
  }

  static async getStripeAccountById(id: string): Promise<StripeAccountModel> {
    return this.prisma.stripeAccount.findUnique({
      where: { id },
      include: {
        store: true,
      },
    });
  }

  static async getStripeAccountByStoreId(storeId: string): Promise<StripeAccountModel> {
    // Check cache first
    const cached = await CacheService.getStripeAccount(storeId);
    if (cached) {
      return cached;
    }

    // Fetch from database
    const stripeAccount = await this.prisma.stripeAccount.findUnique({
      where: { storeId },
      // Remove store include to reduce payload size - frontend doesn't use store data
    });

    // Cache the result (including null)
    if (stripeAccount) {
      await CacheService.setStripeAccount(storeId, stripeAccount);
    }

    return stripeAccount;
  }

  static async updateStripeAccount(id: string, data: UpdateStripeAccount): Promise<StripeAccountModel> {
    const updatedAccount = await this.prisma.stripeAccount.update({
      where: { id },
      data,
    });

    // Clear cache for this store
    await CacheService.clearStripeAccount(updatedAccount.storeId);

    return updatedAccount;
  }

  static async deleteStripeAccount(id: string): Promise<void> {
    // Get storeId before deletion for cache cleanup
    const account = await this.prisma.stripeAccount.findUnique({
      where: { id },
      select: { storeId: true }
    });

    await this.prisma.stripeAccount.delete({
      where: { id }
    });

    // Clear cache
    if (account) {
      await CacheService.clearStripeAccount(account.storeId);
    }
  }

  /**
   * Generate OAuth URL for Standard account connection
   */
  static generateOAuthUrl(storeId: string, state?: string): string {
    const baseUrl = 'https://connect.stripe.com/oauth/authorize';
    const clientId = env.STRIPE_CLIENT_ID;
    const redirectUri = env.STRIPE_OAUTH_REDIRECT_URI;
    
    if (!clientId || !redirectUri) {
      throw new Error('Stripe OAuth configuration missing. Please set STRIPE_CLIENT_ID and STRIPE_OAUTH_REDIRECT_URI environment variables.');
    }

    const params = new URLSearchParams({
      response_type: 'code',
      client_id: clientId,
      scope: 'read_write',
      redirect_uri: redirectUri,
      state: state || storeId, // Use storeId as state to identify the connection
    });

    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * Handle OAuth callback and create Standard account connection
   */
  static async handleOAuthCallback(code: string, state: string): Promise<StripeAccountModel> {
    try {
      const stripe = getStripeInstance();
      
      // Exchange authorization code for access token
      const response = await stripe.oauth.token({
        grant_type: 'authorization_code',
        code,
      });

      const { stripe_user_id } = response;
      
      if (!stripe_user_id) {
        throw new Error('No Stripe account ID received from OAuth response');
      }
      
      // Get store information using the state parameter (storeId)
      const storeId = state;
      const store = await this.prisma.linkedStore.findUnique({
        where: { id: storeId }
      });

      if (!store) {
        throw new Error('Store not found');
      }

      // Check if store already has a Stripe account connected
      const existingAccount = await this.prisma.stripeAccount.findUnique({
        where: { storeId }
      });

      if (existingAccount) {
        // If connecting the same Stripe account, update the existing record
        if (existingAccount.stripeAccountId === stripe_user_id) {
          console.log('Updating existing Stripe account connection');
          const account = await stripe.accounts.retrieve(stripe_user_id);
          return this.updateStripeAccount(existingAccount.id, {
            status: this.determineAccountStatus(account),
          });
        } else {
          // Different Stripe account - this shouldn't happen in normal flow
          throw new Error('This store is already connected to a different Stripe account. Please disconnect first.');
        }
      }

      // Get account details from Stripe
      const account = await stripe.accounts.retrieve(stripe_user_id);

      // Save the account connection to our database
      return this.createStripeAccount({
        storeId,
        stripeAccountId: stripe_user_id,
        status: this.determineAccountStatus(account),
      });
    } catch (error: any) {
      console.error('OAuth callback error:', error);
      
      // Provide more specific error messages
      if (error.type === 'StripeInvalidRequestError') {
        if (error.code === 'authorization_code_not_found') {
          throw new Error('Invalid or expired authorization code. Please try connecting your Stripe account again.');
        }
        if (error.code === 'authorization_code_already_used') {
          throw new Error('This authorization code has already been used. Please try connecting your Stripe account again.');
        }
        if (error.message.includes('does not belong to you')) {
          throw new Error('The authorization code does not match your Stripe application. Please check your Stripe OAuth configuration.');
        }
      }
      
      throw new Error(`Failed to handle OAuth callback: ${error.message}`);
    }
  }

  /**
   * Determine account status based on Stripe account data
   */
  private static determineAccountStatus(account: Stripe.Account): string {
    if (account.details_submitted && account.charges_enabled && account.payouts_enabled) {
      return 'active';
    } else if (account.details_submitted) {
      return 'under_review';
    } else if (account.requirements?.currently_due?.length && account.requirements.currently_due.length > 0) {
      return 'requires_information';
    }
    return 'pending_verification';
  }

  static async generateOnboardingLink(accountId: string, refreshUrl: string, returnUrl: string): Promise<string> {
    try {
      const stripe = getStripeInstance();
      
      const accountLink = await stripe.accountLinks.create({
        account: accountId,
        refresh_url: refreshUrl,
        return_url: returnUrl,
        type: 'account_onboarding',
      });

      return accountLink.url;
    } catch (error: any) {
      throw new Error(`Failed to generate account link: ${error.message}`);
    }
  }

  static async getStripeAccountDetails(accountId: string): Promise<Stripe.Account> {
    try {
      const stripe = getStripeInstance();
      return await stripe.accounts.retrieve(accountId);
    } catch (error: any) {
      throw new Error(`Failed to retrieve account details: ${error.message}`);
    }
  }

  // Sync account status with Stripe and update local database
  static async syncAccountStatus(stripeAccountId: string): Promise<StripeAccountModel> {
    try {
      const stripe = getStripeInstance();
      
      // Get latest account data from Stripe
      const stripeAccount = await stripe.accounts.retrieve(stripeAccountId);
      
      // Determine account status based on Stripe data
      let status = 'pending_verification';
      if (stripeAccount.details_submitted && stripeAccount.charges_enabled && stripeAccount.payouts_enabled) {
        status = 'active';
      } else if (stripeAccount.details_submitted) {
        status = 'under_review';
      } else if (stripeAccount.requirements?.currently_due?.length && stripeAccount.requirements.currently_due.length > 0) {
        status = 'requires_information';
      }

      // Find and update the local account record
      const localAccount = await this.prisma.stripeAccount.findUnique({
        where: { stripeAccountId }
      });

      if (!localAccount) {
        throw new Error('Local Stripe account record not found');
      }

      // Update the account with latest information
      return this.updateStripeAccount(localAccount.id, {
        status,
        stripeAccountId,
      });
    } catch (error: any) {
      throw new Error(`Failed to sync account status: ${error.message}`);
    }
  }

  // Generate account link for onboarding or dashboard access
  static async generateAccountLink(stripeAccountId: string, type: 'account_onboarding' | 'account_update' = 'account_onboarding'): Promise<string> {
    try {
      const stripe = getStripeInstance();
      
      // Get the local account to determine URLs
      const localAccount = await this.prisma.stripeAccount.findUnique({
        where: { stripeAccountId },
        // Only need storeId, not full store data
      });

      if (!localAccount) {
        throw new Error('Stripe account not found in database');
      }

      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
      const refreshUrl = `${baseUrl}/settings?stripe_refresh=true&storeId=${localAccount.storeId}`;
      const returnUrl = `${baseUrl}/settings?stripe_connected=true&storeId=${localAccount.storeId}`;

      const accountLink = await stripe.accountLinks.create({
        account: stripeAccountId,
        refresh_url: refreshUrl,
        return_url: returnUrl,
        type: type,
      });

      return accountLink.url;
    } catch (error: any) {
      throw new Error(`Failed to generate account link: ${error.message}`);
    }
  }

  // Create login link for accessing Stripe dashboard
  static async createDashboardLink(stripeAccountId: string): Promise<string> {
    try {
      const stripe = getStripeInstance();
      
      const loginLink = await stripe.accounts.createLoginLink(stripeAccountId);
      return loginLink.url;
    } catch (error: any) {
      throw new Error(`Failed to create dashboard link: ${error.message}`);
    }
  }

  // Check if account requires additional information
  static async getAccountRequirements(stripeAccountId: string): Promise<{
    currently_due: string[];
    eventually_due: string[];
    past_due: string[];
  }> {
    try {
      const stripe = getStripeInstance();
      const account = await stripe.accounts.retrieve(stripeAccountId);
      
      return {
        currently_due: account.requirements?.currently_due || [],
        eventually_due: account.requirements?.eventually_due || [],
        past_due: account.requirements?.past_due || [],
      };
    } catch (error: any) {
      throw new Error(`Failed to get account requirements: ${error.message}`);
    }
  }

  // Auto-sync all accounts periodically
  static async syncAllAccounts(): Promise<void> {
    try {
      const accounts = await this.getAllStripeAccounts();
      
      for (const account of accounts) {
        if (account.stripeAccountId) {
          try {
            await this.syncAccountStatus(account.stripeAccountId);
          } catch (error) {
            console.error(`Failed to sync account ${account.stripeAccountId}:`, error);
          }
        }
      }
    } catch (error: any) {
      throw new Error(`Failed to sync all accounts: ${error.message}`);
    }
  }

  // ===== CARD SETUP METHODS =====

  /**
   * Create Stripe customer for a store user
   */
  static async createCustomer(email: string, name: string, metadata: any = {}): Promise<string> {
    try {
      const stripe = getStripeInstance();
      
      const customer = await stripe.customers.create({
        email,
        name,
        metadata,
      });

      return customer.id;
    } catch (error: any) {
      throw new Error(`Failed to create Stripe customer: ${error.message}`);
    }
  }

  /**
   * Create setup intent for card collection
   */
  static async createSetupIntent(customerId: string, storeId: string): Promise<{ clientSecret: string; customerId: string }> {
    try {
      const stripe = getStripeInstance();
      
      const setupIntent = await stripe.setupIntents.create({
        customer: customerId,
        payment_method_types: ['card'],
        usage: 'off_session',
        metadata: {
          storeId,
        },
      });

      if (!setupIntent.client_secret) {
        throw new Error('Setup intent client secret is null');
      }

      return {
        clientSecret: setupIntent.client_secret,
        customerId,
      };
    } catch (error: any) {
      throw new Error(`Failed to create setup intent: ${error.message}`);
    }
  }

  /**
   * Attach payment method to customer and set as default
   */
  static async attachPaymentMethod(paymentMethodId: string, customerId: string): Promise<void> {
    try {
      const stripe = getStripeInstance();
      
      // Attach payment method to customer
      await stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId,
      });

      // Set as default payment method
      await stripe.customers.update(customerId, {
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });
    } catch (error: any) {
      throw new Error(`Failed to attach payment method: ${error.message}`);
    }
  }

  /**
   * Get payment method details
   */
  static async getPaymentMethod(paymentMethodId: string): Promise<{
    last4: string;
    brand: string;
    country: string;
    fingerprint: string;
  }> {
    try {
      const stripe = getStripeInstance();
      
      const paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId);
      
      if (paymentMethod.type !== 'card' || !paymentMethod.card) {
        throw new Error('Payment method is not a card');
      }

      return {
        last4: paymentMethod.card.last4,
        brand: paymentMethod.card.brand,
        country: paymentMethod.card.country || 'unknown',
        fingerprint: paymentMethod.card.fingerprint || '',
      };
    } catch (error: any) {
      throw new Error(`Failed to get payment method: ${error.message}`);
    }
  }

  /**
   * Update Stripe account with card information
   */
  static async updateWithCardInfo(accountId: string, cardData: {
    stripeCustomerId: string;
    paymentMethodId: string;
    cardLast4: string;
    cardBrand: string;
    cardCountry: string;
    cardholderName: string;
    billingEmail: string;
  }): Promise<StripeAccountModel> {
    try {
      return this.updateStripeAccount(accountId, {
        stripeCustomerId: cardData.stripeCustomerId,
        paymentMethodId: cardData.paymentMethodId,
        cardLast4: cardData.cardLast4,
        cardBrand: cardData.cardBrand,
        cardCountry: cardData.cardCountry,
        cardholderName: cardData.cardholderName,
        billingEmail: cardData.billingEmail,
        setupCompleted: true,
      });
    } catch (error: any) {
      throw new Error(`Failed to update account with card info: ${error.message}`);
    }
  }

  /**
   * Mark card setup as completed
   */
  static async markSetupCompleted(accountId: string): Promise<StripeAccountModel> {
    try {
      return this.updateStripeAccount(accountId, {
        setupCompleted: true,
      });
    } catch (error: any) {
      throw new Error(`Failed to mark setup as completed: ${error.message}`);
    }
  }

}

// Initialize Stripe when module is loaded
StripeService.initialize();

export default StripeService;
export { StripeService };