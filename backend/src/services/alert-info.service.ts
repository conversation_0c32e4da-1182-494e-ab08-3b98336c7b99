import { PrismaClient } from '@prisma/client';
import { alertInfoSchema, updateAlertInfoSchema, RegistrationStatus, AlertType } from '../models/alert-info.model';
import axios from 'axios';
import { env } from '@/config/environment';
import { sign } from '@/utils/signature.utils';
import logger from '@/utils/logger.util';
import CacheService from './cache.service';

const prisma = new PrismaClient();

interface AlertInfoServiceConfig {
  merchantNo: string;
  signKey: string;
  apiEndpoint: string;
  callbackUrl: string;
}

const config: AlertInfoServiceConfig = {
  merchantNo: env.EARLY_WARNING_MERCHANT_NO || "",
  signKey: env.EARLY_WARNING_SIGN_KEY || "",
  apiEndpoint: env.EARLY_WARNING_API_ENDPOINT || "https://mer.tradefensor.com",
  callbackUrl: env.EARLY_WARNING_CALLBACK_URL || "https://api.quantchargeback.com/early-warning/webhook",
};

/**
 * Create a new alert info and automatically register with merchant alert
 * @param data - alert info data
 * @returns created alert info with registration result
 */
export const createAlertInfo = async (data: any) => {
  // Validate data using Joi schema
  const { error, value } = alertInfoSchema.validate(data);
  if (error) {
    throw new Error(`Validation error: ${error.message}`);
  }

  // Check if the store exists
  const store = await prisma.linkedStore.findUnique({
    where: { id: data.storeId },
  });

  if (!store) {
    throw new Error('Store not found');
  }

  // Create alert info first
  const alertInfo = await prisma.alertInfo.create({
    data: {
      alertType: value.alertType,
      descriptor: value.descriptor || '',
      bin: value.bin || null,
      caid: value.caid || null,
      arn: value.arn || null,
      storeId: value.storeId,
      registrationStatus: RegistrationStatus.WAITING,
    },
  });

  // Automatically register with merchant alert
  try {
    const notifyUrl = `${config.callbackUrl}/alert-info/${alertInfo.id}`;
    // Prepare registration data according to API documentation
    const registrationData = alertInfo.alertType == AlertType.ETHOCA ? {
      notifyUrl,
      alertType: "Ethoca",
      descriptor: alertInfo.descriptor,
      // dba: null //DBA
      // mcc: null, //MCC
      // website: null, //Website
      // arn: null, //ARN
    } : {
      notifyUrl,
      alertType: "RDR",
      descriptor: alertInfo.descriptor,
      cardBin: alertInfo.bin,
      caid: alertInfo.caid,
      // dba: null //DBA
      // mcc: null, //MCC
      // website: null, //Website
      // arn: null, //ARN
    };

    // Generate signature using the sign utility
    const signKey = sign(registrationData, config.signKey);

    // Call TradeDefensor API for registration
    const response = await axios.post(
      `${config.apiEndpoint}/rest/third/predictor/merchant/alert/add`,
      registrationData,
      {
        headers: {
          'Content-Type': 'application/json',
          MerchantNo: config.merchantNo,
          Sign: signKey,
        }
      }
    );
    logger.info("Response: ", response.data);
    logger.info("Register merchant alert response: ", response.data);

    // Clear cache for this store when new alert info is created
    await CacheService.clearAlertInfo(data.storeId);

    return {
      success: true,
      data: alertInfo,
    };

  } catch (error: any) {
    return {
      success: false,
      data: alertInfo,
      error: `Registration failed: ${error.message}`
    };
  }
};

/**
 * Get all alert info for a store
 * @param storeId - store id
 * @returns list of alert info
 */
export const getAlertInfosByStoreId = async (storeId: string) => {
  // Check cache first
  const cached = await CacheService.getAlertInfo(storeId);
  if (cached) {
    return cached;
  }

  // Fetch from database
  const alertInfos = await prisma.alertInfo.findMany({
    where: { storeId },
    orderBy: { createdAt: 'desc' },
  });

  // Cache the result
  await CacheService.setAlertInfo(storeId, alertInfos);

  return alertInfos;
};

/**
 * Get alert info by id
 * @param id - alert info id
 * @returns alert info
 */
export const getAlertInfoById = async (id: string) => {
  return prisma.alertInfo.findUnique({
    where: { id },
    include: { store: true }
  });
};

/**
 * Update alert info
 * @param id - alert info id
 * @param data - alert info data
 * @returns updated alert info
 */
export const updateAlertInfo = async (id: string, data: any) => {
  // Validate data using Joi schema
  const { error, value } = updateAlertInfoSchema.validate(data);
  if (error) {
    throw new Error(`Validation error: ${error.message}`);
  }

  // Check if alert info exists
  const alertInfo = await prisma.alertInfo.findUnique({
    where: { id },
  });

  if (!alertInfo) {
    throw new Error('Alert info not found');
  }

  const updatedAlertInfo = await prisma.alertInfo.update({
    where: { id },
    data: value,
  });

  // Clear cache for this store
  await CacheService.clearAlertInfo(alertInfo.storeId);

  return updatedAlertInfo;
};

/**
 * Delete alert info and close merchant registration if registered
 * @param id - alert info id
 * @returns deletion result
 */
export const deleteAlertInfo = async (id: string) => {
  // Check if alert info exists
  const alertInfo = await prisma.alertInfo.findUnique({
    where: { id },
  });

  if (!alertInfo) {
    throw new Error('Alert info not found');
  }
  try {
    const notifyUrl = `${config.callbackUrl}/alert-info/${alertInfo.id}`;
    // Prepare closure data
    const closureData = alertInfo.alertType == AlertType.ETHOCA ? {
      notifyUrl,
      alertType: "Ethoca",
      descriptor: alertInfo.descriptor,
    } : {
      notifyUrl,
      alertType: "RDR",
      descriptor: alertInfo.descriptor,
      cardBin: alertInfo.bin,
      caid: alertInfo.caid,
    };

    // Generate signature using the sign utility
    const signKey = sign(closureData, config.signKey);

    // Call TradeDefensor API for closure
    const response = await axios.post(
      `${config.apiEndpoint}/rest/third/predictor/merchant/alert/close`,
      closureData,
      {
        headers: {
          'Content-Type': 'application/json',
          MerchantNo: config.merchantNo,
          Sign: signKey,
        }
      }
    );

    logger.info('Merchant alert registration closed before deletion');
    logger.info('Merchant alert registration closed before deletion: ', response.data);
  } catch (error: any) {
    // Log the error but continue with deletion
    logger.error('Failed to close merchant registration before deletion:', error);
  }

  // Delete the alert info
  const deletedAlertInfo = await prisma.alertInfo.update({
    where: { id },
    data: {
      registrationStatus: RegistrationStatus.CLOSING,
    },
  });

  // Clear cache for this store
  await CacheService.clearAlertInfo(alertInfo.storeId);

  return {
    success: true,
    data: deletedAlertInfo,
    message: 'Alert info deleted successfully'
  };
};


/**
 * 商户卡账单状态结果通知 - Handle webhook notification for registration status
 * @param webhookData - webhook notification data
 * @returns processing result
 */
export const handleRegistrationStatusNotification = async (webhookData: any) => {
  try {
    // For webhook verification, you would typically use a different verification method
    // or the verifySignature function from signature.utils if available
    // This is just a placeholder for webhook handling

    const {  alertInfoId,
      status,
      message,
      alertType,
      descriptor,
      dba,
      mcc,
      cardBin,
      caid,
      website,
      arn } = webhookData;

    // Find alert info by registration ID or alert info ID
    const alertInfo = await prisma.alertInfo.findFirst({
      where: { id: alertInfoId },
    });

    if (!alertInfo) {
      throw new Error('Alert info not found for webhook notification');
    }

    // Map webhook status to our registration status

    // Update alert info with webhook data
    const updatedAlertInfo = await prisma.alertInfo.update({
      where: { id: alertInfo.id },
      data: {
        registrationStatus: status,
        registrationMessage: message,
        registeredAt: status === RegistrationStatus.EFFECTED ? new Date() : alertInfo.registeredAt || null,
        closedAt: status === RegistrationStatus.CLOSED ? new Date() : null,
      }
    });

    // Clear cache for this store when registration status is updated
    await CacheService.clearAlertInfo(alertInfo.storeId);

    return {
      success: true,
      data: updatedAlertInfo,
      message: 'Webhook processed successfully'
    };

  } catch (error: any) {
    console.error('Webhook processing error:', error);
    throw new Error(`Webhook processing failed: ${error.message}`);
  }
};


