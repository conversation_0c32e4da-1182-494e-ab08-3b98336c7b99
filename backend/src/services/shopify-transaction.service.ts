import PrismaService from './prisma.service';
import {
  CreateShopifyTransaction,
  ShopifyTransactionModel,
  ShopifyTransactionQuery
} from '../models/shopify-transaction.model';

class ShopifyTransactionService {
  private static prisma: any;
  
  static initialize() {
    this.prisma = PrismaService.getInstance().getClient();
  }

  /**
   * Create a new Shopify transaction
   * @param data Shopify transaction data
   * @returns Created transaction
   */
  static async createTransaction(data: CreateShopifyTransaction): Promise<ShopifyTransactionModel> {
    return this.prisma.shopifyTransaction.create({
      data
    });
  }

  /**
   * Create multiple Shopify transactions
   * @param data Array of Shopify transaction data
   * @returns Number of created transactions
   */
  static async createManyTransactions(data: CreateShopifyTransaction[]): Promise<number> {
    const result = await this.prisma.shopifyTransaction.createMany({
      data,
      skipDuplicates: true
    });
    return result.count;
  }

  /**
   * Get all Shopify transactions with optional filtering
   * @param query Optional query parameters
   * @returns Array of transactions
   */
  static async getAllTransactions(query: ShopifyTransactionQuery = {}): Promise<ShopifyTransactionModel[]> {
    return this.prisma.shopifyTransaction.findMany({
      where: query,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        order: true,
        payout: true
      }
    });
  }

  /**
   * Get Shopify transactions for a specific linked store
   * @param linkedStoreId Linked store ID
   * @returns Array of transactions for the store
   */
  static async getTransactionsByLinkedStoreId(linkedStoreId: string): Promise<ShopifyTransactionModel[]> {
    return this.prisma.shopifyTransaction.findMany({
      where: { linkedStoreId },
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        order: true,
        payout: true
      }
    });
  }

  /**
   * Get Shopify transactions for a specific order
   * @param orderId Order ID
   * @returns Array of transactions for the order
   */
  static async getTransactionsByOrderId(orderId: bigint): Promise<ShopifyTransactionModel[]> {
    return this.prisma.shopifyTransaction.findMany({
      where: { orderId },
      orderBy: {
        processedAt: 'desc'
      },
      include: {
        payout: true,
        disputes: true
      }
    });
  }

  /**
   * Get Shopify transactions for a specific payout
   * @param payoutId Payout ID
   * @returns Array of transactions for the payout
   */
  static async getTransactionsByPayoutId(payoutId: bigint): Promise<ShopifyTransactionModel[]> {
    return this.prisma.shopifyTransaction.findMany({
      where: { payoutId },
      orderBy: {
        processedAt: 'desc'
      },
      include: {
        order: true,
        disputes: true
      }
    });
  }

  /**
   * Get a specific Shopify transaction by ID
   * @param id Transaction ID
   * @returns Transaction or null if not found
   */
  static async getTransactionById(id: bigint): Promise<ShopifyTransactionModel | null> {
    return this.prisma.shopifyTransaction.findUnique({
      where: { id },
      include: {
        order: true,
        payout: true,
        disputes: true
      }
    });
  }

  /**
   * Update an existing Shopify transaction
   * @param id Transaction ID
   * @param data Updated transaction data
   * @returns Updated transaction
   */
  static async updateTransaction(id: bigint, data: Partial<CreateShopifyTransaction>): Promise<ShopifyTransactionModel> {
    return this.prisma.shopifyTransaction.update({
      where: { id },
      data
    });
  }

  /**
   * Delete a Shopify transaction
   * @param id Transaction ID
   * @returns void
   */
  static async deleteTransaction(id: bigint): Promise<void> {
    await this.prisma.shopifyTransaction.delete({
      where: { id }
    });
  }

  /**
   * Count Shopify transactions that match the given criteria
   * @param query Query parameters
   * @returns Count of matching transactions
   */
  static async countTransactions(query: ShopifyTransactionQuery = {}): Promise<number> {
    return this.prisma.shopifyTransaction.count({
      where: query
    });
  }

  /**
   * Get transactions with pagination
   * @param page Page number (1-based)
   * @param limit Items per page
   * @param query Optional query parameters
   * @returns Paginated transactions
   */
  static async getPaginatedTransactions(
    page: number = 1, 
    limit: number = 10,
    query: ShopifyTransactionQuery = {}
  ): Promise<{ data: ShopifyTransactionModel[], total: number, page: number, limit: number }> {
    const skip = (page - 1) * limit;
    
    const [data, total] = await Promise.all([
      this.prisma.shopifyTransaction.findMany({
        where: query,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          order: true,
          payout: true
        }
      }),
      this.prisma.shopifyTransaction.count({ where: query })
    ]);

    return {
      data,
      total,
      page,
      limit
    };
  }
}

// Initialize when module is loaded
ShopifyTransactionService.initialize();

export default ShopifyTransactionService;
