import PrismaService from './prisma.service';
import logger from '@/utils/logger.util';

const prisma = PrismaService.getInstance().getClient();

export interface UsageSummary {
  period: string; // YYYY-MM
  totalBlocks: number;
  totalAmount: number; // in cents
  ethokaBlocks: number;
  rdrBlocks: number;
  planLimit: number;
  planTier: string;
  usagePercentage: number;
  isOverLimit: boolean;
  remainingBlocks: number;
}

export interface BillingPeriod {
  period: string; // YYYY-MM
  startDate: Date;
  endDate: Date;
  totalUsage: number; // in cents
  blockCount: number;
  status: 'current' | 'past';
}

// Plan configuration - Single free plan with usage-based topups
export const PLAN_CONFIG = {
  name: 'Free with Usage-Based Billing',
  blockLimit: 999999, // No block limit
  monthlyPrice: 0, // No monthly fee
  description: 'Free to use, pay only for what you process via automatic $100 topups'
};

// Maximum monthly charge allowed by Shopify
const MAX_MONTHLY_CHARGE = 79999; // $799.99 in cents

class UsageTrackingService {
  /**
   * Track a block being processed
   */
  async trackBlockProcessed(storeId: string, blockType: 'ETHOCA' | 'RDR'): Promise<void> {
    try {
      const period = this.getCurrentPeriod();
      
      await prisma.usageTracking.create({
        data: {
          storeId,
          blockType,
          period,
          timestamp: new Date()
        }
      });
      
      logger.info(`Tracked ${blockType} block for store ${storeId} in period ${period}`);
    } catch (error: any) {
      logger.error('Error tracking block usage:', error);
      throw error;
    }
  }

  /**
   * Track a topup as usage (for Shopify billing)
   */
  async trackTopup(storeId: string, amount: number): Promise<boolean> {
    try {
      const period = this.getCurrentPeriod();
      
      // Check if adding this topup would exceed monthly limit
      const currentUsage = await this.getCurrentPeriodUsage(storeId);
      if (currentUsage.totalAmount + amount > MAX_MONTHLY_CHARGE) {
        logger.warn(`Topup would exceed monthly limit for store ${storeId}. Current: ${currentUsage.totalAmount}, Topup: ${amount}`);
        return false;
      }
      
      // Create a usage record for the topup
      // We'll track it as a special "TOPUP" type
      await prisma.usageTracking.create({
        data: {
          storeId,
          blockType: 'TOPUP' as any, // We'll need to update the schema to support this
          period,
          timestamp: new Date(),
          amount // Store the topup amount
        }
      });
      
      logger.info(`Tracked topup of $${amount / 100} for store ${storeId} in period ${period}`);
      return true;
    } catch (error: any) {
      logger.error('Error tracking topup:', error);
      return false;
    }
  }

  /**
   * Get current billing period (YYYY-MM format)
   */
  getCurrentPeriod(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  }

  /**
   * Get default plan configuration
   * Since we only have one plan (free with usage-based billing),
   * we return the same configuration for all stores
   */
  getDefaultPlanConfig() {
    return {
      planTier: 'free',
      blockLimit: PLAN_CONFIG.blockLimit,
      monthlyPrice: PLAN_CONFIG.monthlyPrice,
      description: PLAN_CONFIG.description
    };
  }

  /**
   * Get usage summary for current period
   */
  async getCurrentPeriodUsage(storeId: string): Promise<UsageSummary> {
    const period = this.getCurrentPeriod();
    const planConfig = this.getDefaultPlanConfig();
    
    const usage = await prisma.usageTracking.findMany({
      where: {
        storeId,
        period
      }
    });
    
    const ethokaBlocks = usage.filter(u => u.blockType === 'ETHOCA').length;
    const rdrBlocks = usage.filter(u => u.blockType === 'RDR').length;
    const topups = usage.filter(u => u.blockType === 'TOPUP');
    const totalBlocks = usage.filter(u => u.blockType !== 'TOPUP').length; // Don't count topups as blocks
    
    // Calculate total amount (only topups count as usage for Shopify billing)
    const topupAmount = topups.reduce((sum, t) => sum + (t.amount || 0), 0);
    const totalAmount = topupAmount;
    
    
    return {
      period,
      totalBlocks,
      totalAmount,
      ethokaBlocks,
      rdrBlocks,
      planLimit: planConfig.blockLimit,
      planTier: planConfig.planTier,
      usagePercentage: 0, // No block limits in free plan
      isOverLimit: false, // No block limits in free plan
      remainingBlocks: 999999 // Unlimited blocks in free plan
    };
  }

  /**
   * Check if store can process more blocks
   */
  async canProcessBlock(storeId: string): Promise<boolean> {
    const usage = await this.getCurrentPeriodUsage(storeId);
    
    // Only check monthly dollar limit since we have unlimited blocks
    if (usage.totalAmount >= MAX_MONTHLY_CHARGE) {
      logger.warn(`Store ${storeId} hit monthly charge limit of $${MAX_MONTHLY_CHARGE / 100}`);
      return false;
    }
    
    // Free plan always allows processing (up to dollar limit)
    return true;
  }

  /**
   * Get billing history for a store
   */
  async getBillingHistory(storeId: string, limit: number = 12): Promise<BillingPeriod[]> {
    // Get all unique periods for this store
    const periods = await prisma.usageTracking.findMany({
      where: { storeId },
      select: { period: true },
      distinct: ['period'],
      orderBy: { period: 'desc' },
      take: limit
    });
    
    const currentPeriod = this.getCurrentPeriod();
    const billingPeriods: BillingPeriod[] = [];
    
    for (const { period } of periods) {
      const usage = await prisma.usageTracking.findMany({
        where: {
          storeId,
          period
        }
      });
      
      const blockCount = usage.filter(u => u.blockType !== 'TOPUP').length;
      const topups = usage.filter(u => u.blockType === 'TOPUP');
      const totalUsage = topups.reduce((sum, t) => sum + (t.amount || 0), 0);
      
      // Parse period to get dates
      const [year, month] = period.split('-').map(Number);
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0);
      
      billingPeriods.push({
        period,
        startDate,
        endDate,
        totalUsage,
        blockCount,
        status: period === currentPeriod ? 'current' : 'past'
      });
    }
    
    return billingPeriods;
  }

  /**
   * Get detailed usage for a specific period
   */
  async getPeriodDetails(storeId: string, period: string) {
    const usage = await prisma.usageTracking.findMany({
      where: {
        storeId,
        period
      },
      orderBy: {
        timestamp: 'desc'
      }
    });
    
    const byDay = new Map<string, { count: number; amount: number }>();
    
    usage.forEach(u => {
      const day = u.timestamp.toISOString().split('T')[0];
      
      if (!byDay.has(day)) {
        byDay.set(day, { count: 0, amount: 0 });
      }
      
      const dayData = byDay.get(day)!;
      
      if (u.blockType === 'TOPUP') {
        // For topups, use the stored amount
        dayData.amount += (u.amount || 0);
      } else {
        // For blocks, just count them (no charge)
        dayData.count++;
      }
    });
    
    return {
      period,
      totalBlocks: usage.filter(u => u.blockType !== 'TOPUP').length,
      totalAmount: usage.filter(u => u.blockType === 'TOPUP').reduce((sum, u) => 
        sum + (u.amount || 0), 0
      ),
      dailyBreakdown: Array.from(byDay.entries()).map(([date, data]) => ({
        date,
        count: data.count,
        amount: data.amount
      })).sort((a, b) => a.date.localeCompare(b.date))
    };
  }

  /**
   * Get plan display name
   */
  getPlanDisplayName(): string {
    return PLAN_CONFIG.name;
  }
}

export const usageTrackingService = new UsageTrackingService();