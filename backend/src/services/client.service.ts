import { RedisPrefix } from "@/constants/prefix";
import crypto from "crypto";
import redisClient from "@/config/redis";
import PrismaService from "./prisma.service";
import IClient from "@/models/client.model";

const prisma = PrismaService.getInstance().getClient();

class ClientService {
  static async create(
    client: Omit<IClient, "id" | "createdAt" | "updatedAt" | "APIKey">
  ) {
    const APIKey = crypto.randomBytes(20).toString("hex");
    const newClient = await prisma.client.create({
      data: { ...client, APIKey },
    });
    return newClient;
  }

  static async isClientExists(email: string) {
    const existingClient = await prisma.client.findUnique({
      where: { email },
    });
    return !!existingClient;
  }

  static async isAPIKeyValid(APIKey: string) {
    const cacheKey = `${RedisPrefix.API_KEY}_${APIKey}`;

    // Check if the API key is in the Redis cache
    const cachedAPIKey = await redisClient.get(cacheKey);
    if (cachedAPIKey) {
      return cachedAPIKey === "true";
    }

    // If not in cache, check the database
    const existingClient = await prisma.client.findFirst({
      where: { APIKey },
    });
    const isValid = !!existingClient;

    // Cache the result in Redis with a TTL of 1 day (60 * 60 * 24 seconds)
    await redisClient.setEx(cacheKey, 24 * 60 * 60, isValid.toString());
    return isValid;
  }
}

export default ClientService;
