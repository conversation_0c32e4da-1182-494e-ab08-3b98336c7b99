import axios from 'axios';
import crypto from 'crypto';
import { ServiceResponse } from '@/constants/type';
import { StatusCodes } from 'http-status-codes';
import { getI18n } from '@/middlewares/language.middleware';
import PrismaService from './prisma.service';
import { env } from '@/config/environment';

const prisma = PrismaService.getInstance().getClient();

// Types for Chargeback Dispute Service
interface ChargebackDisputeConfig {
  merchantNo: string;
  signKey: string;
  apiEndpoint: string;
}

interface DisputeRequest {
  Chargeback: {
    cb_id: string;
    cb_reason_code: string;
    cb_date: string;
    cb_amount: number;
    cb_currency: string;
    [key: string]: any;
  };
  Payment: {
    transaction_id: string;
    transaction_date: string;
    transaction_amount: number;
    transaction_currency: string;
    [key: string]: any;
  };
  "Seller&buyer": {
    seller_name: string;
    seller_id: string;
    buyer_name: string;
    buyer_id: string;
    [key: string]: any;
  };
  Contract: {
    [key: string]: any;
  };
  Products: {
    [key: string]: any;
  };
  Quality: {
    [key: string]: any;
  };
  Shipping: {
    [key: string]: any;
  };
  "Return&Refund": {
    [key: string]: any;
  };
  Aftersales: {
    [key: string]: any;
  };
  History: {
    [key: string]: any;
  };
  Statement: {
    [key: string]: any;
  };
  [key: string]: any;
}

interface DisputeResult {
  merchant_order_no: string;
  order_status: 'Waiting_Mer_materials' | 'SubmitDownload';
  downPath?: string;
  sign: string;
  order_results: Array<{
    order_no: string;
    judgment_result: string;
    judgment_amount: number;
    judgment_currency: string;
    eligibility_note: string;
  }>;
}

class ChargebackDisputeService {
  private static config: ChargebackDisputeConfig = {
    merchantNo: env.CHARGEBACK_MERCHANT_NO || '',
    signKey: env.CHARGEBACK_SIGN_KEY || '',
    apiEndpoint: env.CHARGEBACK_API_ENDPOINT || 'https://mer.tradefensor.com',
  };

  /**
   * Submit chargeback dispute
   * Submits a detailed dispute case to challenge a chargeback
   */
  static submitDispute = async (disputeData: DisputeRequest): Promise<ServiceResponse<DisputeResult>> => {
    const i18n = getI18n();
    try {
      // Add signature to the dispute data
      const signedData = this.addSignature(disputeData);
      
      // Make API request
      const response = await axios.post(
        `${this.config.apiEndpoint}/api/dispute/submit`, 
        signedData,
        {
          headers: {
            'Content-Type': 'application/json',
            'MerchantNo': this.config.merchantNo
          }
        }
      );
      
      const result = response.data;
      
      // Verify the response signature
      if (!this.verifySignature(result)) {
        return {
          success: false,
          status: StatusCodes.BAD_REQUEST,
          message: i18n.t('error.chargebackDispute.invalidSignature'),
          data: undefined,
        };
      }
      
      return {
        success: true,
        status: StatusCodes.OK,
        message: result.order_status === 'SubmitDownload' 
          ? i18n.t('success.chargebackDispute.documentReady') 
          : i18n.t('success.chargebackDispute.waitingForMaterials'),
        data: result,
      };
    } catch (error: any) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.chargebackDispute.internalError'),
        data: undefined,
      };
    }
  };

  /**
   * Check dispute status
   * Retrieves the current status of a previously submitted dispute
   */
  static checkDisputeStatus = async (merchantOrderNo: string): Promise<ServiceResponse<DisputeResult>> => {
    const i18n = getI18n();
    try {
      // Prepare request data with order number
      const requestData = {
        merchant_order_no: merchantOrderNo
      };
      
      // Add signature
      const signedData = this.addSignature(requestData);
      
      // Make API request
      const response = await axios.post(
        `${this.config.apiEndpoint}/api/dispute/status`, 
        signedData,
        {
          headers: {
            'Content-Type': 'application/json',
            'MerchantNo': this.config.merchantNo
          }
        }
      );
      
      const result = response.data;
      
      // Verify the response signature
      if (!this.verifySignature(result)) {
        return {
          success: false,
          status: StatusCodes.BAD_REQUEST,
          message: i18n.t('error.chargebackDispute.invalidSignature'),
          data: undefined,
        };
      }
      
      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t('success.chargebackDispute.statusRetrieved'),
        data: result,
      };
    } catch (error: any) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.chargebackDispute.internalError'),
        data: undefined,
      };
    }
  };

  /**
   * Submit additional materials for a dispute case
   */
  static submitAdditionalMaterials = async (
    merchantOrderNo: string, 
    materials: any
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Prepare request data
      const requestData = {
        merchant_order_no: merchantOrderNo,
        ...materials
      };
      
      // Add signature
      const signedData = this.addSignature(requestData);
      
      // Make API request
      const response = await axios.post(
        `${this.config.apiEndpoint}/api/dispute/additional-materials`, 
        signedData,
        {
          headers: {
            'Content-Type': 'application/json',
            'MerchantNo': this.config.merchantNo
          }
        }
      );
      
      const result = response.data;
      
      // Verify the response signature
      if (!this.verifySignature(result)) {
        return {
          success: false,
          status: StatusCodes.BAD_REQUEST,
          message: i18n.t('error.chargebackDispute.invalidSignature'),
          data: undefined,
        };
      }
      
      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t('success.chargebackDispute.materialsSubmitted'),
        data: result,
      };
    } catch (error: any) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.chargebackDispute.internalError'),
        data: undefined,
      };
    }
  };

  /**
   * Add MD5 Salt signature to the request data
   */
  private static addSignature = (data: any) => {
    // Create a copy of the data
    const signedData = { ...data };
    
    // Sort all parameters in ascending order of parameter name (ASCII)
    const sortedParams = Object.keys(signedData).sort();
    
    // Concatenate key-value pairs with &
    let signString = '';
    sortedParams.forEach(key => {
      if (signString) signString += '&';
      // Need to handle nested objects here by flattening or other appropriate method
      // Simple implementation for basic objects:
      if (typeof signedData[key] !== 'object') {
        signString += `${key}=${signedData[key]}`;
      } else {
        // For nested objects, you may need custom handling based on API requirements
        signString += `${key}=${JSON.stringify(signedData[key])}`;
      }
    });
    
    // Add SignKey to the end
    signString += this.config.signKey;
    
    // Calculate MD5 hash
    const sign = crypto.createHash('md5').update(signString).digest('hex');
    
    // Add sign to the data
    signedData.sign = sign;
    
    return signedData;
  };

  /**
   * Verify the signature of the response
   */
  private static verifySignature = (data: any) => {
    // Extract and remove the sign from data
    const { sign, ...dataWithoutSign } = data;
    
    // Sort all parameters in ascending order of parameter name (ASCII)
    const sortedParams = Object.keys(dataWithoutSign).sort();
    
    // Concatenate key-value pairs with &
    let signString = '';
    sortedParams.forEach(key => {
      if (signString) signString += '&';
      // Need to handle nested objects here by flattening or other appropriate method
      if (typeof dataWithoutSign[key] !== 'object') {
        signString += `${key}=${dataWithoutSign[key]}`;
      } else {
        // For nested objects, custom handling based on API requirements
        signString += `${key}=${JSON.stringify(dataWithoutSign[key])}`;
      }
    });
    
    // Add SignKey to the end
    signString += this.config.signKey;
    
    // Calculate MD5 hash
    const calculatedSign = crypto.createHash('md5').update(signString).digest('hex');
    
    // Compare calculated sign with the one in the response
    return calculatedSign === sign;
  };

  /**
   * Get chargebacks by linked store ID
   * Retrieves all chargebacks (disputes) for a specific linked store
   */
  static getChargebacksByStoreId = async (
    linkedStoreId: string, 
    options?: { 
      page?: number; 
      pageSize?: number; 
      status?: string;
      startDate?: Date;
      endDate?: Date;
    }
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      const page = options?.page || 1;
      const pageSize = options?.pageSize || 10;
      const skip = (page - 1) * pageSize;

      // Get linked store to check provider
      const linkedStore = await prisma.linkedStore.findUnique({
        where: { id: linkedStoreId },
        select: { provider: true }
      });

      if (!linkedStore) {
        return {
          success: false,
          status: StatusCodes.NOT_FOUND,
          message: i18n.t('error.store.notFound'),
          data: undefined,
        };
      }

      let chargebacks: any[] = [];
      let totalCount = 0;

      // Build where clause
      const whereClause: any = { linkedStoreId };
      if (options?.status) {
        // Map frontend status to database status patterns
        const statusMapping: Record<string, string[]> = {
          'open': ['needs_response', 'warning_needs_response'],
          'submitted': ['under_review', 'UNDER_REVIEW', 'warning_under_review'],
          'won': ['won', 'WON', 'accepted'],
          'lost': ['lost', 'LOST', 'charge_refunded']
        };
        
        const dbStatuses = statusMapping[options.status.toLowerCase()];
        if (dbStatuses) {
          whereClause.status = { in: dbStatuses };
        } else {
          whereClause.status = options.status;
        }
      }
      if (options?.startDate || options?.endDate) {
        whereClause.createdAt = {};
        if (options.startDate) {
          whereClause.createdAt.gte = options.startDate;
        }
        if (options.endDate) {
          whereClause.createdAt.lte = options.endDate;
        }
      }

      if (linkedStore.provider === 'shopify') {
        // Get Shopify disputes
        const [shopifyDisputes, count] = await Promise.all([
          prisma.shopifyDispute.findMany({
            where: whereClause,
            skip,
            take: pageSize,
            orderBy: { createdAt: 'desc' },
            include: {
              transaction: {
                include: {
                  order: true
                }
              }
            }
          }),
          prisma.shopifyDispute.count({ where: whereClause })
        ]);

        chargebacks = shopifyDisputes.map(dispute => {
          // Get amount from dispute or transaction (already in cents)
          const amountInCents = dispute.amount || dispute.transaction?.amount || 0;
          
          // Use actual reason from database
          
          return {
            id: dispute.id.toString(),
            provider: 'shopify',
            transactionId: dispute.transactionId?.toString() || null,
            orderId: dispute.transaction?.orderId?.toString() || dispute.orderId?.toString(),
            orderName: dispute.transaction?.order?.name || null,
            status: dispute.status ? dispute.status.toLowerCase() : null,
            amount: amountInCents / 100, // Convert from cents to dollars
            currency: dispute.currency || dispute.transaction?.currency || null,
            reason: dispute.reason || null,
            type: dispute.type || null,
            initiatedAt: dispute.initiatedAt,
            evidenceDueBy: dispute.evidenceDueBy,
            evidenceSentOn: dispute.evidenceSentOn,
            finalizedOn: dispute.finalizedOn,
            networkReasonCode: dispute.networkReasonCode || null,
            createdAt: dispute.createdAt,
            updatedAt: dispute.updatedAt
          };
        });
        totalCount = count;

      } else if (linkedStore.provider === 'stripe') {
        // Get Stripe disputes
        const [stripeDisputes, count] = await Promise.all([
          prisma.stripeDispute.findMany({
            where: whereClause,
            skip,
            take: pageSize,
            orderBy: { createdAt: 'desc' },
            include: {
              charge: {
                include: {
                  paymentIntent: true
                }
              }
            }
          }),
          prisma.stripeDispute.count({ where: whereClause })
        ]);

        chargebacks = stripeDisputes.map(dispute => ({
          id: dispute.id,
          provider: 'stripe',
          chargeId: dispute.chargeId,
          paymentIntentId: dispute.charge?.paymentIntentId,
          status: dispute.status,
          amount: Number(dispute.amount) / 100, // Convert from cents
          currency: dispute.currency,
          reason: dispute.reason,
          networkReasonCode: dispute.networkReasonCode,
          evidence: dispute.evidence,
          evidenceDetails: dispute.evidenceDetails,
          isChargeRefundable: dispute.isChargeRefundable,
          metadata: dispute.metadata,
          createdAt: dispute.createdAt,
          updatedAt: dispute.updatedAt
        }));
        totalCount = count;
      }

      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t('success.chargebackDispute.chargebacksRetrieved'),
        data: {
          chargebacks,
          pagination: {
            page,
            pageSize,
            totalCount,
            totalPages: Math.ceil(totalCount / pageSize)
          }
        },
      };
    } catch (error: any) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.chargebackDispute.internalError'),
        data: undefined,
      };
    }
  };

  /**
   * Normalize status counts by grouping similar statuses
   */
  private static normalizeStatusCounts = (statusGroups: any[]): Record<string, number> => {
    const normalized: Record<string, number> = {
      open: 0,
      submitted: 0,
      won: 0,
      lost: 0
    };

    statusGroups.forEach(item => {
      const status = (item.status || '').toLowerCase();
      const count = item._count.status;

      if (status === 'needs_response' || status === 'warning_needs_response') {
        normalized.open += count;
      } else if (status === 'under_review' || status === 'warning_under_review') {
        normalized.submitted += count;
      } else if (status === 'won' || status === 'accepted') {
        normalized.won += count;
      } else if (status === 'lost' || status === 'charge_refunded') {
        normalized.lost += count;
      }
    });

    return normalized;
  };

  /**
   * Get chargeback statistics by linked store ID
   */
  static getChargebackStats = async (linkedStoreId: string): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Get linked store to check provider
      const linkedStore = await prisma.linkedStore.findUnique({
        where: { id: linkedStoreId },
        select: { provider: true }
      });

      if (!linkedStore) {
        return {
          success: false,
          status: StatusCodes.NOT_FOUND,
          message: i18n.t('error.store.notFound'),
          data: undefined,
        };
      }

      let stats: any = {};

      if (linkedStore.provider === 'shopify') {
        // Get Shopify dispute statistics with transaction amounts
        const [total, byStatus, disputes] = await Promise.all([
          prisma.shopifyDispute.count({ where: { linkedStoreId } }),
          prisma.shopifyDispute.groupBy({
            by: ['status'],
            where: { linkedStoreId },
            _count: { status: true }
          }),
          prisma.shopifyDispute.findMany({
            where: { linkedStoreId },
            include: {
              transaction: true
            }
          })
        ]);

        // Calculate total amount from disputes and their transactions (convert from cents)
        const totalAmountInCents = disputes.reduce((sum, dispute) => {
          const amount = dispute.amount || dispute.transaction?.amount || 0;
          return sum + amount;
        }, 0);

        stats = {
          provider: 'shopify',
          total,
          totalAmount: totalAmountInCents / 100, // Convert from cents to dollars
          byStatus: this.normalizeStatusCounts(byStatus)
        };

      } else if (linkedStore.provider === 'stripe') {
        // Get Stripe dispute statistics
        const [total, byStatus, totalAmount] = await Promise.all([
          prisma.stripeDispute.count({ where: { linkedStoreId } }),
          prisma.stripeDispute.groupBy({
            by: ['status'],
            where: { linkedStoreId },
            _count: { status: true }
          }),
          prisma.stripeDispute.aggregate({
            where: { linkedStoreId },
            _sum: { amount: true }
          })
        ]);

        stats = {
          provider: 'stripe',
          total,
          totalAmount: Number(totalAmount._sum.amount || 0) / 100, // Convert from cents
          byStatus: this.normalizeStatusCounts(byStatus)
        };
      }

      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t('success.chargebackDispute.statsRetrieved'),
        data: stats,
      };
    } catch (error: any) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.chargebackDispute.internalError'),
        data: undefined,
      };
    }
  };
}

export default ChargebackDisputeService;
