import PrismaService from './prisma.service';
import {
  CreateOrder,
  OrderModel,
  UpdateOrder,
  OrderQuery
} from '../models/order.model';

class OrderService {
  private static prisma: any;
  
  static initialize() {
    this.prisma = PrismaService.getInstance().getClient();
  }

  /**
   * Create a new order
   * @param data Order data
   * @returns Created order
   */
  static async createOrder(data: CreateOrder): Promise<OrderModel> {
    return this.prisma.order.create({
      data
    });
  }

  /**
   * Get all orders with optional filtering
   * @param query Optional query parameters
   * @returns Array of orders
   */
  static async getAllOrders(query: OrderQuery = {}): Promise<OrderModel[]> {
    return this.prisma.order.findMany({
      where: query,
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  /**
   * Get orders for a specific store
   * @param storeId Store ID
   * @returns Array of orders for the store
   */
  static async getOrdersByStoreId(storeId: string): Promise<OrderModel[]> {
    return this.prisma.order.findMany({
      where: { storeId },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  /**
   * Get a specific order by ID
   * @param id Order ID
   * @returns Order or null if not found
   */
  static async getOrderById(id: string): Promise<OrderModel | null> {
    return this.prisma.order.findUnique({
      where: { id }
    });
  }

  /**
   * Get a specific order by store ID and order ID
   * @param storeId Store ID
   * @param orderId Order ID from store system
   * @returns Order or null if not found
   */
  static async getOrderByStoreIdAndOrderId(storeId: string, orderId: string): Promise<OrderModel | null> {
    return this.prisma.order.findFirst({
      where: { 
        storeId,
        orderId 
      }
    });
  }

  /**
   * Update an existing order
   * @param id Order ID
   * @param data Updated order data
   * @returns Updated order
   */
  static async updateOrder(id: string, data: UpdateOrder): Promise<OrderModel> {
    return this.prisma.order.update({
      where: { id },
      data
    });
  }

  /**
   * Delete an order
   * @param id Order ID
   * @returns void
   */
  static async deleteOrder(id: string): Promise<void> {
    await this.prisma.order.delete({
      where: { id }
    });
  }
}

// Initialize when module is loaded
OrderService.initialize();

export default OrderService;
