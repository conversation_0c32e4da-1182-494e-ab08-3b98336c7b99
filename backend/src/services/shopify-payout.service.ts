import PrismaService from './prisma.service';
import {
  CreateShopifyPayout,
  ShopifyPayoutModel,
  ShopifyPayoutQuery
} from '../models/shopify-payout.model';

class ShopifyPayoutService {
  private static prisma: any;
  
  static initialize() {
    this.prisma = PrismaService.getInstance().getClient();
  }

  /**
   * Create a new Shopify payout
   * @param data Shopify payout data
   * @returns Created payout
   */
  static async createPayout(data: CreateShopifyPayout): Promise<ShopifyPayoutModel> {
    return this.prisma.shopifyPayout.create({
      data
    });
  }

  /**
   * Create multiple Shopify payouts
   * @param data Array of Shopify payout data
   * @returns Number of created payouts
   */
  static async createManyPayouts(data: CreateShopifyPayout[]): Promise<number> {
    const result = await this.prisma.shopifyPayout.createMany({
      data,
      skipDuplicates: true
    });
    return result.count;
  }

  /**
   * Get all Shopify payouts with optional filtering
   * @param query Optional query parameters
   * @returns Array of payouts
   */
  static async getAllPayouts(query: ShopifyPayoutQuery = {}): Promise<ShopifyPayoutModel[]> {
    // Handle date range query
    const whereClause: any = {...query};
    
    if (whereClause.dateFrom || whereClause.dateTo) {
      whereClause.date = {};
      if (whereClause.dateFrom) {
        whereClause.date.gte = whereClause.dateFrom;
        delete whereClause.dateFrom;
      }
      if (whereClause.dateTo) {
        whereClause.date.lte = whereClause.dateTo;
        delete whereClause.dateTo;
      }
    }

    return this.prisma.shopifyPayout.findMany({
      where: whereClause,
      orderBy: {
        date: 'desc'
      },
      include: {
        transactions: true
      }
    });
  }

  /**
   * Get Shopify payouts for a specific linked store
   * @param linkedStoreId Linked store ID
   * @returns Array of payouts for the store
   */
  static async getPayoutsByLinkedStoreId(linkedStoreId: string): Promise<ShopifyPayoutModel[]> {
    return this.prisma.shopifyPayout.findMany({
      where: { linkedStoreId },
      orderBy: {
        date: 'desc'
      },
      include: {
        transactions: true
      }
    });
  }

  /**
   * Get a specific Shopify payout by ID
   * @param id Payout ID
   * @returns Payout or null if not found
   */
  static async getPayoutById(id: bigint): Promise<ShopifyPayoutModel | null> {
    return this.prisma.shopifyPayout.findUnique({
      where: { id },
      include: {
        transactions: {
          include: {
            order: true
          }
        }
      }
    });
  }

  /**
   * Update an existing Shopify payout
   * @param id Payout ID
   * @param data Updated payout data
   * @returns Updated payout
   */
  static async updatePayout(id: bigint, data: Partial<CreateShopifyPayout>): Promise<ShopifyPayoutModel> {
    return this.prisma.shopifyPayout.update({
      where: { id },
      data
    });
  }

  /**
   * Delete a Shopify payout
   * @param id Payout ID
   * @returns void
   */
  static async deletePayout(id: bigint): Promise<void> {
    await this.prisma.shopifyPayout.delete({
      where: { id }
    });
  }

  /**
   * Count Shopify payouts that match the given criteria
   * @param query Query parameters
   * @returns Count of matching payouts
   */
  static async countPayouts(query: ShopifyPayoutQuery = {}): Promise<number> {
    // Handle date range query
    const whereClause: any = {...query};
    
    if (whereClause.dateFrom || whereClause.dateTo) {
      whereClause.date = {};
      if (whereClause.dateFrom) {
        whereClause.date.gte = whereClause.dateFrom;
        delete whereClause.dateFrom;
      }
      if (whereClause.dateTo) {
        whereClause.date.lte = whereClause.dateTo;
        delete whereClause.dateTo;
      }
    }

    return this.prisma.shopifyPayout.count({
      where: whereClause
    });
  }

  /**
   * Get payouts with pagination
   * @param page Page number (1-based)
   * @param limit Items per page
   * @param query Optional query parameters
   * @returns Paginated payouts
   */
  static async getPaginatedPayouts(
    page: number = 1, 
    limit: number = 10,
    query: ShopifyPayoutQuery = {}
  ): Promise<{ data: ShopifyPayoutModel[], total: number, page: number, limit: number }> {
    const skip = (page - 1) * limit;
    
    // Handle date range query
    const whereClause: any = {...query};
    
    if (whereClause.dateFrom || whereClause.dateTo) {
      whereClause.date = {};
      if (whereClause.dateFrom) {
        whereClause.date.gte = whereClause.dateFrom;
        delete whereClause.dateFrom;
      }
      if (whereClause.dateTo) {
        whereClause.date.lte = whereClause.dateTo;
        delete whereClause.dateTo;
      }
    }

    const [data, total] = await Promise.all([
      this.prisma.shopifyPayout.findMany({
        where: whereClause,
        skip,
        take: limit,
        orderBy: {
          date: 'desc'
        },
        include: {
          transactions: {
            take: 5, // Limit to 5 transactions per payout in pagination
          }
        }
      }),
      this.prisma.shopifyPayout.count({ where: whereClause })
    ]);

    return {
      data,
      total,
      page,
      limit
    };
  }
}

// Initialize when module is loaded
ShopifyPayoutService.initialize();

export default ShopifyPayoutService;
