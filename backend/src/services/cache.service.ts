import redisClient from "../config/redis";
import logger from "../utils/logger.util";

export enum Cache<PERSON>ey {
  STRIPE_ACCOUNT = "stripe:account",
  ALERT_INFO = "alert:info",
  DASHBOARD_METRICS = "dashboard:metrics",
  CHARGEBACK_CHART = "dashboard:chart",
  MATCHED_ORDER = "matched:order",
}

class CacheService {
  private static TTL = {
    SHORT: 2 * 60, // 2 minutes
    MEDIUM: 5 * 60, // 5 minutes
    LONG: 15 * 60, // 15 minutes
  };

  /**
   * Set cache with TTL
   */
  static async set(
    key: string,
    value: any,
    ttlSeconds: number = this.TTL.MEDIUM
  ): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      await redisClient.setEx(key, ttlSeconds, serializedValue);
      logger.info(`Cache set: ${key} (TTL: ${ttlSeconds}s)`);
    } catch (error: any) {
      logger.error(`Cache set error for key ${key}: ${error.message}`);
    }
  }

  /**
   * Get cache
   */
  static async get<T>(key: string): Promise<T | null> {
    // tmp disable cache
    // return null;
    try {
      const value = await redisClient.get(key);
      if (!value) {
        return null;
      }
      logger.info(`Cache hit: ${key}`);
      return JSON.parse(value) as T;
    } catch (error: any) {
      logger.error(`Cache get error for key ${key}: ${error.message}`);
      return null;
    }
  }

  /**
   * Delete cache
   */
  static async delete(key: string): Promise<void> {
    try {
      await redisClient.del(key);
      logger.info(`Cache deleted: ${key}`);
    } catch (error: any) {
      logger.error(`Cache delete error for key ${key}: ${error.message}`);
    }
  }

  /**
   * Delete cache by pattern
   */
  static async deletePattern(pattern: string): Promise<void> {
    try {
      const keys = await redisClient.keys(pattern);
      if (keys.length > 0) {
        await redisClient.del(keys);
        logger.info(`Cache pattern deleted: ${pattern} (${keys.length} keys)`);
      }
    } catch (error: any) {
      logger.error(
        `Cache delete pattern error for ${pattern}: ${error.message}`
      );
    }
  }

  /**
   * Check if cache exists
   */
  static async exists(key: string): Promise<boolean> {
    try {
      const result = await redisClient.exists(key);
      return result === 1;
    } catch (error: any) {
      logger.error(`Cache exists error for key ${key}: ${error.message}`);
      return false;
    }
  }

  // Convenience methods for specific cache types

  static async setStripeAccount(storeId: string, data: any): Promise<void> {
    const key = `${CacheKey.STRIPE_ACCOUNT}:${storeId}`;
    await this.set(key, data, this.TTL.MEDIUM);
  }

  static async getStripeAccount(storeId: string): Promise<any> {
    const key = `${CacheKey.STRIPE_ACCOUNT}:${storeId}`;
    return await this.get(key);
  }

  static async clearStripeAccount(storeId?: string): Promise<void> {
    if (storeId) {
      const key = `${CacheKey.STRIPE_ACCOUNT}:${storeId}`;
      await this.delete(key);
    } else {
      await this.deletePattern(`${CacheKey.STRIPE_ACCOUNT}:*`);
    }
  }

  static async setAlertInfo(storeId: string, data: any): Promise<void> {
    const key = `${CacheKey.ALERT_INFO}:${storeId}`;
    await this.set(key, data, this.TTL.MEDIUM);
  }

  static async getAlertInfo(storeId: string): Promise<any> {
    const key = `${CacheKey.ALERT_INFO}:${storeId}`;
    return await this.get(key);
  }

  static async clearAlertInfo(storeId?: string): Promise<void> {
    if (storeId) {
      const key = `${CacheKey.ALERT_INFO}:${storeId}`;
      await this.delete(key);
    } else {
      await this.deletePattern(`${CacheKey.ALERT_INFO}:*`);
    }
  }

  static async setDashboardMetrics(
    storeId: string,
    filters: string,
    data: any
  ): Promise<void> {
    const key = `${CacheKey.DASHBOARD_METRICS}:${storeId}:${filters}`;
    await this.set(key, data, this.TTL.SHORT);
  }

  static async getDashboardMetrics(
    storeId: string,
    filters: string
  ): Promise<any> {
    const key = `${CacheKey.DASHBOARD_METRICS}:${storeId}:${filters}`;
    return await this.get(key);
  }

  static async clearDashboardMetrics(storeId?: string): Promise<void> {
    if (storeId) {
      await this.deletePattern(`${CacheKey.DASHBOARD_METRICS}:${storeId}:*`);
    } else {
      await this.deletePattern(`${CacheKey.DASHBOARD_METRICS}:*`);
    }
  }

  static async setMatchedOrder(
    blockId: string,
    storeId: string,
    data: any
  ): Promise<void> {
    const key = `${CacheKey.MATCHED_ORDER}:${blockId}:${storeId}`;
    await this.set(key, data, this.TTL.LONG);
  }

  static async getMatchedOrder(blockId: string, storeId: string): Promise<any> {
    const key = `${CacheKey.MATCHED_ORDER}:${blockId}:${storeId}`;
    return await this.get(key);
  }

  static async clearMatchedOrder(
    blockId?: string,
    storeId?: string
  ): Promise<void> {
    if (blockId && storeId) {
      const key = `${CacheKey.MATCHED_ORDER}:${blockId}:${storeId}`;
      await this.delete(key);
    } else if (blockId) {
      await this.deletePattern(`${CacheKey.MATCHED_ORDER}:${blockId}:*`);
    } else if (storeId) {
      await this.deletePattern(`${CacheKey.MATCHED_ORDER}:*:${storeId}`);
    } else {
      await this.deletePattern(`${CacheKey.MATCHED_ORDER}:*`);
    }
  }
}

export default CacheService;
