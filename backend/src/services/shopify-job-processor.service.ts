import { getBackgroundJobService } from "./background-job.service";
import ShopifySyncService from "./shopify-sync.service";
import PrismaService from "./prisma.service";
import { ShopifyStoreData } from "../models/linked-store.model";

class ShopifyJobProcessorService {
  private syncService: ShopifySyncService;
  private prisma;

  constructor() {
    this.syncService = new ShopifySyncService();
    this.prisma = PrismaService.getInstance().getClient();
    this.registerProcessors();
  }

  private async getStoreCredentials(storeId: string): Promise<{ shop: string; accessToken: string }> {
    const linkedStore = await (this.prisma as any).linkedStore.findUnique({
      where: { id: storeId }
    });

    if (!linkedStore || linkedStore.provider !== 'shopify') {
      throw new Error('Linked store not found or not a Shopify store');
    }

    const storeData = linkedStore.data as ShopifyStoreData;
    if (!storeData || !storeData.accessToken || !storeData.domain) {
      throw new Error('Invalid store data or missing credentials');
    }

    return {
      shop: storeData.domain,
      accessToken: storeData.accessToken
    };
  }

  private registerProcessors(): void {
    const backgroundJobService = getBackgroundJobService();
    
    backgroundJobService.registerProcessor('shopify-sync', async (data) => {
      await this.processSyncJob(data);
    });

    backgroundJobService.registerProcessor('shopify-bulk-sync', async (data) => {
      await this.processBulkSyncJob(data);
    });

    backgroundJobService.registerProcessor('shopify-force-sync', async (data) => {
      await this.processForceSyncJob(data);
    });
  }

  private async processSyncJob(data: {
    storeId: string;
    syncTypes?: ('orders' | 'payouts' | 'disputes' | 'transactions')[];
    shop: string;
    accessToken: string;
  }): Promise<void> {
    const { storeId, syncTypes, shop, accessToken } = data;
    
    console.log(`Processing sync job for store: ${storeId} with shop: ${shop}`);
    
    try {
      // Use provided shop and accessToken for direct Shopify API access
      await this.syncService.syncStoreData(storeId, {
        syncTypes: syncTypes || ['orders', 'payouts', 'disputes', 'transactions']
      }, shop, accessToken);
      
      console.log(`Sync job completed successfully for store: ${storeId}`);
    } catch (error) {
      console.error(`Sync job failed for store: ${storeId}`, error);
      throw error;
    }
  }

  private async processBulkSyncJob(data: {
    syncTypes?: ('orders' | 'payouts' | 'disputes' | 'transactions')[];
  }): Promise<void> {
    const { syncTypes } = data;
    
    console.log('Processing bulk sync job');
    
    try {
      // Get all Shopify stores
      const stores = await (this.prisma as any).linkedStore.findMany({
        where: {
          provider: 'shopify'
        }
      });

      // Schedule individual sync jobs for each store
      for (const store of stores) {
        try {
          const storeData = store.data as ShopifyStoreData;
          if (storeData && storeData.accessToken && storeData.domain) {
            await this.scheduleRegularSync(
              store.id,
              storeData.domain,
              storeData.accessToken,
              syncTypes
            );
          }
        } catch (error) {
          console.error(`Failed to schedule sync for store ${store.id}:`, error);
        }
      }
      
      console.log(`Bulk sync job completed successfully - scheduled ${stores.length} individual syncs`);
    } catch (error) {
      console.error('Bulk sync job failed', error);
      throw error;
    }
  }

  private async processForceSyncJob(data: {
    storeId: string;
    syncTypes?: ('orders' | 'payouts' | 'disputes' | 'transactions')[];
    shop: string;
    accessToken: string;
  }): Promise<void> {
    const { storeId, syncTypes, shop, accessToken } = data;
    
    console.log(`Processing force sync job for store: ${storeId} with shop: ${shop}`);
    
    try {
      // Use provided shop and accessToken for direct Shopify API access
      await this.syncService.syncStoreData(storeId, {
        forceSync: true,
        syncTypes: syncTypes || ['orders', 'payouts', 'disputes', 'transactions']
      }, shop, accessToken);
      
      console.log(`Force sync job completed successfully for store: ${storeId}`);
    } catch (error) {
      console.error(`Force sync job failed for store: ${storeId}`, error);
      throw error;
    }
  }

  async scheduleBulkSync(syncTypes?: ('orders' | 'payouts' | 'disputes' | 'transactions')[]): Promise<string> {
    const backgroundJobService = getBackgroundJobService();
    
    return await backgroundJobService.addJob('shopify-bulk-sync', {
      syncTypes
    }, {
      priority: 1,
      delay: 5000
    });
  }

  async scheduleForceSync(
    storeId: string,
    shop: string,
    accessToken: string,
    syncTypes?: ('orders' | 'payouts' | 'disputes' | 'transactions')[]
  ): Promise<string> {
    const backgroundJobService = getBackgroundJobService();
    
    return await backgroundJobService.addJob('shopify-force-sync', {
      storeId,
      syncTypes,
      shop,
      accessToken
    }, {
      priority: 8,
      delay: 1000
    });
  }

  async scheduleForceSyncByStoreId(
    storeId: string,
    syncTypes?: ('orders' | 'payouts' | 'disputes' | 'transactions')[]
  ): Promise<string> {
    const { shop, accessToken } = await this.getStoreCredentials(storeId);
    return this.scheduleForceSync(storeId, shop, accessToken, syncTypes);
  }

  async scheduleRegularSync(
    storeId: string,
    shop: string,
    accessToken: string,
    syncTypes?: ('orders' | 'payouts' | 'disputes' | 'transactions')[]
  ): Promise<string> {
    const backgroundJobService = getBackgroundJobService();
    
    return await backgroundJobService.addJob('shopify-sync', {
      storeId,
      syncTypes,
      shop,
      accessToken
    }, {
      priority: 5,
      delay: 2000
    });
  }

  async scheduleRegularSyncByStoreId(
    storeId: string,
    syncTypes?: ('orders' | 'payouts' | 'disputes' | 'transactions')[]
  ): Promise<string> {
    const { shop, accessToken } = await this.getStoreCredentials(storeId);
    return this.scheduleRegularSync(storeId, shop, accessToken, syncTypes);
  }

  getJobStats() {
    const backgroundJobService = getBackgroundJobService();
    return backgroundJobService.getStats();
  }

  getShopifyJobs() {
    const backgroundJobService = getBackgroundJobService();
    return [
      ...backgroundJobService.getJobsByType('shopify-sync'),
      ...backgroundJobService.getJobsByType('shopify-bulk-sync'),
      ...backgroundJobService.getJobsByType('shopify-force-sync')
    ];
  }

  clearCompletedJobs(): number {
    const backgroundJobService = getBackgroundJobService();
    return backgroundJobService.clearCompletedJobs();
  }

  clearFailedJobs(): number {
    const backgroundJobService = getBackgroundJobService();
    return backgroundJobService.clearFailedJobs();
  }
}

let shopifyJobProcessorInstance: ShopifyJobProcessorService | null = null;

export const getShopifyJobProcessor = (): ShopifyJobProcessorService => {
  if (!shopifyJobProcessorInstance) {
    shopifyJobProcessorInstance = new ShopifyJobProcessorService();
  }
  return shopifyJobProcessorInstance;
};

export default ShopifyJobProcessorService;