import PrismaService from './prisma.service';
import {
  CreatePaymentHistory,
  PaymentHistoryModel,
  UpdatePaymentHistory,
  PaymentHistoryQuery
} from '../models/payment-history.model';

class PaymentHistoryService {
  private static prisma: any;
  
  static initialize() {
    this.prisma = PrismaService.getInstance().getClient();
  }

  static async createPaymentHistory(data: CreatePaymentHistory): Promise<PaymentHistoryModel> {
    return this.prisma.paymentHistory.create({
      data
    });
  }

  static async getAllPaymentHistory(query: PaymentHistoryQuery = {}): Promise<PaymentHistoryModel[]> {
    return this.prisma.paymentHistory.findMany({
      where: query,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        bill: true
      }
    });
  }

  static async getPaymentHistoryByStoreId(storeId: string, page: number = 1, limit: number = 10): Promise<{
    items: PaymentHistoryModel[];
    total: number;
    page: number;
    limit: number;
  }> {
    const offset = (page - 1) * limit;

    const [items, total] = await Promise.all([
      this.prisma.paymentHistory.findMany({
        where: { linkedStoreId: storeId },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
        include: {
          bill: true
        }
      }),
      this.prisma.paymentHistory.count({
        where: { linkedStoreId: storeId },
      }),
    ]);

    return { items, total, page, limit };
  }

  static async getPaymentHistoryById(id: string): Promise<PaymentHistoryModel> {
    return this.prisma.paymentHistory.findUnique({
      where: { id },
      include: {
        bill: true
      }
    });
  }

  static async updatePaymentHistory(id: string, data: UpdatePaymentHistory): Promise<PaymentHistoryModel> {
    return this.prisma.paymentHistory.update({
      where: { id },
      data
    });
  }

  static async deletePaymentHistory(id: string): Promise<void> {
    await this.prisma.paymentHistory.delete({
      where: { id }
    });
  }
}

// Initialize when module is loaded
PaymentHistoryService.initialize();

export default PaymentHistoryService;
export { PaymentHistoryService };