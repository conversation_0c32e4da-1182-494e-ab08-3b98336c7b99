import { StatusCodes } from "http-status-codes";
import PrismaService from "./prisma.service";
import { ServiceResponse } from "@/constants/type";
import {
  BlockType,
  FeedbackStatus,
} from "@/models/matched-blocks.model";
import logger from "@/utils/logger.util";
import {
  subDays,
  subMonths,
  startOfDay,
  endOfDay,
  startOfYear,
} from "date-fns";
import redisClient from "@/config/redis";
import { RedisPrefix } from "@/constants/prefix";

enum TimeRange {
  TODAY = "today",
  SEVEN_DAYS = "7d",
  THIRTY_DAYS = "30d",
  THREE_MONTHS = "3m",
  SIX_MONTHS = "6m",
  YTD = "ytd",
  ONE_YEAR = "1y",
  ALL_TIME = "all",
  CUSTOM = "custom",
}

enum ViewMode {
  OVERVIEW = "overview",
  LIST = "list",
}

enum SortBy {
  DATE_ASC = "date_asc",
  DATE_DESC = "date_desc",
  AMOUNT_ASC = "amount_asc",
  AMOUNT_DESC = "amount_desc",
}

const prisma = PrismaService.getInstance().getClient();

class MatchedBlocksService {
  /**
   * Get matched blocks by user ID with support for overview and list views
   */
  static getBlocksByUserId = async (
    userId: string,
    page: number = 1,
    limit: number = 10,
    filters?: Record<string, any>
  ): Promise<ServiceResponse<any>> => {
    try {
      logger.info(`Getting matched blocks for user ${userId}`, {
        filters,
        page,
        limit,
      });

      const where = this.buildWhereConditions({ ...filters, userId });
      const { viewMode = ViewMode.LIST, sortBy = SortBy.DATE_DESC } = filters || {};

      // Build orderBy based on sortBy parameter
      const orderBy = this.buildOrderBy(sortBy);

      switch (viewMode) {
        case ViewMode.OVERVIEW:
          return await this.getOverviewData(where);
        case ViewMode.LIST:
        default:
          // Standard list query with metadata
          const offset = (page - 1) * limit;

        // Create base where clause without type constraints for meta counts
        const baseWhere = { ...where };
        delete baseWhere.type;
        if (baseWhere.AND) {
          baseWhere.AND = baseWhere.AND.filter(
            (condition: any) =>
              !condition.type &&
              !condition.OR?.some((orCondition: any) => orCondition.type)
          );
        }

        const [blocks, total, totalEthoca, totalRdr] = await Promise.all([
          prisma.matchedBlock.findMany({
            where,
            orderBy,
            skip: offset,
            take: limit,
          }),
          prisma.matchedBlock.count({ where }),
          prisma.matchedBlock.count({
            where: {
              ...baseWhere,
              type: BlockType.ETHOCA,
            },
          }),
          prisma.matchedBlock.count({
            where: {
              ...baseWhere,
              type: BlockType.RDR,
            },
          }),
        ]);

        return {
          success: true,
          status: StatusCodes.OK,
          message: "Matched blocks retrieved successfully",
          data: {
            items: blocks,
            pagination: {
              total,
              page,
              size: limit,
              totalPages: Math.ceil(total / limit),
            },
            meta: {
              totalEthoca,
              totalRdr,
            },
          },
        };
      }
    } catch (error: any) {
      logger.error("Error getting matched blocks by user ID:", error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || "Failed to fetch matched blocks",
      };
    }
  };

  /**
   * Get overview data for matched blocks
   */
  static getOverviewData = async (
    where: Record<string, any>
  ): Promise<ServiceResponse<any>> => {
    try {
      // Generate cache key based on where conditions
      const cacheKey = `${RedisPrefix.MATCHED_BLOCK_OVERVIEW}_${JSON.stringify(
        where
      )}`;

      // Create base where clause without type constraints for separate counts
      const baseWhere = { ...where };
      delete baseWhere.type;
      if (baseWhere.AND) {
        baseWhere.AND = baseWhere.AND.filter(
          (condition: any) =>
            !condition.type &&
            !condition.OR?.some((orCondition: any) => orCondition.type)
        );
      }

      // Extract date range conditions for transactions/disputes query
      const dateCondition = where.AND?.find((condition: any) => condition.alertTime);
      const transactionWhere: any = {
        userId: baseWhere.userId || where.userId,
      };
      const disputeWhere: any = {
        userId: baseWhere.userId || where.userId,
        type: 'chargeback',
      };

      // Add date conditions if they exist
      if (dateCondition?.alertTime) {
        transactionWhere.createdAt = {
          gte: dateCondition.alertTime.gte,
          lte: dateCondition.alertTime.lte,
        };
        disputeWhere.createdAt = {
          gte: dateCondition.alertTime.gte,
          lte: dateCondition.alertTime.lte,
        };
      }

      const [totalBlocks, , , blockedBlocks, totalTransactions, totalDisputes] =
        await Promise.all([
          prisma.matchedBlock.count({ where }),
          prisma.matchedBlock.count({
            where: {
              ...baseWhere,
              type: BlockType.ETHOCA,
            },
          }),
          prisma.matchedBlock.count({
            where: {
              ...baseWhere,
              type: BlockType.RDR,
            },
          }),
          prisma.matchedBlock.count({
            where: {
              ...where,
              feedbackStatus: FeedbackStatus.SENT,
            },
          }),
          prisma.unifiedTransaction.count({ where: transactionWhere }),
          prisma.unifiedDispute.count({ where: disputeWhere }),
        ]);

      // Calculate actual chargeback rate based on transactions vs chargebacks
      const actualChargebackRate = totalTransactions > 0 ? (totalDisputes / totalTransactions) * 100 : 0;
      const blockedRate =
        totalBlocks > 0 ? (blockedBlocks / totalBlocks) * 100 : 0;

      const overviewResult = {
        success: true,
        status: StatusCodes.OK,
        message: "success.block.overviewVolume",
        data: {
          blocks: {
            count: totalBlocks,
            blocked: blockedBlocks,
          },
          rate: {
            actualChargeback: actualChargebackRate.toFixed(2),
            blocked: blockedRate.toFixed(2),
          },
          goal: {
            actualChargeback: actualChargebackRate.toFixed(2),
            chargebackGoal: "1.00",
            profitGoalProgress: "0.00",
          },
        },
      };

      // Cache for 5 minutes
      await redisClient.setEx(cacheKey, 300, JSON.stringify(overviewResult));
      return overviewResult;
    } catch (error: any) {
      logger.error(`Error in getOverviewData:`, error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || "Failed to get matched blocks overview",
      };
    }
  };

  /**
   * Determine aggregation strategy based on time range requirements
   */
  private static getAggregationStrategy(timeRange: string) {
    switch (timeRange) {
      case TimeRange.TODAY:
        // Hourly data points, 2-hour intervals (max 12 data points)
        return { type: 'hourly', intervals: 12 };
      case TimeRange.YTD:
      case TimeRange.ONE_YEAR:
        // Monthly data points
        return { type: 'monthly', intervals: 12 };
      case TimeRange.THIRTY_DAYS:
      case TimeRange.SEVEN_DAYS:
        // Daily data points
        return { type: 'daily', intervals: null };
      case TimeRange.THREE_MONTHS:
        // 6 equal time intervals
        return { type: 'equalIntervals', intervals: 6 };
      case TimeRange.SIX_MONTHS:
        // 6 equal time intervals (monthly)
        return { type: 'monthly', intervals: 6 };
      case TimeRange.ALL_TIME:
      case "all_time":
        // 10 equal time intervals spanning entire data range
        return { type: 'equalIntervals', intervals: 10 };
      case TimeRange.CUSTOM:
      case "custom":
        // 10 equal time intervals for user-specified range
        return { type: 'equalIntervals', intervals: 10 };
      default:
        return { type: 'daily', intervals: null };
    }
  }

  /**
   * Get hourly chargeback data for Today view
   */
  private static async getHourlyChargebackData(userId: string, startDate: Date, endDate: Date, maxIntervals: number) {
    // Calculate actual duration and adjust interval if needed
    const durationHours = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60);
    const intervalHours = Math.max(1, Math.ceil(durationHours / maxIntervals));
    const actualMaxIntervals = Math.min(maxIntervals, Math.ceil(durationHours / intervalHours));

    // Generate time buckets for the range
    const timeBuckets: Date[] = [];
    const current = new Date(startDate);
    current.setMinutes(0, 0, 0); // Round to hour
    
    while (current <= endDate && timeBuckets.length < actualMaxIntervals) {
      timeBuckets.push(new Date(current));
      current.setHours(current.getHours() + intervalHours);
    }

    // Get all transactions and disputes for the time range using ORM
    const [transactions, disputes] = await Promise.all([
      prisma.unifiedTransaction.findMany({
        where: {
          userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          createdAt: true,
        },
      }),
      prisma.unifiedDispute.findMany({
        where: {
          userId,
          type: 'chargeback',
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          createdAt: true,
        },
      }),
    ]);

    // Group data by time buckets
    const bucketData = timeBuckets.map((bucketStart, index) => {
      const bucketEnd = index < timeBuckets.length - 1 
        ? timeBuckets[index + 1] 
        : new Date(bucketStart.getTime() + intervalHours * 60 * 60 * 1000);

      const transactionCount = transactions.filter(t => 
        t.createdAt >= bucketStart && t.createdAt < bucketEnd
      ).length;

      const disputeCount = disputes.filter(d => 
        d.createdAt >= bucketStart && d.createdAt < bucketEnd
      ).length;

      const chargebackRate = transactionCount > 0 ? (disputeCount / transactionCount) * 100 : 0;

      return {
        date: bucketStart.toLocaleTimeString("en-US", {
          hour: "numeric",
          hour12: true,
        }),
        rate: parseFloat(chargebackRate.toFixed(3)),
        disputeCount: disputeCount,
        transactionCount: transactionCount,
      };
    });

    return bucketData;
  }

  /**
   * Get daily chargeback data
   */
  private static async getDailyChargebackData(userId: string, startDate: Date, endDate: Date) {
    // Generate date buckets for the range
    const dateBuckets: Date[] = [];
    const current = new Date(startDate);
    current.setHours(0, 0, 0, 0); // Start of day
    
    while (current <= endDate) {
      dateBuckets.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }

    // Get all transactions and disputes for the time range using ORM
    const [transactions, disputes] = await Promise.all([
      prisma.unifiedTransaction.findMany({
        where: {
          userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          createdAt: true,
        },
      }),
      prisma.unifiedDispute.findMany({
        where: {
          userId,
          type: 'chargeback',
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          createdAt: true,
        },
      }),
    ]);

    // Group data by date buckets
    const bucketData = dateBuckets.map((bucketStart) => {
      const bucketEnd = new Date(bucketStart);
      bucketEnd.setDate(bucketEnd.getDate() + 1); // Next day

      const transactionCount = transactions.filter(t => {
        const tDate = new Date(t.createdAt);
        tDate.setHours(0, 0, 0, 0);
        return tDate.getTime() === bucketStart.getTime();
      }).length;

      const disputeCount = disputes.filter(d => {
        const dDate = new Date(d.createdAt);
        dDate.setHours(0, 0, 0, 0);
        return dDate.getTime() === bucketStart.getTime();
      }).length;

      const chargebackRate = transactionCount > 0 ? (disputeCount / transactionCount) * 100 : 0;

      return {
        date: bucketStart.toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
        }),
        rate: parseFloat(chargebackRate.toFixed(3)),
        disputeCount: disputeCount,
        transactionCount: transactionCount,
      };
    });

    return bucketData;
  }

  /**
   * Get monthly chargeback data
   */
  private static async getMonthlyChargebackData(userId: string, startDate: Date, endDate: Date) {
    // Generate month buckets for the range
    const monthBuckets: Date[] = [];
    const current = new Date(startDate.getFullYear(), startDate.getMonth(), 1); // Start of month
    const end = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
    
    while (current <= end) {
      monthBuckets.push(new Date(current));
      current.setMonth(current.getMonth() + 1);
    }

    // Get all transactions and disputes for the time range using ORM
    const [transactions, disputes] = await Promise.all([
      prisma.unifiedTransaction.findMany({
        where: {
          userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          createdAt: true,
        },
      }),
      prisma.unifiedDispute.findMany({
        where: {
          userId,
          type: 'chargeback',
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          createdAt: true,
        },
      }),
    ]);

    // Group data by month buckets
    const bucketData = monthBuckets.map((bucketStart) => {
      const bucketEnd = new Date(bucketStart);
      bucketEnd.setMonth(bucketEnd.getMonth() + 1); // Next month

      const transactionCount = transactions.filter(t => {
        const tDate = new Date(t.createdAt);
        return tDate.getFullYear() === bucketStart.getFullYear() && 
               tDate.getMonth() === bucketStart.getMonth();
      }).length;

      const disputeCount = disputes.filter(d => {
        const dDate = new Date(d.createdAt);
        return dDate.getFullYear() === bucketStart.getFullYear() && 
               dDate.getMonth() === bucketStart.getMonth();
      }).length;

      const chargebackRate = transactionCount > 0 ? (disputeCount / transactionCount) * 100 : 0;

      return {
        date: bucketStart.toLocaleDateString("en-US", {
          month: "short",
          year: "numeric",
        }),
        rate: parseFloat(chargebackRate.toFixed(3)),
        disputeCount: disputeCount,
        transactionCount: transactionCount,
      };
    });

    return bucketData;
  }

  /**
   * Get equal interval chargeback data for All Time and Custom ranges
   */
  private static async getEqualIntervalChargebackData(userId: string, startDate: Date, endDate: Date, intervals: number) {
    // Calculate interval duration in seconds
    const totalDurationMs = endDate.getTime() - startDate.getTime();
    
    // Validate minimum duration (at least 1 hour per interval)
    const minDurationMs = 60 * 60 * 1000; // 1 hour
    if (totalDurationMs < minDurationMs * intervals) {
      // Reduce intervals to ensure minimum duration
      intervals = Math.max(1, Math.floor(totalDurationMs / minDurationMs));
    }
    
    // Validate maximum duration (no more than 1 year per interval)
    const maxDurationMs = 365 * 24 * 60 * 60 * 1000; // 1 year
    if (totalDurationMs > maxDurationMs * intervals) {
      // Increase intervals to ensure maximum duration
      intervals = Math.ceil(totalDurationMs / maxDurationMs);
    }
    
    const intervalDurationMs = totalDurationMs / intervals;
    
    // Generate interval buckets for the range
    const intervalBuckets: Array<{ start: Date; end: Date }> = [];
    for (let i = 0; i < intervals; i++) {
      const intervalStart = new Date(startDate.getTime() + i * intervalDurationMs);
      const intervalEnd = new Date(startDate.getTime() + (i + 1) * intervalDurationMs);
      intervalBuckets.push({ start: intervalStart, end: intervalEnd });
    }

    // Get all transactions and disputes for the time range using ORM
    const [transactions, disputes] = await Promise.all([
      prisma.unifiedTransaction.findMany({
        where: {
          userId,
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          createdAt: true,
        },
      }),
      prisma.unifiedDispute.findMany({
        where: {
          userId,
          type: 'chargeback',
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        select: {
          createdAt: true,
        },
      }),
    ]);

    // Group data by interval buckets
    const bucketData = intervalBuckets.map(({ start, end }) => {
      const transactionCount = transactions.filter(t => 
        t.createdAt >= start && t.createdAt < end
      ).length;

      const disputeCount = disputes.filter(d => 
        d.createdAt >= start && d.createdAt < end
      ).length;

      const chargebackRate = transactionCount > 0 ? (disputeCount / transactionCount) * 100 : 0;

      // Format date based on interval duration
      let dateLabel: string;
      if (intervalDurationMs > 30 * 24 * 60 * 60 * 1000) { // More than 30 days
        dateLabel = start.toLocaleDateString("en-US", {
          month: "short",
          year: "numeric",
        });
      } else {
        dateLabel = start.toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
        });
      }

      return {
        date: dateLabel,
        rate: parseFloat(chargebackRate.toFixed(3)),
        disputeCount: disputeCount,
        transactionCount: transactionCount,
      };
    });

    return bucketData;
  }

  /**
   * Builds orderBy clause for Prisma queries based on sortBy parameter
   * Simple sorting by date and amount only
   */
  private static buildOrderBy(
    sortBy: string = SortBy.DATE_DESC
  ): Record<string, any> {
    switch (sortBy) {
      case SortBy.DATE_ASC:
        return { alertTime: "asc" };
      case SortBy.DATE_DESC:
        return { alertTime: "desc" };
      case SortBy.AMOUNT_ASC:
        return { amount: "asc" };
      case SortBy.AMOUNT_DESC:
        return { amount: "desc" };
      default:
        return { alertTime: "desc" }; // Default to newest first
    }
  }

  /**
   * Builds standardized where conditions for Prisma queries
   * Simplified version for matched blocks
   */
  private static buildWhereConditions(
    filters: Record<string, any> = {}
  ): Record<string, any> {
    const queryConditions: Record<string, any> = {};
    const andClause: any[] = [];

    const {
      userId,
      type,
      feedbackStatus,
      timeRange,
      startDate,
      endDate,
      provider,
      search,
      minAmount,
      maxAmount,
      currency,
      cardBin,
      authCode,
      descriptor,
    } = filters;

    // Required userId filter
    if (userId) {
      andClause.push({ userId });
    }

    // Text search across multiple fields
    if (search && search.trim()) {
      const searchTerm = search.trim();
      andClause.push({
        OR: [
          { id: { contains: searchTerm, mode: "insensitive" } }, // Search by Alert ID
          { descriptor: { contains: searchTerm, mode: "insensitive" } },
          { cardNumber: { contains: searchTerm, mode: "insensitive" } },
          { authCode: { contains: searchTerm, mode: "insensitive" } },
          { cardBin: { contains: searchTerm, mode: "insensitive" } },
          // ETHOCA specific search fields
          { arn: { contains: searchTerm, mode: "insensitive" } },
          // RDR specific search fields
          { caid: { contains: searchTerm, mode: "insensitive" } },
        ],
      });
    }

    // Optional filters
    if (type !== undefined) {
      if (Array.isArray(type)) {
        andClause.push({ type: { in: type } });
      } else {
        andClause.push({ type });
      }
    }

    if (provider) {
      andClause.push({ provider });
    }

    if (feedbackStatus !== undefined) {
      if (Array.isArray(feedbackStatus)) {
        andClause.push({ feedbackStatus: { in: feedbackStatus } });
      } else {
        andClause.push({ feedbackStatus });
      }
    }

    // Amount range filters
    if (minAmount !== undefined || maxAmount !== undefined) {
      const amountFilter: any = {};
      if (minAmount !== undefined) {
        amountFilter.gte = parseFloat(minAmount);
      }
      if (maxAmount !== undefined) {
        amountFilter.lte = parseFloat(maxAmount);
      }
      andClause.push({ amount: amountFilter });
    }

    // Currency filter
    if (currency) {
      andClause.push({ currency });
    }

    // Specific field filters
    if (cardBin) {
      andClause.push({ cardBin: { contains: cardBin, mode: "insensitive" } });
    }

    if (authCode) {
      andClause.push({ authCode: { contains: authCode, mode: "insensitive" } });
    }

    if (descriptor) {
      andClause.push({
        descriptor: { contains: descriptor, mode: "insensitive" },
      });
    }

    // Date range filters
    if (timeRange !== undefined) {
      const now = new Date();
      let gte: Date | undefined;
      let lte: Date = endOfDay(now);

      switch (timeRange) {
        case TimeRange.TODAY:
          gte = startOfDay(now);
          break;
        case TimeRange.SEVEN_DAYS:
          gte = startOfDay(subDays(now, 7));
          break;
        case TimeRange.THIRTY_DAYS:
          gte = startOfDay(subDays(now, 30));
          break;
        case TimeRange.THREE_MONTHS:
          gte = startOfDay(subMonths(now, 3));
          break;
        case TimeRange.SIX_MONTHS:
          gte = startOfDay(subMonths(now, 6));
          break;
        case TimeRange.YTD:
          // Year to Date: January 1st of current year to today
          gte = startOfYear(now);
          break;
        case TimeRange.ONE_YEAR:
          // Last 12 Months: Rolling 12 months from today
          gte = startOfDay(subMonths(now, 12));
          break;
        case TimeRange.ALL_TIME:
          gte = undefined; // No start date limit for all time
          lte = endOfDay(now);
          break;
        case TimeRange.CUSTOM:
          if (startDate && endDate) {
            gte = startOfDay(new Date(startDate));
            lte = endOfDay(new Date(endDate));
          }
          break;
      }

      if (gte) {
        andClause.push({
          alertTime: {
            gte,
            lte,
          },
        });
      }
    }

    // Construct query conditions
    if (andClause.length > 0) {
      if (andClause.length === 1) {
        return andClause[0];
      } else {
        queryConditions.AND = andClause;
      }
    }

    return queryConditions;
  }

  /**
   * Get chargeback rate chart data based on matched blocks
   * Returns chargeback rates for visualization with specific time period requirements
   */
  static getChargebackRateChart = async (
    userId: string,
    filters: Record<string, any> = {}
  ): Promise<ServiceResponse<any>> => {
    try {
      // Generate cache key based on userId and filters
      const cacheKey = `${
        RedisPrefix.MATCHED_BLOCK_GROUPED
      }_chart_${userId}_${JSON.stringify(filters)}`;

      // Determine date range based on timeRange filter
      const now = new Date();
      let startDate: Date;
      let endDate: Date = new Date();
      const timeRange = filters.timeRange || TimeRange.TODAY; // Default to 'Today'

      switch (timeRange) {
        case "all_time":
        case TimeRange.ALL_TIME:
          // Get earliest transaction date for this user's stores using ORM
          const earliestTransaction = await prisma.unifiedTransaction.findFirst({
            where: {
              userId,
            },
            orderBy: {
              createdAt: 'asc',
            },
            select: {
              createdAt: true,
            },
          });
          const minDate = earliestTransaction?.createdAt;
          startDate = minDate || new Date("2023-01-01");
          endDate = now;
          break;
        case "custom":
        case TimeRange.CUSTOM:
          if (filters.startDate && filters.endDate) {
            startDate = new Date(filters.startDate);
            endDate = new Date(filters.endDate);
          } else {
            startDate = startOfDay(subDays(now, 30));
            endDate = now;
          }
          break;
        case TimeRange.TODAY:
          startDate = startOfDay(now);
          endDate = now;
          break;
        case TimeRange.SEVEN_DAYS:
          startDate = startOfDay(subDays(now, 7));
          endDate = now;
          break;
        case TimeRange.THIRTY_DAYS:
          startDate = startOfDay(subDays(now, 30));
          endDate = now;
          break;
        case TimeRange.THREE_MONTHS:
          startDate = startOfDay(subMonths(now, 3));
          endDate = now;
          break;
        case TimeRange.SIX_MONTHS:
          startDate = startOfDay(subMonths(now, 6));
          endDate = now;
          break;
        case TimeRange.YTD:
          // Year to Date: January 1st of current year to today
          startDate = startOfYear(now);
          endDate = now;
          break;
        case TimeRange.ONE_YEAR:
          // Last 12 Months: Rolling 12 months from today
          startDate = startOfDay(subMonths(now, 12));
          endDate = now;
          break;
        default:
          startDate = startOfDay(subDays(now, 30));
          endDate = now;
      }

      // Get aggregated data for chargeback rate calculation based on specific time period requirements
      let chartData: any[] = [];

      // Determine aggregation strategy based on specific requirements
      const aggregationStrategy = this.getAggregationStrategy(timeRange);

      // Execute the appropriate aggregation query based on strategy
      switch (aggregationStrategy.type) {
        case 'hourly':
          chartData = await this.getHourlyChargebackData(userId, startDate, endDate, aggregationStrategy.intervals || 12);
          break;
        case 'daily':
          chartData = await this.getDailyChargebackData(userId, startDate, endDate);
          break;
        case 'monthly':
          chartData = await this.getMonthlyChargebackData(userId, startDate, endDate);
          break;
        case 'equalIntervals':
          chartData = await this.getEqualIntervalChargebackData(userId, startDate, endDate, aggregationStrategy.intervals || 10);
          break;
        default:
          chartData = await this.getDailyChargebackData(userId, startDate, endDate);
      }

      const chartResult = {
        success: true,
        status: StatusCodes.OK,
        message: "Chargeback rate chart data retrieved successfully",
        data: chartData,
      };

      // Cache for 5 minutes
      await redisClient.setEx(cacheKey, 300, JSON.stringify(chartResult));
      return chartResult;
    } catch (error: any) {
      logger.error(`Error in getChargebackRateChart:`, error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || "Failed to get chargeback rate chart data",
      };
    }
  };
}

export default MatchedBlocksService;
