import { PrismaClient } from "@prisma/client";
import PrismaService from "../prisma.service";
import { LinkedStore } from "../../models/linked-store.model";

class StripeStoreService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = PrismaService.getInstance().getClient();
  }


  /**
   * Get store by ID from linked_store table where provider = 'stripe'
   * @param storeId - Store ID
   * @returns LinkedStore record or null
   */
  async getStoreById(storeId: string): Promise<LinkedStore | null> {
    try {
      const store = await (this.prisma as any).linkedStore.findFirst({
        where: {
          id: storeId,
          provider: "stripe",
          isActive: true, // Only return active stores
        },
      });

      console.log("🔍 [StripeStoreService.getStoreById] Store:", store);

      return store;
    } catch (error) {
      console.error("Error fetching store by ID:", error);
      throw new Error(`Failed to fetch store by ID: ${(error as Error).message}`);
    }
  }

  /**
   * Delete a linked store
   * @param storeId - Store ID
   * @param userId - User ID for authorization
   */
  async deleteLinkedStore(storeId: string, userId: string): Promise<LinkedStore> {
    return (this.prisma as any).linkedStore.delete({
      where: {
        id: storeId,
        userId,
        provider: "stripe",
      },
    });
  }

  /**
   * Get store details and refresh from provider
   * For Stripe stores, this means getting latest Stripe account details
   * @param storeId - Store ID
   * @returns Refreshed store details
   */
  async getStoreDetails(storeId: string): Promise<LinkedStore> {
    const store = await (this.prisma as any).linkedStore.findFirst({
      where: {
        id: storeId,
        provider: "stripe",
      },
    });

    console.log("Store details ------:", store);

    if (!store) {
      throw new Error("Store not found");
    }

    const data = store.data as any;

    // For Stripe stores, we can try to get additional details from Stripe API
    // if the store has stripe account connected
    let refreshedData = data;

    // Try to get stripe account details if available
    try {
      const stripeAccount = await (this.prisma as any).stripeAccount.findUnique({
        where: { storeId }
      });

      if (stripeAccount && stripeAccount.stripeAccountId) {
        // Import Stripe service to get account details
        const StripeService = (await import('../stripe.service')).default;
        const stripeDetails = await StripeService.getStripeAccountDetails(stripeAccount.stripeAccountId);

        // Update store data with Stripe details
        refreshedData = {
          ...data,
          stripeAccount: {
            id: stripeDetails.id,
            email: stripeDetails.email,
            details_submitted: stripeDetails.details_submitted,
            charges_enabled: stripeDetails.charges_enabled,
            payouts_enabled: stripeDetails.payouts_enabled,
            business_profile: stripeDetails.business_profile,
            requirements: stripeDetails.requirements
          },
          lastFetchedAt: new Date().toISOString(),
        };
      }
    } catch (stripeError) {
      // Log warning but don't fail if Stripe details can't be fetched
      console.warn(`Could not refresh Stripe details for store ${storeId}:`, stripeError);
      refreshedData = {
        ...data,
        lastFetchedAt: new Date().toISOString(),
      };
    }

    // Update store data with new information
    const updatedStore = await (this.prisma as any).linkedStore.update({
      where: {
        id: store.id,
      },
      data: {
        data: refreshedData,
      },
    });

    return updatedStore;
  }



}

export default StripeStoreService;