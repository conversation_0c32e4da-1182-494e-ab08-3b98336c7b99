import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';
import axios from 'axios';
import <PERSON><PERSON> from 'stripe';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import { env } from '../../config/environment';
import { LinkedStore } from '../../models/linked-store.model';
import logger from '../../utils/logger.util';

// Encryption utility for tokens
class TokenEncryption {
  private algorithm = 'aes-256-gcm';
  private secretKey: Buffer;

  constructor() {
    // Use JWT_SECRET as encryption key, or generate one
    const key = env.JWT_SECRET || 'default-encryption-key-for-development';
    this.secretKey = crypto.scryptSync(key, 'salt', 32);
  }

  encrypt(text: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(this.algorithm, this.secretKey, iv);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = (cipher as any).getAuthTag();
    
    return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
  }

  decrypt(encryptedData: string): string {
    const parts = encryptedData.split(':');
    const iv = Buffer.from(parts[0], 'hex');
    const authTag = Buffer.from(parts[1], 'hex');
    const encrypted = parts[2];
    
    const decipher = crypto.createDecipheriv(this.algorithm, this.secretKey, iv);
    (decipher as any).setAuthTag(authTag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}

interface StripeAppTokens {
  accessToken: string;
  liveMode: boolean;
  refreshToken: string;
  scope: string;
  stripePublishableKey: string;
  stripeUserId: string;
  tokenType: string;
}

interface StripeAppStoreData {
  accessToken: string;
  refreshToken: string;
  stripeUserId: string;
  lastRefreshedAt: string;
  accountDetails?: any;
  [key: string]: any; // Index signature for Prisma JSON compatibility
}

export class StripeOAuthService {
  private prisma: PrismaClient;
  private tokenEncryption: TokenEncryption;
  
  constructor() {
    this.prisma = new PrismaClient();
    this.tokenEncryption = new TokenEncryption();
  }
  
  /**
   * Exchange authorization code for tokens
   */
  async exchangeCodeForTokens(code: string): Promise<StripeAppTokens> {
    try {
      const response = await axios.post('https://api.stripe.com/v1/oauth/token', 
        new URLSearchParams({
          code,
          grant_type: 'authorization_code',
        }),
        {
          headers: {
            'Authorization': `Bearer ${env.STRIPE_APP_SECRET_KEY}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );
      
      const data = response.data;
      
      if (!data.access_token || !data.refresh_token || !data.stripe_user_id) {
        logger.error('Invalid token response from Stripe:', { data });
        throw new Error('Invalid token response from Stripe');
      }
      
      return {
        accessToken: data.access_token,
        liveMode: data.livemode,
        refreshToken: data.refresh_token,
        scope: data.scope,
        stripePublishableKey: data.stripe_publishable_key,
        stripeUserId: data.stripe_user_id,
        tokenType: data.token_type,
      };
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        logger.error(`Stripe OAuth token exchange failed: ${error.response?.status} ${error.response?.data}`);
      } else {
        logger.error('Error exchanging code for tokens:', { error });
      }
      throw error;
    }
  }

  /**
   * Get Stripe account details by account ID using GET /v1/accounts/:id
   */
  async getStripeAccountById(accountId: string, token: string): Promise<Stripe.Account | null> {
    try {
      // Create Stripe client with provided token
      const stripeClient = new Stripe(token, {
        apiVersion: env.STRIPE_APP_API_VERSION as Stripe.LatestApiVersion,
        typescript: true,
      });

      // Retrieve account details using Stripe API
      const account = await stripeClient.accounts.retrieve(accountId);

      return account;
    } catch (error: any) {
      if (error.type === 'StripePermissionError') {
        logger.error('Permission denied accessing Stripe account:', { error: error.message, accountId });
      } else if (error.type === 'StripeInvalidRequestError') {
        logger.error('Invalid request to Stripe account:', { error: error.message, accountId });
      } else {
        logger.error('Error getting Stripe account by ID:', { error, accountId });
      }
      return null;
    }
  }

  /**
   * Handle auth callback - kiểm tra và tạo/cập nhật store tương tự Shopify
   */
  async handleAuthCallback(
    tokens: StripeAppTokens, 
    accountDetails: Stripe.Account,
    providedUserId?: string
  ): Promise<LinkedStore> {
    try {
      const storeData: StripeAppStoreData = {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        stripeUserId: tokens.stripeUserId,
        lastRefreshedAt: new Date().toISOString(),
        accountDetails: {
          id: accountDetails.id,
          business_type: accountDetails.business_type,
          country: accountDetails.country,
          default_currency: accountDetails.default_currency,
          email: accountDetails.email,
          type: accountDetails.type,
          business_profile: accountDetails.business_profile,
          capabilities: accountDetails.capabilities,
          charges_enabled: accountDetails.charges_enabled,
          payouts_enabled: accountDetails.payouts_enabled,
        },
        liveMode: tokens.liveMode,
        scope: tokens.scope,
        stripePublishableKey: tokens.stripePublishableKey,
        tokenType: tokens.tokenType,
      };

      let userId: string | undefined;

      // Step 1: Check if store already exists in linked_stores
      const existingStore = await this.prisma.linkedStore.findUnique({
        where: {
          provider_providerStoreId: {
            provider: 'stripe',
            providerStoreId: accountDetails.id,
          }
        },
        include: {
          user: true
        }
      });

      if (existingStore) {
        // If store exists, update and return store information
        userId = existingStore.userId;
        
        logger.info(`Updating existing Stripe store: ${existingStore.id} for account: ${accountDetails.id}`);
        
        const updatedStore = await this.prisma.linkedStore.update({
          where: {
            id: existingStore.id,
          },
          data: {
            storeName: this.getStripeStoreName(accountDetails),
            data: storeData,
            isActive: true,
          },
        });

        // Reactivate the user if they were inactive
        if (existingStore.user && !existingStore.user.isActive) {
          await this.prisma.user.update({
            where: { id: userId },
            data: {
              isActive: true,
            }
          });
          logger.info(`Reactivated user ${userId} during Stripe store update`);
        }

        return updatedStore;
      } else {
        // Step 2: Store doesn't exist, prioritize provided userId if available
        if (providedUserId) {
          // Check if provided userId exists and is valid
          const existingUserById = await this.prisma.user.findUnique({
            where: { id: providedUserId },
          });

          if (existingUserById) {
            userId = providedUserId;
            logger.info(`Using provided userId for Stripe account: ${accountDetails.id}, userId: ${providedUserId}`);
            
            // Reactivate user if inactive
            if (!existingUserById.isActive) {
              await this.prisma.user.update({
                where: { id: userId },
                data: { isActive: true },
              });
              logger.info(`Reactivated user ${userId} during Stripe store creation`);
            }
          } else {
            logger.warn(`Provided userId ${providedUserId} not found, falling back to email-based lookup`);
          }
        }

        // Step 3: If no valid userId from above, check if user exists with the email
        if (!userId) {
          const userEmail = accountDetails.email || `stripe_${accountDetails.id}@stripe.com`;
          
          const existingUser = await this.prisma.user.findUnique({
            where: {
              email: userEmail,
            },
          });

          if (existingUser) {
            // User exists, use existing user and reactivate if necessary
            userId = existingUser.id;
            
            if (!existingUser.isActive) {
              await this.prisma.user.update({
                where: { id: userId },
                data: {
                  isActive: true,
                }
              });
              logger.info(`Reactivated existing user for Stripe account: ${accountDetails.id}, email: ${userEmail}`);
            } else {
              logger.info(`Using existing active user for Stripe account: ${accountDetails.id}, email: ${userEmail}`);
            }
          } else {
            // User doesn't exist, create new user
            const DEFAULT_PASSWORD = "User@123";
            const hashedPassword = await bcrypt.hash(DEFAULT_PASSWORD, 10);
            userId = uuidv4();
            const userCode = `STRIPE_${accountDetails.id}_${Date.now()}`;

            try {
              await this.prisma.user.create({
                data: {
                  id: userId,
                  code: userCode,
                  fullName: this.getStripeStoreName(accountDetails),
                  phoneNumber: `+1000000${accountDetails.id.slice(-6)}`,
                  email: userEmail,
                  password: hashedPassword,
                  gender: "other",
                  birthDate: new Date(),
                  status: true,
                  note: `Auto-created from Stripe account: ${accountDetails.id}`,
                },
              });
              logger.info(`User created for Stripe account: ${accountDetails.id}, email: ${userEmail}`);
            } catch (error: any) {
              logger.error(`Failed to create user for Stripe account: ${accountDetails.id}`, error);
              throw error;
            }
          }
        }

        // Step 4: Create new LinkedStore with the userId (existing or new)
        if (!userId) {
          throw new Error('Unable to determine userId for Stripe store creation');
        }

        try {
          const linkedStore = await this.prisma.linkedStore.create({
            data: {
              userId: userId,
              storeName: this.getStripeStoreName(accountDetails),
              provider: 'stripe',
              providerStoreId: accountDetails.id,
              data: storeData,
            },
          });
          logger.info(`Stripe store created: ${linkedStore.id} for account: ${accountDetails.id} with userId: ${userId}`);

          return linkedStore;
        } catch (error: any) {
          logger.error(`Failed to create Stripe store for account: ${accountDetails.id}`, error);
          throw error;
        }
      }
    } catch (error: any) {
      logger.error('Error handling Stripe auth callback:', { error: error.message });
      throw new Error(
        `Failed to handle Stripe auth: ${(error as Error).message}`
      );
    }
  }

  /**
   * Disconnect/deactivate a Stripe store
   */
  async disconnectStore(storeId: string): Promise<void> {
    try {
      // Find the linked store
      const linkedStore = await this.prisma.linkedStore.findUnique({
        where: {
          id: storeId,
          provider: 'stripe'
        },
      });

      if (!linkedStore) {
        throw new Error(`Stripe store with ID ${storeId} not found`);
      }

      logger.info(`Disconnecting Stripe store: ${storeId} (providerStoreId: ${linkedStore.providerStoreId})`);

      // Deactivate the linked store (soft delete)
      await this.prisma.linkedStore.update({
        where: {
          id: storeId,
        },
        data: {
          isActive: false,
          // Clear sensitive data but keep for reference
          data: {
            ...linkedStore.data as any,
            disconnectedAt: new Date().toISOString(),
          },
        },
      });

      logger.info(`Stripe store ${storeId} has been disconnected successfully`);
    } catch (error: any) {
      logger.error('Error disconnecting Stripe store:', { error: error.message, storeId });
      throw new Error(`Failed to disconnect Stripe store: ${error.message}`);
    }
  }

  /**
   * Generate a display name for the Stripe account
   */
  private getStripeStoreName(account: Stripe.Account): string {
    // Try business name first
    if (account.business_profile?.name) {
      return account.business_profile.name;
    }
    
    // Try email
    if (account.email) {
      return account.email;
    }
    
    // Fallback to account ID
    return `Stripe Account ${account.id}`;
  }
}