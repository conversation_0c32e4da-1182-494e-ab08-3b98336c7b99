import PrismaService from './prisma.service';
import logger from '@/utils/logger.util';
import { BillService } from './bill.service';
import { paypalService } from './paypal.service';
import { BillStatus } from '@prisma/client';
import { BLOCK_PRICING } from '@/config/block-pricing.config';
import PaymentHistoryService from './payment-history.service';
import { usageTrackingService } from './usage-tracking.service';

const prisma = PrismaService.getInstance().getClient();

interface BalanceDeductionResult {
  success: boolean;
  newBalance?: number;
  error?: string;
}

class StoreBalanceService {
  private static readonly DEFAULT_TOPUP_AMOUNT = 10000; // $100 in cents
  private static readonly MIN_BALANCE_THRESHOLD = 1000; // $10 in cents
  private static readonly BALANCE_SETTING_KEY = 'current_balance';
  private static readonly BLOCK_FEE_PERCENTAGE = 0.01; // 1% of transaction amount

  /**
   * Get current balance for a store
   */
  static async getCurrentBalance(storeId: string): Promise<number> {
    try {
      const setting = await prisma.storeSettings.findUnique({
        where: {
          storeId_name: {
            storeId,
            name: this.BALANCE_SETTING_KEY
          }
        }
      });

      if (setting) {
        return parseInt(setting.value);
      }

      // Create new setting with value 0 if it doesn't exist
      await prisma.storeSettings.create({
        data: {
          storeId,
          name: this.BALANCE_SETTING_KEY,
          value: '0'
        }
      });

      logger.info(`Created new balance setting for store ${storeId} with initial value: 0`);
      return 0;
    } catch (error: any) {
      logger.error('Error getting current balance:', error);
      return 0;
    }
  }

  /**
   * Update store balance
   */
  static async updateBalance(storeId: string, newBalance: number): Promise<void> {
    try {
      await prisma.storeSettings.upsert({
        where: {
          storeId_name: {
            storeId,
            name: this.BALANCE_SETTING_KEY
          }
        },
        update: {
          value: newBalance.toString()
        },
        create: {
          storeId,
          name: this.BALANCE_SETTING_KEY,
          value: newBalance.toString()
        }
      });

      logger.info(`Updated balance for store ${storeId}: ${newBalance}`);

      // Check if balance fell below minimum threshold
      if (newBalance < this.MIN_BALANCE_THRESHOLD && newBalance >= 0) {
        logger.info(`Balance for store ${storeId} is below minimum threshold. Initiating proactive topup.`);
        // Run proactive topup in background
        this.performProactiveTopup(storeId).catch(error => {
          logger.error(`Failed to perform proactive topup for store ${storeId}:`, error);
        });
      }
    } catch (error: any) {
      logger.error('Error updating balance:', error);
      throw error;
    }
  }

  /**
   * Get block processing fee based on block type
   */
  static getBlockFee(blockType: 'RDR' | 'ETHOCA'): number {
    return BLOCK_PRICING[blockType];
  }

  /**
   * Deduct balance from store account
   */
  static async deductBalance(storeId: string, amount: number, description: string): Promise<BalanceDeductionResult> {
    try {
      const currentBalance = await this.getCurrentBalance(storeId);

      if (currentBalance >= amount) {
        // Sufficient balance, proceed with deduction
        const newBalance = currentBalance - amount;
        await this.updateBalance(storeId, newBalance);

        // Create bill record for the deduction
        const bill = await BillService.createBill({
          linkedStoreId: storeId,
          amount: -amount, // Negative amount to indicate deduction
          currency: 'USD',
          description: description,
          status: BillStatus.PAID,
          dueDate: new Date()
        });

        // Create payment history record for the block processing fee
        await PaymentHistoryService.createPaymentHistory({
          linkedStoreId: storeId,
          billId: bill.id,
          amount: -amount, // Negative amount to match the bill
          currency: 'USD',
          description: description,
          status: 'SUCCEEDED',
          paymentDate: new Date()
        });

        logger.info(`Deducted ${amount} from store ${storeId} balance. New balance: ${newBalance}`);

        // Check if balance is low
        if (newBalance < this.MIN_BALANCE_THRESHOLD) {
          logger.warn(`Low balance warning for store ${storeId}: ${newBalance}`);
          // TODO: Send low balance notification
        }

        return { success: true, newBalance };
      } else {
        // Insufficient balance, attempt auto topup
        logger.info(`Insufficient balance for store ${storeId}. Current: ${currentBalance}, Required: ${amount}`);

        // Check if store can process more (monthly limit) - for Shopify billing
        const canProcess = await usageTrackingService.canProcessBlock(storeId);

        if (!canProcess) {
          logger.error(`Store ${storeId} has reached monthly usage limit`);
          return {
            success: false,
            error: 'Monthly usage limit reached ($799.99). Service will resume next billing cycle.'
          };
        }

        // Determine which topup method to use
        const linkedStore = await (prisma as any).linkedStore.findFirst({
          where: {
            id: storeId,
            provider: 'shopify'
          }
        });

        let topupSuccess = false;

        if (linkedStore) {
          // Use Shopify auto topup
          topupSuccess = await this.performAutoTopupShopify(storeId);
        }

        if (topupSuccess) {
          // After successful topup, retry deduction
          return await this.deductBalance(storeId, amount, description);
        }

        return {
          success: false,
          error: 'Insufficient balance and auto-topup failed'
        };
      }
    } catch (error: any) {
      logger.error('Error deducting balance:', error);
      return {
        success: false,
        error: error.message || 'Failed to deduct balance'
      };
    }
  }

  /**
   * Perform automatic topup for Shopify auto billing
   * This creates a usage charge against the existing subscription
   */
  static async performAutoTopupShopify(storeId: string): Promise<boolean> {
    try {
      logger.info(`Starting Shopify auto topup for store ${storeId}`);

      // Import ShopifyService dynamically to avoid circular dependency
      const { default: ShopifyService } = await import('./shopify.service');
      const shopifyService = new ShopifyService();

      // Get existing subscription for this store
      const subscription = await shopifyService.getSubscription(storeId);
      
      if (!subscription) {
        logger.error(`No subscription found for store ${storeId}`);
        return false;
      }

      if (subscription.status !== 'ACTIVE') {
        logger.error(`Subscription not active for store ${storeId}. Status: ${subscription.status}`);
        return false;
      }

      // Create a usage record for the topup amount
      const usageRecord = await shopifyService.createUsageRecord(
        storeId,
        `Balance top-up - $${this.DEFAULT_TOPUP_AMOUNT / 100}`,
        1, // quantity
        this.DEFAULT_TOPUP_AMOUNT // price in cents
      );

      if (usageRecord) {
        // Update the account balance
        const currentBalance = await this.getCurrentBalance(storeId);
        const newBalance = currentBalance + this.DEFAULT_TOPUP_AMOUNT;
        await this.updateBalance(storeId, newBalance);

        // Create a bill record for tracking
        const bill = await BillService.createBill({
          linkedStoreId: storeId,
          amount: this.DEFAULT_TOPUP_AMOUNT,
          currency: 'USD',
          description: 'Automatic balance top-up (Shopify Auto Billing)',
          status: BillStatus.PAID, // Usage charges are automatically billed
          dueDate: new Date()
        });

        // Create payment history record
        await PaymentHistoryService.createPaymentHistory({
          linkedStoreId: storeId,
          billId: bill.id,
          amount: this.DEFAULT_TOPUP_AMOUNT,
          currency: 'USD',
          description: 'Shopify auto billing topup',
          status: 'SUCCEEDED',
          paymentDate: new Date()
        });

        logger.info(`Successfully created usage charge for $${this.DEFAULT_TOPUP_AMOUNT / 100} for store ${storeId}`);
        return true;
      } else {
        logger.error(`Failed to create usage charge for store ${storeId}`);
        return false;
      }
    } catch (error: any) {
      logger.error('Error performing Shopify auto topup:', error);
      return false;
    }
  }

  /**
   * Perform automatic topup for a store (Legacy - for non-Shopify billing)
   */
  static async performAutoTopup(storeId: string): Promise<boolean> {
    try {
      logger.info(`Starting auto topup for store ${storeId}`);

      // Check available payment methods
      const paymentMethods = await this.getAvailablePaymentMethods(storeId);

      if (paymentMethods.paypal) {
        return await this.topupViaPayPal(storeId);
      } else if (paymentMethods.stripe) {
        return await this.topupViaStripe(storeId);
      } else {
        logger.error(`No payment methods available for store ${storeId}`);
        // TODO: Create notification for user to update payment method
        await this.createPaymentMethodUpdateNotification(storeId);
        return false;
      }
    } catch (error: any) {
      logger.error('Error performing auto topup:', error);
      return false;
    }
  }

  /**
   * Check available payment methods for a store
   */
  static async getAvailablePaymentMethods(storeId: string): Promise<{ paypal: boolean, stripe: boolean }> {
    try {
      const [paypalAgreement, stripeAccount] = await Promise.all([
        prisma.payPalAgreement.findFirst({
          where: {
            linkedStoreId: storeId,
            status: 'ACTIVE'
          }
        }),
        prisma.stripeAccount.findUnique({
          where: {
            storeId: storeId
          }
        })
      ]);

      return {
        paypal: !!paypalAgreement,
        stripe: !!(stripeAccount && stripeAccount.setupCompleted)
      };
    } catch (error: any) {
      logger.error('Error checking payment methods:', error);
      return { paypal: false, stripe: false };
    }
  }

  /**
   * Topup via PayPal
   */
  static async topupViaPayPal(storeId: string): Promise<boolean> {
    try {
      const agreement = await prisma.payPalAgreement.findFirst({
        where: {
          linkedStoreId: storeId,
          status: 'ACTIVE'
        }
      });

      if (!agreement) {
        logger.error(`No active PayPal agreement for store ${storeId}`);
        return false;
      }

      // Create bill for topup
      const bill = await BillService.createBill({
        linkedStoreId: storeId,
        amount: this.DEFAULT_TOPUP_AMOUNT,
        currency: 'USD',
        description: 'Automatic balance top-up via PayPal',
        status: BillStatus.PENDING,
        dueDate: new Date()
      });

      try {
        // Charge via PayPal
        const payment = await paypalService.chargeAgreement(
          agreement.agreementId,
          this.DEFAULT_TOPUP_AMOUNT,
          bill.id
        );

        if (payment) {
          // Update bill status
          await BillService.updateBill(bill.id, {
            status: BillStatus.PAID
          });

          // Update balance
          const currentBalance = await this.getCurrentBalance(storeId);
          await this.updateBalance(storeId, currentBalance + this.DEFAULT_TOPUP_AMOUNT);

          // Create payment history record
          await prisma.paymentHistory.create({
            data: {
              linkedStoreId: storeId,
              billId: bill.id,
              amount: this.DEFAULT_TOPUP_AMOUNT,
              currency: 'USD',
              description: `PayPal payment - ${payment.payment.id}`,
              status: 'SUCCEEDED',
              paymentDate: new Date()
            }
          });

          logger.info(`Successfully topped up ${this.DEFAULT_TOPUP_AMOUNT} for store ${storeId} via PayPal`);
          return true;
        }
      } catch (paymentError) {
        // Update bill status to failed
        await BillService.updateBill(bill.id, {
          status: BillStatus.FAILED
        });
        throw paymentError;
      }

      return false;
    } catch (error: any) {
      logger.error('Error topping up via PayPal:', error);
      return false;
    }
  }

  /**
   * Topup via Stripe
   */
  static async topupViaStripe(storeId: string): Promise<boolean> {
    try {
      const stripeAccount = await prisma.stripeAccount.findUnique({
        where: {
          storeId: storeId
        }
      });

      if (!stripeAccount || !stripeAccount.setupCompleted || !stripeAccount.paymentMethodId) {
        logger.error(`No valid Stripe payment method for store ${storeId}`);
        return false;
      }

      // Create bill for topup
      const bill = await BillService.createBill({
        linkedStoreId: storeId,
        amount: this.DEFAULT_TOPUP_AMOUNT,
        currency: 'USD',
        description: 'Automatic balance top-up via Stripe',
        status: BillStatus.PENDING,
        dueDate: new Date()
      });

      try {
        // Import Stripe
        const { getStripeInstance } = await import('../config/stripe');
        const stripe = getStripeInstance();

        // Create payment intent using stored payment method
        const paymentIntent = await stripe.paymentIntents.create({
          amount: this.DEFAULT_TOPUP_AMOUNT,
          currency: 'usd',
          customer: stripeAccount.stripeCustomerId!,
          payment_method: stripeAccount.paymentMethodId,
          off_session: true,
          confirm: true,
          metadata: {
            billId: bill.id,
            storeId: storeId,
            type: 'auto_topup'
          }
        });

        if (paymentIntent.status === 'succeeded') {
          // Update bill status
          await BillService.updateBill(bill.id, {
            status: BillStatus.PAID
          });

          // Update balance
          const currentBalance = await this.getCurrentBalance(storeId);
          await this.updateBalance(storeId, currentBalance + this.DEFAULT_TOPUP_AMOUNT);

          // Create payment history record
          await prisma.paymentHistory.create({
            data: {
              linkedStoreId: storeId,
              billId: bill.id,
              amount: this.DEFAULT_TOPUP_AMOUNT,
              currency: 'USD',
              description: `Stripe payment - ${paymentIntent.id}`,
              status: 'SUCCEEDED',
              paymentDate: new Date()
            }
          });

          logger.info(`Successfully topped up ${this.DEFAULT_TOPUP_AMOUNT} for store ${storeId} via Stripe`);
          return true;
        } else {
          logger.error(`Payment intent not succeeded: ${paymentIntent.status}`);
          throw new Error(`Payment failed with status: ${paymentIntent.status}`);
        }
      } catch (paymentError: any) {
        // Update bill status to failed
        await BillService.updateBill(bill.id, {
          status: BillStatus.FAILED
        });

        // Create failed payment history record
        await prisma.paymentHistory.create({
          data: {
            linkedStoreId: storeId,
            billId: bill.id,
            amount: this.DEFAULT_TOPUP_AMOUNT,
            currency: 'USD',
            description: `Failed Stripe payment - ${paymentError.message || 'Unknown error'}`,
            status: 'FAILED',
            paymentDate: new Date()
          }
        });

        throw paymentError;
      }
    } catch (error: any) {
      logger.error('Error topping up via Stripe:', error);
      return false;
    }
  }

  /**
   * Perform proactive topup to maintain minimum balance
   * This is triggered when balance falls below $10 after a deduction
   */
  static async performProactiveTopup(storeId: string): Promise<void> {
    try {
      logger.info(`Starting proactive topup for store ${storeId} to maintain minimum balance`);

      // Check if store can process more (monthly limit)
      const canProcess = await usageTrackingService.canProcessBlock(storeId);

      if (!canProcess) {
        logger.warn(`Store ${storeId} has reached monthly usage limit. Cannot perform proactive topup.`);
        return;
      }

      // Get store info to check if using Shopify billing
      const linkedStore = await (prisma as any).linkedStore.findFirst({
        where: {
          id: storeId,
          provider: 'shopify'
        }
      });

      if (linkedStore) {
        // Store uses Shopify billing - perform Shopify topup
        const topupSuccess = await this.performAutoTopupShopify(storeId);

        if (topupSuccess) {
          logger.info(`Proactive topup completed successfully for store ${storeId}`);
        } else {
          logger.error(`Proactive topup failed for store ${storeId}`);
        }
      } else {
        // Legacy billing - use PayPal/Stripe
        const topupSuccess = await this.performAutoTopup(storeId);

        if (topupSuccess) {
          logger.info(`Proactive topup completed successfully for store ${storeId}`);
        } else {
          logger.error(`Proactive topup failed for store ${storeId}`);
        }
      }
    } catch (error: any) {
      logger.error(`Error performing proactive topup for store ${storeId}:`, error);
    }
  }

  /**
   * Create notification for payment method update
   */
  static async createPaymentMethodUpdateNotification(storeId: string): Promise<void> {
    try {
      // TODO: Implement notification system
      // For now, just log the warning
      logger.warn(`Store ${storeId} needs to update payment method for auto-topup`);

      // You can implement email notification, in-app notification, etc.
      // Example: await NotificationService.send(storeId, 'PAYMENT_METHOD_REQUIRED', {...})
    } catch (error: any) {
      logger.error('Error creating payment method update notification:', error);
    }
  }

  /**
   * Manual topup (for user-initiated topups)
   */
  static async manualTopup(storeId: string, amount: number, paymentMethod: 'PAYPAL' | 'STRIPE'): Promise<boolean> {
    try {
      const topupAmount = Math.max(amount, 1000); // Minimum $10 topup

      if (paymentMethod === 'PAYPAL') {
        // Update the topup amount temporarily
        const originalAmount = this.DEFAULT_TOPUP_AMOUNT;
        (this as any).DEFAULT_TOPUP_AMOUNT = topupAmount;
        const result = await this.topupViaPayPal(storeId);
        (this as any).DEFAULT_TOPUP_AMOUNT = originalAmount;
        return result;
      } else if (paymentMethod === 'STRIPE') {
        // Update the topup amount temporarily
        const originalAmount = this.DEFAULT_TOPUP_AMOUNT;
        (this as any).DEFAULT_TOPUP_AMOUNT = topupAmount;
        const result = await this.topupViaStripe(storeId);
        (this as any).DEFAULT_TOPUP_AMOUNT = originalAmount;
        return result;
      }

      return false;
    } catch (error: any) {
      logger.error('Error performing manual topup:', error);
      return false;
    }
  }

  /**
   * Check and maintain minimum balance for all active stores or a specific store
   * This can be called by a scheduled job
   */
  static async checkAndMaintainMinimumBalances(storeId?: string): Promise<void> {
    try {
      if (storeId) {
        // Check balance for specific store
        logger.info(`Starting balance check for specific store: ${storeId}`);
        
        const balance = await this.getCurrentBalance(storeId);
        
        if (balance >= 0 && balance < this.MIN_BALANCE_THRESHOLD) {
          logger.info(`Store ${storeId} has low balance: $${balance / 100}. Initiating proactive topup.`);
          
          // Run proactive topup for the specific store
          await this.performProactiveTopup(storeId);
          
          logger.info(`Completed balance check for store ${storeId}`);
        } else {
          logger.info(`Store ${storeId} has sufficient balance: $${balance / 100}. No topup needed.`);
        }
      } else {
        // Check balance for all stores
        logger.info('Starting scheduled check for minimum balances');

        // Get all stores with low balance
        const settings = await prisma.storeSettings.findMany({
          where: {
            name: this.BALANCE_SETTING_KEY,
            value: {
              lt: this.MIN_BALANCE_THRESHOLD.toString()
            }
          }
        });

        logger.info(`Found ${settings.length} stores with balance below minimum threshold`);

        for (const setting of settings) {
          const balance = parseInt(setting.value);

          // Only process if balance is positive but below threshold
          if (balance >= 0 && balance < this.MIN_BALANCE_THRESHOLD) {
            logger.info(`Store ${setting.storeId} has low balance: $${balance / 100}. Initiating proactive topup.`);

            // Run proactive topup in background for each store
            this.performProactiveTopup(setting.storeId).catch(error => {
              logger.error(`Failed to perform proactive topup for store ${setting.storeId}:`, error);
            });
          }
        }

        logger.info('Completed scheduled check for minimum balances');
      }
    } catch (error: any) {
      logger.error('Error checking minimum balances:', error);
    }
  }
}

export default StoreBalanceService;