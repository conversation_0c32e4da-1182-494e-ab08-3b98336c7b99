import { PrismaClient } from "@prisma/client";
import PrismaService from "./prisma.service";
import ShopifyService from "./shopify.service";
import { ShopifyStoreData } from "../models/linked-store.model";
import { CreateShopifyOrder } from "../models/shopify-order.model";
import { CreateShopifyPayout } from "../models/shopify-payout.model";
import { CreateShopifyDispute } from "../models/shopify-dispute.model";
import { CreateShopifyTransaction } from "../models/shopify-transaction.model";

export interface SyncOptions {
  forceSync?: boolean;
  syncTypes?: ('orders' | 'payouts' | 'disputes' | 'transactions')[];
}

class ShopifySyncService {
  private prisma: PrismaClient;
  private shopifyService: ShopifyService;

  constructor() {
    this.prisma = PrismaService.getInstance().getClient();
    this.shopifyService = new ShopifyService();
  }

  async syncStoreData(linkedStoreId: string, options: SyncOptions = {}, shop: string, accessToken: string): Promise<void> {
    const { forceSync = false, syncTypes = ['orders', 'payouts', 'disputes', 'transactions'] } = options;

    try {
      // Use provided shop and accessToken for direct Shopify API access
      const client = this.shopifyService.createRestClient(shop, accessToken);

      // Get existing store data for updating lastSyncedAt
      const linkedStore = await (this.prisma as any).linkedStore.findUnique({
        where: { id: linkedStoreId }
      });

      if (!linkedStore || linkedStore.provider !== 'shopify') {
        throw new Error('Linked store not found or not a Shopify store');
      }

      const storeData = linkedStore.data as ShopifyStoreData;

      await Promise.all([
        syncTypes.includes('orders') && this.syncOrders(client, linkedStoreId, forceSync),
        syncTypes.includes('payouts') && this.syncPayouts(client, linkedStoreId, forceSync),
        syncTypes.includes('disputes') && this.syncDisputes(client, linkedStoreId, forceSync),
        syncTypes.includes('transactions') && this.syncTransactions(client, linkedStoreId, forceSync)
      ].filter(Boolean));

      // Update lastSyncedAt timestamp
      await (this.prisma as any).linkedStore.update({
        where: { id: linkedStoreId },
        data: {
          data: {
            ...storeData,
            lastSyncedAt: new Date().toISOString()
          }
        }
      });

    } catch (error) {
      console.error(`Error syncing store data for ${linkedStoreId}:`, error);
      throw error;
    }
  }

  private async syncOrders(client: any, linkedStoreId: string, forceSync: boolean): Promise<void> {
    try {
      let allOrders: any[] = [];
      let sinceId: string | undefined = undefined;
      const limit = 250; // Shopify's max limit per request

      // Fetch all orders using pagination
      while (true) {
        const query: any = {
          status: 'any',
          limit: limit
        };

        if (sinceId) {
          query.since_id = sinceId;
        } else {
          query.order = 'id asc'; // Only use order when not using since_id
        }

        const ordersResponse = await client.get({
          path: 'orders',
          query: query
        });

        const orders = ordersResponse.body.orders || [];

        if (orders.length === 0) {
          break; // No more orders
        }

        allOrders = allOrders.concat(orders);

        const records = orders.map((order: any) => this.buildOrderData(order, linkedStoreId));

        await (this.prisma as any).shopifyOrder.createMany({
          data: records,
          skipDuplicates: true
        });

        // Sync transactions for each order in this batch
        for (const order of orders) {
          await this.syncOrderTransactions(client, linkedStoreId, order.id.toString());
        }

        // // Sync transactions for each order in this batch (in parallel)
        // await Promise.all(
        //   orders.map((order: any) =>
        //     this.syncOrderTransactions(client, linkedStoreId, order.id.toString())
        //   )
        // );

        // If we got less than the limit, we've reached the end
        if (orders.length < limit) {
          break;
        }

        // Set since_id to the last order's id for next iteration
        sinceId = orders[orders.length - 1].id;
      }

      console.log(`Synced ${allOrders.length} orders for store ${linkedStoreId}`);
    } catch (error) {
      console.error('Error syncing orders:', error);
      if ((error as any).response?.status !== 403) {
        throw error;
      }
    }
  }

  private buildOrderData(order: any, linkedStoreId: string): CreateShopifyOrder {
    return {
      id: BigInt(order.id),
      linkedStoreId,
      name: order.name,
      note: order.note,
      tags: order.tags,
      test: order.test,
      email: order.email,
      phone: order.phone,
      token: order.token,
      appId: order.app_id,
      number: order.number,
      orderNumber: order.order_number,
      currency: order.currency,
      customerData: order.customer,
      customer: order.customer,
      closedAt: order.closed_at ? new Date(order.closed_at) : null,
      confirmed: order.confirmed,
      confirmationNumber: order.confirmation_number,
      deviceId: order.device_id?.toString(),
      poNumber: order.po_number,
      reference: order.reference,
      cancelReason: order.cancel_reason,
      cancelledAt: order.cancelled_at ? new Date(order.cancelled_at) : null,
      taxData: order.tax_lines,
      taxLines: order.tax_lines,
      totalTax: order.total_tax ? parseFloat(order.total_tax) : null,
      totalTaxSet: order.total_tax_set,
      browserIp: order.browser_ip,
      cartToken: order.cart_token,
      checkoutToken: order.checkout_token,
      clientDetails: order.client_details,
      company: order.company,
      createdAt: order.created_at ? new Date(order.created_at) : null,
      updatedAt: order.updated_at ? new Date(order.updated_at) : null,
      processedAt: order.processed_at ? new Date(order.processed_at) : null,
      lineItems: order.line_items,
      discountApplications: order.discount_applications,
      discountCodes: order.discount_codes,
      sourceUrl: order.source_url,
      sourceIdentifier: order.source_identifier,
      sourceName: order.source_name,
      taxExempt: order.tax_exempt,
      checkoutId: order.checkout_id ? BigInt(order.checkout_id) : null,
      locationId: order.location_id ? BigInt(order.location_id) : null,
      merchantBusinessEntityId: order.merchant_business_entity_id,
      merchantOfRecordAppId: order.merchant_of_record_app_id ? BigInt(order.merchant_of_record_app_id) : null,
      totalPrice: order.total_price ? parseFloat(order.total_price) : null,
      totalPriceSet: order.total_price_set,
      currentTotalPrice: order.current_total_price ? parseFloat(order.current_total_price) : null,
      currentTotalPriceSet: order.current_total_price_set,
      subtotalPrice: order.subtotal_price ? parseFloat(order.subtotal_price) : null,
      subtotalPriceSet: order.subtotal_price_set,
      currentSubtotalPrice: order.current_subtotal_price ? parseFloat(order.current_subtotal_price) : null,
      currentSubtotalPriceSet: order.current_subtotal_price_set,
      totalDiscounts: order.total_discounts ? parseFloat(order.total_discounts) : null,
      totalDiscountsSet: order.total_discounts_set,
      currentTotalDiscounts: order.current_total_discounts ? parseFloat(order.current_total_discounts) : null,
      currentTotalDiscountsSet: order.current_total_discounts_set,
      totalLineItemsPrice: order.total_line_items_price ? parseFloat(order.total_line_items_price) : null,
      totalLineItemsPriceSet: order.total_line_items_price_set,
      totalShippingPriceSet: order.total_shipping_price_set,
      totalOutstanding: order.total_outstanding ? parseFloat(order.total_outstanding) : null,
      totalTipReceived: order.total_tip_received ? parseFloat(order.total_tip_received) : null,
      totalWeight: order.total_weight,
      fulfillments: order.fulfillments,
      fulfillmentStatus: order.fulfillment_status,
      landingSite: order.landing_site,
      landingSiteRef: order.landing_site_ref,
      referringSite: order.referring_site,
      contactEmail: order.contact_email,
      paymentTerms: order.payment_terms,
      paymentGatewayNames: order.payment_gateway_names,
      shippingLines: order.shipping_lines,
      taxesIncluded: order.taxes_included,
      billingAddress: order.billing_address,
      shippingAddress: order.shipping_address,
      customerLocale: order.customer_locale,
      buyerAcceptsMarketing: order.buyer_accepts_marketing,
      refunds: order.refunds,
      dutiesIncluded: order.duties_included,
      currentTotalDutiesSet: order.current_total_duties_set,
      originalTotalDutiesSet: order.original_total_duties_set,
      estimatedTaxes: order.estimated_taxes,
      noteAttributes: order.note_attributes,
      financialStatus: order.financial_status,
      orderStatusUrl: order.order_status_url,
      currentTotalAdditionalFeesSet: order.current_total_additional_fees_set,
      originalTotalAdditionalFeesSet: order.original_total_additional_fees_set,
      totalCashRoundingPaymentAdjustmentSet: order.total_cash_rounding_payment_adjustment_set,
      totalCashRoundingRefundAdjustmentSet: order.total_cash_rounding_refund_adjustment_set,
      userId: order.user_id ? BigInt(order.user_id) : null
    };
  }

  private async syncPayouts(client: any, linkedStoreId: string, forceSync: boolean): Promise<void> {
    try {
      let since_id = undefined;

      if (!forceSync) {
        const lastPayout = await (this.prisma as any).shopifyPayout.findFirst({
          where: { linkedStoreId },
          orderBy: { id: 'desc' }
        });
        since_id = lastPayout?.id?.toString();
      }

      const params: any = { limit: 250 };
      if (since_id) params.since_id = since_id;

      const response = await client.get({
        path: 'shopify_payments/payouts',
        query: params
      });

      const payouts = response.body.payouts || [];

      for (const payout of payouts) {
        const payoutData: CreateShopifyPayout = {
          id: BigInt(payout.id),
          linkedStoreId,
          date: payout.date ? new Date(payout.date) : null,
          amount: payout.amount ? parseFloat(payout.amount) : null,
          status: payout.status,
          summary: payout.summary,
          currency: payout.currency
        };

        await (this.prisma as any).shopifyPayout.upsert({
          where: {
            id: payoutData.id
          },
          update: payoutData,
          create: payoutData
        });
      }

      console.log(`Synced ${payouts.length} payouts for store ${linkedStoreId}`);
    } catch (error) {
      console.error('Error syncing payouts:', error);
      if ((error as any).response?.status !== 403) {
        throw error;
      }
    }
  }

  private async syncDisputes(client: any, linkedStoreId: string, forceSync: boolean): Promise<void> {
    try {
      let since_id = undefined;

      if (!forceSync) {
        const lastDispute = await (this.prisma as any).shopifyDispute.findFirst({
          where: { linkedStoreId },
          orderBy: { id: 'desc' }
        });
        since_id = lastDispute?.id?.toString();
      }

      const params: any = { limit: 250 };
      if (since_id) params.since_id = since_id;

      const response = await client.get({
        path: 'shopify_payments/disputes',
        query: params
      });

      const disputes = response.body.disputes || [];

      for (const dispute of disputes) {
        const disputeData: CreateShopifyDispute = {
          id: BigInt(dispute.id),
          linkedStoreId,
          transactionId: dispute.transaction_id ? BigInt(dispute.transaction_id) : null,
          type: dispute.type,
          amount: dispute.amount ? parseFloat(dispute.amount) : null,
          reason: dispute.reason,
          status: dispute.status,
          currency: dispute.currency,
          orderId: dispute.order_id ? BigInt(dispute.order_id) : null,
          finalizedOn: dispute.finalized_on ? new Date(dispute.finalized_on) : null,
          initiatedAt: dispute.initiated_at ? new Date(dispute.initiated_at) : null,
          evidenceDueBy: dispute.evidence_due_by ? new Date(dispute.evidence_due_by) : null,
          evidenceSentOn: dispute.evidence_sent_on ? new Date(dispute.evidence_sent_on) : null,
          networkReasonCode: dispute.network_reason_code
        };

        await (this.prisma as any).shopifyDispute.upsert({
          where: {
            id: disputeData.id
          },
          update: disputeData,
          create: disputeData
        });
      }

      console.log(`Synced ${disputes.length} disputes for store ${linkedStoreId}`);
    } catch (error) {
      console.error('Error syncing disputes:', error);
      if ((error as any).response?.status !== 403) {
        throw error;
      }
    }
  }

  private async syncTransactions(client: any, linkedStoreId: string, forceSync: boolean): Promise<void> {
    try {
      let since_id = undefined;

      if (!forceSync) {
        const lastTransaction = await (this.prisma as any).shopifyTransaction.findFirst({
          where: { linkedStoreId },
          orderBy: { id: 'desc' }
        });
        since_id = lastTransaction?.id?.toString();
      }

      const params: any = { limit: 250 };
      if (since_id) params.since_id = since_id;

      const response = await client.get({
        path: 'shopify_payments/balance/transactions',
        query: params
      });

      const transactions = response.body.transactions || [];

      const transactionRecords = transactions.map((transaction: any) =>
        this.buildTransactionData(transaction, linkedStoreId)
      );

      await (this.prisma as any).shopifyTransaction.createMany({
        data: transactionRecords,
        skipDuplicates: true
      });

      console.log(`Synced ${transactions.length} transactions for store ${linkedStoreId}`);
    } catch (error) {
      console.error('Error syncing transactions:', error);
      if ((error as any).response?.status !== 403) {
        throw error;
      }
    }
  }

  private buildTransactionData(transaction: any, linkedStoreId: string, orderId?: string): CreateShopifyTransaction {
    return {
      id: BigInt(transaction.id),
      linkedStoreId,
      orderId: orderId ? BigInt(orderId) : (transaction.order_id ? BigInt(transaction.order_id) : null),
      payoutId: transaction.payout_id ? BigInt(transaction.payout_id) : null,
      fee: transaction.fee ? parseFloat(transaction.fee) : null,
      net: transaction.net ? parseFloat(transaction.net) : null,
      test: transaction.test,
      type: transaction.type,
      amount: transaction.amount ? parseFloat(transaction.amount) : null,
      amountRounding: transaction.amount_rounding ? parseFloat(transaction.amount_rounding) : null,
      authorization: transaction.authorization,
      authorizationExpiresAt: transaction.authorization_expires_at ? new Date(transaction.authorization_expires_at) : null,
      currency: transaction.currency,
      deviceId: transaction.device_id ? BigInt(transaction.device_id) : null,
      errorCode: transaction.error_code,
      extendedAuthorizationAttributes: transaction.extended_authorization_attributes,
      gateway: transaction.gateway,
      kind: transaction.kind,
      locationId: transaction.location_id,
      message: transaction.message,
      parentId: transaction.parent_id ? BigInt(transaction.parent_id) : null,
      paymentDetails: transaction.payment_details,
      paymentsRefundAttributes: transaction.payments_refund_attributes,
      processedAt: transaction.processed_at ? new Date(transaction.processed_at) : null,
      receipt: transaction.receipt,
      sourceName: transaction.source_name,
      status: transaction.status,
      totalUnsettledSet: transaction.total_unsettled_set,
      userId: transaction.user_id ? BigInt(transaction.user_id) : null,
      currencyExchangeAdjustment: transaction.currency_exchange_adjustment,
      manualPaymentGateway: transaction.manual_payment_gateway,
      sourceId: transaction.source_id ? BigInt(transaction.source_id) : null,
      sourceType: transaction.source_type,
      payoutStatus: transaction.payout_status,
      sourceOrderId: transaction.source_order_id ? BigInt(transaction.source_order_id) : null,
      adjustmentReason: transaction.adjustment_reason,
      sourceOrderTransactionId: transaction.source_order_transaction_id ? BigInt(transaction.source_order_transaction_id) : null,
      adjustmentOrderTransactions: transaction.adjustment_order_transactions,
      referenceCardNumber: transaction.reference_card_number,
      referenceAmount: transaction.reference_amount,
      referenceTransactionTime: transaction.reference_transaction_time ? new Date(transaction.reference_transaction_time) : null,
      referenceCurrency: transaction.reference_currency,
      referenceArn: transaction.reference_arn,
      referenceAuthorizationCode: transaction.reference_authorization_code
    };
  }

  private async syncOrderTransactions(client: any, linkedStoreId: string, orderId: string): Promise<void> {
    try {
      const response = await client.get({
        path: `orders/${orderId}/transactions`
      });

      const transactions = response.body.transactions || [];

      const transactionRecords = transactions.map((transaction: any) =>
        this.buildTransactionData(transaction, linkedStoreId, orderId)
      );

      await (this.prisma as any).shopifyTransaction.createMany({
        data: transactionRecords,
        skipDuplicates: true
      });

      console.log(`Synced ${transactions.length} transactions for order ${orderId}`);
    } catch (error) {
      console.error(`Error syncing transactions for order ${orderId}:`, error);
      if ((error as any).response?.status !== 403) {
        throw error;
      }
    }
  }

}

export default ShopifySyncService;