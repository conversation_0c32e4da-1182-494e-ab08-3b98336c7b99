import { ServiceResponse } from "../constants/type";
import { StatusCodes } from "http-status-codes";
import { getI18n } from "../middlewares/language.middleware";
import PrismaService from "./prisma.service";
import { AlertType, FeedbackStatus } from "../models/alert.model";

const prisma = PrismaService.getInstance().getClient();

class AlertFeedbackService {
  static getFeedbackByPredictorId = async (
    predictorId: string
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      const feedback = await prisma.alertFeedback.findUnique({
        where: { predictorId },
        include: { alert: true },
      });

      if (!feedback) {
        return {
          success: false,
          status: StatusCodes.NOT_FOUND,
          message:
            i18n.t("alertFeedback.common.notFound") || "Feedback not found",
        };
      }

      return {
        success: true,
        status: StatusCodes.OK,
        message:
          i18n.t("alertFeedback.crud.getFeedbackSuccess") ||
          "Get feedback successfully",
        data: feedback,
      };
    } catch (error: any) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message:
          error.message ||
          i18n.t("alertFeedback.crud.getFeedbackFailed") ||
          "Failed to get feedback",
      };
    }
  };

  static getFeedbacks = async (
    pageNumber: number,
    pageSize: number,
    filters: Record<string, any> = {}
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Xây dựng điều kiện where
      const where: any = {};

      // Thêm bộ lọc
      if (filters.outcome) {
        where.outcome = filters.outcome;
      }

      if (filters.refunded) {
        where.refunded = filters.refunded;
      }

      if (filters.isFraud) {
        where.isFraud = filters.isFraud;
      }

      // Bộ lọc theo từ khóa
      if (filters.keyword) {
        where.OR = [
          { outcome: { contains: filters.keyword, mode: "insensitive" } },
          { comments: { contains: filters.keyword, mode: "insensitive" } },
          { refundNo: { contains: filters.keyword, mode: "insensitive" } },
        ];
      }

      // Lấy feedbacks với phân trang
      const [feedbacks, total] = await Promise.all([
        prisma.alertFeedback.findMany({
          where,
          include: { alert: true },
          orderBy: { createdAt: "desc" },
          skip: (pageNumber - 1) * pageSize,
          take: pageSize,
        }),
        prisma.alertFeedback.count({ where }),
      ]);

      return {
        success: true,
        message:
          i18n.t("alertFeedback.crud.fetchSuccess") ||
          "Fetch feedbacks successfully",
        status: StatusCodes.OK,
        data: {
          total,
          items: feedbacks,
          pageNumber,
          pageSize,
          totalPages: Math.ceil(total / pageSize),
        },
      };
    } catch (error: any) {
      return {
        success: false,
        message:
          error.message ||
          i18n.t("alertFeedback.crud.fetchFailed") ||
          "Failed to fetch feedbacks",
        status: StatusCodes.INTERNAL_SERVER_ERROR,
      };
    }
  };

  static getFeedbacksByAlertType = async (
    pageNumber: number,
    pageSize: number,
    alertType: AlertType
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Lấy feedbacks cho một loại alert cụ thể
      const [feedbacks, total] = await Promise.all([
        prisma.alertFeedback.findMany({
          where: {
            alert: {
              type: alertType,
            },
          },
          include: { alert: true },
          orderBy: { createdAt: "desc" },
          skip: (pageNumber - 1) * pageSize,
          take: pageSize,
        }),
        prisma.alertFeedback.count({
          where: {
            alert: {
              type: alertType,
            },
          },
        }),
      ]);

      return {
        success: true,
        message:
          i18n.t("alertFeedback.crud.fetchSuccess") ||
          "Fetch feedbacks successfully",
        status: StatusCodes.OK,
        data: {
          total,
          items: feedbacks,
          pageNumber,
          pageSize,
          totalPages: Math.ceil(total / pageSize),
        },
      };
    } catch (error: any) {
      return {
        success: false,
        message:
          error.message ||
          i18n.t("alertFeedback.crud.fetchFailed") ||
          "Failed to fetch feedbacks",
        status: StatusCodes.INTERNAL_SERVER_ERROR,
      };
    }
  };

  static createFeedback = async (data: any): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Kiểm tra xem alert có tồn tại không
      const alert = await prisma.alert.findUnique({
        where: { id: data.predictorId },
      });

      if (!alert) {
        return {
          success: false,
          status: StatusCodes.NOT_FOUND,
          message: i18n.t("alert.common.notFound") || "Alert not found",
        };
      }

      // Kiểm tra xem feedback đã tồn tại chưa
      const existingFeedback = await prisma.alertFeedback.findUnique({
        where: { predictorId: data.predictorId },
      });

      if (existingFeedback) {
        return {
          success: false,
          status: StatusCodes.CONFLICT,
          message:
            i18n.t("alertFeedback.validation.feedbackExists") ||
            "Feedback already exists",
        };
      }

      // Tạo feedback
      const newFeedback = await prisma.alertFeedback.create({
        data: {
          ...data,
          id: undefined, // Để Prisma tự động tạo ID
        },
        include: { alert: true },
      });

      // Cập nhật trạng thái feedback của alert
      await prisma.alert.update({
        where: { id: data.predictorId },
        data: {
          feedbackStatus: FeedbackStatus.SENT,
          feedbackTime: new Date(),
        },
      });

      return {
        success: true,
        status: StatusCodes.CREATED,
        message:
          i18n.t("alertFeedback.crud.createSuccess") ||
          "Create feedback successfully",
        data: newFeedback,
      };
    } catch (error: any) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message:
          error.message ||
          i18n.t("alertFeedback.crud.createFailed") ||
          "Failed to create feedback",
      };
    }
  };

  static updateFeedback = async (
    id: string,
    data: any
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Kiểm tra xem feedback có tồn tại không
      const existingFeedback = await prisma.alertFeedback.findUnique({
        where: { id },
      });

      if (!existingFeedback) {
        return {
          success: false,
          status: StatusCodes.NOT_FOUND,
          message:
            i18n.t("alertFeedback.common.notFound") || "Feedback not found",
        };
      }

      // Cập nhật feedback
      const updatedFeedback = await prisma.alertFeedback.update({
        where: { id },
        data,
        include: { alert: true },
      });

      return {
        success: true,
        status: StatusCodes.OK,
        message:
          i18n.t("alertFeedback.crud.updateSuccess") ||
          "Update feedback successfully",
        data: updatedFeedback,
      };
    } catch (error: any) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message:
          error.message ||
          i18n.t("alertFeedback.crud.updateFailed") ||
          "Failed to update feedback",
      };
    }
  };

  static deleteFeedback = async (id: string): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Kiểm tra xem feedback có tồn tại không
      const existingFeedback = await prisma.alertFeedback.findUnique({
        where: { id },
        include: { alert: true },
      });

      if (!existingFeedback) {
        return {
          success: false,
          status: StatusCodes.NOT_FOUND,
          message:
            i18n.t("alertFeedback.common.notFound") || "Feedback not found",
        };
      }

      // Cập nhật lại trạng thái của alert
      await prisma.alert.update({
        where: { id: existingFeedback.predictorId },
        data: {
          feedbackStatus: FeedbackStatus.PENDING,
          feedbackTime: null,
        },
      });

      // Xóa feedback
      await prisma.alertFeedback.delete({
        where: { id },
      });

      return {
        success: true,
        status: StatusCodes.OK,
        message:
          i18n.t("alertFeedback.crud.deleteSuccess") ||
          "Delete feedback successfully",
      };
    } catch (error: any) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message:
          error.message ||
          i18n.t("alertFeedback.crud.deleteFailed") ||
          "Failed to delete feedback",
      };
    }
  };

  // Phương thức gửi feedback đến ETHOCA hoặc RDR
  static sendFeedback = async (id: string): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Kiểm tra xem feedback có tồn tại không
      const feedback = await prisma.alertFeedback.findUnique({
        where: { id },
        include: { alert: true },
      });

      if (!feedback) {
        return {
          success: false,
          status: StatusCodes.NOT_FOUND,
          message:
            i18n.t("alertFeedback.common.notFound") || "Feedback not found",
        };
      }

      // TODO: Implement logic to send feedback to ETHOCA or RDR based on alert.type
      // Giả lập việc gửi thành công
      const isSuccess = true;

      if (isSuccess) {
        // Cập nhật trạng thái sau khi gửi thành công
        await prisma.alert.update({
          where: { id: feedback.predictorId },
          data: {
            feedbackStatus: FeedbackStatus.SENT,
            feedbackTime: new Date(),
          },
        });

        return {
          success: true,
          status: StatusCodes.OK,
          message:
            i18n.t("alertFeedback.action.sendSuccess") ||
            "Send feedback successfully",
        };
      } else {
        // Cập nhật trạng thái sau khi gửi thất bại
        await prisma.alert.update({
          where: { id: feedback.predictorId },
          data: {
            feedbackStatus: FeedbackStatus.FAILED,
          },
        });

        return {
          success: false,
          status: StatusCodes.BAD_GATEWAY,
          message:
            i18n.t("alertFeedback.action.sendFailed") ||
            "Failed to send feedback",
        };
      }
    } catch (error: any) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message:
          error.message ||
          i18n.t("alertFeedback.action.sendError") ||
          "Error sending feedback",
      };
    }
  };
}

export default AlertFeedbackService;
