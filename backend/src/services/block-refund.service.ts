import { PrismaClient, Prisma } from "@prisma/client";
import { IBlockRefund, IRefundRequest } from "../models/block-refund.model";
import logger from "@/utils/logger.util";

const prisma = new PrismaClient();

export class BlockRefundService {
  /**
   * Create a new refund
   */
  async create(data: IRefundRequest): Promise<IBlockRefund> {
    try {
      const refund = await prisma.blockRefund.create({
        data: {
          blockId: data.blockId,
          storeId: data.storeId,
          orderId: data.orderId,
          amount: data.amount,
          currency: data.currency,
          reason: data.reason,
          note: data.note,
          notify: data.notify ?? true,
          restock: data.restock ?? true,
          refundLineItems: data.refundLineItems ? JSON.parse(JSON.stringify(data.refundLineItems)) : Prisma.DbNull,
          shipping: data.shipping ? JSON.parse(JSON.stringify(data.shipping)) : Prisma.DbNull,
          orderAdjustments: data.orderAdjustments ? JSON.parse(JSON.stringify(data.orderAdjustments)) : Prisma.DbNull,
          processedAt: new Date(),
        },
        include: {
          block: true,
          linkedStore: true,
          shopifyOrder: true,
        },
      });

      logger.info(`Refund created for block ${data.blockId}`);
      return this.formatRefund(refund);
    } catch (error: any) {
      logger.error("Error creating refund:", error);
      throw error;
    }
  }

  /**
   * Get refund by ID
   */
  async findById(id: string): Promise<IBlockRefund | null> {
    const refund = await prisma.blockRefund.findUnique({
      where: { id },
      include: {
        block: true,
        linkedStore: true,
        shopifyOrder: true,
      },
    });

    return refund ? this.formatRefund(refund) : null;
  }

  /**
   * Get all refunds with optional filters
   */
  async findAll(filters?: {
    blockId?: string;
    storeId?: string;
    orderId?: bigint;
  }): Promise<IBlockRefund[]> {
    const refunds = await prisma.blockRefund.findMany({
      where: filters,
      include: {
        block: true,
        linkedStore: true,
        shopifyOrder: true,
      },
      orderBy: { createdAt: "desc" },
    });

    return refunds.map(refund => this.formatRefund(refund));
  }

  /**
   * Update refund by ID
   */
  async update(id: string, data: Partial<IRefundRequest>): Promise<IBlockRefund> {
    try {
      const updateData: any = {};
      
      if (data.amount !== undefined) updateData.amount = data.amount;
      if (data.currency !== undefined) updateData.currency = data.currency;
      if (data.reason !== undefined) updateData.reason = data.reason;
      if (data.note !== undefined) updateData.note = data.note;
      if (data.notify !== undefined) updateData.notify = data.notify;
      if (data.restock !== undefined) updateData.restock = data.restock;
      if (data.refundLineItems !== undefined) {
        updateData.refundLineItems = data.refundLineItems ? JSON.parse(JSON.stringify(data.refundLineItems)) : Prisma.DbNull;
      }
      if (data.shipping !== undefined) {
        updateData.shipping = data.shipping ? JSON.parse(JSON.stringify(data.shipping)) : Prisma.DbNull;
      }
      if (data.orderAdjustments !== undefined) {
        updateData.orderAdjustments = data.orderAdjustments ? JSON.parse(JSON.stringify(data.orderAdjustments)) : Prisma.DbNull;
      }

      const refund = await prisma.blockRefund.update({
        where: { id },
        data: updateData,
        include: {
          block: true,
          linkedStore: true,
          shopifyOrder: true,
        },
      });

      logger.info(`Refund ${id} updated`);
      return this.formatRefund(refund);
    } catch (error: any) {
      logger.error(`Error updating refund ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete refund by ID
   */
  async delete(id: string): Promise<void> {
    try {
      await prisma.blockRefund.delete({
        where: { id },
      });

      logger.info(`Refund ${id} deleted`);
    } catch (error: any) {
      logger.error(`Error deleting refund ${id}:`, error);
      throw error;
    }
  }

  /**
   * Format refund to convert Decimal to number
   */
  private formatRefund(refund: any): IBlockRefund {
    return {
      ...refund,
      amount: Number(refund.amount),
    };
  }
}

export default BlockRefundService;