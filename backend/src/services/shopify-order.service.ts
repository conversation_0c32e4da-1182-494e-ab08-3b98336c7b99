import PrismaService from './prisma.service';
import {
  CreateShopifyOrder,
  ShopifyOrderModel,
  ShopifyOrderQuery
} from '../models/shopify-order.model';

class ShopifyOrderService {
  private static prisma: any;
  
  static initialize() {
    this.prisma = PrismaService.getInstance().getClient();
  }

  /**
   * Create a new Shopify order
   * @param data Shopify order data
   * @returns Created order
   */
  static async createOrder(data: CreateShopifyOrder): Promise<ShopifyOrderModel> {
    return this.prisma.shopifyOrder.create({
      data
    });
  }

  /**
   * Create multiple Shopify orders
   * @param data Array of Shopify order data
   * @returns Created orders
   */
  static async createManyOrders(data: CreateShopifyOrder[]): Promise<number> {
    const result = await this.prisma.shopifyOrder.createMany({
      data,
      skipDuplicates: true
    });
    return result.count;
  }

  /**
   * Get all Shopify orders with optional filtering
   * @param query Optional query parameters
   * @returns Array of orders
   */
  static async getAllOrders(query: ShopifyOrderQuery = {}): Promise<ShopifyOrderModel[]> {
    return this.prisma.shopifyOrder.findMany({
      where: query,
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  /**
   * Get Shopify orders for a specific linked store
   * @param linkedStoreId Linked store ID
   * @returns Array of orders for the store
   */
  static async getOrdersByLinkedStoreId(linkedStoreId: string): Promise<ShopifyOrderModel[]> {
    return this.prisma.shopifyOrder.findMany({
      where: { linkedStoreId },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  /**
   * Get a specific Shopify order by ID
   * @param id Order ID
   * @returns Order or null if not found
   */
  static async getOrderById(id: bigint): Promise<ShopifyOrderModel | null> {
    return this.prisma.shopifyOrder.findUnique({
      where: { id }
    });
  }

  /**
   * Get a specific Shopify order by store ID and order number
   * @param linkedStoreId Linked store ID
   * @param orderNumber Order number from Shopify
   * @returns Order or null if not found
   */
  static async getOrderByStoreIdAndOrderNumber(
    linkedStoreId: string, 
    orderNumber: number
  ): Promise<ShopifyOrderModel | null> {
    return this.prisma.shopifyOrder.findFirst({
      where: { 
        linkedStoreId,
        orderNumber 
      }
    });
  }

  /**
   * Update an existing Shopify order
   * @param id Order ID
   * @param data Updated order data
   * @returns Updated order
   */
  static async updateOrder(id: bigint, data: Partial<CreateShopifyOrder>): Promise<ShopifyOrderModel> {
    return this.prisma.shopifyOrder.update({
      where: { id },
      data
    });
  }

  /**
   * Delete a Shopify order
   * @param id Order ID
   * @returns void
   */
  static async deleteOrder(id: bigint): Promise<void> {
    await this.prisma.shopifyOrder.delete({
      where: { id }
    });
  }

  /**
   * Count Shopify orders that match the given criteria
   * @param query Query parameters
   * @returns Count of matching orders
   */
  static async countOrders(query: ShopifyOrderQuery = {}): Promise<number> {
    return this.prisma.shopifyOrder.count({
      where: query
    });
  }

  /**
   * Get orders with pagination
   * @param page Page number (1-based)
   * @param limit Items per page
   * @param query Optional query parameters
   * @returns Paginated orders
   */
  static async getPaginatedOrders(
    page: number = 1, 
    limit: number = 10,
    query: ShopifyOrderQuery = {}
  ): Promise<{ data: ShopifyOrderModel[], total: number, page: number, limit: number }> {
    const skip = (page - 1) * limit;
    
    const [data, total] = await Promise.all([
      this.prisma.shopifyOrder.findMany({
        where: query,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc'
        }
      }),
      this.prisma.shopifyOrder.count({ where: query })
    ]);

    return {
      data,
      total,
      page,
      limit
    };
  }
}

// Initialize when module is loaded
ShopifyOrderService.initialize();

export default ShopifyOrderService;
