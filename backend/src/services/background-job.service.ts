import { EventEmitter } from 'events';

export interface JobData {
  id: string;
  type: string;
  data: any;
  priority?: number;
  delay?: number;
  attempts?: number;
  maxAttempts?: number;
  createdAt: Date;
  processedAt?: Date;
  completedAt?: Date;
  failedAt?: Date;
  error?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
}

export interface JobProcessor {
  (data: any): Promise<void>;
}

class BackgroundJobService extends EventEmitter {
  private jobs: Map<string, JobData> = new Map();
  private processors: Map<string, JobProcessor> = new Map();
  private processing: boolean = false;
  private concurrency: number = 5;
  private activeJobs: Set<string> = new Set();
  private retryDelays: number[] = [1000, 5000, 15000, 30000, 60000];

  constructor(concurrency: number = 5) {
    super();
    this.concurrency = concurrency;
  }

  registerProcessor(jobType: string, processor: JobProcessor): void {
    this.processors.set(jobType, processor);
  }

  async addJob(
    type: string,
    data: any,
    options: {
      priority?: number;
      delay?: number;
      attempts?: number;
      maxAttempts?: number;
    } = {}
  ): Promise<string> {
    const jobId = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const job: JobData = {
      id: jobId,
      type,
      data,
      priority: options.priority || 0,
      delay: options.delay || 0,
      attempts: options.attempts || 0,
      maxAttempts: options.maxAttempts || 3,
      createdAt: new Date(),
      status: 'pending'
    };

    this.jobs.set(jobId, job);
    
    this.emit('job:added', job);
    
    if (job.delay && job.delay > 0) {
      setTimeout(() => {
        this.processJobs();
      }, job.delay);
    } else {
      this.processJobs();
    }

    return jobId;
  }

  private async processJobs(): Promise<void> {
    if (this.processing || this.activeJobs.size >= this.concurrency) {
      return;
    }

    this.processing = true;

    try {
      const pendingJobs = Array.from(this.jobs.values())
        .filter(job => 
          job.status === 'pending' && 
          !this.activeJobs.has(job.id) &&
          (job.delay === 0 || !job.delay || Date.now() - job.createdAt.getTime() >= job.delay)
        )
        .sort((a, b) => (b.priority || 0) - (a.priority || 0));

      const jobsToProcess = pendingJobs.slice(0, this.concurrency - this.activeJobs.size);

      const processingPromises = jobsToProcess.map(job => this.processJob(job));
      
      if (processingPromises.length > 0) {
        await Promise.allSettled(processingPromises);
      }
    } finally {
      this.processing = false;
      
      if (this.hasPendingJobs()) {
        setTimeout(() => this.processJobs(), 1000);
      }
    }
  }

  private async processJob(job: JobData): Promise<void> {
    if (!this.processors.has(job.type)) {
      job.status = 'failed';
      job.error = `No processor found for job type: ${job.type}`;
      job.failedAt = new Date();
      this.emit('job:failed', job);
      return;
    }

    this.activeJobs.add(job.id);
    job.status = 'processing';
    job.processedAt = new Date();
    
    this.emit('job:started', job);

    try {
      const processor = this.processors.get(job.type)!;
      await processor(job.data);
      
      job.status = 'completed';
      job.completedAt = new Date();
      this.emit('job:completed', job);
      
    } catch (error) {
      job.attempts = (job.attempts || 0) + 1;
      job.error = error instanceof Error ? error.message : String(error);
      
      if (job.attempts >= (job.maxAttempts || 3)) {
        job.status = 'failed';
        job.failedAt = new Date();
        this.emit('job:failed', job);
      } else {
        job.status = 'pending';
        const retryDelay = this.retryDelays[Math.min(job.attempts - 1, this.retryDelays.length - 1)];
        job.delay = retryDelay;
        job.createdAt = new Date();
        
        this.emit('job:retry', job);
        
        setTimeout(() => {
          this.processJobs();
        }, retryDelay);
      }
    } finally {
      this.activeJobs.delete(job.id);
    }
  }

  private hasPendingJobs(): boolean {
    return Array.from(this.jobs.values()).some(job => job.status === 'pending');
  }

  getJob(jobId: string): JobData | undefined {
    return this.jobs.get(jobId);
  }

  getJobsByType(type: string): JobData[] {
    return Array.from(this.jobs.values()).filter(job => job.type === type);
  }

  getJobsByStatus(status: JobData['status']): JobData[] {
    return Array.from(this.jobs.values()).filter(job => job.status === status);
  }

  getAllJobs(): JobData[] {
    return Array.from(this.jobs.values());
  }

  removeJob(jobId: string): boolean {
    return this.jobs.delete(jobId);
  }

  clearCompletedJobs(): number {
    const completedJobs = this.getJobsByStatus('completed');
    let removed = 0;
    
    for (const job of completedJobs) {
      if (this.jobs.delete(job.id)) {
        removed++;
      }
    }
    
    return removed;
  }

  clearFailedJobs(): number {
    const failedJobs = this.getJobsByStatus('failed');
    let removed = 0;
    
    for (const job of failedJobs) {
      if (this.jobs.delete(job.id)) {
        removed++;
      }
    }
    
    return removed;
  }

  getStats(): {
    total: number;
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    active: number;
  } {
    const jobs = this.getAllJobs();
    
    return {
      total: jobs.length,
      pending: jobs.filter(j => j.status === 'pending').length,
      processing: jobs.filter(j => j.status === 'processing').length,
      completed: jobs.filter(j => j.status === 'completed').length,
      failed: jobs.filter(j => j.status === 'failed').length,
      active: this.activeJobs.size
    };
  }

  start(): void {
    this.processJobs();
  }

  stop(): void {
    this.processing = false;
    this.removeAllListeners();
  }
}

let backgroundJobInstance: BackgroundJobService | null = null;

export const getBackgroundJobService = (): BackgroundJobService => {
  if (!backgroundJobInstance) {
    backgroundJobInstance = new BackgroundJobService();
    
    backgroundJobInstance.on('job:added', (job) => {
      console.log(`Job added: ${job.type} (${job.id})`);
    });
    
    backgroundJobInstance.on('job:started', (job) => {
      console.log(`Job started: ${job.type} (${job.id})`);
    });
    
    backgroundJobInstance.on('job:completed', (job) => {
      console.log(`Job completed: ${job.type} (${job.id})`);
    });
    
    backgroundJobInstance.on('job:failed', (job) => {
      console.error(`Job failed: ${job.type} (${job.id}) - ${job.error}`);
    });
    
    backgroundJobInstance.on('job:retry', (job) => {
      console.log(`Job retry: ${job.type} (${job.id}) - Attempt ${job.attempts}`);
    });
    
    backgroundJobInstance.start();
  }
  
  return backgroundJobInstance;
};

export default BackgroundJobService;