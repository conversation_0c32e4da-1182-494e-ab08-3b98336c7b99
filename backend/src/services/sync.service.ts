import { env } from "../config/environment";
import axios from "axios";

class SyncService {
  async syncSingleStore(storeId: string): Promise<void> {
    try {
      // Check if sync service is configured and available
      if (!env.SYNC_SERVICE_URL) {
        console.log("Sync service URL not configured, skipping sync");
        return;
      }
      
      console.log(`Attempting to sync store ${storeId} with sync service: ${env.SYNC_SERVICE_URL}`);
      
      const response = await axios.post(
        `${env.SYNC_SERVICE_URL}/sync/`,
        {
          store_id: storeId,
        },
        {
          timeout: 15000, // Increased to 15 second timeout
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
      console.log("Sync response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error syncing store:", error);
      // Don't throw error - just log it and continue
      // The OAuth callback should still work even if sync fails
      console.log("Sync failed, but continuing with OAuth callback");
    }
  }
}

export default SyncService;