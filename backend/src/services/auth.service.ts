import jwt, { Secret, SignOptions } from "jsonwebtoken";
import { env } from "@/config/environment";
import UserService from "@/services/user.service";
import { ServiceResponse } from "@/constants/type";
import redisClient from "@/config/redis";
import { RedisPrefix } from "@/constants/prefix";
import bcrypt from "bcryptjs";
import { StatusCodes } from "http-status-codes";
import { getI18n } from "@/middlewares/language.middleware";
import PrismaService from "./prisma.service";
import Convert from "@/utils/convert.util";


interface TokenResponse {
  token: string;
  expiresAt: Date;
}

const prisma = PrismaService.getInstance().getClient();

class AuthService {
  static register = async (data: any): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Check if user exists by email or phone
      const userExists = await prisma.user.findFirst({
        where: {
          OR: [{ email: data.email }, { phoneNumber: data.phoneNumber }],
        },
      });

      if (userExists) {
        return {
          success: false,
          message: i18n.t("auth.register.exists"),
          status: StatusCodes.CONFLICT,
        };
      }


      // Generate code
      const userCount = await prisma.user.count();
      const code = `USR${(userCount + 1).toString().padStart(3, "0")}`;

      // Hash password
      const hashedPassword = await bcrypt.hash(data.password, 10);

      // Create user
      const newUser = await prisma.user.create({
        data: {
          code,
          fullName: data.fullName,
          email: data.email,
          phoneNumber: data.phoneNumber,
          password: hashedPassword,
          gender: data.gender || "male",
          birthDate: data.birthDate || new Date(),
          address: data.address,
          note: data.note,
          status: data.status !== undefined ? data.status : true,
        },
      });

      return {
        success: true,
        message: i18n.t("auth.register.success"),
        status: StatusCodes.CREATED,
        data: {
          id: newUser.id,
          code: newUser.code,
          fullName: newUser.fullName,
          email: newUser.email,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: i18n.t("auth.register.failed"),
        status: StatusCodes.INTERNAL_SERVER_ERROR,
      };
    }
  };

  static login = async (data: any): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      const { email, password } = data;

      // Find user by email
      const user = await prisma.user.findUnique({
        where: { email },
      });

      if (!user) {
        return {
          success: false,
          message: i18n.t("auth.login.invalid"),
          status: StatusCodes.FORBIDDEN,
        };
      }

      // Verify password using bcrypt
      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        return {
          success: false,
          message: i18n.t("auth.login.invalid"),
          status: StatusCodes.FORBIDDEN,
        };
      }

      // Generate tokens if password is valid
      const userInfo = {
        id: user.id,
        code: user.code,
        email: user.email,
        phoneNumber: user.phoneNumber,
      };

      const [accessTokenResponse, refreshTokenResponse] = await Promise.all([
        AuthService.generateAccessToken(userInfo),
        AuthService.generateRefreshToken(userInfo),
      ]);

      return {
        success: true,
        message: i18n.t("auth.login.success"),
        status: StatusCodes.OK,
        data: {
          user: {
            ...userInfo,
            fullName: user.fullName,
          },
          accessToken: accessTokenResponse.token,
          refreshToken: refreshTokenResponse.token,
          accessTokenExpiresAt: accessTokenResponse.expiresAt,
          refreshTokenExpiresAt: refreshTokenResponse.expiresAt,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: i18n.t("auth.login.failed"),
        status: StatusCodes.INTERNAL_SERVER_ERROR,
      };
    }
  };

  static logout = async (
    refreshToken: string
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      const tokenDeleted = await prisma.token.deleteMany({
        where: { refreshToken },
      });

      if (!tokenDeleted || tokenDeleted.count === 0) {
        return {
          success: false,
          message: i18n.t("auth.logout.failed"),
          status: StatusCodes.BAD_REQUEST,
        };
      }

      return {
        success: true,
        message: i18n.t("auth.logout.success"),
        status: StatusCodes.OK,
      };
    } catch (error) {
      return {
        success: false,
        message: i18n.t("auth.logout.failed"),
        status: StatusCodes.INTERNAL_SERVER_ERROR,
      };
    }
  };

  static isRefreshTokenValid = async (token: string): Promise<boolean> => {
    try {
      const storedToken = await prisma.token.findFirst({
        where: { refreshToken: token },
      });
      return !!storedToken;
    } catch (error) {
      return false;
    }
  };

  static generateAccessToken = (user: any): TokenResponse => {
    const tokenPayload = {
      id: user.id,
      code: user.code,
      email: user.email,
      phoneNumber: user.phoneNumber,
      roles: user.roles,
    };

    const token = jwt.sign(
      tokenPayload,
      env.JWT_SECRET as Secret,
      {
        expiresIn: env.ACCESS_TOKEN_TTL,
        algorithm: "HS256",
      } as SignOptions
    );

    // Calculate expiration time
    const expiresIn = Convert.TTLToSecond(env.ACCESS_TOKEN_TTL);
    const expiresAt = new Date(Date.now() + expiresIn * 1000);

    // Store in Redis for quicker validation
    redisClient.setEx(
      `${RedisPrefix.ACCESS_TOKEN}_${token}`,
      expiresIn,
      user?.id
    );

    return {
      token,
      expiresAt,
    };
  };

  static generateRefreshToken = (user: any): TokenResponse => {
    try {
      const refreshToken = jwt.sign(
        { id: user.id },
        env.JWT_SECRET as Secret,
        {
          expiresIn: env.REFRESH_TOKEN_TTL,
          algorithm: "HS256",
        } as SignOptions
      );

      // Calculate expiration time
      const expiresIn = Convert.TTLToSecond(env.REFRESH_TOKEN_TTL);
      const expiresAt = new Date(Date.now() + expiresIn * 1000);

      const accessTokenResponse = this.generateAccessToken(user);

      // Store token in database
      prisma.token
        .create({
          data: {
            userId: user.id,
            token: accessTokenResponse.token,
            refreshToken,
            expiresAt: expiresAt,
          },
        })
        .catch((err) => console.error("Token creation failed:", err));

      return {
        token: refreshToken,
        expiresAt,
      };
    } catch (error) {
      console.error("Error generating refresh token:", error);
      throw error;
    }
  };

  static changePassword = async (
    id: string,
    newPassword: string,
    oldPassword: string
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      const user = await prisma.user.findUnique({
        where: { id },
      });

      if (!user) {
        return {
          success: false,
          message: i18n.t("auth.changePassword.userNotFound"),
          status: StatusCodes.NOT_FOUND,
        };
      }

      // Verify old password
      const isValidPassword = await bcrypt.compare(oldPassword, user.password);
      if (!isValidPassword) {
        return {
          success: false,
          message: i18n.t("auth.changePassword.incorrectPassword"),
          status: StatusCodes.FORBIDDEN,
        };
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Update password
      await prisma.user.update({
        where: { id },
        data: { password: hashedPassword },
      });

      // Invalidate all tokens
      await prisma.token.deleteMany({
        where: { userId: id },
      });

      return {
        success: true,
        message: i18n.t("auth.changePassword.success"),
        status: StatusCodes.OK,
      };
    } catch (error) {
      return {
        success: false,
        message: i18n.t("auth.changePassword.failed"),
        status: StatusCodes.INTERNAL_SERVER_ERROR,
      };
    }
  };
}

export default AuthService;
