import { PrismaClient } from "@prisma/client";
import PrismaService from "./prisma.service";
import logger from "@/utils/logger.util";
import redisClient from "@/config/redis";
import { RedisPrefix } from "@/constants/prefix";

export interface UninstallDetectionResult {
  success: boolean;
  message: string;
  wasUninstalled: boolean;
  userId?: string;
  storeId?: string;
}

export interface ReinstallResult {
  success: boolean;
  message: string;
  wasReinstall: boolean;
  userId?: string;
  storeId?: string;
}

class ShopifyUninstallService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = PrismaService.getInstance().getClient();
  }

  /**
   * Detect uninstall from API errors (401/403 responses)
   * This provides immediate uninstall detection since there's no real-time webhook
   */
  async detectUninstallFromApiError(
    shopDomain: string,
    accessToken: string,
    errorCode: number
  ): Promise<UninstallDetectionResult> {
    try {
      // Only treat 401 (Unauthorized) as potential uninstall
      // 403 could be permissions issue, not necessarily uninstall
      if (errorCode !== 401) {
        return {
          success: false,
          message: "Error code does not indicate uninstall",
          wasUninstalled: false,
        };
      }

      logger.info(`Potential uninstall detected for shop: ${shopDomain}, error: ${errorCode}`);

      // Find the linked store
      const linkedStore = await this.prisma.linkedStore.findFirst({
        where: {
          provider: "shopify",
          data: {
            path: ["domain"],
            equals: shopDomain,
          },
        },
        include: {
          user: true,
          shopifySubscription: true,
        },
      });

      if (!linkedStore) {
        logger.warn(`No linked store found for shop: ${shopDomain}`);
        return {
          success: false,
          message: "Store not found",
          wasUninstalled: false,
        };
      }

      // Check if already marked as uninstalled
      if (linkedStore.uninstalledAt) {
        logger.info(`Store ${shopDomain} already marked as uninstalled`);
        return {
          success: true,
          message: "Store already marked as uninstalled",
          wasUninstalled: true,
          userId: linkedStore.userId,
          storeId: linkedStore.id,
        };
      }

      // Mark as temporarily uninstalled (pending official shop/redact webhook)
      await this.handleTemporaryUninstall(linkedStore.id, linkedStore.userId);

      logger.info(`Marked store ${shopDomain} as temporarily uninstalled`);

      return {
        success: true,
        message: "Store marked as temporarily uninstalled",
        wasUninstalled: true,
        userId: linkedStore.userId,
        storeId: linkedStore.id,
      };
    } catch (error) {
      logger.error("Error detecting uninstall from API error:", { error: error instanceof Error ? error.message : String(error) });
      return {
        success: false,
        message: "Failed to detect uninstall",
        wasUninstalled: false,
      };
    }
  }

  /**
   * Handle temporary uninstall (immediate response to API errors)
   * This doesn't permanently delete data, just deactivates access
   */
  async handleTemporaryUninstall(storeId: string, userId: string): Promise<void> {
    const now = new Date();

    await this.prisma.$transaction(async (tx) => {
      // 1. Mark user as temporarily uninstalled
      await tx.user.update({
        where: { id: userId },
        data: {
          isActive: false,
          uninstalledAt: now,
        },
      });

      // 2. Mark store as temporarily uninstalled
      await tx.linkedStore.update({
        where: { id: storeId },
        data: {
          isActive: false,
          uninstalledAt: now,
        },
      });

      // 3. Update subscription status to cancelled
      await tx.shopifySubscription.updateMany({
        where: { linkedStoreId: storeId },
        data: {
          status: "CANCELLED",
          cancelledAt: now,
        },
      });

      // 4. Invalidate all user sessions
      await this.invalidateUserSessions(userId);
    });

    logger.info(`Temporary uninstall completed for user: ${userId}, store: ${storeId}`);
  }

  /**
   * Handle permanent uninstall (called from shop/redact webhook after 48 hours)
   */
  async handlePermanentUninstall(shopDomain: string, shopId?: string): Promise<void> {
    try {
      // Find stores by domain or shopId
      const whereCondition = shopId
        ? {
            OR: [
              {
                provider: "shopify",
                data: {
                  path: ["domain"],
                  equals: shopDomain,
                },
              },
              {
                provider: "shopify",
                providerStoreId: shopId,
              },
            ],
          }
        : {
            provider: "shopify",
            data: {
              path: ["domain"],
              equals: shopDomain,
            },
          };

      const linkedStores = await this.prisma.linkedStore.findMany({
        where: whereCondition,
        include: {
          user: true,
          shopifySubscription: true,
        },
      });

      if (linkedStores.length === 0) {
        logger.warn(`No stores found for permanent deletion: ${shopDomain}`);
        return;
      }

      for (const store of linkedStores) {
        await this.permanentlyDeleteStoreData(store.id, store.userId);
      }

      logger.info(`Permanent uninstall completed for ${linkedStores.length} stores`);
    } catch (error) {
      logger.error("Error handling permanent uninstall:", { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  /**
   * Permanently delete store data (GDPR compliance)
   */
  private async permanentlyDeleteStoreData(storeId: string, userId: string): Promise<void> {
    await this.prisma.$transaction(async (tx) => {
      // Delete related data first (respecting foreign key constraints)
      await tx.shopifyUsageRecord.deleteMany({
        where: {
          subscription: {
            linkedStoreId: storeId,
          },
        },
      });

      await tx.shopifySubscription.deleteMany({
        where: { linkedStoreId: storeId },
      });

      await tx.blockRefund.deleteMany({
        where: { storeId: storeId },
      });

      await tx.blockUsage.deleteMany({
        where: { storeId: storeId },
      });

      await tx.mappedBlock.deleteMany({
        where: { linkedStoreId: storeId },
      });

      await tx.shopifyDispute.deleteMany({
        where: { linkedStoreId: storeId },
      });

      await tx.shopifyPayout.deleteMany({
        where: { linkedStoreId: storeId },
      });

      await tx.shopifyTransaction.deleteMany({
        where: { linkedStoreId: storeId },
      });

      await tx.shopifyOrder.deleteMany({
        where: { linkedStoreId: storeId },
      });

      await tx.bill.deleteMany({
        where: { linkedStoreId: storeId },
      });

      await tx.paymentHistory.deleteMany({
        where: { linkedStoreId: storeId },
      });

      await tx.usageTracking.deleteMany({
        where: { storeId: storeId },
      });

      await tx.storeSettings.deleteMany({
        where: { storeId: storeId },
      });

      // Delete the store itself
      await tx.linkedStore.delete({
        where: { id: storeId },
      });

      // Check if user has any other stores
      const userStores = await tx.linkedStore.count({
        where: { userId },
      });

      // If no other stores, delete the user
      if (userStores === 0) {
        await tx.token.deleteMany({
          where: { userId },
        });

        await tx.user.delete({
          where: { id: userId },
        });
      }
    });

    logger.info(`Permanently deleted store data: ${storeId}, user: ${userId}`);
  }

  /**
   * Invalidate all user sessions and tokens
   */
  private async invalidateUserSessions(userId: string): Promise<void> {
    try {
      // Delete all database tokens
      const deletedTokens = await this.prisma.token.deleteMany({
        where: { userId },
      });

      // Clear Redis cache for all user tokens
      // Note: This is a simplified approach - in production you might want to track tokens more precisely
      const redisKeys = await redisClient.keys(`${RedisPrefix.ACCESS_TOKEN}_*`);
      for (const key of redisKeys) {
        const tokenUserId = await redisClient.get(key);
        if (tokenUserId === userId) {
          await redisClient.del(key);
        }
      }

      logger.info(`Invalidated ${deletedTokens.count} tokens for user: ${userId}`);
    } catch (error) {
      logger.error("Error invalidating user sessions:", { error: error instanceof Error ? error.message : String(error) });
    }
  }

  /**
   * Detect reinstall attempt and handle reactivation
   */
  async detectAndHandleReinstall(
    shopDomain: string,
    accessToken: string,
    shopData: any
  ): Promise<ReinstallResult> {
    try {
      // Look for existing deactivated store
      const existingStore = await this.prisma.linkedStore.findFirst({
        where: {
          provider: "shopify",
          data: {
            path: ["domain"],
            equals: shopDomain,
          },
          uninstalledAt: {
            not: null,
          },
        },
        include: {
          user: true,
          shopifySubscription: true,
        },
      });

      if (!existingStore) {
        return {
          success: false,
          message: "No previous installation found",
          wasReinstall: false,
        };
      }

      // Reactivate the user and store
      await this.reactivateUserAndStore(existingStore.id, existingStore.userId, accessToken, shopData);

      logger.info(`Reinstall detected and handled for shop: ${shopDomain}`);

      return {
        success: true,
        message: "Reinstall detected and user reactivated",
        wasReinstall: true,
        userId: existingStore.userId,
        storeId: existingStore.id,
      };
    } catch (error) {
      logger.error("Error detecting reinstall:", { error: error instanceof Error ? error.message : String(error) });
      return {
        success: false,
        message: "Failed to detect reinstall",
        wasReinstall: false,
      };
    }
  }

  /**
   * Reactivate user and store after reinstall
   */
  private async reactivateUserAndStore(
    storeId: string,
    userId: string,
    accessToken: string,
    shopData: any
  ): Promise<void> {
    await this.prisma.$transaction(async (tx) => {
      // 1. Reactivate user
      await tx.user.update({
        where: { id: userId },
        data: {
          isActive: true,
          uninstalledAt: null,
        },
      });

      // 2. Reactivate store with new access token
      await tx.linkedStore.update({
        where: { id: storeId },
        data: {
          isActive: true,
          uninstalledAt: null,
          data: {
            ...shopData,
            accessToken,
            domain: shopData.domain,
            lastFetchedAt: new Date().toISOString(),
          },
        },
      });

      // 3. Reactivate subscription if it exists
      const subscription = await tx.shopifySubscription.findFirst({
        where: { linkedStoreId: storeId },
      });

      if (subscription) {
        await tx.shopifySubscription.update({
          where: { id: subscription.id },
          data: {
            status: "PENDING", // Reset to pending - will be activated when user accepts billing
            cancelledAt: null,
          },
        });
      }
    });

    logger.info(`Reactivated user: ${userId}, store: ${storeId}`);
  }

  /**
   * Check if a store is currently uninstalled
   */
  async isStoreUninstalled(shopDomain: string): Promise<boolean> {
    try {
      const store = await this.prisma.linkedStore.findFirst({
        where: {
          provider: "shopify",
          data: {
            path: ["domain"],
            equals: shopDomain,
          },
          uninstalledAt: {
            not: null,
          },
        },
      });

      return !!store;
    } catch (error) {
      logger.error("Error checking store uninstall status:", { error: error instanceof Error ? error.message : String(error) });
      return false;
    }
  }

  /**
   * Get uninstall status for a user
   */
  async getUserUninstallStatus(userId: string): Promise<{
    isUninstalled: boolean;
    uninstalledAt?: Date;
    storeCount: number;
    activeStoreCount: number;
  }> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        include: {
          linkedStores: {
            where: {
              provider: "shopify",
            },
          },
        },
      });

      if (!user) {
        return {
          isUninstalled: false,
          storeCount: 0,
          activeStoreCount: 0,
        };
      }

      const totalStores = user.linkedStores.length;
      const activeStores = user.linkedStores.filter(store => store.isActive).length;

      return {
        isUninstalled: !user.isActive,
        uninstalledAt: user.uninstalledAt || undefined,
        storeCount: totalStores,
        activeStoreCount: activeStores,
      };
    } catch (error) {
      logger.error("Error getting user uninstall status:", { error: error instanceof Error ? error.message : String(error) });
      return {
        isUninstalled: false,
        storeCount: 0,
        activeStoreCount: 0,
      };
    }
  }
}

export default ShopifyUninstallService; 