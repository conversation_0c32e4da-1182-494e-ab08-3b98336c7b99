import { ServiceResponse } from "@/constants/type";
import { StatusCodes } from "http-status-codes";
import { getI18n } from "@/middlewares/language.middleware";
import AuthService from "./auth.service";
import Convert from "@/utils/convert.util";
import PrismaService from "./prisma.service";
import bcrypt from "bcryptjs";


const prisma = PrismaService.getInstance().getClient();

class UserService {
  static getUser = async (id: string): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      const user = await prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          code: true,
          fullName: true,
          phoneNumber: true,
          email: true,
          gender: true,
          address: true,
          birthDate: true,
        },
      });

      if (!user) {
        return {
          success: false,
          status: StatusCodes.NOT_FOUND,
          message: i18n.t("user.common.notFound"),
        };
      }

      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t("user.crud.getUserSuccess"),
        data: user,
      };
    } catch (error) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: i18n.t("user.crud.getUserFailed"),
      };
    }
  };

  static getUsers = async (
    pageNumber: number,
    pageSize: number,
    keyword: string,
    status?: string,
    genders?: string
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Build where conditions
      const where: any = {
        OR: [
          { fullName: { contains: keyword, mode: "insensitive" } },
          { phoneNumber: { contains: keyword, mode: "insensitive" } },
          { code: { contains: keyword, mode: "insensitive" } },
        ],
      };

      if (genders) {
        const gendersArray = Convert.convertQueryToArray(genders);
        where.gender = { in: gendersArray };
      }

      if (status) {
        const statusArray = Convert.convertQueryToArray(status);
        where.status = { in: statusArray.map((status) => status === "true") };
      }

      // Get users with pagination if pageNumber !== 0
      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          orderBy: { createdAt: "desc" },
          skip: pageNumber !== 0 ? (pageNumber - 1) * pageSize : undefined,
          take: pageNumber !== 0 ? pageSize : undefined,
          select: {
            id: true,
            code: true,
            fullName: true,
            phoneNumber: true,
            email: true,
            gender: true,
            address: true,
            birthDate: true,
            createdAt: true,
          },
        }),
        prisma.user.count({ where }),
      ]);

      return {
        success: true,
        message: i18n.t("user.crud.fetchSuccess"),
        status: StatusCodes.OK,
        data:
          pageNumber !== 0
            ? { total, items: users, pageNumber, pageSize }
            : { total, items: users },
      };
    } catch (error) {
      return {
        success: false,
        message: i18n.t("user.crud.fetchFailed"),
        status: StatusCodes.INTERNAL_SERVER_ERROR,
      };
    }
  };


  static updateUser = async (
    userId: string,
    update: {
      fullName?: string;
      email?: string;
      phoneNumber?: string;
      address?: string;
      note?: string;
      gender?: string;
      birthDate?: Date;
      status?: boolean;
    }
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      const existingUser = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!existingUser) {
        return {
          success: false,
          status: StatusCodes.NOT_FOUND,
          message: i18n.t("user.common.notFound"),
        };
      }

      // Check if email or phone already exist for other users
      if (update.email || update.phoneNumber) {
        const duplicateUser = await prisma.user.findFirst({
          where: {
            id: { not: userId },
            OR: [
              ...(update.email ? [{ email: update.email }] : []),
              ...(update.phoneNumber
                ? [{ phoneNumber: update.phoneNumber }]
                : []),
            ],
          },
        });

        if (duplicateUser) {
          return {
            success: false,
            status: StatusCodes.CONFLICT,
            message: i18n.t("user.validation.emailPhoneExists"),
          };
        }
      }

      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: update,
        select: {
          id: true,
          code: true,
          fullName: true,
          phoneNumber: true,
          email: true,
          gender: true,
          address: true,
          birthDate: true,
          status: true,
        },
      });

      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t("user.crud.updateSuccess"),
        data: updatedUser,
      };
    } catch (error) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: i18n.t("user.crud.updateFailed"),
      };
    }
  };

  static deleteUser = async (userId: string): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        return {
          success: false,
          status: StatusCodes.NOT_FOUND,
          message: i18n.t("user.common.notFound"),
        };
      }

      await prisma.user.delete({
        where: { id: userId },
      });

      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t("user.crud.deleteSuccess"),
      };
    } catch (error) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: i18n.t("user.crud.deleteFailed"),
      };
    }
  };

  static createUser = async (data: any): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Check if user exists by email or phone
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [{ email: data.email }, { phoneNumber: data.phoneNumber }],
        },
      });

      if (existingUser) {
        return {
          success: false,
          status: StatusCodes.CONFLICT,
          message: i18n.t("user.validation.emailPhoneExists"),
        };
      }


      // Generate code
      const userCount = await prisma.user.count();
      const code = `USR${(userCount + 1).toString().padStart(3, "0")}`;

      // Hash password
      const hashedPassword = await bcrypt.hash(data.password, 10);

      // Create user
      const newUser = await prisma.user.create({
        data: {
          code,
          fullName: data.fullName,
          email: data.email,
          phoneNumber: data.phoneNumber,
          password: hashedPassword,
          gender: data.gender || "male",
          birthDate: data.birthDate || new Date(),
          address: data.address,
          note: data.note,
          status: data.status !== undefined ? data.status : true,
        },
      });

      return {
        success: true,
        status: StatusCodes.CREATED,
        message: i18n.t("user.crud.createSuccess"),
        data: {
          id: newUser.id,
          code: newUser.code,
          fullName: newUser.fullName,
          email: newUser.email,
        },
      };
    } catch (error) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: i18n.t("user.crud.createFailed"),
      };
    }
  };
}

export default UserService;
