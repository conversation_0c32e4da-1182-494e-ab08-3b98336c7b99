import PrismaService from './prisma.service';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>odel,
  UpdateBill,
  Bill<PERSON>uery
} from '../models/bill.model';
import { getStripeInstance } from '../config/stripe';
import { paypalService } from './paypal.service';

class BillService {
  private static prisma: any;
  
  static initialize() {
    this.prisma = PrismaService.getInstance().getClient();
  }

  static async createBill(data: CreateBill): Promise<BillModel> {
    return this.prisma.bill.create({
      data
    });
  }

  static async getAllBills(query: BillQuery = {}): Promise<BillModel[]> {
    return this.prisma.bill.findMany({
      where: query,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        paymentHistories: {
          orderBy: {
            paymentDate: 'desc'
          }
        }
      }
    });
  }

  static async getBillsByStoreId(storeId: string, page: number = 1, limit: number = 10): Promise<{
    items: BillModel[];
    total: number;
    page: number;
    limit: number;
  }> {
    const offset = (page - 1) * limit;

    const [items, total] = await Promise.all([
      this.prisma.bill.findMany({
        where: { linkedStoreId: storeId },
        orderBy: { createdAt: 'desc' },
        skip: offset,
        take: limit,
        include: {
          paymentHistories: {
            orderBy: {
              paymentDate: 'desc'
            }
          }
        }
      }),
      this.prisma.bill.count({
        where: { linkedStoreId: storeId },
      }),
    ]);

    return { items, total, page, limit };
  }

  static async getBillById(id: string): Promise<BillModel> {
    return this.prisma.bill.findUnique({
      where: { id },
      include: {
        paymentHistories: {
          orderBy: {
            paymentDate: 'desc'
          }
        }
      }
    });
  }

  static async updateBill(id: string, data: UpdateBill): Promise<BillModel> {
    return this.prisma.bill.update({
      where: { id },
      data
    });
  }

  static async deleteBill(id: string): Promise<void> {
    await this.prisma.bill.delete({
      where: { id }
    });
  }

  static async generatePaymentLink({
    stripeAccountId,
    amount,
    billId,
    currency = 'USD',
    description
  }: {
    stripeAccountId: string;
    amount: number;
    billId: string;
    currency?: string;
    description?: string;
  }): Promise<string> {
    try {
      const stripe = getStripeInstance();

      // First create a product
      const product = await stripe.products.create({
        name: description || `Bill ${billId}`,
        description: description || `Payment for bill ${billId}`,
      }, {
        stripeAccount: stripeAccountId,
      });

      // Then create a price for the product
      const price = await stripe.prices.create({
        currency: currency,
        unit_amount: Math.round(amount * 100), // Convert to cents
        product: product.id,
      }, {
        stripeAccount: stripeAccountId,
      });

      // Finally create the payment link with the price
      const paymentLink = await stripe.paymentLinks.create({
        line_items: [
          {
            price: price.id,
            quantity: 1,
          },
        ],
        metadata: {
          billId: billId,
          stripeAccountId: stripeAccountId,
        },
      }, {
        stripeAccount: stripeAccountId,
      });

      return paymentLink.url;
    } catch (error: any) {
      throw new Error(`Failed to generate payment link: ${error.message}`);
    }
  }

  static async processAutomaticPayment(billId: string): Promise<any> {
    const bill = await this.prisma.bill.findUnique({
      where: { id: billId },
      include: {
        linkedStore: {
          include: {
            paypalAgreements: {
              where: { status: 'ACTIVE' },
              take: 1
            }
          }
        }
      }
    });

    if (!bill || !bill.linkedStore.paypalAgreements[0]) {
      throw new Error('No active PayPal agreement found');
    }

    const agreement = bill.linkedStore.paypalAgreements[0];
    
    try {
      const payment = await paypalService.chargeAgreement(
        agreement.agreementId,
        bill.amount, // Already in cents
        billId
      );

      // Update bill status
      await this.prisma.bill.update({
        where: { id: billId },
        data: { 
          status: 'PAID'
        }
      });

      // Create payment history record
      await this.prisma.paymentHistory.create({
        data: {
          linkedStoreId: bill.linkedStoreId,
          billId: billId,
          amount: bill.amount,
          currency: bill.currency,
          paymentMethod: 'PAYPAL',
          transactionId: payment.dbRecord.paymentId,
          status: 'SUCCESS',
          paymentDate: new Date()
        }
      });

      return payment;
    } catch (error: any) {
      // Log error and send notification
      console.error('Auto payment failed:', error);
      
      // Create failed payment history record
      await this.prisma.paymentHistory.create({
        data: {
          linkedStoreId: bill.linkedStoreId,
          billId: billId,
          amount: bill.amount,
          currency: bill.currency,
          paymentMethod: 'PAYPAL',
          status: 'FAILED',
          paymentDate: new Date(),
          failureReason: error.message || 'Unknown error'
        }
      });
      
      throw error;
    }
  }
}

// Initialize when module is loaded
BillService.initialize();

export default BillService;
export { BillService };