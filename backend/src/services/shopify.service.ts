import { PrismaClient } from "@prisma/client";
import PrismaService from "./prisma.service";
import { shopify } from "../config/shopify";
import {
  LinkedStore,
  ShopifyStoreData,
} from "../models/linked-store.model";
import { env } from "../config/environment";
import crypto from "crypto";
import axios from "axios";
import { v4 as uuidv4 } from "uuid";
import bcrypt from "bcryptjs";
import { getBackgroundJobService } from "./background-job.service";
import SyncService from "./sync.service";
import BlockRefundService from "./block-refund.service";
import { wrapShopifyRestCall, wrapShopifyGraphQLCall } from "../utils/shopify-api-wrapper.util";
import logger from "@/utils/logger.util";
import { BILLING_PLANS, SUBSCRIPTION_STATUS } from "../constants/billing";
import ShopifyUninstallService from "./shopify-uninstall.service";

class ShopifyService {
  private prisma: PrismaClient;
  private blockRefundService: BlockRefundService;

  constructor() {
    this.prisma = PrismaService.getInstance().getClient();
    this.blockRefundService = new BlockRefundService();
  }

  /**
   * Begins the Shopify authorization flow
   */
  beginAuth(shop: string, redirectPath: string): string {
    try {
      // Sử dụng các giá trị từ config thay vì hard-coded
      const apiKey = env.SHOPIFY_API_KEY;
      const scopes = env.SHOPIFY_SCOPES;

      // Tạo redirect URL động dựa trên SERVER_PUBLIC_URL và redirectPath
      const redirectUri = `${env.SHOPIFY_HOST_NAME}${redirectPath}`;

      // Tạo URL cài đặt app Shopify
      const authUrl = `https://${shop}/admin/oauth/authorize?client_id=${apiKey}&scope=${scopes}&redirect_uri=${encodeURIComponent(
        redirectUri
      )}`;

      return authUrl;
    } catch (error) {
      console.error("Error beginning auth:", error);
      throw new Error("Failed to generate authorization URL");
    }
  }

  /**
   * Connect store with access token
   */
  async connectWithToken(
    shop: string,
    accessToken: string,
    userId: string
  ): Promise<LinkedStore> {
    try {
      // Initialize the REST client with provided token
      const client = this.createRestClient(shop, accessToken);

      // Validate the access token by trying to get store information with uninstall detection
      const shopResponse = await wrapShopifyRestCall(
        () => client.get({ path: "shop" }),
        {
          shop: shop,
          accessToken: accessToken
        },
        'connectWithToken_validate_access'
      );

      const shopData = shopResponse.body as any;

      const storeData: ShopifyStoreData = {
        shop: shopData.shop,
        accessToken,
        domain: shop,
        lastFetchedAt: new Date().toISOString(),
      };

      // Check if store already exists for this user
      const existingStore = await (this.prisma as any).linkedStore.findFirst({
        where: {
          userId,
          provider: "shopify",
          providerStoreId: shopData.shop.id.toString(),
        },
      });

      if (existingStore) {
        // Update existing store
        return (this.prisma as any).linkedStore.update({
          where: {
            id: existingStore.id,
          },
          data: {
            storeName: shopData.shop.name,
            data: storeData,
          },
        });
      }

      // Create new store
      const linkedStore = await (this.prisma as any).linkedStore.create({
        data: {
          userId,
          storeName: shopData.shop.name,
          provider: "shopify",
          providerStoreId: shopData.shop.id.toString(),
          data: storeData,
        },
      });

      const backgroundJobService = getBackgroundJobService();
      await backgroundJobService.addJob('shopify-force-sync', {
        storeId: linkedStore.id,
        syncTypes: ['orders'],
        shop: shop,
        accessToken: accessToken
      }, {
        priority: 8,
        delay: 1000
      });

      return linkedStore;
    } catch (error) {
      console.error("Error connecting with token:", error);
      throw new Error(
        `Failed to connect Shopify store: ${(error as Error).message}`
      );
    }
  }

  /**
   * Handles the Shopify callback to exchange code for access token
   */
  async handleShopifyCallback(code: string, shop: string) {
    try {
      // Exchange the authorization code for an access token
      // Sử dụng trực tiếp REST API của Shopify thay vì dùng shopify.auth.callback
      // để tránh vấn đề cookie không tìm thấy
      const apiKey = env.SHOPIFY_API_KEY;
      const apiSecret = env.SHOPIFY_API_SECRET;

      // Gọi Shopify OAuth API trực tiếp
      const response = await axios.post(
        `https://${shop}/admin/oauth/access_token`,
        {
          client_id: apiKey,
          client_secret: apiSecret,
          code: code,
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const data = response.data;
      return {
        session: {
          shop,
          accessToken: data.access_token,
          scope: data.scope,
        },
      };
    } catch (error) {
      console.error("Error handling callback:", error);
      throw new Error("Failed to complete Shopify integration");
    }
  }

  /**
   * Creates a Shopify REST client
   */
  createRestClient(shop: string, accessToken: string) {
    return new shopify.clients.Rest({
      session: {
        shop,
        accessToken,
      } as any,
    });
  }

  /**
   * Generate authorization URL for Shopify OAuth
   */
  // async generateAuthUrl(shop: string, userId: string): Promise<string> {
  //   try {
  //     return this.beginAuth(shop, "/api/shopify/callback", userId);
  //   } catch (error) {
  //     console.error("Error generating auth URL:", error);
  //     throw new Error("Failed to generate authorization URL");
  //   }
  // }

  /**
   * Handle OAuth callback from Shopify
   */
  async handleCallback(shop: string, code: string): Promise<LinkedStore> {
    try {
      // Exchange code for access token
      const callbackResult = await this.handleShopifyCallback(code, shop);
      const accessToken = callbackResult.session.accessToken || "";

      if (!accessToken) {
        throw new Error("Failed to obtain access token");
      }

      // Initialize the REST client
      const client = this.createRestClient(shop, accessToken);

      // Get store details
      const shopResponse = await client.get({
        path: "shop",
      });

      const shopData = shopResponse.body as any;

      const storeData: ShopifyStoreData = {
        shop: shopData.shop,
        accessToken,
        domain: shop,
      };

      console.log("Shopify callback data:", JSON.stringify(shopData, null, 2));

      // Store in database using Prisma's client
      const linkedStore = await (this.prisma as any).linkedStore.create({
        data: {
          userId: "c1e98025-244f-45c2-b900-19656677fb60",
          storeName: shopData.shop.name,
          provider: "shopify",
          providerStoreId: shopData.shop.id.toString(),
          data: storeData,
        },
      });

      return linkedStore;
    } catch (error) {
      console.error("Error handling callback:", error);
      throw new Error("Failed to complete Shopify integration");
    }
  }

  /**
   * Handle OAuth callback from Shopify with userId
   */
  async handleCallbackWithUserId(shop: string, code: string, userId: string): Promise<LinkedStore> {
    try {
      // Exchange code for access token
      const callbackResult = await this.handleShopifyCallback(code, shop);
      const accessToken = callbackResult.session.accessToken || "";

      if (!accessToken) {
        throw new Error("Failed to obtain access token");
      }

      // Initialize the REST client
      const client = this.createRestClient(shop, accessToken);

      console.log("Client:", JSON.stringify(client, null, 2));

      // Get store details
      const shopResponse = await client.get({
        path: "shop",
      });

      const shopData = shopResponse.body as any;

      const storeData: ShopifyStoreData = {
        shop: shopData.shop,
        accessToken,
        domain: shop,
        lastFetchedAt: new Date().toISOString(),
      };

      console.log("Store data:", JSON.stringify(storeData, null, 2));

      // Check if store already exists for this user
      const existingStore = await (this.prisma as any).linkedStore.findFirst({
        where: {
          userId,
          provider: "shopify",
          providerStoreId: shopData.shop.id.toString(),
        },
      });

      if (existingStore) {
        // Update existing store
        return (this.prisma as any).linkedStore.update({
          where: {
            id: existingStore.id,
          },
          data: {
            storeName: shopData.shop.name,
            data: storeData,
          },
        });
      }

      // Store in database using Prisma's client with the provided userId
      const linkedStore = await (this.prisma as any).linkedStore.create({
        data: {
          userId,
          storeName: shopData.shop.name,
          provider: "shopify",
          providerStoreId: shopData.shop.id.toString(),
          data: storeData,
        },
      });

      const backgroundJobService = getBackgroundJobService();
      await backgroundJobService.addJob('shopify-force-sync', {
        storeId: linkedStore.id,
        syncTypes: ['orders'],
        shop: shop,
        accessToken: accessToken
      }, {
        priority: 8,
        delay: 1000
      });

      return linkedStore;
    } catch (error) {
      console.error("Error handling callback with userId:", error);
      throw new Error("Failed to complete Shopify integration");
    }
  }

  /**
   * Handle auth callback - kiểm tra và tạo/cập nhật store
   */
  async handleAuthCallback(shop: string, accessToken: string): Promise<LinkedStore> {
    try {
      // Initialize the REST client
      const client = this.createRestClient(shop, accessToken);

      // Get store details
      const shopResponse = await client.get({
        path: "shop",
      });

      const shopData = shopResponse.body as any;

      const storeData: ShopifyStoreData = {
        shop: shopData.shop,
        accessToken,
        domain: shop,
        lastFetchedAt: new Date().toISOString(),
      };

      // Step 0: Check for reinstall first
      const uninstallService = new ShopifyUninstallService();
      const reinstallResult = await uninstallService.detectAndHandleReinstall(shop, accessToken, storeData);
      
      if (reinstallResult.wasReinstall) {
        logger.info(`Reinstall detected for shop: ${shop}`, {
          userId: reinstallResult.userId,
          storeId: reinstallResult.storeId,
          message: reinstallResult.message
        });
        
        // Return the reactivated store
        const reactivatedStore = await (this.prisma as any).linkedStore.findFirst({
          where: {
            id: reinstallResult.storeId,
            provider: "shopify",
          },
        });
        
        if (reactivatedStore) {
          // Start background sync for the reactivated store
          const syncService = new SyncService();
          syncService.syncSingleStore(reactivatedStore.id).then(() => {
            console.log(`Force sync job added for reinstalled Shopify store: ${shop}`);
          }).catch((error: any) => {
            console.error(`Failed to add force sync job for reinstalled Shopify store: ${shop}`, error);
          });
          
          return reactivatedStore;
        }
      }

      // Step 1: Check if store already exists in linked_stores (including inactive ones)
      const existingStore = await (this.prisma as any).linkedStore.findFirst({
        where: {
          provider: "shopify",
          providerStoreId: shopData.shop.id.toString(),
        },
        include: {
          user: true
        }
      });

      let storeId = uuidv4();
      let userId: string;

      if (existingStore) {
        // If store exists, update and return store information
        storeId = existingStore.id;
        userId = existingStore.userId;
        
        try {
          console.log(`Store was active: ${existingStore.isActive}, User was active: ${existingStore.user?.isActive}`);
          
          const updatedStore = await (this.prisma as any).linkedStore.update({
            where: {
              id: existingStore.id,
            },
            data: {
              storeName: shopData.shop.name,
              data: storeData,
              isActive: true,
              uninstalledAt: null, // Clear uninstall timestamp
            },
          });
          
          // Reactivate the user if they were inactive
          if (existingStore.user && !existingStore.user.isActive) {
            await (this.prisma as any).user.update({
              where: { id: userId },
              data: {
                isActive: true,
                uninstalledAt: null, // Clear uninstall timestamp
              }
            });
            console.log(`Reactivated user ${userId} during store reinstallation`);
          }
          
          console.log(`Store updated/reactivated for Shopify store: ${shop}`);
          
          // Check and create subscription if needed for existing store
          try {
            console.log(`🔍 Checking for existing subscription for existing store: ${storeId}`);
            const existingSubscription = await (this.prisma as any).shopifySubscription.findUnique({
              where: { linkedStoreId: storeId }
            });

            if (!existingSubscription) {
              console.log(`💰 Creating subscription for existing store: ${storeId}`);
              await (this.prisma as any).shopifySubscription.create({
                data: {
                  linkedStoreId: storeId,
                  planName: BILLING_PLANS.PROFESSIONAL.name,
                  planType: BILLING_PLANS.PROFESSIONAL.type,
                  status: SUBSCRIPTION_STATUS.PENDING,
                  amount: BILLING_PLANS.PROFESSIONAL.amount,
                  currency: BILLING_PLANS.PROFESSIONAL.currency,
                  billingInterval: BILLING_PLANS.PROFESSIONAL.interval,
                  cappedAmount: BILLING_PLANS.PROFESSIONAL.cappedAmountCents,
                  currentUsage: 0,
                  metadata: {
                    autoCreated: true,
                    createdAt: new Date().toISOString(),
                    shop: shop
                  }
                }
              });
              console.log(`✅ Subscription created for existing store: ${storeId} with status: PENDING`);
            } else {
              console.log(`✅ Subscription already exists for existing store: ${storeId} with status: ${existingSubscription.status}`);
            }
          } catch (error: any) {
            console.error(`⚠️ Failed to create subscription for existing store: ${storeId}`, error);
          }

          // Start background sync
          const syncService = new SyncService();
          syncService.syncSingleStore(storeId).then(() => {
            console.log(`Force sync job added for Shopify store: ${shop}`);
          }).catch((error: any) => {
            console.error(`Failed to add force sync job for Shopify store: ${shop}`, error);
          });

          return {
            id: storeId,
            storeName: shopData.shop.name,
            provider: "shopify",
            providerStoreId: shopData.shop.id.toString(),
            data: storeData,
            userId: userId, // Add userId to the response
          };
        } catch (error: any) {
          console.error(`Failed to update store for Shopify store: ${shop}`, error);
          throw error;
        }
      } else {
        // Step 2: Store doesn't exist, check if user exists with the email (including inactive ones)
        const userEmail = shopData.shop.email || `shop${shopData.shop.id}@shopify.com`;
        
        const existingUser = await (this.prisma as any).user.findUnique({
          where: {
            email: userEmail,
          },
        });

        if (existingUser) {
          // User exists, use existing user and reactivate if necessary
          userId = existingUser.id;
          
          if (!existingUser.isActive) {
            await (this.prisma as any).user.update({
              where: { id: userId },
              data: {
                isActive: true,
                uninstalledAt: null, // Clear uninstall timestamp
              }
            });
            console.log(`Reactivated existing user for Shopify store: ${shop}, email: ${userEmail}`);
          } else {
            console.log(`Using existing active user for Shopify store: ${shop}, email: ${userEmail}`);
          }
        } else {
          // User doesn't exist, create new user
          const DEFAULT_PASSWORD = "User@123";
          const hashedPassword = await bcrypt.hash(DEFAULT_PASSWORD, 10);
          userId = uuidv4();
          const userCode = `SHOPIFY_${shopData.shop.id}_${Date.now()}`;

          try {
            await (this.prisma as any).user.create({
              data: {
                id: userId,
                code: userCode,
                fullName: shopData.shop.name || `Shopify Store ${shopData.shop.id}`,
                phoneNumber: shopData.shop.phone || `+1000000${shopData.shop.id}`,
                email: userEmail,
                password: hashedPassword,
                gender: "other",
                birthDate: new Date(),
                status: true,
                note: `Auto-created from Shopify store: ${shop}`,
              },
            });
            console.log(`User created for Shopify store: ${shop}, email: ${userEmail}`);
          } catch (error: any) {
            console.error(`Failed to create user for Shopify store: ${shop}`, error);
            throw error;
          }
        }

        // Step 3: Create new LinkedStore with the userId (existing or new)
        try {
          await (this.prisma as any).linkedStore.create({
            data: {
              id: storeId,
              userId: userId,
              storeName: shopData.shop.name,
              provider: "shopify",
              providerStoreId: shopData.shop.id.toString(),
              data: storeData,
            },
          });
          console.log(`Store created for Shopify store: ${shop} with userId: ${userId}`);
        } catch (error: any) {
          console.error(`Failed to create store for Shopify store: ${shop}`, error);
          throw error;
        }
      }

      // Step 4: Create subscription if it doesn't exist
      try {
        console.log(`🔍 Checking for existing subscription for store: ${storeId}`);
        const existingSubscription = await (this.prisma as any).shopifySubscription.findUnique({
          where: { linkedStoreId: storeId }
        });

        if (!existingSubscription) {
          console.log(`💰 Creating subscription for store: ${storeId}`);
          // Create a basic subscription record (will be activated later via billing flow)
          await (this.prisma as any).shopifySubscription.create({
            data: {
              linkedStoreId: storeId,
              planName: BILLING_PLANS.PROFESSIONAL.name,
              planType: BILLING_PLANS.PROFESSIONAL.type,
              status: SUBSCRIPTION_STATUS.PENDING,
              amount: BILLING_PLANS.PROFESSIONAL.amount,
              currency: BILLING_PLANS.PROFESSIONAL.currency,
              billingInterval: BILLING_PLANS.PROFESSIONAL.interval,
              cappedAmount: BILLING_PLANS.PROFESSIONAL.cappedAmountCents,
              currentUsage: 0,
              metadata: {
                autoCreated: true,
                createdAt: new Date().toISOString(),
                shop: shop
              }
            }
          });
          console.log(`✅ Subscription created for store: ${storeId} with status: PENDING`);
        } else {
          console.log(`✅ Subscription already exists for store: ${storeId} with status: ${existingSubscription.status}`);
        }
      } catch (error: any) {
        console.error(`⚠️ Failed to create subscription for store: ${storeId}`, error);
        // Don't fail the entire flow if subscription creation fails
      }

      // Start background sync for the store
      const syncService = new SyncService();
      syncService.syncSingleStore(storeId).then(() => {
        console.log(`Force sync job added for Shopify store: ${shop}`);
      }).catch((error: any) => {
        console.error(`Failed to add force sync job for Shopify store: ${shop}`, error);
      });

      return {
        id: storeId,
        storeName: shopData.shop.name,
        provider: "shopify",
        providerStoreId: shopData.shop.id.toString(),
        data: storeData,
        userId: userId, // Add userId to the response
      };
    } catch (error) {
      console.error("Error handling auth callback:", error);
      throw new Error(
        `Failed to handle Shopify auth: ${(error as Error).message}`
      );
    }
  }


  /**
   * Get user's stores
   */
  async getUserStores(userId: string): Promise<LinkedStore[]> {
    try {
      console.log("🔍 [ShopifyService.getUserStores] Querying for userId:", userId);
      
      const stores = await (this.prisma as any).linkedStore.findMany({
        where: {
          userId: userId,
          provider: "shopify",
          isActive: true, // Only return active stores
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      console.log("✅ [ShopifyService.getUserStores] Query result count:", stores?.length || 0);
      console.log("✅ [ShopifyService.getUserStores] Query results:", stores);

      // Let's also check if there are ANY stores for this user regardless of provider
      const allUserStores = await (this.prisma as any).linkedStore.findMany({
        where: {
          userId: userId,
        },
        orderBy: {
          createdAt: "desc",
        },
      });
      console.log("🔍 [ShopifyService.getUserStores] All stores for user (any provider):", allUserStores?.length || 0);
      console.log("🔍 [ShopifyService.getUserStores] All stores data:", allUserStores);

      // Let's also check if there are stores with a different userId
      const allStores = await (this.prisma as any).linkedStore.findMany({
        select: {
          id: true,
          userId: true,
          provider: true,
          storeName: true,
          createdAt: true,
        },
        orderBy: {
          createdAt: "desc",
        },
      });
      console.log("🔍 [ShopifyService.getUserStores] All stores in database:", allStores?.length || 0);
      console.log("🔍 [ShopifyService.getUserStores] All stores preview:", allStores);

      return stores;
    } catch (error) {
      console.error("❌ [ShopifyService.getUserStores] Error getting user stores:", error);
      throw new Error("Failed to get user stores");
    }
  }

  /**
   * Get store by ID
   */
  async getStoreById(id: string): Promise<LinkedStore | null> {
    try {
      const store = await (this.prisma as any).linkedStore.findFirst({
        where: {
          id: id,
          provider: "shopify",
          isActive: true, // Only return active stores
        },
      });

      return store;
    } catch (error) {
      console.error("Error fetching store by ID:", error);
      throw new Error(`Failed to fetch store by ID: ${(error as Error).message}`);
    }
  }

  /**
   * Delete a linked store
   */
  async deleteLinkedStore(id: string, userId: string): Promise<LinkedStore> {
    return (this.prisma as any).linkedStore.delete({
      where: {
        id,
        userId,
      },
    });
  }

  /**
   * Get store details (refreshes data from Shopify)
   */
  async getStoreDetails(storeId: string): Promise<LinkedStore> {
    const store = await (this.prisma as any).linkedStore.findFirst({
      where: {
        id: storeId,
        provider: "shopify",
      },
    });

    console.log("Store details ------:", store);

    if (!store) {
      throw new Error("Store not found");
    }

    const data = store.data as ShopifyStoreData;

    // Initialize the REST client
    const client = this.createRestClient(data.domain, data.accessToken);

    // Fetch shop info only with uninstall detection
    const shopResponse = await wrapShopifyRestCall(
      () => client.get({ path: "shop" }),
      {
        storeId: storeId,
        shop: data.domain,
        accessToken: data.accessToken
      },
      'getStoreDetails_shop_info'
    )
      .then((response) => response.body)
      .catch((error) => {
        console.error("Could not fetch shop info:", error);
        return { shop: data.shop }; // Return existing data if fetch fails
      });

    // Update store data with new information (excluding disputes, orders, products, customers, transactions, payouts)
    const updatedStoreData: ShopifyStoreData = {
      ...data,
      shop: shopResponse.shop,
      lastFetchedAt: new Date().toISOString(),
    };

    // Update store data with direct Prisma client
    const updatedStore = await (this.prisma as any).linkedStore.update({
      where: {
        id: store.id,
      },
      data: {
        data: updatedStoreData,
      },
    });

    const backgroundJobService = getBackgroundJobService();
    await backgroundJobService.addJob('shopify-force-sync', {
      storeId: updatedStore.id,
      syncTypes: ['orders'],
      shop: data.domain,
      accessToken: data.accessToken
    }, {
      priority: 8,
      delay: 1000
    });

    return updatedStore;
  }

  /**
   * Get transactions for a specific order
   */
  async getOrderTransactions(
    storeId: string,
    orderId: string,
    userId: string
  ): Promise<any> {
    const store = await (this.prisma as any).linkedStore.findFirst({
      where: {
        id: storeId,
        userId,
      },
    });

    if (!store) {
      throw new Error("Store not found");
    }

    const data = store.data as ShopifyStoreData;

    // Initialize the REST client
    const client = this.createRestClient(data.domain, data.accessToken);

    // Get transactions for the specific order
    try {
      const response = await client.get({
        path: `orders/${orderId}/transactions`,
      });

      return response.body;
    } catch (error) {
      console.error(`Error fetching transactions for order ${orderId}:`, error);
      throw new Error(
        `Failed to fetch transaction data: ${(error as Error).message}`
      );
    }
  }

  /**
   * Creates a GraphQL client for Shopify
   */
  createGraphQLClient(shop: string, accessToken: string) {
    return new shopify.clients.Graphql({
      session: {
        shop,
        accessToken,
      } as any,
    });
  }

  /**
   * Create BlockRefund record regardless of Shopify refund success/failure
   */
  async createBlockRefund(
    blockId: string,
    storeId: string,
    orderId: string,
    shopifyRefund: any = null,
    shopifyError: Error | null = null
  ): Promise<any> {
    const blockRefundData: any = {
      blockId,
      storeId,
      orderId: BigInt(orderId),
      amount: shopifyRefund?.totalRefundedSet?.presentmentMoney?.amount ? parseFloat(shopifyRefund.totalRefundedSet.presentmentMoney.amount) : 0,
      currency: shopifyRefund?.totalRefundedSet?.presentmentMoney?.currencyCode || "USD",
      reason: "customer",
      note: shopifyError ? `Shopify refund failed: ${shopifyError.message}` : "Auto-generated full refund",
      notify: true,
      restock: true,
    };

    // Only add optional fields if they have values
    if (shopifyRefund?.id) {
      // Extract numeric ID from GraphQL ID
      const numericId = shopifyRefund.id.split('/').pop();
      blockRefundData.refundId = BigInt(numericId);
    }
    if (shopifyError || shopifyRefund) {
      blockRefundData.transactions = shopifyError ? { error: shopifyError.message } : shopifyRefund;
    }
    if (shopifyRefund?.adminGraphqlApiId) {
      blockRefundData.adminGraphqlApiId = shopifyRefund.adminGraphqlApiId;
    }

    const blockRefund = await this.blockRefundService.create(blockRefundData);
    
    logger.info(`Block refund created for block ${blockId} with order ${orderId}`);

    return {
      ...blockRefund,
      parentId: blockRefund.parentId?.toString(),
      orderId: blockRefund.orderId.toString(),
      refundId: blockRefund.refundId?.toString(),
      userId: blockRefund.userId?.toString(),
    };
  }

  /**
   * Create a full refund for an order automatically using GraphQL
   */
  async createRefund(
    storeId: string,
    orderId: string,
    blockId?: string
  ): Promise<any> {
    logger.info(`[Shopify Refund] Starting refund process for order ${orderId} in store ${storeId}`);

    const store = await (this.prisma as any).linkedStore.findFirst({
      where: {
        id: storeId,
        provider: "shopify",
      },
    });

    if (!store) {
      logger.error(`[Shopify Refund] Store not found: ${storeId}`);
      throw new Error("Store not found");
    }

    const data = store.data as ShopifyStoreData;
    logger.info(`[Shopify Refund] Found store: ${data.domain}`);

    // Initialize the GraphQL client
    const client = this.createGraphQLClient(data.domain, data.accessToken);

    try {
      // Step 1: Get order details using GraphQL
      logger.info(`[Shopify Refund] Step 1: Fetching order details for ${orderId}`);
      const orderQuery = `
        query getOrder($id: ID!) {
          order(id: $id) {
            id
            name
            totalPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            lineItems(first: 50) {
              edges {
                node {
                  id
                  quantity
                  name
                  refundableQuantity
                }
              }
            }
            transactions(first: 50) {
              id
              kind
              gateway
              amount
              status
            }
            refunds(first: 50) {
              id
              createdAt
              refundLineItems(first: 50) {
                edges {
                  node {
                    quantity
                    lineItem {
                      id
                    }
                  }
                }
              }
            }
          }
        }
      `;

      const orderResponse = await client.request(orderQuery, {
        variables: {
          id: `gid://shopify/Order/${orderId}`
        }
      });

      const orderData = orderResponse.data?.order;
      if (!orderData) {
        throw new Error(`Order ${orderId} not found`);
      }

      logger.info(`[Shopify Refund] Order found: ${orderData.name}, Total: ${orderData.totalPriceSet.shopMoney.amount} ${orderData.totalPriceSet.shopMoney.currencyCode}, Line items: ${orderData.lineItems.edges.length}`);

      // Step 2: Calculate refundable line items
      const refundableLineItems = orderData.lineItems.edges
        .filter((edge: any) => edge.node.refundableQuantity > 0)
        .map((edge: any) => ({
          lineItemId: edge.node.id,
          quantity: edge.node.refundableQuantity,
          restockType: "NO_RESTOCK"
        }));

      if (refundableLineItems.length === 0) {
        logger.info(`[Shopify Refund] No refundable items found for order ${orderId}`);
        throw new Error("No refundable items found - order may already be fully refunded");
      }

      logger.info(`[Shopify Refund] Step 2: Prepared ${refundableLineItems.length} line items for refund`);

      // Step 3: Create refund using GraphQL mutation
      logger.info(`[Shopify Refund] Step 3: Creating refund`);
      const refundMutation = `
        mutation refundCreate($input: RefundInput!) {
          refundCreate(input: $input) {
            refund {
              id
              createdAt
              note
              totalRefundedSet {
                presentmentMoney {
                  amount
                  currencyCode
                }
              }
              refundLineItems(first: 10) {
                edges {
                  node {
                    quantity
                    lineItem {
                      id
                      name
                    }
                  }
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const refundResponse = await client.request(refundMutation, {
        variables: {
          input: {
            orderId: `gid://shopify/Order/${orderId}`,
            note: "Automatic refund processed via chargeback system",
            notify: true,
            refundLineItems: refundableLineItems,
            shipping: {
              fullRefund: true
            },
            transactions: [
              {
                orderId: `gid://shopify/Order/${orderId}`,
                kind: "REFUND",
                gateway: orderData.transactions.find((t: any) => t.kind === "SALE")?.gateway || "cash",
                amount: orderData.totalPriceSet.shopMoney.amount,
                parentId: orderData.transactions.find((t: any) => t.kind === "SALE")?.id
              }
            ]
          }
        }
      });

      const refundResult = refundResponse.data?.refundCreate;
      if (!refundResult) {
        throw new Error('Invalid GraphQL response');
      }
      
      if (refundResult.userErrors && refundResult.userErrors.length > 0) {
        const errors = refundResult.userErrors.map((error: any) => `${error.field}: ${error.message}`).join(', ');
        throw new Error(`GraphQL Refund Errors: ${errors}`);
      }

      logger.info(`[Shopify Refund] Refund created successfully for order ${orderId}. Refund ID: ${refundResult.refund.id}`);
      
      // Create BlockRefund record if blockId is provided
      if (blockId) {
        const blockRefund = await this.createBlockRefund(blockId, storeId, orderId, refundResult.refund);
        return {
          success: true,
          data: {
            shopifyRefund: refundResult.refund,
            blockRefund
          }
        };
      }
      
      return {
        success: true,
        data: refundResult.refund
      };
    } catch (error) {
      logger.error(`[Shopify Refund] Error creating refund for order ${orderId}: ${(error as Error).message}`);
      
      // Create BlockRefund record with error if blockId is provided
      if (blockId) {
        const blockRefund = await this.createBlockRefund(blockId, storeId, orderId, null, error as Error);
        return {
          success: false,
          message: `Failed to create refund: ${(error as Error).message}`,
          blockRefund
        };
      }
      
      return {
        success: false,
        message: `Failed to create refund: ${(error as Error).message}`
      };
    }
  }


  /**
   * Cancel an order automatically
   */
  async cancelOrder(
    storeId: string,
    orderId: string
  ): Promise<any> {
    const store = await (this.prisma as any).linkedStore.findFirst({
      where: {
        id: storeId,
        provider: "shopify",
      },
    });

    if (!store) {
      throw new Error("Store not found");
    }

    const data = store.data as ShopifyStoreData;

    // Initialize the REST client
    const client = this.createRestClient(data.domain, data.accessToken);

    try {
      // Cancel order with auto refund
      const response = await client.post({
        path: `orders/${orderId}/cancel`,
        data: {
          reason: "customer",
          email: true,
          refund: true,
          restock: true
        },
      });

      return response.body;
    } catch (error) {
      console.error(`Error canceling order ${orderId}:`, error);
      throw new Error(
        `Failed to cancel order: ${(error as Error).message}`
      );
    }
  }

  /**
   * Calculate refund for an order automatically
   */
  async calculateRefund(
    storeId: string,
    orderId: string
  ): Promise<any> {
    const store = await (this.prisma as any).linkedStore.findFirst({
      where: {
        id: storeId,
        provider: "shopify",
      },
    });

    if (!store) {
      throw new Error("Store not found");
    }

    const data = store.data as ShopifyStoreData;

    // Initialize the REST client
    const client = this.createRestClient(data.domain, data.accessToken);

    try {
      // Get order details
      const orderResponse = await client.get({
        path: `orders/${orderId}`,
      });

      const order = orderResponse.body.order;

      // Build refund data for calculation
      const refundData: any = {
        shipping: {
          full_refund: true
        },
        refund_line_items: []
      };

      // Add all line items
      if (order.line_items && order.line_items.length > 0) {
        refundData.refund_line_items = order.line_items.map((item: any) => ({
          line_item_id: item.id,
          quantity: item.quantity
        }));
      }

      // Calculate refund
      const response = await client.post({
        path: `orders/${orderId}/refunds/calculate`,
        data: {
          refund: refundData
        },
      });

      return response.body;
    } catch (error) {
      console.error(`Error calculating refund for order ${orderId}:`, error);
      throw new Error(
        `Failed to calculate refund: ${(error as Error).message}`
      );
    }
  }

  /**
   * Find related Shopify orders by transaction or descriptor information
   * Used to automatically find orders related to dispute blocks
   */
  async findOrdersByBlockInfo(
    storeId: string,
    userId: string,
    blockInfo: {
      descriptor?: string;
      transactionId?: string;
      amount?: string;
      cardBin?: string;
      cardNumber?: string;
    }
  ): Promise<any> {
    const store = await (this.prisma as any).linkedStore.findFirst({
      where: {
        id: storeId,
        userId,
      },
    });

    if (!store) {
      throw new Error("Store not found");
    }

    const data = store.data as ShopifyStoreData;

    // Initialize the REST client
    const client = this.createRestClient(data.domain, data.accessToken);

    try {
      // Get all orders
      const ordersResponse = await client.get({
        path: "orders",
        query: {
          limit: 50,
          status: "any",
          financial_status: "paid",
        },
      });

      const orders = ordersResponse.body.orders || [];

      // If no orders found
      if (!orders.length) {
        return { orders: [] };
      }

      // We'll score each order based on how well it matches the block info
      const scoredOrders = [];

      for (const order of orders) {
        let score = 0;

        // Get transactions for this order
        const transactionsResponse = await client.get({
          path: `orders/${order.id}/transactions`,
        });

        const transactions = transactionsResponse.body.transactions || [];
        order.transactions = transactions;

        // Look for matches in transactions
        for (const transaction of transactions) {
          // Exact match on transaction ID
          if (
            blockInfo.transactionId &&
            transaction.id.toString() === blockInfo.transactionId
          ) {
            score += 50;
          }

          // Match on amount
          if (
            blockInfo.amount &&
            Math.abs(
              parseFloat(transaction.amount) - parseFloat(blockInfo.amount)
            ) < 0.01
          ) {
            score += 20;
          }

          // Match on card details if available
          if (transaction.payment_details) {
            if (
              blockInfo.cardBin &&
              transaction.payment_details.credit_card_bin === blockInfo.cardBin
            ) {
              score += 15;
            }

            if (
              blockInfo.cardNumber &&
              transaction.payment_details.credit_card_number ===
              blockInfo.cardNumber
            ) {
              score += 15;
            }
          }
        }

        // Add descriptor match score (looking for partial matches too)
        if (blockInfo.descriptor) {
          // Check if order name contains descriptor or vice versa
          const normalizedDescriptor = blockInfo.descriptor.toLowerCase();
          const normalizedOrderName = order.name.toLowerCase();

          if (
            normalizedOrderName.includes(normalizedDescriptor) ||
            normalizedDescriptor.includes(normalizedOrderName)
          ) {
            score += 10;
          }

          // Check if financial gateway name includes descriptor
          if (
            order.gateway &&
            normalizedDescriptor.includes(order.gateway.toLowerCase())
          ) {
            score += 5;
          }
        }

        if (score > 0) {
          scoredOrders.push({
            order,
            score,
          });
        }
      }

      // Sort by score (highest first)
      scoredOrders.sort((a, b) => b.score - a.score);

      // Return orders without the score
      return {
        orders: scoredOrders.map((item) => item.order),
      };
    } catch (error) {
      console.error(`Error finding orders for block:`, error);
      throw new Error(
        `Failed to find related orders: ${(error as Error).message}`
      );
    }
  }

  /**
   * Verifies the HMAC signature from Shopify to ensure request integrity.
   */
  isValidShopifyHmac = (query: Record<string, any>): boolean => {
    const { hmac, ...rest } = query;

    const sortedQuery = Object.keys(rest)
      .sort()
      .map((key) => `${key}=${rest[key]}`)
      .join("&");

    const generatedHmac = crypto
      .createHmac("sha256", env.SHOPIFY_API_SECRET)
      .update(sortedQuery)
      .digest("hex");

    return hmac === generatedHmac;
  };

  /**
   * Validates timestamp to prevent replay attacks.
   */
  isTimestampValid = (timestamp: string): boolean => {
    const FIVE_MINUTES = 5 * 60 * 1000;
    const now = Date.now();
    const timestampInt = parseInt(timestamp, 10) * 1000;
    return Math.abs(now - timestampInt) <= FIVE_MINUTES;
  };

  /**
   * Get store info by storeId
   */
  async getStoreInfo(storeId: string) {
    try {
      const linkedStore = await (this.prisma as any).linkedStore.findFirst({
        where: { 
          id: storeId,
          provider: 'shopify'
        },
        select: {
          id: true,
          storeName: true,
          provider: true,
          providerStoreId: true,
          data: true,
          createdAt: true
        }
      });

      if (!linkedStore) {
        return null;
      }

      const data = linkedStore.data as ShopifyStoreData;
      
      return {
        id: linkedStore.id,
        name: linkedStore.storeName,
        shopifyShop: data.domain,
        shopifyAccessToken: data.accessToken,
        createdAt: linkedStore.createdAt
      };
    } catch (error: any) {
      logger.error('Error getting store info:', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw error;
    }
  }

  /**
   * Create a new subscription cycle when monthly limit is reached
   */
  async createNewSubscriptionCycle(
    shop: string,
    accessToken: string,
    currentUsageAmount: number
  ): Promise<{ success: boolean; confirmationUrl?: string; chargeId?: string; error?: string }> {
    try {
      logger.info(`Creating new subscription cycle for shop ${shop} with usage amount: $${currentUsageAmount / 100}`);

      // Create GraphQL client
      const client = this.createGraphQLClient(shop, accessToken);

      // Create a new app subscription charge via GraphQL
      // Note: We're creating a one-time charge, not a recurring subscription
      const mutation = `
        mutation appPurchaseOneTimeCreate($name: String!, $price: MoneyInput!, $returnUrl: URL!) {
          appPurchaseOneTimeCreate(
            name: $name,
            price: $price,
            returnUrl: $returnUrl
          ) {
            appPurchaseOneTime {
              id
              name
              price {
                amount
                currencyCode
              }
              status
              confirmationUrl
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        name: `Usage charges for ${new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}`,
        price: {
          amount: (currentUsageAmount / 100).toFixed(2), // Convert cents to dollars
          currencyCode: "USD"
        },
        returnUrl: `${env.SHOPIFY_HOST_NAME}/auth/shopify-callback?shop=${shop}&charge_id=CHARGE_ID`
      };

      const response = await client.request(mutation, { variables });

      if (response.data?.appPurchaseOneTimeCreate?.userErrors?.length > 0) {
        const errors = response.data.appPurchaseOneTimeCreate.userErrors;
        logger.error('GraphQL errors creating app purchase:', errors);
        return {
          success: false,
          error: errors[0].message
        };
      }

      const charge = response.data?.appPurchaseOneTimeCreate?.appPurchaseOneTime;

      if (!charge) {
        return {
          success: false,
          error: 'Failed to create app purchase'
        };
      }

      logger.info(`Successfully created app purchase: ${charge.id}`);

      return {
        success: true,
        confirmationUrl: charge.confirmationUrl,
        chargeId: charge.id
      };

    } catch (error: any) {
      logger.error('Error creating new subscription cycle:', error);
      return {
        success: false,
        error: error.message || 'Failed to create new subscription cycle'
      };
    }
  }

  /**
   * Activate an app charge after user confirms it
   */
  async activateAppCharge(
    shop: string,
    accessToken: string,
    chargeId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      logger.info(`Activating app charge ${chargeId} for shop ${shop}`);

      // Create REST client
      const client = this.createRestClient(shop, accessToken);

      // Get the charge details first
      const chargeResponse = await client.get({
        path: `application_charges/${chargeId}`
      });

      const charge = chargeResponse.body as any;

      if (charge.status === 'accepted') {
        // Activate the charge
        const activateResponse = await client.post({
          path: `application_charges/${chargeId}/activate`,
          data: {}
        });

        if (activateResponse.body) {
          logger.info(`Successfully activated charge ${chargeId}`);
          return { success: true };
        }
      } else if (charge.status === 'active') {
        logger.info(`Charge ${chargeId} is already active`);
        return { success: true };
      } else {
        return {
          success: false,
          error: `Charge status is ${charge.status}, cannot activate`
        };
      }

      return {
        success: false,
        error: 'Failed to activate charge'
      };

    } catch (error: any) {
      logger.error('Error activating app charge:', error);
      return {
        success: false,
        error: error.message || 'Failed to activate app charge'
      };
    }
  }

  /**
   * Get subscription for a linked store
   */
  async getSubscription(linkedStoreId: string): Promise<any> {
    try {
      const subscription = await (this.prisma as any).shopifySubscription.findUnique({
        where: { linkedStoreId },
        include: {
          linkedStore: true,
          usageRecords: {
            where: { status: 'PENDING' },
            orderBy: { createdAt: 'desc' }
          }
        }
      });

      return subscription;
    } catch (error: any) {
      logger.error('Error getting subscription:', error);
      throw error;
    }
  }

  /**
   * Create a new Shopify subscription using GraphQL App Billing API
   */
  async createSubscription(linkedStoreId: string): Promise<{ success: boolean; confirmationUrl?: string; error?: string }> {
    try {
      const linkedStore = await (this.prisma as any).linkedStore.findUnique({
        where: { id: linkedStoreId },
        include: { shopifySubscription: true }
      });

      if (!linkedStore) {
        return { success: false, error: 'Store not found' };
      }

      const storeData = linkedStore.data as ShopifyStoreData;
      const shop = storeData.domain;
      const accessToken = storeData.accessToken;

      // Check if subscription already exists and handle different statuses
      let shouldCreateNewSubscription = true;
      let currentSubscription = null;
      
      if (linkedStore.shopifySubscription) {
        currentSubscription = linkedStore.shopifySubscription;
        
        // If subscription is ACTIVE, verify it actually exists in Shopify
        if (currentSubscription.status === SUBSCRIPTION_STATUS.ACTIVE) {
          // Check if we have a Shopify subscription ID
          if (currentSubscription.shopifySubscriptionId) {
            try {
              // Verify the subscription exists in Shopify
              const client = this.createGraphQLClient(shop, accessToken);
              const query = `
                query getSubscription($id: ID!) {
                  node(id: $id) {
                    ... on AppSubscription {
                      id
                      status
                    }
                  }
                }
              `;
              
              const response = await client.request(query, {
                variables: { id: currentSubscription.shopifySubscriptionId }
              });
              
              if (response.data?.node?.id && response.data.node.status === 'ACTIVE') {
                logger.info(`Verified existing ACTIVE subscription in Shopify for store ${linkedStoreId}`);
                return { success: false, error: 'Subscription is already active' };
              } else {
                logger.warn(`ACTIVE subscription in database but not found or inactive in Shopify for store ${linkedStoreId}, will create new subscription`);
                shouldCreateNewSubscription = true;
              }
            } catch (error) {
              logger.warn(`Error verifying existing subscription in Shopify for store ${linkedStoreId}: ${error instanceof Error ? error.message : String(error)}`);
              logger.info(`Will create new subscription to replace missing Shopify subscription`);
              shouldCreateNewSubscription = true;
            }
          } else {
            logger.warn(`ACTIVE subscription in database but no shopifySubscriptionId for store ${linkedStoreId}, will create new subscription`);
            shouldCreateNewSubscription = true;
          }
        } else {
          // For PENDING, CANCELLED, EXPIRED, DECLINED, FROZEN statuses, create new subscription
          logger.info(`Found existing subscription with status ${currentSubscription.status} for store ${linkedStoreId}, will create new subscription`);
          shouldCreateNewSubscription = true;
        }
      } else {
        logger.info(`No existing subscription found for store ${linkedStoreId}, will create new subscription`);
        shouldCreateNewSubscription = true;
      }
      
      // If we don't need to create a new subscription, return early
      if (!shouldCreateNewSubscription) {
        return { success: false, error: 'Subscription is already active' };
      }

      // Create GraphQL client
      const client = this.createGraphQLClient(shop, accessToken);

      // Create subscription using GraphQL
      const mutation = `
        mutation appSubscriptionCreate($name: String!, $lineItems: [AppSubscriptionLineItemInput!]!, $returnUrl: URL!, $test: Boolean) {
          appSubscriptionCreate(
            name: $name,
            lineItems: $lineItems,
            returnUrl: $returnUrl,
            test: $test
          ) {
            appSubscription {
              id
              name
              status
              lineItems {
                id
                plan {
                  pricingDetails {
                    ... on AppUsagePricing {
                      cappedAmount {
                        amount
                        currencyCode
                      }
                      terms
                    }
                  }
                }
              }
            }
            confirmationUrl
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        name: BILLING_PLANS.PROFESSIONAL.name,
        lineItems: [{
          plan: {
            appUsagePricingDetails: {
              cappedAmount: {
                amount: BILLING_PLANS.PROFESSIONAL.cappedAmount,
                currencyCode: BILLING_PLANS.PROFESSIONAL.currency
              },
              terms: `Usage-based billing with $${BILLING_PLANS.PROFESSIONAL.cappedAmount} monthly cap`
            }
          }
        }],
        returnUrl: `${env.SHOPIFY_HOST_NAME}/billing/callback?shop=${shop}&store_id=${linkedStoreId}`,
        test: env.SHOPIFY_BILLING_TEST_MODE
      };

      // Log subscription creation mode
      if (env.SHOPIFY_BILLING_TEST_MODE) {
        logger.info(`Creating TEST subscription for store ${linkedStoreId} (no charges will be made)`);
      } else {
        logger.info(`Creating LIVE subscription for store ${linkedStoreId}`);
      }

      const response = await client.request(mutation, { variables });

      if (response.data?.appSubscriptionCreate?.userErrors?.length > 0) {
        const errors = response.data.appSubscriptionCreate.userErrors;
        logger.error('GraphQL errors creating subscription:', errors);
        return {
          success: false,
          error: errors[0].message
        };
      }

      const subscription = response.data?.appSubscriptionCreate?.appSubscription;
      const confirmationUrl = response.data?.appSubscriptionCreate?.confirmationUrl;

      if (!subscription || !confirmationUrl) {
        return {
          success: false,
          error: 'Failed to create subscription'
        };
      }

      // Save or update subscription in database
      if (currentSubscription && (currentSubscription.status === SUBSCRIPTION_STATUS.PENDING || shouldCreateNewSubscription)) {
        // Update existing subscription with Shopify subscription details
        await (this.prisma as any).shopifySubscription.update({
          where: { id: currentSubscription.id },
          data: {
            shopifySubscriptionId: subscription.id,
            planName: BILLING_PLANS.PROFESSIONAL.name,
            planType: BILLING_PLANS.PROFESSIONAL.type,
            status: SUBSCRIPTION_STATUS.PENDING,
            amount: BILLING_PLANS.PROFESSIONAL.amount,
            currency: BILLING_PLANS.PROFESSIONAL.currency,
            billingInterval: BILLING_PLANS.PROFESSIONAL.interval,
            cappedAmount: BILLING_PLANS.PROFESSIONAL.cappedAmountCents,
            confirmationUrl,
            returnUrl: variables.returnUrl,
            metadata: {
              ...(currentSubscription.metadata || {}),
              testMode: env.SHOPIFY_BILLING_TEST_MODE,
              updatedAt: new Date().toISOString()
            }
          }
        });
        logger.info(`Updated existing subscription ${currentSubscription.id} for store ${linkedStoreId}`);
      } else {
        // Create new subscription record
        await (this.prisma as any).shopifySubscription.create({
          data: {
            linkedStoreId,
            shopifySubscriptionId: subscription.id,
            planName: BILLING_PLANS.PROFESSIONAL.name,
            planType: BILLING_PLANS.PROFESSIONAL.type,
            status: SUBSCRIPTION_STATUS.PENDING,
            amount: BILLING_PLANS.PROFESSIONAL.amount,
            currency: BILLING_PLANS.PROFESSIONAL.currency,
            billingInterval: BILLING_PLANS.PROFESSIONAL.interval,
            cappedAmount: BILLING_PLANS.PROFESSIONAL.cappedAmountCents,
            confirmationUrl,
            returnUrl: variables.returnUrl,
            metadata: {
              testMode: env.SHOPIFY_BILLING_TEST_MODE,
              createdAt: new Date().toISOString()
            }
          }
        });
        logger.info(`Created new subscription ${subscription.id} for store ${linkedStoreId}`);
      }

      return {
        success: true,
        confirmationUrl
      };

    } catch (error: any) {
      logger.error('Error creating subscription:', error);
      return {
        success: false,
        error: error.message || 'Failed to create subscription'
      };
    }
  }

  /**
   * Cancel a Shopify subscription
   */
  async cancelSubscription(linkedStoreId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const subscription = await (this.prisma as any).shopifySubscription.findUnique({
        where: { linkedStoreId },
        include: { linkedStore: true }
      });

      if (!subscription) {
        return { success: false, error: 'Subscription not found' };
      }

      const linkedStore = subscription.linkedStore;
      const storeData = linkedStore.data as ShopifyStoreData;
      const shop = storeData.domain;
      const accessToken = storeData.accessToken;

      // Cancel subscription using GraphQL
      const client = this.createGraphQLClient(shop, accessToken);

      const mutation = `
        mutation appSubscriptionCancel($id: ID!) {
          appSubscriptionCancel(id: $id) {
            appSubscription {
              id
              status
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const variables = {
        id: subscription.shopifySubscriptionId
      };

      const response = await client.request(mutation, { variables });

      if (response.data?.appSubscriptionCancel?.userErrors?.length > 0) {
        const errors = response.data.appSubscriptionCancel.userErrors;
        logger.error('GraphQL errors cancelling subscription:', errors);
        return {
          success: false,
          error: errors[0].message
        };
      }

      // Update subscription status in database
      await (this.prisma as any).shopifySubscription.update({
        where: { linkedStoreId },
        data: {
          status: SUBSCRIPTION_STATUS.CANCELLED,
          cancelledAt: new Date()
        }
      });

      logger.info(`Cancelled subscription ${subscription.shopifySubscriptionId} for store ${linkedStoreId}`);

      return { success: true };

    } catch (error: any) {
      logger.error('Error cancelling subscription:', error);
      return {
        success: false,
        error: error.message || 'Failed to cancel subscription'
      };
    }
  }

  /**
   * Create usage record for billing
   */
  async createUsageRecord(linkedStoreId: string, description: string, quantity: number, price: number): Promise<any> {
    try {
      const subscription = await (this.prisma as any).shopifySubscription.findUnique({
        where: { linkedStoreId },
        include: { linkedStore: true }
      });

      if (!subscription) {
        throw new Error('Subscription not found');
      }

      const totalAmount = quantity * price;
      const storeData = subscription.linkedStore.data as any;
      const shop = storeData.domain;
      const accessToken = storeData.accessToken;

      // Create GraphQL client
      const client = this.createGraphQLClient(shop, accessToken);

      // Create usage record via Shopify GraphQL API
      const mutation = `
        mutation appUsageRecordCreate($description: String!, $price: MoneyInput!, $subscriptionLineItemId: ID!) {
          appUsageRecordCreate(
            description: $description
            price: $price
            subscriptionLineItemId: $subscriptionLineItemId
          ) {
            appUsageRecord {
              id
              description
              price {
                amount
                currencyCode
              }
              createdAt
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      // Get the subscription details from Shopify to get the line item ID
      const subscriptionQuery = `
        query getSubscription($id: ID!) {
          node(id: $id) {
            ... on AppSubscription {
              id
              lineItems {
                id
                plan {
                  pricingDetails {
                    ... on AppUsagePricing {
                      cappedAmount {
                        amount
                        currencyCode
                      }
                    }
                  }
                }
              }
            }
          }
        }
      `;

      const subscriptionResponse = await client.request(subscriptionQuery, {
        variables: { id: subscription.shopifySubscriptionId }
      });

      const subscriptionData = subscriptionResponse.data?.node;
      if (!subscriptionData || !subscriptionData.lineItems || subscriptionData.lineItems.length === 0) {
        throw new Error('No subscription line items found');
      }

      // Use the first line item ID (usage-based subscription typically has one line item)
      const lineItemId = subscriptionData.lineItems[0].id;

      const variables = {
        description,
        price: {
          amount: (totalAmount / 100).toFixed(2), // Convert cents to dollars
          currencyCode: "USD"
        },
        subscriptionLineItemId: lineItemId
      };

      logger.info(`Creating usage record in Shopify for subscription ${subscription.shopifySubscriptionId}: ${description} - $${totalAmount / 100}`);

      const response = await client.request(mutation, { variables });

      if (response.data?.appUsageRecordCreate?.userErrors?.length > 0) {
        const errors = response.data.appUsageRecordCreate.userErrors;
        logger.error('GraphQL errors creating usage record:', errors);
        throw new Error(`Failed to create usage record: ${errors[0].message}`);
      }

      const shopifyUsageRecord = response.data?.appUsageRecordCreate?.appUsageRecord;

      if (!shopifyUsageRecord) {
        throw new Error('Failed to create usage record in Shopify');
      }

      // Store local usage record with Shopify ID
      const usageRecord = await (this.prisma as any).shopifyUsageRecord.create({
        data: {
          subscriptionId: subscription.id,
          shopifyRecordId: shopifyUsageRecord.id,
          description,
          quantity,
          price,
          totalAmount,
          billingDate: new Date(),
          status: 'CREATED',
          metadata: {
            testMode: env.SHOPIFY_BILLING_TEST_MODE,
            createdAt: new Date().toISOString(),
            shopifyResponse: shopifyUsageRecord
          }
        }
      });

      // Update subscription current usage
      await (this.prisma as any).shopifySubscription.update({
        where: { linkedStoreId },
        data: {
          currentUsage: {
            increment: totalAmount
          }
        }
      });

      logger.info(`Successfully created usage record in Shopify: ${shopifyUsageRecord.id} - $${totalAmount / 100}`);

      return usageRecord;

    } catch (error: any) {
      logger.error('Error creating usage record:', error);
      throw error;
    }
  }

  /**
   * Update subscription status (used by webhooks)
   */
  async updateSubscriptionStatus(linkedStoreId: string, status: string, webhookData?: any): Promise<any> {
    try {
      const updateData: any = {
        status,
        webhookData
      };

      if (status === SUBSCRIPTION_STATUS.ACTIVE) {
        updateData.activatedAt = new Date();
      } else if (status === SUBSCRIPTION_STATUS.CANCELLED) {
        updateData.cancelledAt = new Date();
      }

      const subscription = await (this.prisma as any).shopifySubscription.update({
        where: { linkedStoreId },
        data: updateData
      });

      logger.info(`Updated subscription status for store ${linkedStoreId}: ${status}`);

      return subscription;

    } catch (error: any) {
      logger.error('Error updating subscription status:', error);
      throw error;
    }
  }
}

export default ShopifyService;