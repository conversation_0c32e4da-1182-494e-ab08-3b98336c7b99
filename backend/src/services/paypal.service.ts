import { paypalClient } from '../config/paypal';
import PrismaService from './prisma.service';
import { 
  OrdersController, 
  CheckoutPaymentIntent,
  OrderApplicationContextLandingPage,
  OrderApplicationContextShippingPreference,
  OrderApplicationContextUserAction
} from '@paypal/paypal-server-sdk';

export class PayPalService {
  private ordersApi = new OrdersController(paypalClient);
  private prisma: any;

  constructor() {
    this.prisma = PrismaService.getInstance().getClient();
  }

  // Create PayPal order for billing agreement
  async createBillingOrder(linkedStoreId: string, returnUrl: string, cancelUrl: string) {
    try {
      // Create an order that will be used for billing agreement
      const orderRequest = {
        intent: CheckoutPaymentIntent.Capture,
        purchaseUnits: [{
          referenceId: linkedStoreId,
          description: 'Setup for automatic bill payments',
          amount: {
            currencyCode: 'USD',
            value: '1.00' // Initial setup fee (can be refunded)
          }
        }],
        applicationContext: {
          brandName: 'QuantChargeBack',
          landingPage: OrderApplicationContextLandingPage.Billing,
          shippingPreference: OrderApplicationContextShippingPreference.NoShipping,
          userAction: OrderApplicationContextUserAction.PayNow,
          returnUrl: returnUrl,
          cancelUrl: cancelUrl
        }
      };

      const response = await this.ordersApi.createOrder({
        body: orderRequest
      });

      return response.result;
    } catch (error) {
      console.error('Error creating billing order:', error);
      throw error;
    }
  }

  // Execute agreement after user approval
  async executeAgreement(orderId: string, linkedStoreId: string) {
    try {
      // Check if agreement already exists
      const existingAgreement = await this.prisma.payPalAgreement.findUnique({
        where: { agreementId: orderId }
      });

      if (existingAgreement) {
        // Return existing agreement if already executed
        return { 
          order: { id: orderId, status: 'COMPLETED' }, 
          dbRecord: existingAgreement,
          message: 'Agreement already exists' 
        };
      }

      // Capture the order
      const captureResponse = await this.ordersApi.captureOrder({
        id: orderId
      });

      if (captureResponse.result.status !== 'COMPLETED') {
        throw new Error('Order capture failed');
      }

      // Save agreement to database using upsert to handle race conditions
      const agreement = await this.prisma.payPalAgreement.upsert({
        where: { agreementId: orderId },
        update: {
          status: 'ACTIVE',
          updatedAt: new Date()
        },
        create: {
          linkedStoreId,
          agreementId: orderId,
          status: 'ACTIVE'
        }
      });

      return { order: captureResponse.result, dbRecord: agreement };
    } catch (error) {
      console.error('Error executing agreement:', error);
      throw error;
    }
  }

  // Process payment for a bill
  async chargeAgreement(agreementId: string, amount: number, billId?: string) {
    try {
      // Find the agreement
      const agreement = await this.prisma.payPalAgreement.findUnique({
        where: { agreementId }
      });

      if (!agreement || agreement.status !== 'ACTIVE') {
        throw new Error('Agreement not found or not active');
      }

      // Create a new order for the payment
      const orderRequest = {
        intent: CheckoutPaymentIntent.Capture,
        purchaseUnits: [{
          referenceId: billId || 'payment',
          description: `Payment for bill ${billId || 'N/A'}`,
          amount: {
            currencyCode: 'USD',
            value: (amount / 100).toFixed(2) // Convert cents to dollars
          }
        }]
      };

      const orderResponse = await this.ordersApi.createOrder({
        body: orderRequest
      });

      // Auto-capture the payment
      const captureResponse = await this.ordersApi.captureOrder({
        id: orderResponse.result.id!
      });

      // Save payment record
      const payment = await this.prisma.payPalPayment.create({
        data: {
          agreementId: agreement.id,
          billId,
          paymentId: captureResponse.result.id!,
          amount: Math.round(amount), // Ensure integer (cents)
          currency: 'USD',
          status: captureResponse.result.status === 'COMPLETED' ? 'COMPLETED' : 'PENDING'
        }
      });

      return { payment: captureResponse.result, dbRecord: payment };
    } catch (error) {
      console.error('Error charging agreement:', error);
      throw error;
    }
  }

  // Cancel agreement
  async cancelAgreement(agreementId: string) {
    try {
      // Update database status
      await this.prisma.payPalAgreement.update({
        where: { agreementId },
        data: { status: 'CANCELLED' }
      });

      return { success: true };
    } catch (error) {
      console.error('Error cancelling agreement:', error);
      throw error;
    }
  }

  // Get agreement details
  async getAgreement(agreementId: string) {
    try {
      const agreement = await this.prisma.payPalAgreement.findUnique({
        where: { agreementId },
        include: {
          payments: {
            orderBy: { createdAt: 'desc' },
            take: 5
          }
        }
      });

      return agreement;
    } catch (error) {
      console.error('Error getting agreement:', error);
      throw error;
    }
  }
}

export const paypalService = new PayPalService();