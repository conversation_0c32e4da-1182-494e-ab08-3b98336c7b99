import axios from 'axios';
import crypto from 'crypto';
import { ServiceResponse } from '@/constants/type';
import { StatusCodes } from 'http-status-codes';
import { getI18n } from '@/middlewares/language.middleware';
import PrismaService from './prisma.service';
import { env } from '@/config/environment';

const prisma = PrismaService.getInstance().getClient();

// Types for Compensation Service
interface CompensationConfig {
  merchantNo: string;
  signKey: string;
  apiEndpoint: string;
}

interface CompensationRequest {
  transaction_id: string;
  merchant_order_no: string;
  compensation_amount: number;
  compensation_currency: string;
  reason: string;
  evidence_files?: string[]; // Array of file URLs or base64 encoded files
  additional_details?: Record<string, any>;
  [key: string]: any;
}

interface CompensationResult {
  request_id: string;
  status: string;
  processed_amount: number;
  processed_currency: string;
  created_at: string;
  sign: string;
  [key: string]: any;
}

class CompensationService {
  private static config: CompensationConfig = {
    merchantNo: env.COMPENSATION_MERCHANT_NO || '',
    signKey: env.COMPENSATION_SIGN_KEY || '',
    apiEndpoint: env.COMPENSATION_API_ENDPOINT || 'https://mer.tradefensor.com',
  };

  /**
   * Submit compensation request
   * Initiates a compensation request for a transaction
   */
  static submitCompensationRequest = async (
    compensationData: CompensationRequest
  ): Promise<ServiceResponse<CompensationResult>> => {
    const i18n = getI18n();
    try {
      // Add signature to the compensation data
      const signedData = this.addSignature(compensationData);
      
      // Make API request
      const response = await axios.post(
        `${this.config.apiEndpoint}/api/compensation/submit`, 
        signedData,
        {
          headers: {
            'Content-Type': 'application/json',
            'MerchantNo': this.config.merchantNo
          }
        }
      );
      
      const result = response.data;
      
      // Verify the response signature
      if (!this.verifySignature(result)) {
        return {
          success: false,
          status: StatusCodes.BAD_REQUEST,
          message: i18n.t('error.compensation.invalidSignature'),
          data: undefined,
        };
      }
      
      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t('success.compensation.requestSubmitted'),
        data: result,
      };
    } catch (error: any) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.compensation.internalError'),
        data: undefined,
      };
    }
  };

  /**
   * Check compensation status
   * Retrieves the current status of a previously submitted compensation request
   */
  static checkCompensationStatus = async (requestId: string): Promise<ServiceResponse<CompensationResult>> => {
    const i18n = getI18n();
    try {
      // Prepare request data
      const requestData = {
        request_id: requestId
      };
      
      // Add signature
      const signedData = this.addSignature(requestData);
      
      // Make API request
      const response = await axios.post(
        `${this.config.apiEndpoint}/api/compensation/status`, 
        signedData,
        {
          headers: {
            'Content-Type': 'application/json',
            'MerchantNo': this.config.merchantNo
          }
        }
      );
      
      const result = response.data;
      
      // Verify the response signature
      if (!this.verifySignature(result)) {
        return {
          success: false,
          status: StatusCodes.BAD_REQUEST,
          message: i18n.t('error.compensation.invalidSignature'),
          data: undefined,
        };
      }
      
      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t('success.compensation.statusRetrieved'),
        data: result,
      };
    } catch (error: any) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.compensation.internalError'),
        data: undefined,
      };
    }
  };

  /**
   * Submit additional evidence for a compensation request
   */
  static submitAdditionalEvidence = async (
    requestId: string, 
    evidenceFiles: string[]
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Prepare request data
      const requestData = {
        request_id: requestId,
        evidence_files: evidenceFiles
      };
      
      // Add signature
      const signedData = this.addSignature(requestData);
      
      // Make API request
      const response = await axios.post(
        `${this.config.apiEndpoint}/api/compensation/evidence`, 
        signedData,
        {
          headers: {
            'Content-Type': 'application/json',
            'MerchantNo': this.config.merchantNo
          }
        }
      );
      
      const result = response.data;
      
      // Verify the response signature
      if (!this.verifySignature(result)) {
        return {
          success: false,
          status: StatusCodes.BAD_REQUEST,
          message: i18n.t('error.compensation.invalidSignature'),
          data: undefined,
        };
      }
      
      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t('success.compensation.evidenceSubmitted'),
        data: result,
      };
    } catch (error: any) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.compensation.internalError'),
        data: undefined,
      };
    }
  };

  /**
   * List all compensation requests
   */
  static listCompensationRequests = async (
    page: number = 1,
    limit: number = 10,
    filters?: Record<string, any>
  ): Promise<ServiceResponse<any>> => {
    const i18n = getI18n();
    try {
      // Prepare request data
      const requestData = {
        page,
        limit,
        ...filters
      };
      
      // Add signature
      const signedData = this.addSignature(requestData);
      
      // Make API request
      const response = await axios.post(
        `${this.config.apiEndpoint}/api/compensation/list`, 
        signedData,
        {
          headers: {
            'Content-Type': 'application/json',
            'MerchantNo': this.config.merchantNo
          }
        }
      );
      
      const result = response.data;
      
      // Verify the response signature
      if (!this.verifySignature(result)) {
        return {
          success: false,
          status: StatusCodes.BAD_REQUEST,
          message: i18n.t('error.compensation.invalidSignature'),
          data: undefined,
        };
      }
      
      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t('success.compensation.listRetrieved'),
        data: result,
      };
    } catch (error: any) {
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.compensation.internalError'),
        data: undefined,
      };
    }
  };

  /**
   * Add MD5 Salt signature to the request data
   */
  private static addSignature = (data: any) => {
    // Create a copy of the data
    const signedData = { ...data };
    
    // Sort all parameters in ascending order of parameter name (ASCII)
    const sortedParams = Object.keys(signedData).sort();
    
    // Concatenate key-value pairs with &
    let signString = '';
    sortedParams.forEach(key => {
      if (signString) signString += '&';
      // Handle different types of values
      if (typeof signedData[key] !== 'object') {
        signString += `${key}=${signedData[key]}`;
      } else if (Array.isArray(signedData[key])) {
        signString += `${key}=${signedData[key].join(',')}`;
      } else {
        signString += `${key}=${JSON.stringify(signedData[key])}`;
      }
    });
    
    // Add SignKey to the end
    signString += this.config.signKey;
    
    // Calculate MD5 hash
    const sign = crypto.createHash('md5').update(signString).digest('hex');
    
    // Add sign to the data
    signedData.sign = sign;
    
    return signedData;
  };

  /**
   * Verify the signature of the response
   */
  private static verifySignature = (data: any) => {
    // Extract and remove the sign from data
    const { sign, ...dataWithoutSign } = data;
    
    // Sort all parameters in ascending order of parameter name (ASCII)
    const sortedParams = Object.keys(dataWithoutSign).sort();
    
    // Concatenate key-value pairs with &
    let signString = '';
    sortedParams.forEach(key => {
      if (signString) signString += '&';
      // Handle different types of values
      if (typeof dataWithoutSign[key] !== 'object') {
        signString += `${key}=${dataWithoutSign[key]}`;
      } else if (Array.isArray(dataWithoutSign[key])) {
        signString += `${key}=${dataWithoutSign[key].join(',')}`;
      } else {
        signString += `${key}=${JSON.stringify(dataWithoutSign[key])}`;
      }
    });
    
    // Add SignKey to the end
    signString += this.config.signKey;
    
    // Calculate MD5 hash
    const calculatedSign = crypto.createHash('md5').update(signString).digest('hex');
    
    // Compare calculated sign with the one in the response
    return calculatedSign === sign;
  };
}

export default CompensationService;
