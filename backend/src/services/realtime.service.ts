import { EntityType } from "../constants/type";
import SocketService from "./socket.service";

class RealtimeService {
  static async updateUserSubscription(data: any, userId: string) {
    SocketService.emitToUserIdInEntity(
      EntityType.USER,
      userId,
      "user-subscription",
      data
    );
  }
  static async updatePredictionRequest(data: any, userId: string) {
    SocketService.emitToUserIdInEntity(
      EntityType.USER,
      userId,
      "prediction-request",
      data
    );
  }
  static async updatePayment(data: any, paymentCode: string) {
    SocketService.emitToUserIdInEntity(
      EntityType.PAYMENT,
      paymentCode,
      "payment",
      data
    );
  }

  static async listenFacePrediction(data: any, predictionCode: string) {
    SocketService.emitToUserIdInEntity(
      EntityType.PREDICTION,
      predictionCode,
      "face-prediction",
      data
    );
  }

  static async sendAnalysisResult(data: any, predictionCode: string) {
    SocketService.emitToUserIdInEntity(
      EntityType.PREDICTION,
      predictionCode,
      "analysis",
      data
    );
  }
}

export default RealtimeService;
