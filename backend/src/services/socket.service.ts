import { Server, Socket } from "socket.io";
import http from "http";
import { EntityType } from "@/constants/type";

const getUserIdFromHeaders = (headers: any) => {
  return headers["x-user-id"];
};
const getPredictionCodeFromHeaders = (headers: any) => {
  return headers["x-prediction-code"];
};
const getPaymentCodeFromHeaders = (headers: any) => {
  return headers["x-payment-code"];
};

const getUserIdFromQuery = (query: any) => {
  return query["userId"];
};
const getPredictionCodeFromQuery = (query: any) => {
  return query["predictionCode"];
};
const getPaymentCodeFromQuery = (query: any) => {
  return query["paymentCode"];
};
class SocketService {
  private static instance: SocketService;
  private static io: Server | undefined;

  public static getInstance(): SocketService {
    if (!SocketService.instance) {
      SocketService.instance = new SocketService();
    }
    return SocketService.instance;
  }

  public static initSocket(server: http.Server): void {
    this.io = new Server(server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"],
      },
      path: "/socket/v2",
    });

    this.io
      .of(`/v2/${EntityType.SYSTEM.toLowerCase()}`)
      .on("connection", (socket: Socket) => {
        console.log(
          `Socket | A user [${socket.id}] connected to ${EntityType.SYSTEM}`
        );
        socket.on("disconnect", () => {
          console.log(
            `Socket | User [${socket.id}] disconnected from ${EntityType.SYSTEM}`
          );
        });
      });

    this.io
      .of(`/v2/${EntityType.USER.toLowerCase()}`)
      .on("connection", (socket: Socket) => {
        const userId =
          getUserIdFromQuery(socket.handshake.query) ||
          getUserIdFromHeaders(socket.handshake.headers);
        socket.join(userId);
        console.log(
          `Socket | A user [${socket.id} - ${userId}] connected to ${EntityType.USER}`
        );
        socket.on("disconnect", () => {
          console.log(
            `Socket | User [${socket.id} - ${userId}] disconnected from ${EntityType.USER}`
          );
        });
      });

    this.io
      .of(`/v2/${EntityType.PAYMENT.toLowerCase()}`)
      .on("connection", (socket: Socket) => {
        const userId =
          getUserIdFromQuery(socket.handshake.query) ||
          getUserIdFromHeaders(socket.handshake.headers);
        const paymentCode =
          getPaymentCodeFromQuery(socket.handshake.query) ||
          getPaymentCodeFromHeaders(socket.handshake.headers);
        socket.join(paymentCode);
        console.log(
          `Socket | A user [${socket.id} - ${userId}] with payment [${paymentCode}] connected to ${EntityType.PAYMENT}`
        );
        socket.on("disconnect", () => {
          console.log(
            `Socket | User [${socket.id} - ${userId}] payment [${paymentCode}] disconnected from ${EntityType.PAYMENT}`
          );
        });
      });

    this.io.on("connection", async (socket: Socket) => {
      const userId =
        getUserIdFromQuery(socket.handshake.query) ||
        getUserIdFromHeaders(socket.handshake.headers);
      socket.join(userId);
      console.log(`Socket | A user [${socket.id}] connected`);

      socket.on("disconnect", () => {
        console.log(`Socket | User [${socket.id}] disconnected`);
      });
    });
  }

  public static getIO(): Server {
    if (!this.io) {
      throw new Error("Socket.IO instance not initialized.");
    }
    return this.io;
  }

  public static emit(event: string, data: any): void {
    if (!this.io) {
      throw new Error("Socket.IO instance not initialized.");
    }
    this.io.emit(event, data);
  }

  public static emitToUserIdInEntity(
    entity: EntityType,
    userId: string,
    event: string,
    data: any
  ): void {
    if (!this.io) {
      throw new Error("Socket.IO instance not initialized.");
    }
    this.io.of(`/v2/${entity.toLowerCase()}`).to(userId).emit(event, data);
  }
}
export default SocketService;
