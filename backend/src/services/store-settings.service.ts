import { PrismaClient } from "@prisma/client";
import PrismaService from "./prisma.service";
import {
  IStoreSetting,
  IStoreSettings,
  CreateStoreSettingRequest,
  UpdateStoreSettingRequest,
  UpdateStoreSettingsRequest,
  createStoreSettingSchema,
  updateStoreSettingSchema,
  updateStoreSettingsSchema,
} from "../models/store-settings.model";

class StoreSettingsService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = PrismaService.getInstance().getClient();
  }



  /**
   * Get all store settings by store ID as key-value object
   */
  async getByStoreId(storeId: string): Promise<IStoreSettings> {
    try {
      const storeSettings = await this.prisma.storeSettings.findMany({
        where: { storeId },
      });

      // Convert array to key-value object
      const settingsObject: IStoreSettings = {};
      storeSettings.forEach(setting => {
        settingsObject[setting.name] = setting.value;
      });

      return settingsObject;
    } catch (error) {
      console.error("Error fetching store settings:", error);
      throw new Error(`Failed to fetch store settings: ${(error as Error).message}`);
    }
  }

  /**
   * Get a specific setting by store ID and name
   */
  async getSetting(storeId: string, name: string): Promise<IStoreSetting | null> {
    try {
      const setting = await this.prisma.storeSettings.findUnique({
        where: { 
          storeId_name: {
            storeId,
            name
          }
        },
      });

      return setting;
    } catch (error) {
      console.error("Error fetching store setting:", error);
      throw new Error(`Failed to fetch store setting: ${(error as Error).message}`);
    }
  }

  /**
   * Create or update a single setting
   */
  async upsertSetting(data: CreateStoreSettingRequest): Promise<IStoreSetting> {
    try {
      // Validate data
      const { error, value } = createStoreSettingSchema.validate(data);
      if (error) {
        throw new Error(`Validation error: ${error.message}`);
      }



      // Create or update store setting
      const storeSetting = await this.prisma.storeSettings.upsert({
        where: { 
          storeId_name: {
            storeId: value.storeId,
            name: value.name
          }
        },
        create: {
          storeId: value.storeId,
          name: value.name,
          value: value.value,
        },
        update: {
          value: value.value,
        },
      });

      return storeSetting;
    } catch (error) {
      console.error("Error creating/updating store setting:", error);
      throw new Error(`Failed to save store setting: ${(error as Error).message}`);
    }
  }

  /**
   * Update multiple settings at once
   */
  async updateSettings(storeId: string, data: UpdateStoreSettingsRequest): Promise<IStoreSettings> {
    try {
      // Validate data
      const { error, value } = updateStoreSettingsSchema.validate(data);
      if (error) {
        throw new Error(`Validation error: ${error.message}`);
      }



      // Update settings using transactions
      await this.prisma.$transaction(async (tx) => {
        for (const [name, settingValue] of Object.entries(value.settings)) {
          await tx.storeSettings.upsert({
            where: { 
              storeId_name: {
                storeId,
                name
              }
            },
            create: {
              storeId,
              name,
              value: String(settingValue),
            },
            update: {
              value: String(settingValue),
            },
          });
        }
      });

      // Return updated settings
      return await this.getByStoreId(storeId);
    } catch (error) {
      console.error("Error updating store settings:", error);
      throw new Error(`Failed to update store settings: ${(error as Error).message}`);
    }
  }

  /**
   * Delete a specific setting
   */
  async deleteSetting(storeId: string, name: string): Promise<void> {
    try {
      await this.prisma.storeSettings.delete({
        where: { 
          storeId_name: {
            storeId,
            name
          }
        },
      });
    } catch (error) {
      console.error("Error deleting store setting:", error);
      throw new Error(`Failed to delete store setting: ${(error as Error).message}`);
    }
  }

  /**
   * Delete all settings for a store
   */
  async deleteAllSettings(storeId: string): Promise<void> {
    try {
      await this.prisma.storeSettings.deleteMany({
        where: { storeId },
      });
    } catch (error) {
      console.error("Error deleting store settings:", error);
      throw new Error(`Failed to delete store settings: ${(error as Error).message}`);
    }
  }
}

export default StoreSettingsService; 