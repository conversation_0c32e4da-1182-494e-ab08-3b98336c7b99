import { ServiceResponse } from "../constants/type";
import { StatusCodes } from "http-status-codes";
import PrismaService from "./prisma.service";
import { LinkedStore } from "../models/linked-store.model";

const prisma = PrismaService.getInstance().getClient();

class LinkedStoreService {
  /**
   * Get all linked stores for a user (all providers)
   * @param userId - User ID to get stores for
   * @returns Promise<ServiceResponse<LinkedStore[]>>
   */
  static getLinkedStoresByUserId = async (userId: string): Promise<ServiceResponse<LinkedStore[]>> => {
    try {
      console.log("🔍 [LinkedStoreService.getLinkedStoresByUserId] Querying for userId:", userId);

      const linkedStores = await prisma.linkedStore.findMany({
        where: {
          userId: userId,
          isActive: true,
        },
        orderBy: {
          createdAt: "desc",
        },
        select: {
          id: true,
          userId: true,
          storeName: true,
          provider: true,
          providerStoreId: true,
          data: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      console.log("✅ [LinkedStoreService.getLinkedStoresByUserId] Found stores:", linkedStores?.length || 0);

      return {
        success: true,
        status: StatusCodes.OK,
        message: "Get linked stores successfully",
        data: linkedStores,
      };
    } catch (error) {
      console.error("❌ [LinkedStoreService.getLinkedStoresByUserId] Error getting linked stores:", error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: "Failed to get linked stores",
      };
    }
  };

}

export default LinkedStoreService;