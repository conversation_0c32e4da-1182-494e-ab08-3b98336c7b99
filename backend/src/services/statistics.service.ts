import PrismaService from "@/services/prisma.service";
import { IStatCountry } from "@/models/stat-country.model";
import { IStatReason } from "@/models/stat-reason.model";
import { ServiceResponse } from "@/constants/type";
import { StatusCodes } from "http-status-codes";
import { getI18n } from "@/middlewares/language.middleware";
import logger from "@/utils/logger.util";

export interface ChargebackCountryStats {
  countryCode: string;
  countryName: string;
  count: number;
}

export interface ChargebackReasonStats {
  reasonCode: string;
  reasonName: string;
  count: number;
}

export interface ChargebackStatsOverview {
  countries: ChargebackCountryStats[];
  reasons: ChargebackReasonStats[];
}

export interface GetStatsParams {
  limit?: number;
  storeId?: string;
  provider?: string;
}

class StatisticsService {
  private prisma = PrismaService.getInstance().getClient();

  /**
   * Get top chargeback countries for a user
   */
  async getTopChargebackCountries(
    userId: string,
    params: GetStatsParams = {}
  ): Promise<ServiceResponse<ChargebackCountryStats[]>> {
    const i18n = getI18n();

    try {
      const { limit = 10, storeId, provider } = params;

      // Build where clause
      const where: any = {
        userId,
      };

      if (storeId) {
        where.linkedStoreId = storeId;
      }

      if (provider) {
        where.provider = provider;
      }

      // Query stat_country table
      const countryStats = await (this.prisma as any).statCountry.findMany({
        where,
        select: {
          countryCode: true,
          countryName: true,
          count: true,
        },
        orderBy: {
          count: "desc",
        },
        take: Math.min(limit, 50), // Cap at 50 for performance
      });

      // Group by country code and sum counts if multiple providers/stores
      const groupedStats = new Map<string, ChargebackCountryStats>();

      for (const stat of countryStats) {
        const key = stat.countryCode;
        if (groupedStats.has(key)) {
          const existing = groupedStats.get(key)!;
          existing.count += stat.count;
        } else {
          groupedStats.set(key, {
            countryCode: stat.countryCode,
            countryName: stat.countryName,
            count: stat.count,
          });
        }
      }

      // Convert to array and sort by count
      const result = Array.from(groupedStats.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, limit);

      logger.info(
        `Retrieved ${result.length} country stats for user ${userId}`
      );

      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t("statistics.countries.retrieved"),
        data: result,
      };
    } catch (error: any) {
      logger.error("Error getting top chargeback countries:", error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("statistics.countries.error"),
      };
    }
  }

  /**
   * Get top chargeback reasons for a user
   */
  async getTopChargebackReasons(
    userId: string,
    params: GetStatsParams = {}
  ): Promise<ServiceResponse<ChargebackReasonStats[]>> {
    const i18n = getI18n();

    try {
      const { limit = 10, storeId, provider } = params;

      // Build where clause
      const where: any = {
        userId,
      };

      if (storeId) {
        where.linkedStoreId = storeId;
      }

      if (provider) {
        where.provider = provider;
      }

      // Query stat_reason table
      const reasonStats = await (this.prisma as any).statReason.findMany({
        where,
        select: {
          reasonCode: true,
          reasonName: true,
          count: true,
        },
        orderBy: {
          count: "desc",
        },
        take: Math.min(limit, 50), // Cap at 50 for performance
      });

      // Group by reason code and sum counts if multiple providers/stores
      const groupedStats = new Map<string, ChargebackReasonStats>();

      for (const stat of reasonStats) {
        const key = stat.reasonCode;
        if (groupedStats.has(key)) {
          const existing = groupedStats.get(key)!;
          existing.count += stat.count;
        } else {
          groupedStats.set(key, {
            reasonCode: stat.reasonCode,
            reasonName: stat.reasonName,
            count: stat.count,
          });
        }
      }

      // Convert to array and sort by count
      const result = Array.from(groupedStats.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, limit);

      logger.info(`Retrieved ${result.length} reason stats for user ${userId}`);

      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t("statistics.reasons.retrieved"),
        data: result,
      };
    } catch (error: any) {
      logger.error("Error getting top chargeback reasons:", error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("statistics.reasons.error"),
      };
    }
  }

  /**
   * Get combined chargeback statistics overview
   */
  async getChargebackStatsOverview(
    userId: string,
    params: GetStatsParams = {}
  ): Promise<ServiceResponse<ChargebackStatsOverview>> {
    const i18n = getI18n();

    try {
      const { limit = 10 } = params;

      // Get both countries and reasons in parallel
      const [countriesResult, reasonsResult] = await Promise.all([
        this.getTopChargebackCountries(userId, { ...params, limit }),
        this.getTopChargebackReasons(userId, { ...params, limit }),
      ]);

      if (!countriesResult.success || !reasonsResult.success) {
        return {
          success: false,
          status: StatusCodes.INTERNAL_SERVER_ERROR,
          message: i18n.t("statistics.overview.error"),
        };
      }

      const result: ChargebackStatsOverview = {
        countries: countriesResult.data || [],
        reasons: reasonsResult.data || [],
      };

      logger.info(
        `Retrieved stats overview for user ${userId}: ${result.countries.length} countries, ${result.reasons.length} reasons`
      );

      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t("statistics.overview.retrieved"),
        data: result,
      };
    } catch (error: any) {
      logger.error("Error getting chargeback stats overview:", error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("statistics.overview.error"),
      };
    }
  }

  /**
   * Reset statistics for a user (for testing purposes)
   */
  async resetUserStatistics(
    userId: string,
    storeId?: string
  ): Promise<
    ServiceResponse<{ deletedCountries: number; deletedReasons: number }>
  > {
    const i18n = getI18n();

    try {
      const where: any = { userId };
      if (storeId) {
        where.linkedStoreId = storeId;
      }

      // Delete statistics in parallel
      const [deletedCountries, deletedReasons] = await Promise.all([
        (this.prisma as any).statCountry.deleteMany({ where }),
        (this.prisma as any).statReason.deleteMany({ where }),
      ]);

      const result = {
        deletedCountries: deletedCountries.count,
        deletedReasons: deletedReasons.count,
      };

      logger.info(
        `Reset statistics for user ${userId}: ${result.deletedCountries} countries, ${result.deletedReasons} reasons`
      );

      return {
        success: true,
        status: StatusCodes.OK,
        message: i18n.t("statistics.reset.success"),
        data: result,
      };
    } catch (error: any) {
      logger.error("Error resetting user statistics:", error);
      return {
        success: false,
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("statistics.reset.error"),
      };
    }
  }
}

export const statisticsService = new StatisticsService();
