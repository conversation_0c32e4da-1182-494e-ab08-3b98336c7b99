export const BILLING_PLANS = {
  PROFESSIONAL: {
    name: "Professional Plan",
    amount: 0, // Base amount in cents ($0.00)
    currency: "USD",
    interval: "EVERY_30_DAYS",
    cappedAmount: 800.00, // Usage-based billing cap in dollars
    cappedAmountCents: 80000, // Usage-based billing cap in cents
    features: [
      "Worldwide coverage",
      ">95% coverage percentage", 
      "Auto refund",
      "Mastercard/Visa network support"
    ],
    type: "USAGE_BASED"
  }
} as const;

export const BILLING_CONFIG = {
  isTestMode: process.env.NODE_ENV === 'development' || process.env.SHOPIFY_BILLING_TEST_MODE === 'true',
  testModeMessage: 'Test mode - no actual charges will be made'
} as const;

export const SUBSCRIPTION_STATUS = {
  PENDING: "PENDING",
  ACTIVE: "ACTIVE", 
  CANCELLED: "CANCELLED",
  EXPIRED: "EXPIRED",
  DECLINED: "DECLINED",
  FROZEN: "FROZEN"
} as const;

export const USAGE_RECORD_STATUS = {
  PENDING: "PENDING",
  BILLED: "BILLED", 
  FAILED: "FAILED"
} as const;

export const BILLING_INTERVALS = {
  EVERY_30_DAYS: "EVERY_30_DAYS",
  ANNUAL: "ANNUAL"
} as const;

export const PLAN_TYPES = {
  TIME_BASED: "TIME_BASED",
  USAGE_BASED: "USAGE_BASED", 
  HYBRID: "HYBRID"
} as const;

// Pricing per block/usage
export const USAGE_PRICING = {
  BLOCK_PROCESSING: {
    price: 100, // $1.00 per block in cents
    description: "Block processing fee"
  }
} as const;

// Helper functions
export const formatCurrency = (amountInCents: number): string => {
  return `$${(amountInCents / 100).toFixed(2)}`;
};

export const convertDollarsToCents = (dollars: number): number => {
  return Math.round(dollars * 100);
};

export const convertCentsToDollars = (cents: number): number => {
  return cents / 100;
}; 