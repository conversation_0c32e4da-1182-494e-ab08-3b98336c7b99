export enum RoleType {
  ADMIN = "admin",
  USER = "user",
}

export enum DataType {
  RAW = "raw",
  REFINED = "refined",
}

export enum RequestType {
  FULL = "full",
  SHORT = "short",
  NORMAL = "normal",
}

export enum AuthActionType {
  LOGIN = "login",
  REGISTER = "register",
}

export enum EntityType {
  SYSTEM = "SYSTEM",
  USER = "USER",
  PAYMENT = "PAYMENT",
  PREDICTION = "PREDICTION",
}

export enum PaymentStatusType {
  PENDING = "pending",
  COMPLETED = "completed",
  FAILED = "failed",
  REFUNDED = "refunded",
  CANCELLED = "cancelled",
}

export interface ServiceResponse<T> {
  success: boolean;
  message: string;
  status?: number;
  data?: T;
}
