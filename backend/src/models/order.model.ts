import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

// Define Order type from prisma
export interface IOrder {
  id: string;
  storeId: string;
  orderId: string;
  customerName?: string | null;
  customerEmail?: string | null;
  amount: number;
  currency: string;
  status: string;
  items?: any;
  shippingAddress?: any;
  paymentMethod?: string | null;
  paymentDetails?: any;
  metadata?: any;
  createdAt: Date;
  updatedAt: Date;
}

// Export for use in other files
export interface OrderModel extends IOrder {}

// Query parameters for retrieving orders
export interface OrderQuery {
  id?: string;
  storeId?: string;
  orderId?: string;
  status?: string;
  customerEmail?: string;
}

// Data for creating a new order
export interface CreateOrder {
  storeId: string;
  orderId: string;
  customerName?: string | null;
  customerEmail?: string | null;
  amount: number;
  currency: string;
  status: string;
  items?: any;
  shippingAddress?: any;
  paymentMethod?: string | null;
  paymentDetails?: any;
  metadata?: any;
}

// Data for updating an order
export interface UpdateOrder {
  customerName?: string | null;
  customerEmail?: string | null;
  amount?: number;
  currency?: string;
  status?: string;
  items?: any;
  shippingAddress?: any;
  paymentMethod?: string | null;
  paymentDetails?: any;
  metadata?: any;
}

// Define validation schema for Order
export const orderSchema = Joi.object({
  storeId: Joi.string().required(),
  orderId: Joi.string().required(),
  customerName: Joi.string().allow(null),
  customerEmail: Joi.string().email().allow(null),
  amount: Joi.number().required(),
  currency: Joi.string().required(),
  status: Joi.string().required(),
  items: Joi.object(),
  shippingAddress: Joi.object(),
  paymentMethod: Joi.string().allow(null),
  paymentDetails: Joi.object(),
  metadata: Joi.object()
});

export const updateOrderSchema = Joi.object({
  customerName: Joi.string().allow(null),
  customerEmail: Joi.string().email().allow(null),
  amount: Joi.number(),
  currency: Joi.string(),
  status: Joi.string(),
  items: Joi.object(),
  shippingAddress: Joi.object(),
  paymentMethod: Joi.string().allow(null),
  paymentDetails: Joi.object(),
  metadata: Joi.object()
});
