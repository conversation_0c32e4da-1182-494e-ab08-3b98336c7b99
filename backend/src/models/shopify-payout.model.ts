import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

// Define ShopifyPayout type from prisma
export interface IShopifyPayout {
  id: bigint;
  linkedStoreId: string;
  date?: Date | null;
  amount?: number | null;
  status?: string | null;
  summary?: any;
  currency?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// Export for use in other files
export interface ShopifyPayoutModel extends IShopifyPayout {}

// Query parameters for retrieving shopify payouts
export interface ShopifyPayoutQuery {
  id?: bigint;
  linkedStoreId?: string;
  status?: string;
  currency?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

// Data for creating a new shopify payout
export interface CreateShopifyPayout {
  id: bigint;
  linkedStoreId: string;
  date?: Date | null;
  amount?: number | null;
  status?: string | null;
  summary?: any;
  currency?: string | null;
}

// Validation schema for creating shopify payout
export const createShopifyPayoutSchema = Joi.object({
  id: Joi.number().required(),
  linkedStoreId: Joi.string().required(),
  date: Joi.date(),
  amount: Joi.number(),
  status: Joi.string(),
  summary: Joi.object(),
  currency: Joi.string()
});
