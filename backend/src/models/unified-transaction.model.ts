import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

/**
 * Unified Transaction Model
 * Represents transaction data from all providers (Shopify, Stripe) in a unified format
 * Optimized for rate calculations and multi-provider aggregation
 */

// Base unified transaction interface
export interface IUnifiedTransaction {
  id: string;
  userId: string;
  linkedStoreId: string;
  provider: string; // 'shopify' or 'stripe'
  
  // Core transaction data
  providerTransactionId: string; // Original ID from provider
  amount: bigint; // Amount in cents
  currency: string;
  status: string;
  
  // Time tracking
  processedAt: Date; // When transaction was processed (key for chargeback rate calculations)
  createdAt: Date;
  updatedAt: Date;
}

// Joi validation schema
export const unifiedTransactionSchema = Joi.object({
  id: Joi.string().required(),
  userId: Joi.string().required(),
  linkedStoreId: Joi.string().required(),
  provider: Joi.string().valid('shopify', 'stripe').required(),
  
  // Core transaction data
  providerTransactionId: Joi.string().required(),
  amount: Joi.number().integer().min(0).required(),
  currency: Joi.string().length(3).required(),
  status: Joi.string().required(),
  
  // Time tracking
  processedAt: Joi.date().required(),
  createdAt: Joi.date().required(),
  updatedAt: Joi.date().required()
});


// Export prisma client for use in services  
export const prisma = new PrismaClient();

export default IUnifiedTransaction;