import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

// Define role type from prisma
export interface IRole {
  id: string;
  code: string;
  name: string;
  description?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// Define validation schema for role
export const roleSchema = Joi.object({
  code: Joi.string().required(),
  name: Joi.string().required(),
  description: Joi.string().allow("", null),
});

// Export prisma client for use in services
export const prisma = new PrismaClient().$extends({
  model: {
    role: {
      // Add any custom methods here
    },
  },
});

export default IRole;
