import { Prisma, PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";
import { AlertType } from "./alert.model";

// Define feedback status enum
export enum FeedbackStatus {
  PENDING = "PENDING",
  SENT = "SENT",
  FAILED = "FAILED",
}

// Define outcome enum từ hình ảnh được cung cấp
export enum FeedbackOutcome {
  STOPPED = "stopped",
  PARTIALLY_STOPPED = "partially_stopped",
  PREVIOUSLY_CANCELLED = "previously_cancelled",
  MISSED = "missed",
  NOTFOUND = "notfound",
  ACCOUNT_SUSPENDED = "account_suspended",
  SHIPPER_CONTACTED = "shipper_contacted",
  OTHER = "other",
}

// Define refunded enum
export enum RefundedStatus {
  REFUNDED = "refunded",
  NOT_REFUNDED = "not_refunded",
}

// Define alert feedback interface
export interface IAlertFeedback {
  id: string;
  predictorId: string; // <PERSON><PERSON><PERSON> kết với id của Alert

  // Các trường dữ liệu từ hình ảnh Ethoca Feedback
  outcome: FeedbackOutcome;
  refunded: RefundedStatus;
  comments?: string | null;
  isFraud?: string | null;
  matchOrderNo?: string | null;
  refundNo?: string | null;
  refundDate?: string | null;
  refundAmount?: string | null;
  refundCurrency?: string | null;

  // Các trường dữ liệu bổ sung
  data?: Record<string, any> | null;
  errorData?: Record<string, any> | null;

  createdAt: Date;
  updatedAt: Date;
}

// Define validation schema for AlertFeedback
export const alertFeedbackSchema = Joi.object({
  id: Joi.string(),
  predictorId: Joi.string().required(), // Liên kết với id của Alert

  outcome: Joi.string()
    .valid(
      FeedbackOutcome.STOPPED,
      FeedbackOutcome.PARTIALLY_STOPPED,
      FeedbackOutcome.PREVIOUSLY_CANCELLED,
      FeedbackOutcome.MISSED,
      FeedbackOutcome.NOTFOUND,
      FeedbackOutcome.ACCOUNT_SUSPENDED,
      FeedbackOutcome.SHIPPER_CONTACTED,
      FeedbackOutcome.OTHER
    )
    .required(),
  refunded: Joi.string()
    .valid(RefundedStatus.REFUNDED, RefundedStatus.NOT_REFUNDED)
    .required(),
  comments: Joi.string().allow(null, ""),
  isFraud: Joi.string().allow(null, ""),
  matchOrderNo: Joi.string().allow(null, ""),
  refundNo: Joi.string().allow(null, ""),
  refundDate: Joi.string().allow(null, ""),
  refundAmount: Joi.string().allow(null, ""),
  refundCurrency: Joi.string().allow(null, ""),

  data: Joi.object().allow(null),
  errorData: Joi.object().allow(null),
});

// Export prisma client for use in services
export const prisma = new PrismaClient().$extends({
  model: {
    alertFeedback: {
      // Add any custom methods here
    },
  },
});

export default IAlertFeedback;
