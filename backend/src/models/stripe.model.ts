import { StripeAccount, LinkedStore, User } from '@prisma/client';

// Stripe Account Models
export interface StripeAccountModel extends StripeAccount {
  store?: LinkedStore & {
    user?: User;
  };
}

export interface CreateStripeAccount {
  storeId: string;
  stripeAccountId: string | null;
  status?: string;
}

export interface UpdateStripeAccount {
  stripeAccountId?: string;
  status?: string;
  stripeCustomerId?: string;
  paymentMethodId?: string;
  cardLast4?: string;
  cardBrand?: string;
  cardCountry?: string;
  cardholderName?: string;
  billingEmail?: string;
  setupCompleted?: boolean;
}

export interface StripeAccountQuery {
  id?: string;
  storeId?: string;
  stripeAccountId?: string;
  status?: string;
}

// Stripe Connect Account Creation
export interface StripeAccountCreationOptions {
  businessName?: string;
  email?: string;
  country?: string;
  businessType?: 'individual' | 'company';
}

// Webhook Events
export interface StripeWebhookEvent {
  id: string;
  type: string;
  data: {
    object: any;
  };
}