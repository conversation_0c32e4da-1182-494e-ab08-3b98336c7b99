import { PrismaClient } from "@prisma/client";
import Joi from "joi";

// Define ShopifyTransaction type from prisma
export interface IShopifyTransaction {
  id: bigint;
  linkedStoreId: string;
  orderId?: bigint | null;
  payoutId?: bigint | null;
  fee?: number | null;
  net?: number | null;
  test?: boolean | null;
  type?: string | null;
  amount?: number | null;
  amountRounding?: number | null;
  authorization?: string | null;
  authorizationExpiresAt?: Date | null;
  currency?: string | null;
  deviceId?: bigint | null;
  errorCode?: string | null;
  extendedAuthorizationAttributes?: any | null;
  gateway?: string | null;
  kind?: string | null;
  locationId?: any | null;
  message?: string | null;
  parentId?: bigint | null;
  paymentDetails?: any | null;
  paymentsRefundAttributes?: any | null;
  processedAt?: Date | null;
  receipt?: any | null;
  sourceName?: string | null;
  status?: string | null;
  totalUnsettledSet?: any | null;
  userId?: bigint | null;
  currencyExchangeAdjustment?: any | null;
  manualPaymentGateway?: boolean | null;
  sourceId?: bigint | null;
  sourceType?: string | null;
  payoutStatus?: string | null;
  sourceOrderId?: bigint | null;
  adjustmentReason?: string | null;
  sourceOrderTransactionId?: bigint | null;
  adjustmentOrderTransactions?: any | null;
  referenceCardNumber?: string | null;
  referenceAmount?: number | null;
  referenceTransactionTime?: Date | null;
  referenceCurrency?: string | null;
  referenceArn?: string | null;
  referenceAuthorizationCode?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// Export for use in other files
export interface ShopifyTransactionModel extends IShopifyTransaction {}

// Query parameters for retrieving shopify transactions
export interface ShopifyTransactionQuery {
  id?: bigint;
  linkedStoreId?: string;
  orderId?: bigint;
  payoutId?: bigint;
  type?: string;
  payoutStatus?: string;
  sourceOrderId?: bigint;
}

// Data for creating a new shopify transaction
export interface CreateShopifyTransaction {
  id: bigint;
  linkedStoreId: string;
  orderId?: bigint | null;
  payoutId?: bigint | null;
  fee?: number | null;
  net?: number | null;
  test?: boolean | null;
  type?: string | null;
  amount?: number | null;
  amountRounding?: number | null;
  authorization?: string | null;
  authorizationExpiresAt?: Date | null;
  currency?: string | null;
  deviceId?: bigint | null;
  errorCode?: string | null;
  extendedAuthorizationAttributes?: any | null;
  gateway?: string | null;
  kind?: string | null;
  locationId?: any | null;
  message?: string | null;
  parentId?: bigint | null;
  paymentDetails?: any | null;
  paymentsRefundAttributes?: any | null;
  processedAt?: Date | null;
  receipt?: any | null;
  sourceName?: string | null;
  status?: string | null;
  totalUnsettledSet?: any | null;
  userId?: bigint | null;
  currencyExchangeAdjustment?: any | null;
  manualPaymentGateway?: boolean | null;
  sourceId?: bigint | null;
  sourceType?: string | null;
  payoutStatus?: string | null;
  sourceOrderId?: bigint | null;
  adjustmentReason?: string | null;
  sourceOrderTransactionId?: bigint | null;
  adjustmentOrderTransactions?: any | null;
  referenceCardNumber?: string | null;
  referenceAmount?: number | null;
  referenceTransactionTime?: Date | null;
  referenceCurrency?: string | null;
  referenceArn?: string | null;
  referenceAuthorizationCode?: string | null;
}

// Validation schema for creating shopify transaction
export const createShopifyTransactionSchema = Joi.object({
  id: Joi.number().required(),
  linkedStoreId: Joi.string().required(),
  orderId: Joi.number(),
  payoutId: Joi.number(),
  fee: Joi.number(),
  net: Joi.number(),
  test: Joi.boolean(),
  type: Joi.string(),
  amount: Joi.number(),
  amountRounding: Joi.number(),
  authorization: Joi.string(),
  authorizationExpiresAt: Joi.date(),
  currency: Joi.string(),
  deviceId: Joi.number(),
  errorCode: Joi.string(),
  extendedAuthorizationAttributes: Joi.object(),
  gateway: Joi.string(),
  kind: Joi.string(),
  locationId: Joi.object(),
  message: Joi.string(),
  parentId: Joi.number(),
  paymentDetails: Joi.object(),
  paymentsRefundAttributes: Joi.object(),
  processedAt: Joi.date(),
  receipt: Joi.object(),
  sourceName: Joi.string(),
  status: Joi.string(),
  totalUnsettledSet: Joi.object(),
  userId: Joi.number(),
  currencyExchangeAdjustment: Joi.object(),
  manualPaymentGateway: Joi.boolean(),
  sourceId: Joi.number(),
  sourceType: Joi.string(),
  sourceOrderId: Joi.number(),
  payoutStatus: Joi.string(),
  adjustmentReason: Joi.string(),
  sourceOrderTransactionId: Joi.number(),
  adjustmentOrderTransactions: Joi.object(),
  referenceCardNumber: Joi.string(),
  referenceAmount: Joi.number(),
  referenceTransactionTime: Joi.date(),
  referenceCurrency: Joi.string(),
  referenceArn: Joi.string(),
  referenceAuthorizationCode: Joi.string()
});
