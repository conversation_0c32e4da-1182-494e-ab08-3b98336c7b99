import { PaymentHistory } from '@prisma/client';

// Payment History Models
export interface PaymentHistoryModel extends PaymentHistory {}

export interface CreatePaymentHistory {
  linkedStoreId: string;
  billId: string;
  amount: number;
  currency: string;
  description?: string;
  status: 'SUCCEEDED' | 'FAILED' | 'PENDING' | 'REFUNDED';
  paymentDate: Date;
}

export interface CreatePaymentHistoryRequest {
  linkedStoreId?: string;
  billId: string;
  amount: number;
  currency: string;
  description?: string;
  status: string;
  paymentDate: Date;
}

export interface UpdatePaymentHistory {
  status?: string;
  paymentMethodType?: string;
  paymentMethodDetails?: string;
  customerEmail?: string;
}

export interface PaymentHistoryQuery {
  id?: string;
  billId?: string;
  stripeAccountId?: string;
  status?: string;
  type?: string;
}