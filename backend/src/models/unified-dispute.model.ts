import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

/**
 * Unified Dispute Model
 * Represents dispute data from all providers (Shopify, Stripe) in a unified format
 * Optimized for rate calculations and multi-provider aggregation
 */

// Keep only essential constants
export const DISPUTE_TYPE_CHARGEBACK = 'chargeback';

// Base unified dispute interface
export interface IUnifiedDispute {
  id: string;
  userId: string;
  linkedStoreId: string;
  provider: string; // 'shopify' or 'stripe'
  
  // Core dispute data
  providerDisputeId: string; // Original ID from provider
  unifiedTransactionId?: string | null; // Optional reference to unified transaction
  amount: bigint; // Dispute amount in cents
  currency: string;
  type: string;
  status: string;
  reason?: string | null;
  
  // Time tracking
  initiatedAt?: Date | null; // When dispute was initiated (key for chargeback rate calculations)
  createdAt: Date;
  updatedAt: Date;
}

// Joi validation schema
export const unifiedDisputeSchema = Joi.object({
  id: Joi.string().required(),
  userId: Joi.string().required(),
  linkedStoreId: Joi.string().required(),
  provider: Joi.string().valid('shopify', 'stripe').required(),
  
  // Core dispute data
  providerDisputeId: Joi.string().required(),
  unifiedTransactionId: Joi.string().allow(null, ""),
  amount: Joi.number().integer().min(0).required(),
  currency: Joi.string().length(3).required(),
  type: Joi.string().required(),
  status: Joi.string().required(),
  reason: Joi.string().allow(null, ""),
  
  // Time tracking
  initiatedAt: Joi.date().allow(null),
  createdAt: Joi.date().required(),
  updatedAt: Joi.date().required()
});


// Export prisma client for use in services
export const prisma = new PrismaClient();

export default IUnifiedDispute;