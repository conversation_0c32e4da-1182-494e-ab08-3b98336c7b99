import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

// Define Address interface
export interface Address {
  address1?: string;
  address2?: string;
  city?: string;
  company?: string | null;
  country?: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  province?: string;
  zip?: string;
  name?: string;
  province_code?: string;
  country_code?: string;
  latitude?: string;
  longitude?: string;
}

// Define ClientDetails interface
export interface ClientDetails {
  accept_language?: string;
  browser_height?: number;
  browser_ip?: string;
  browser_width?: number;
  session_hash?: string;
  user_agent?: string;
}

// Define Company interface
export interface Company {
  id?: number;
  location_id?: number;
}

// Define Customer interface
export interface Customer {
  id?: number;
  email?: string;
  created_at?: string;
  updated_at?: string;
  first_name?: string;
  last_name?: string;
  state?: string;
  note?: string | null;
  verified_email?: boolean;
  multipass_identifier?: string | null;
  tax_exempt?: boolean;
  tax_exemptions?: any;
  phone?: string;
  tags?: string;
  currency?: string;
  addresses?: any;
  admin_graphql_api_id?: string;
  default_address?: any;
}

// Define MoneySet interface
export interface MoneySet {
  shop_money?: {
    amount?: string;
    currency_code?: string;
  };
  presentment_money?: {
    amount?: string;
    currency_code?: string;
  };
}

// Define NoteAttribute interface
export interface NoteAttribute {
  name?: string;
  value?: string;
}

// Define DiscountApplication interface
export interface DiscountApplication {
  type?: string;
  title?: string;
  description?: string;
  value?: string;
  value_type?: string;
  allocation_method?: string;
  target_selection?: string;
  target_type?: string;
  code?: string;
}

// Define DiscountCode interface
export interface DiscountCode {
  code?: string;
  amount?: string;
  type?: string;
}

// Define PaymentTerms interface
export interface PaymentTerms {
  amount?: number;
  currency?: string;
  payment_terms_name?: string;
  payment_terms_type?: string;
  due_in_days?: number;
  payment_schedules?: {
    amount?: number;
    currency?: string;
    issued_at?: string;
    due_at?: string;
    completed_at?: string | null;
    expected_payment_method?: string;
  }[];
}

// Define AttributedStaff interface
export interface AttributedStaff {
  id?: string;
  quantity?: number;
}

// Define Property interface
export interface Property {
  name?: string;
  value?: string;
}

// Define TaxLine interface
export interface TaxLine {
  title?: string;
  price?: string;
  price_set?: MoneySet;
  channel_liable?: boolean;
  rate?: number;
}

// Define Duty interface
export interface Duty {
  id?: string;
  harmonized_system_code?: string;
  country_code_of_origin?: string;
  shop_money?: {
    amount?: string;
    currency_code?: string;
  };
  presentment_money?: {
    amount?: string;
    currency_code?: string;
  };
  tax_lines?: TaxLine[];
  admin_graphql_api_id?: string;
}

// Define OriginLocation interface
export interface OriginLocation {
  id?: number;
  country_code?: string;
  province_code?: string;
  name?: string;
  address1?: string;
  address2?: string;
  city?: string;
  zip?: string;
}

// Define LineItem interface
export interface LineItem {
  attributed_staffs?: AttributedStaff[];
  fulfillable_quantity?: number;
  fulfillment_service?: string;
  fulfillment_status?: string;
  grams?: number;
  id?: number;
  price?: string;
  product_id?: number;
  quantity?: number;
  current_quantity?: number;
  requires_shipping?: boolean;
  sku?: string;
  title?: string;
  variant_id?: number;
  variant_title?: string;
  vendor?: string;
  name?: string;
  gift_card?: boolean;
  price_set?: MoneySet;
  properties?: Property[];
  taxable?: boolean;
  tax_lines?: TaxLine[];
  total_discount?: string;
  total_discount_set?: MoneySet;
  discount_allocations?: {
    amount?: string;
    discount_application_index?: number;
    amount_set?: MoneySet;
  }[];
  origin_location?: OriginLocation;
  duties?: Duty[];
}

// Define Fulfillment interface
export interface Fulfillment {
  created_at?: string;
  id?: number;
  order_id?: number;
  status?: string;
  tracking_company?: string;
  tracking_number?: string;
  updated_at?: string;
}

// Define ShippingLine interface
export interface ShippingLine {
  code?: string;
  price?: string;
  price_set?: MoneySet;
  discounted_price?: string;
  discounted_price_set?: MoneySet;
  source?: string;
  title?: string;
  tax_lines?: TaxLine[];
  carrier_identifier?: string;
  requested_fulfillment_service_id?: string;
  is_removed?: boolean;
}

// Define Refund interface
export interface Refund {
  id?: number;
  order_id?: number;
  created_at?: string;
  note?: string | null;
  user_id?: number | null;
  processed_at?: string;
  refund_line_items?: any[];
  transactions?: any[];
  order_adjustments?: any[];
}

// Define ShopifyOrder type from prisma
export interface IShopifyOrder {
  id: bigint;
  linkedStoreId: string;
  name?: string | null;
  note?: string | null;
  tags?: string | null;
  test?: boolean | null;
  email?: string | null;
  phone?: string | null;
  token?: string | null;
  appId?: number | null;
  number?: number | null;
  orderNumber?: number | null;
  currency?: string | null;
  customerData?: any;
  customer?: Customer | null;
  closedAt?: Date | null;
  confirmed?: boolean | null;
  confirmationNumber?: string | null;
  deviceId?: string | null;
  poNumber?: string | null;
  reference?: string | null;
  cancelReason?: string | null;
  cancelledAt?: Date | null;
  taxData?: any;
  taxLines?: TaxLine[] | null;
  totalTax?: number | null;
  totalTaxSet?: MoneySet | null;
  browserIp?: string | null;
  cartToken?: string | null;
  checkoutToken?: string | null;
  clientDetails?: ClientDetails | null;
  company?: Company | null;
  createdAt?: Date | null;
  updatedAt?: Date | null;
  processedAt?: Date | null;
  lineItems?: LineItem[] | null;
  discountApplications?: DiscountApplication[] | null;
  discountCodes?: DiscountCode[] | null;
  sourceUrl?: string | null;
  sourceIdentifier?: string | null;
  sourceName?: string | null;
  taxExempt?: boolean | null;
  checkoutId?: bigint | null;
  locationId?: bigint | null;
  merchantBusinessEntityId?: string | null;
  merchantOfRecordAppId?: bigint | null;
  totalPrice?: number | null;
  totalPriceSet?: MoneySet | null;
  currentTotalPrice?: number | null;
  currentTotalPriceSet?: MoneySet | null;
  subtotalPrice?: number | null;
  subtotalPriceSet?: MoneySet | null;
  currentSubtotalPrice?: number | null;
  currentSubtotalPriceSet?: MoneySet | null;
  totalDiscounts?: number | null;
  totalDiscountsSet?: MoneySet | null;
  currentTotalDiscounts?: number | null;
  currentTotalDiscountsSet?: MoneySet | null;
  totalLineItemsPrice?: number | null;
  totalLineItemsPriceSet?: MoneySet | null;
  totalShippingPriceSet?: MoneySet | null;
  totalOutstanding?: number | null;
  totalTipReceived?: number | null;
  totalWeight?: number | null;
  fulfillments?: Fulfillment[] | null;
  fulfillmentStatus?: string | null;
  landingSite?: string | null;
  landingSiteRef?: string | null;
  referringSite?: string | null;
  contactEmail?: string | null;
  paymentTerms?: PaymentTerms | null;
  paymentGatewayNames?: string[] | null;
  shippingLines?: ShippingLine[] | null;
  taxesIncluded?: boolean | null;
  billingAddress?: Address | null;
  shippingAddress?: Address | null;
  customerLocale?: string | null;
  buyerAcceptsMarketing?: boolean | null;
  refunds?: Refund[] | null;
  dutiesIncluded?: boolean | null;
  currentTotalDutiesSet?: MoneySet | null;
  originalTotalDutiesSet?: MoneySet | null;
  estimatedTaxes?: boolean | null;
  noteAttributes?: NoteAttribute[] | null;
  financialStatus?: string | null;
  orderStatusUrl?: { order_status_url?: string } | null;
  currentTotalAdditionalFeesSet?: MoneySet | null;
  originalTotalAdditionalFeesSet?: MoneySet | null;
  totalCashRoundingPaymentAdjustmentSet?: MoneySet | null;
  totalCashRoundingRefundAdjustmentSet?: MoneySet | null;
  userId?: bigint | null;
  systemCreatedAt: Date;
  systemUpdatedAt: Date;
}

// Export for use in other files
export interface ShopifyOrderModel extends IShopifyOrder {}

// Query parameters for retrieving shopify orders
export interface ShopifyOrderQuery {
  id?: bigint;
  linkedStoreId?: string;
  orderNumber?: number;
  financialStatus?: string;
  email?: string;
}

// Data for creating a new shopify order
export interface CreateShopifyOrder {
  id: bigint;
  linkedStoreId: string;
  name?: string | null;
  note?: string | null;
  tags?: string | null;
  test?: boolean | null;
  email?: string | null;
  phone?: string | null;
  token?: string | null;
  appId?: number | null;
  number?: number | null;
  orderNumber?: number | null;
  currency?: string | null;
  customerData?: any;
  customer?: Customer | null;
  closedAt?: Date | null;
  confirmed?: boolean | null;
  confirmationNumber?: string | null;
  deviceId?: string | null;
  poNumber?: string | null;
  reference?: string | null;
  cancelReason?: string | null;
  cancelledAt?: Date | null;
  taxData?: any;
  taxLines?: TaxLine[] | null;
  totalTax?: number | null;
  totalTaxSet?: MoneySet | null;
  browserIp?: string | null;
  cartToken?: string | null;
  checkoutToken?: string | null;
  clientDetails?: ClientDetails | null;
  company?: Company | null;
  createdAt?: Date | null;
  updatedAt?: Date | null;
  processedAt?: Date | null;
  lineItems?: LineItem[] | null;
  discountApplications?: DiscountApplication[] | null;
  discountCodes?: DiscountCode[] | null;
  sourceUrl?: string | null;
  sourceIdentifier?: string | null;
  sourceName?: string | null;
  taxExempt?: boolean | null;
  checkoutId?: bigint | null;
  locationId?: bigint | null;
  merchantBusinessEntityId?: string | null;
  merchantOfRecordAppId?: bigint | null;
  totalPrice?: number | null;
  totalPriceSet?: MoneySet | null;
  currentTotalPrice?: number | null;
  currentTotalPriceSet?: MoneySet | null;
  subtotalPrice?: number | null;
  subtotalPriceSet?: MoneySet | null;
  currentSubtotalPrice?: number | null;
  currentSubtotalPriceSet?: MoneySet | null;
  totalDiscounts?: number | null;
  totalDiscountsSet?: MoneySet | null;
  currentTotalDiscounts?: number | null;
  currentTotalDiscountsSet?: MoneySet | null;
  totalLineItemsPrice?: number | null;
  totalLineItemsPriceSet?: MoneySet | null;
  totalShippingPriceSet?: MoneySet | null;
  totalOutstanding?: number | null;
  totalTipReceived?: number | null;
  totalWeight?: number | null;
  fulfillments?: Fulfillment[] | null;
  fulfillmentStatus?: string | null;
  landingSite?: string | null;
  landingSiteRef?: string | null;
  referringSite?: string | null;
  contactEmail?: string | null;
  paymentTerms?: PaymentTerms | null;
  paymentGatewayNames?: string[] | null;
  shippingLines?: ShippingLine[] | null;
  taxesIncluded?: boolean | null;
  billingAddress?: Address | null;
  shippingAddress?: Address | null;
  customerLocale?: string | null;
  buyerAcceptsMarketing?: boolean | null;
  refunds?: Refund[] | null;
  dutiesIncluded?: boolean | null;
  currentTotalDutiesSet?: MoneySet | null;
  originalTotalDutiesSet?: MoneySet | null;
  estimatedTaxes?: boolean | null;
  noteAttributes?: NoteAttribute[] | null;
  financialStatus?: string | null;
  orderStatusUrl?: { order_status_url?: string } | null;
  currentTotalAdditionalFeesSet?: MoneySet | null;
  originalTotalAdditionalFeesSet?: MoneySet | null;
  totalCashRoundingPaymentAdjustmentSet?: MoneySet | null;
  totalCashRoundingRefundAdjustmentSet?: MoneySet | null;
  userId?: bigint | null;
}

// Validation schema for creating shopify order
export const createShopifyOrderSchema = Joi.object({
  id: Joi.number().required(),
  linkedStoreId: Joi.string().required(),
  name: Joi.string().allow(null),
  note: Joi.string().allow(null),
  tags: Joi.string().allow(null),
  test: Joi.boolean().allow(null),
  email: Joi.string().email().allow(null),
  phone: Joi.string().allow(null),
  token: Joi.string().allow(null),
  appId: Joi.number().allow(null),
  number: Joi.number().allow(null),
  orderNumber: Joi.number().allow(null),
  currency: Joi.string().allow(null),
  customerData: Joi.any(),
  customer: Joi.object().allow(null),
  closedAt: Joi.date().allow(null),
  confirmed: Joi.boolean().allow(null),
  confirmationNumber: Joi.string().allow(null),
  deviceId: Joi.string().allow(null),
  poNumber: Joi.string().allow(null),
  reference: Joi.string().allow(null),
  cancelReason: Joi.string().allow(null),
  cancelledAt: Joi.date().allow(null),
  taxData: Joi.any(),
  taxLines: Joi.array().allow(null),
  totalTax: Joi.number().allow(null),
  totalTaxSet: Joi.object().allow(null),
  browserIp: Joi.string().allow(null),
  cartToken: Joi.string().allow(null),
  checkoutToken: Joi.string().allow(null),
  clientDetails: Joi.object().allow(null),
  company: Joi.object().allow(null),
  createdAt: Joi.date().allow(null),
  updatedAt: Joi.date().allow(null),
  processedAt: Joi.date().allow(null),
  lineItems: Joi.array().allow(null),
  discountApplications: Joi.array().allow(null),
  discountCodes: Joi.array().allow(null),
  sourceUrl: Joi.string().allow(null),
  sourceIdentifier: Joi.string().allow(null),
  sourceName: Joi.string().allow(null),
  taxExempt: Joi.boolean().allow(null),
  checkoutId: Joi.number().allow(null),
  locationId: Joi.number().allow(null),
  merchantBusinessEntityId: Joi.string().allow(null),
  merchantOfRecordAppId: Joi.number().allow(null),
  totalPrice: Joi.number().allow(null),
  totalPriceSet: Joi.object().allow(null),
  currentTotalPrice: Joi.number().allow(null),
  currentTotalPriceSet: Joi.object().allow(null),
  subtotalPrice: Joi.number().allow(null),
  subtotalPriceSet: Joi.object().allow(null),
  currentSubtotalPrice: Joi.number().allow(null),
  currentSubtotalPriceSet: Joi.object().allow(null),
  totalDiscounts: Joi.number().allow(null),
  totalDiscountsSet: Joi.object().allow(null),
  currentTotalDiscounts: Joi.number().allow(null),
  currentTotalDiscountsSet: Joi.object().allow(null),
  totalLineItemsPrice: Joi.number().allow(null),
  totalLineItemsPriceSet: Joi.object().allow(null),
  totalShippingPriceSet: Joi.object().allow(null),
  totalOutstanding: Joi.number().allow(null),
  totalTipReceived: Joi.number().allow(null),
  totalWeight: Joi.number().allow(null),
  fulfillments: Joi.array().allow(null),
  fulfillmentStatus: Joi.string().allow(null),
  landingSite: Joi.string().allow(null),
  landingSiteRef: Joi.string().allow(null),
  referringSite: Joi.string().allow(null),
  contactEmail: Joi.string().allow(null),
  paymentTerms: Joi.object().allow(null),
  paymentGatewayNames: Joi.array().allow(null),
  shippingLines: Joi.array().allow(null),
  taxesIncluded: Joi.boolean().allow(null),
  billingAddress: Joi.object().allow(null),
  shippingAddress: Joi.object().allow(null),
  customerLocale: Joi.string().allow(null),
  buyerAcceptsMarketing: Joi.boolean().allow(null),
  refunds: Joi.array().allow(null),
  dutiesIncluded: Joi.boolean().allow(null),
  currentTotalDutiesSet: Joi.object().allow(null),
  originalTotalDutiesSet: Joi.object().allow(null),
  estimatedTaxes: Joi.boolean().allow(null),
  noteAttributes: Joi.array().allow(null),
  financialStatus: Joi.string().allow(null),
  orderStatusUrl: Joi.object().allow(null),
  currentTotalAdditionalFeesSet: Joi.object().allow(null),
  originalTotalAdditionalFeesSet: Joi.object().allow(null),
  totalCashRoundingPaymentAdjustmentSet: Joi.object().allow(null),
  totalCashRoundingRefundAdjustmentSet: Joi.object().allow(null),
  userId: Joi.number().allow(null)
});
