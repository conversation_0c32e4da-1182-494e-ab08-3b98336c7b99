import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

// Define user type from prisma
export interface IUser {
  id: string;
  code: string;
  fullName: string;
  phoneNumber: string;
  email: string;
  password: string;
  address?: string | null;
  gender: string;
  birthDate: Date;
  status: boolean;
  note?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// For authenticated user information
export interface AuthenticatedUser {
  id: string;
  code: string;
  email: string;
  phoneNumber: string;
  roles: string[];
}

// Define validation schema for user
export const userSchema = Joi.object({
  fullName: Joi.string().required(),
  phoneNumber: Joi.string().required(),
  email: Joi.string().required(),
  password: Joi.string().required(),
  address: Joi.string().allow("", null),
  gender: Joi.string().valid("male", "female", "other").required(),
  birthDate: Joi.date().required(),
  status: Joi.boolean().default(true),
  note: Joi.string().allow("", null),
});

// Export prisma client for use in services
export const prisma = new PrismaClient().$extends({
  model: {
    user: {
      // Add any custom methods here
    },
  },
});

export default IUser;
