import { PrismaClient, Prisma } from "@prisma/client";
import Jo<PERSON> from "joi";

// Define refund reason enum
export enum RefundReason {
  CUSTOMER_INITIATED = "CUSTOMER_INITIATED",
  FRAUD = "FRAUD",
  DUPLICATE = "DUPLICATE",
  PRODUCT_NOT_RECEIVED = "PRODUCT_NOT_RECEIVED",
  PRODUCT_UNACCEPTABLE = "PRODUCT_UNACCEPTABLE",
  PRODUCT_DAMAGED = "PRODUCT_DAMAGED",
  BILLING_ERROR = "BILLING_ERROR",
  OTHER = "OTHER",
}

// Define refund status enum
export enum RefundStatus {
  PENDING = "PENDING",
  PROCESSING = "PROCESSING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
}

// Define block refund interface
export interface IBlockRefund {
  id: string;
  blockId: string;
  storeId: string;
  orderId: bigint;
  refundId?: bigint | null;
  amount: number;
  currency: string;
  reason?: string | null;
  note?: string | null;
  notify: boolean;
  shipping?: any | null;
  refundLineItems?: any | null;
  transactions?: any | null;
  orderAdjustments?: any | null;
  duties?: any | null;
  gateway?: string | null;
  parentId?: bigint | null;
  processedAt?: Date | null;
  restock: boolean;
  userId?: bigint | null;
  adminGraphqlApiId?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// Define refund line item interface
export interface IRefundLineItem {
  lineItemId: bigint;
  quantity: number;
  restockType?: string;
  locationId?: bigint;
}

// Define shipping refund interface
export interface IShippingRefund {
  amount: number;
  tax: number;
  maximumRefundable: number;
}

// Define order adjustment interface
export interface IOrderAdjustment {
  amount: number;
  taxAmount: number;
  kind: string;
  reason: string;
}

// Define refund request interface
export interface IRefundRequest {
  blockId: string;
  storeId: string;
  orderId: bigint;
  amount: number;
  currency: string;
  reason?: RefundReason;
  note?: string;
  notify?: boolean;
  refundLineItems?: IRefundLineItem[];
  shipping?: IShippingRefund;
  orderAdjustments?: IOrderAdjustment[];
  restock?: boolean;
}

// Define validation schema for refund request
export const refundRequestSchema = Joi.object({
  blockId: Joi.string().required(),
  storeId: Joi.string().uuid().required(),
  orderId: Joi.number().integer().positive().required(),
  amount: Joi.number().positive().required(),
  currency: Joi.string().length(3).uppercase().required(),
  reason: Joi.string().valid(...Object.values(RefundReason)),
  note: Joi.string().max(500),
  notify: Joi.boolean().default(true),
  refundLineItems: Joi.array().items(
    Joi.object({
      lineItemId: Joi.number().integer().positive().required(),
      quantity: Joi.number().integer().positive().required(),
      restockType: Joi.string().valid("no_restock", "cancel", "return"),
      locationId: Joi.number().integer().positive(),
    })
  ),
  shipping: Joi.object({
    amount: Joi.number().min(0).required(),
    tax: Joi.number().min(0).required(),
    maximumRefundable: Joi.number().min(0).required(),
  }),
  orderAdjustments: Joi.array().items(
    Joi.object({
      amount: Joi.number().required(),
      taxAmount: Joi.number().required(),
      kind: Joi.string().valid("shipping_refund", "refund_discrepancy").required(),
      reason: Joi.string().required(),
    })
  ),
  restock: Joi.boolean().default(true),
});

// Define validation schema for block refund
export const blockRefundSchema = Joi.object({
  id: Joi.string().uuid(),
  blockId: Joi.string().required(),
  storeId: Joi.string().uuid().required(),
  orderId: Joi.number().integer().positive().required(),
  refundId: Joi.number().integer().positive().allow(null),
  amount: Joi.number().positive().required(),
  currency: Joi.string().length(3).uppercase().required(),
  reason: Joi.string().allow(null),
  note: Joi.string().max(500).allow(null),
  notify: Joi.boolean().default(true),
  shipping: Joi.object().allow(null),
  refundLineItems: Joi.object().allow(null),
  transactions: Joi.object().allow(null),
  orderAdjustments: Joi.object().allow(null),
  duties: Joi.object().allow(null),
  gateway: Joi.string().allow(null),
  parentId: Joi.number().integer().positive().allow(null),
  processedAt: Joi.date().allow(null),
  restock: Joi.boolean().default(true),
  userId: Joi.number().integer().positive().allow(null),
  adminGraphqlApiId: Joi.string().allow(null),
  createdAt: Joi.date(),
  updatedAt: Joi.date(),
});

// Export prisma client for use in services
export const prisma = new PrismaClient().$extends({
  model: {
    blockRefund: {
      // Find refunds by block
      async findByBlockId(blockId: string) {
        return prisma.blockRefund.findMany({
          where: { blockId },
          include: {
            block: true,
            linkedStore: true,
            shopifyOrder: true,
          },
        });
      },
      
      // Find refunds by store
      async findByStoreId(storeId: string) {
        return prisma.blockRefund.findMany({
          where: { storeId },
          include: {
            block: true,
            linkedStore: true,
            shopifyOrder: true,
          },
        });
      },
      
      // Find refunds by order
      async findByOrderId(orderId: bigint) {
        return prisma.blockRefund.findMany({
          where: { orderId },
          include: {
            block: true,
            linkedStore: true,
            shopifyOrder: true,
          },
        });
      },
    },
  },
});

// BlockRefundModel class for additional business logic
export class BlockRefundModel {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  async create(data: Prisma.BlockRefundCreateInput) {
    return this.prisma.blockRefund.create({ data });
  }

  async update(id: string, data: Prisma.BlockRefundUpdateInput) {
    return this.prisma.blockRefund.update({
      where: { id },
      data,
    });
  }

  async findOne(id: string) {
    return this.prisma.blockRefund.findUnique({
      where: { id },
      include: {
        block: true,
        linkedStore: true,
        shopifyOrder: true,
      },
    });
  }

  async findByBlockAndOrder(blockId: string, orderId: string) {
    return this.prisma.blockRefund.findFirst({
      where: {
        blockId,
        orderId: BigInt(orderId),
      },
    });
  }

  async delete(id: string) {
    return this.prisma.blockRefund.delete({
      where: { id },
    });
  }
}

export default IBlockRefund;