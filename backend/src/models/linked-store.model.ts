import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

// Define LinkedStore type from prisma
export interface ILinkedStore {
  id: string;
  userId: string;
  storeName: string;
  provider: string;
  providerStoreId?: string | null;
  data: any;
  isActive?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Export for use in other files
export interface LinkedStore {
  id: string;
  userId?: string;
  storeName: string;
  provider: string;
  providerStoreId?: string | null;
  data: any;
  isActive?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ShopifyStoreData {
  shop: any;
  accessToken: string;
  domain: string;
  lastFetchedAt?: string;
}

export interface ShopifyCallbackState {
  userId: string;
}

// Define validation schema for LinkedStore
export const linkedStoreSchema = Joi.object({
  userId: Joi.string().required(),
  storeName: Joi.string().required(),
  provider: Joi.string()
    .valid("shopify", "stripe", "chargebee", "recurly", "braintree", "manual")
    .required(),
  providerStoreId: Joi.string().allow("", null),
  data: Joi.object().required(),
});

// Export prisma client for use in services
export const prisma = new PrismaClient().$extends({
  model: {
    linkedStore: {
      // Add any custom methods here
    },
  },
});

export default ILinkedStore;
