import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

// Define alert types as enum thay vì union type
export enum AlertType {
  ETHOCA = "ETHOCA",
  RDR = "RDR",
}

// Define feedback status enum
export enum FeedbackStatus {
  PENDING = "PENDING",
  SENT = "SENT",
  FAILED = "FAILED",
}

// Define base alert interface with common fields
export interface IBaseAlert {
  id: string;
  alertId: string;
  alertTime: string;
  alertType: string;
  amount: string;
  currency: string;
  descriptor: string;
  authCode?: string | null;
  cardBin?: string | null;
  cardNumber?: string | null;
  chargebackCode?: string | null;
  disputeAmount?: string | null;
  disputeCurrency?: string | null;
  transactionTime?: string | null;

  // Feedback fields
  feedbackStatus: FeedbackStatus;
  feedbackTime?: Date | null;

  // Meta fields
  type: AlertType;
  createdAt: Date;
  updatedAt: Date;
}

// Define ETHOCA specific alert interface
export interface IEthocaAlert extends IBaseAlert {
  type: AlertType.ETHOCA;

  // ETHOCA required fields
  age: string;

  // ETHOCA specific fields
  alertSource?: string | null;
  arn?: string | null;
  issuer?: string | null;
  initiatedBy?: string | null;
  liability?: string | null;
  merchantCategoryCode?: string | null;
  transactionId?: string | null;
  transactionType?: string | null;
}

// Define RDR specific alert interface
export interface IRdrAlert extends IBaseAlert {
  type: AlertType.RDR;

  // ETHOCA field (optional for RDR)
  age?: string | null;

  // RDR specific fields
  acquirerBin?: string | null;
  acquirerReferenceNumber?: string | null;
  alertStatus?: string | null;
  caid?: string | null;
  descriptorContact?: string | null;
  ruleName?: string | null;
  ruleType?: string | null;
}

// Combine both alert types
export type IAlert = IEthocaAlert | IRdrAlert;

// Define validation schema for alert
export const alertSchema = Joi.object({
  id: Joi.string().required(),
  alertId: Joi.string().required(),
  alertTime: Joi.string().required(),
  alertType: Joi.string().required(),
  amount: Joi.string().required(),
  currency: Joi.string().required(),
  descriptor: Joi.string().required(),
  authCode: Joi.string().allow(null, ""),
  cardBin: Joi.string().allow(null, ""),
  cardNumber: Joi.string().allow(null, ""),
  chargebackCode: Joi.string().allow(null, ""),
  disputeAmount: Joi.string().allow(null, ""),
  disputeCurrency: Joi.string().allow(null, ""),
  transactionTime: Joi.string().allow(null, ""),
  feedbackStatus: Joi.string()
    .valid(FeedbackStatus.PENDING, FeedbackStatus.SENT, FeedbackStatus.FAILED)
    .default(FeedbackStatus.PENDING),
  feedbackTime: Joi.date().allow(null),
  age: Joi.string().allow(null, ""),
  alertSource: Joi.string().allow(null, ""),
  arn: Joi.string().allow(null, ""),
  issuer: Joi.string().allow(null, ""),
  initiatedBy: Joi.string().allow(null, ""),
  liability: Joi.string().allow(null, ""),
  merchantCategoryCode: Joi.string().allow(null, ""),
  transactionId: Joi.string().allow(null, ""),
  transactionType: Joi.string().allow(null, ""),
  acquirerBin: Joi.string().allow(null, ""),
  acquirerReferenceNumber: Joi.string().allow(null, ""),
  alertStatus: Joi.string().allow(null, ""),
  caid: Joi.string().allow(null, ""),
  descriptorContact: Joi.string().allow(null, ""),
  ruleName: Joi.string().allow(null, ""),
  ruleType: Joi.string().allow(null, ""),
  type: Joi.string()
    .valid(AlertType.ETHOCA, AlertType.RDR)
    .default(AlertType.ETHOCA),
}).when(
  Joi.object({
    type: Joi.string().valid(AlertType.ETHOCA).required(),
  }).unknown(),
  {
    then: Joi.object({
      age: Joi.string().required(),
    }),
  }
);

// Type guard function to check if alert is ETHOCA
export function isEthocaAlert(alert: IAlert): alert is IEthocaAlert {
  return alert.type === AlertType.ETHOCA;
}

// Type guard function to check if alert is RDR
export function isRdrAlert(alert: IAlert): alert is IRdrAlert {
  return alert.type === AlertType.RDR;
}

// Export prisma client for use in services
export const prisma = new PrismaClient().$extends({
  model: {
    alert: {
      // Add any custom methods here
      findEthocaAlerts() {
        return prisma.alert.findMany({
          where: { type: AlertType.ETHOCA },
        });
      },
      findRdrAlerts() {
        return prisma.alert.findMany({
          where: { type: AlertType.RDR },
        });
      },
      // Phương thức lấy alerts có feedback
      async findAlertsWithFeedback() {
        // Sử dụng raw query để lấy alert có feedback
        // cho đến khi model AlertFeedback được tạo sau migration
        return prisma.$queryRaw`
          SELECT a.*, af.* FROM "alerts" a
          JOIN "alert_feedbacks" af ON a."id" = af."predictor_id"
        `;
      },
    },
    // Model AlertFeedback sẽ được thêm khi prisma generate models
  },
});

export default IAlert;
