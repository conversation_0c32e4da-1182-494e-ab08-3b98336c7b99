import { Bill } from '@prisma/client';

// Bill Models
export interface BillModel extends Bill {}

export interface CreateBill {
  linkedStoreId: string;
  invoiceId?: string;
  amount: number;
  currency?: string;
  description?: string;
  status?: string;
  dueDate: Date;
}

export interface UpdateBill {
  invoiceId?: string;
  amount?: number;
  currency?: string;
  description?: string;
  status?: string;
  dueDate?: Date;
}

export interface BillQuery {
  id?: string;
  linkedStoreId?: string;
  status?: string;
}