import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

// Define ShopifyDispute type from prisma
export interface IShopifyDispute {
  id: bigint;
  linkedStoreId: string;
  transactionId?: bigint | null;
  type?: string | null;
  amount?: number | null;
  reason?: string | null;
  status?: string | null;
  currency?: string | null;
  orderId?: bigint | null;
  finalizedOn?: Date | null;
  initiatedAt?: Date | null;
  evidenceDueBy?: Date | null;
  evidenceSentOn?: Date | null;
  networkReasonCode?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// Export for use in other files
export interface ShopifyDisputeModel extends IShopifyDispute {}

// Query parameters for retrieving shopify disputes
export interface ShopifyDisputeQuery {
  id?: bigint;
  linkedStoreId?: string;
  transactionId?: bigint;
  type?: string;
  reason?: string;
  status?: string;
  orderId?: bigint;
  initiatedFrom?: Date;
  initiatedTo?: Date;
  evidenceDueFrom?: Date;
  evidenceDueTo?: Date;
}

// Data for creating a new shopify dispute
export interface CreateShopifyDispute {
  id: bigint;
  linkedStoreId: string;
  transactionId?: bigint | null;
  type?: string | null;
  amount?: number | null;
  reason?: string | null;
  status?: string | null;
  currency?: string | null;
  orderId?: bigint | null;
  finalizedOn?: Date | null;
  initiatedAt?: Date | null;
  evidenceDueBy?: Date | null;
  evidenceSentOn?: Date | null;
  networkReasonCode?: string | null;
}

// Validation schema for creating shopify dispute
export const createShopifyDisputeSchema = Joi.object({
  id: Joi.number().required(),
  linkedStoreId: Joi.string().required(),
  transactionId: Joi.number(),
  type: Joi.string(),
  amount: Joi.number(),
  reason: Joi.string(),
  status: Joi.string(),
  currency: Joi.string(),
  orderId: Joi.number(),
  initiatedAt: Joi.date(),
  evidenceDueBy: Joi.date(),
  networkReasonCode: Joi.string()
});
