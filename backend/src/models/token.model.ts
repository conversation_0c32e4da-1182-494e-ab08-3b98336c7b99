import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

// Define token type from prisma
export interface IToken {
  id: string;
  userId: string;
  token: string;
  refreshToken?: string | null;
  userAgent?: string | null;
  ipAddress?: string | null;
  expiresAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Define validation schema for token
export const tokenSchema = Joi.object({
  userId: Joi.string().required(),
  token: Joi.string().required(),
  refreshToken: Joi.string().allow("", null),
  userAgent: Joi.string().allow("", null),
  ipAddress: Joi.string().allow("", null),
  expiresAt: Joi.date().required(),
});

// Export prisma client for use in services
export const prisma = new PrismaClient().$extends({
  model: {
    token: {
      // Add any custom methods here
    },
  },
});

export default IToken;
