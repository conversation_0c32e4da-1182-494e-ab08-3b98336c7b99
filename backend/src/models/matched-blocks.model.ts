import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

// Block types mirroring Alert types
export enum BlockType {
  ETHOCA = "ETHOCA",
  RDR = "RDR",
}

// Define feedback status enum
export enum FeedbackStatus {
  PENDING = "PENDING",
  SENT = "SENT",
  FAILED = "FAILED",
}

// Define MatchedBlock interface based on Prisma schema
export interface IMatchedBlock {
  id: string; // Same as Block ID
  userId: string;
  linkedStoreId: string;
  provider: string; // "shopify", "stripe", etc.
  
  // Matched transaction data - flexible JSON structure to store matched orders/transactions
  matchedData?: any; // Store matched transactions/orders info
  
  // Block data fields (copied from blocks table)
  alertId: string;
  alertTime: Date;
  alertType: string;
  amount: number;
  currency: string;
  descriptor: string;
  authCode?: string | null;
  cardBin?: string | null;
  cardNumber?: string | null;
  chargebackCode?: string | null;
  disputeAmount?: number | null;
  disputeCurrency?: string | null;
  transactionTime?: Date | null;
  age?: string | null;
  alertSource?: string | null;
  arn?: string | null;
  issuer?: string | null;
  initiatedBy?: string | null;
  liability?: string | null;
  merchantCategoryCode?: string | null;
  transactionId_Block?: string | null;
  transactionType?: string | null;
  acquirerBin?: string | null;
  acquirerReferenceNumber?: string | null;
  alertStatus?: string | null;
  caid?: string | null;
  descriptorContact?: string | null;
  ruleName?: string | null;
  ruleType?: string | null;
  type: BlockType;
  feedbackData?: any | null;
  feedbackStatus: FeedbackStatus;
  feedbackTime?: Date | null;
  
  createdAt: Date;
  updatedAt: Date;
}

// Define validation schema for matched block
export const matchedBlockSchema = Joi.object({
  id: Joi.string().required(),
  userId: Joi.string().required(),
  linkedStoreId: Joi.string().required(),
  provider: Joi.string().required(),
  matchedData: Joi.object().allow(null),
  alertId: Joi.string().required(),
  alertTime: Joi.date().required(),
  alertType: Joi.string().required(),
  amount: Joi.number().integer().required(),
  currency: Joi.string().required(),
  descriptor: Joi.string().required(),
  authCode: Joi.string().allow(null, ""),
  cardBin: Joi.string().allow(null, ""),
  cardNumber: Joi.string().allow(null, ""),
  chargebackCode: Joi.string().allow(null, ""),
  disputeAmount: Joi.number().integer().allow(null),
  disputeCurrency: Joi.string().allow(null, ""),
  transactionTime: Joi.date().allow(null),
  age: Joi.string().allow(null, ""),
  alertSource: Joi.string().allow(null, ""),
  arn: Joi.string().allow(null, ""),
  issuer: Joi.string().allow(null, ""),
  initiatedBy: Joi.string().allow(null, ""),
  liability: Joi.string().allow(null, ""),
  merchantCategoryCode: Joi.string().allow(null, ""),
  transactionId_Block: Joi.string().allow(null, ""),
  transactionType: Joi.string().allow(null, ""),
  acquirerBin: Joi.string().allow(null, ""),
  acquirerReferenceNumber: Joi.string().allow(null, ""),
  alertStatus: Joi.string().allow(null, ""),
  caid: Joi.string().allow(null, ""),
  descriptorContact: Joi.string().allow(null, ""),
  ruleName: Joi.string().allow(null, ""),
  ruleType: Joi.string().allow(null, ""),
  type: Joi.string()
    .valid(BlockType.ETHOCA, BlockType.RDR)
    .default(BlockType.ETHOCA),
  feedbackData: Joi.object().allow(null),
  feedbackStatus: Joi.string()
    .valid(FeedbackStatus.PENDING, FeedbackStatus.SENT, FeedbackStatus.FAILED)
    .default(FeedbackStatus.PENDING),
  feedbackTime: Joi.date().allow(null),
});

// Type guard function to check if block is ETHOCA
export function isEthocaMatchedBlock(block: IMatchedBlock): boolean {
  return block.type === BlockType.ETHOCA;
}

// Type guard function to check if block is RDR
export function isRdrMatchedBlock(block: IMatchedBlock): boolean {
  return block.type === BlockType.RDR;
}

// Export prisma client for use in services
export const prisma = new PrismaClient().$extends({
  model: {
    matchedBlock: {
      // Get matched blocks by userId
      findByUserId(userId: string) {
        return prisma.matchedBlock.findMany({
          where: { userId },
          orderBy: { alertTime: 'desc' },
        });
      },
      
      // Get matched blocks by userId with pagination
      findByUserIdPaginated(userId: string, page: number = 1, limit: number = 10) {
        return prisma.matchedBlock.findMany({
          where: { userId },
          orderBy: { alertTime: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
        });
      },
      
      // Count matched blocks by userId
      countByUserId(userId: string) {
        return prisma.matchedBlock.count({
          where: { userId },
        });
      },
      
      // Get matched blocks by userId and type
      findByUserIdAndType(userId: string, type: BlockType) {
        return prisma.matchedBlock.findMany({
          where: { 
            userId,
            type 
          },
          orderBy: { alertTime: 'desc' },
        });
      },
    },
  },
});

export default IMatchedBlock;