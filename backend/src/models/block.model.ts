import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

// Block types mirroring Alert types
export enum BlockType {
  ETHOCA = "ETHOCA",
  RDR = "RDR",
}

// Define feedback status enum
export enum FeedbackStatus {
  PENDING = "PENDING",
  SENT = "SENT",
  FAILED = "FAILED",
}

// Define base block interface with common fields
export interface IBaseBlock {
  id: string;
  alertId: string;
  alertTime: Date;
  alertType: string;
  amount: number;
  currency: string;
  descriptor: string;
  authCode?: string | null;
  cardBin?: string | null;
  cardNumber?: string | null;
  chargebackCode?: string | null;
  disputeAmount?: number | null;
  disputeCurrency?: string | null;
  transactionTime?: Date | null;

  // Feedback fields
  feedbackStatus: FeedbackStatus;
  feedbackTime?: Date | null;
  feedbackData?: any | null;

  // Meta fields
  type: BlockType;
  createdAt: Date;
  updatedAt: Date;
}

// Define ETHOCA specific block interface
export interface IEthocaBlock extends IBaseBlock {
  type: BlockType.ETHOCA;

  // ETHOCA required fields
  age: string;

  // ETHOCA specific fields
  alertSource?: string | null;
  arn?: string | null;
  issuer?: string | null;
  initiatedBy?: string | null;
  liability?: string | null;
  merchantCategoryCode?: string | null;
  transactionId?: string | null;
  transactionType?: string | null;
}

// Define RDR specific block interface
export interface IRdrBlock extends IBaseBlock {
  type: BlockType.RDR;

  // ETHOCA field (optional for RDR)
  age?: string | null;

  // RDR specific fields
  acquirerBin?: string | null;
  acquirerReferenceNumber?: string | null;
  alertStatus?: string | null;
  caid?: string | null;
  descriptorContact?: string | null;
  ruleName?: string | null;
  ruleType?: string | null;
}

// Combine both block types
export type IBlock = IEthocaBlock | IRdrBlock;

// Block feedback structure
export interface IBlockFeedback {
  predictorId: string;
  outcome: string;
  refunded: string;
  comments?: string;
  isFraud?: string;
  matchOrderNo?: string;
  refundNo?: string;
  refundDate?: string;
  refundAmount?: string;
  refundCurrency?: string;
  errorData?: any;
}

// Define validation schema for block
export const blockSchema = Joi.object({
  id: Joi.string().required(),
  alertId: Joi.string().required(),
  alertTime: Joi.date().required(),
  alertType: Joi.string().required(),
  amount: Joi.number().integer().required(),
  currency: Joi.string().required(),
  descriptor: Joi.string().required(),
  authCode: Joi.string().allow(null, ""),
  cardBin: Joi.string().allow(null, ""),
  cardNumber: Joi.string().allow(null, ""),
  chargebackCode: Joi.string().allow(null, ""),
  disputeAmount: Joi.number().integer().allow(null),
  disputeCurrency: Joi.string().allow(null, ""),
  transactionTime: Joi.date().allow(null),
  feedbackStatus: Joi.string()
    .valid(FeedbackStatus.PENDING, FeedbackStatus.SENT, FeedbackStatus.FAILED)
    .default(FeedbackStatus.PENDING),
  feedbackTime: Joi.date().allow(null),
  feedbackData: Joi.object().allow(null),
  age: Joi.string().allow(null, ""),
  alertSource: Joi.string().allow(null, ""),
  arn: Joi.string().allow(null, ""),
  issuer: Joi.string().allow(null, ""),
  initiatedBy: Joi.string().allow(null, ""),
  liability: Joi.string().allow(null, ""),
  merchantCategoryCode: Joi.string().allow(null, ""),
  transactionId: Joi.string().allow(null, ""),
  transactionType: Joi.string().allow(null, ""),
  acquirerBin: Joi.string().allow(null, ""),
  acquirerReferenceNumber: Joi.string().allow(null, ""),
  alertStatus: Joi.string().allow(null, ""),
  caid: Joi.string().allow(null, ""),
  descriptorContact: Joi.string().allow(null, ""),
  ruleName: Joi.string().allow(null, ""),
  ruleType: Joi.string().allow(null, ""),
  type: Joi.string()
    .valid(BlockType.ETHOCA, BlockType.RDR)
    .default(BlockType.ETHOCA),
}).when(
  Joi.object({
    type: Joi.string().valid(BlockType.ETHOCA).required(),
  }).unknown(),
  {
    then: Joi.object({
      age: Joi.string().required(),
    }),
  }
);

// Type guard function to check if block is ETHOCA
export function isEthocaBlock(block: IBlock): block is IEthocaBlock {
  return block.type === BlockType.ETHOCA;
}

// Type guard function to check if block is RDR
export function isRdrBlock(block: IBlock): block is IRdrBlock {
  return block.type === BlockType.RDR;
}

// Export prisma client for use in services
export const prisma = new PrismaClient().$extends({
  model: {
    block: {
      // Add any custom methods here
      findEthocaBlocks() {
        return prisma.block.findMany({
          where: { type: BlockType.ETHOCA },
        });
      },
      findRdrBlocks() {
        return prisma.block.findMany({
          where: { type: BlockType.RDR },
        });
      },
      // Method to get blocks with feedback
      async findBlocksWithFeedback() {
        return await prisma.$queryRaw`SELECT * FROM "blocks" WHERE "feedback_data" IS NOT NULL`;
      },
    },
  },
});

export default IBlock; 