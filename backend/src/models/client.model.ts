import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

// Define client type from prisma
export interface IClient {
  id: string;
  name: string;
  email: string;
  description?: string | null;
  APIKey: string;
  createdAt: Date;
  updatedAt: Date;
}

// Define validation schema for client
export const clientSchema = Joi.object({
  name: Joi.string().required(),
  email: Joi.string().email().required(),
  description: Joi.string().allow("", null),
  APIKey: Joi.string(),
});

// Export prisma client for use in services
export const prisma = new PrismaClient().$extends({
  model: {
    client: {
      // Add any custom methods here
    },
  },
});

export default IClient;
