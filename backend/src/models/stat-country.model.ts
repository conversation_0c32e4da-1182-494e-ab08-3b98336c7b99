import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

/**
 * StatCountry Model
 * Tracks chargeback statistics by country for analytics and reporting
 * Groups chargeback data by user, store, provider, and country
 */

// StatCountry interface
export interface IStatCountry {
  id: string;
  userId: string;
  linkedStoreId: string;
  provider: string; // 'shopify', 'stripe', etc.
  countryCode: string; // ISO 2-letter country code (US, CA, GB, etc.)
  countryName: string; // Full country name
  count: number; // Number of chargebacks from this country
  createdAt: Date;
  updatedAt: Date;
}

// Create stats input interface
export interface ICreateStatCountryInput {
  userId: string;
  linkedStoreId: string;
  provider: string;
  countryCode: string;
  countryName: string;
  count?: number; // Optional, defaults to 0
}

// Update stats input interface
export interface IUpdateStatCountryInput {
  count?: number;
  countryName?: string; // Allow updating country name if needed
}

// Joi validation schema
export const statCountrySchema = Joi.object({
  userId: Joi.string().required(),
  linkedStoreId: Joi.string().required(),
  provider: Joi.string().valid('shopify', 'stripe').required(),
  countryCode: Joi.string().length(2).uppercase().required(),
  countryName: Joi.string().min(1).max(100).required(),
  count: Joi.number().integer().min(0).default(0)
});

// Update validation schema
export const updateStatCountrySchema = Joi.object({
  count: Joi.number().integer().min(0),
  countryName: Joi.string().min(1).max(100)
}).min(1); // At least one field must be provided

// Export prisma client for use in services
export const prisma = new PrismaClient().$extends({
  model: {
    statCountry: {
      // Custom method to increment count
      async incrementCount(where: { userId: string; linkedStoreId: string; provider: string; countryCode: string }) {
        return prisma.statCountry.upsert({
          where: {
            userId_linkedStoreId_provider_countryCode: where
          },
          update: {
            count: { increment: 1 },
            updatedAt: new Date()
          },
          create: {
            id: crypto.randomUUID(),
            userId: where.userId,
            linkedStoreId: where.linkedStoreId,
            provider: where.provider,
            countryCode: where.countryCode,
            countryName: '', // Will need to be filled by service
            count: 1,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });
      }
    }
  }
});

export default IStatCountry;