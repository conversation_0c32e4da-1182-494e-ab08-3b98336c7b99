import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

// Define AlertInfo type
export interface IAlertInfo {
  id: string;
  alertType: string; // "RDR" or "ETHOCA"
  descriptor: string;
  bin?: string | null;
  caid?: string | null;
  arn?: string | null;
  registrationStatus: string; // "WAITING", "EFFECTED", "CLOSING", "CLOSED"
  registrationMessage?: string | null;
  registeredAt?: Date | null;
  closedAt?: Date | null;
  storeId: string;
  createdAt: Date;
  updatedAt: Date;
}

// Export for use in other files
export interface AlertInfo {
  id: string;
  alertType: string;
  descriptor: string;
  bin?: string | null;
  caid?: string | null;
  arn?: string | null;
  registrationStatus: string;
  registrationMessage?: string | null;
  registeredAt?: Date | null;
  closedAt?: Date | null;
  storeId: string;
  createdAt: Date;
  updatedAt: Date;
}

// Define enum for alert types
export enum AlertType {
  RDR = "RDR",
  ETHOCA = "ETHOCA"
}

// Define enum for registration status
export enum RegistrationStatus {
  WAITING = "WAITING", // 等待预警开通中
  EFFECTED = "EFFECTED", // 已生效  
  CLOSING = "CLOSING", // 关闭中
  CLOSED = "CLOSED" // 已关闭
}

// Define validation schema for AlertInfo
export const alertInfoSchema = Joi.object({
  alertType: Joi.string().valid(AlertType.RDR, AlertType.ETHOCA).required(),
  descriptor: Joi.string().required(),
  bin: Joi.string().allow(null, ''),
  caid: Joi.string().allow(null, ''),
  arn: Joi.string().allow(null, ''),
  storeId: Joi.string().required()
});

// Define validation schema for updating AlertInfo
export const updateAlertInfoSchema = Joi.object({
  alertType: Joi.string().valid(AlertType.RDR, AlertType.ETHOCA),
  descriptor: Joi.string(),
  bin: Joi.string().allow(null, ''),
  caid: Joi.string().allow(null, ''),
  arn: Joi.string().allow(null, ''),
  registrationStatus: Joi.string().valid(...Object.values(RegistrationStatus)),
  registrationMessage: Joi.string().allow(null, '')
}).min(1); // At least one field must be provided

// Export prisma client for use in services
export const prisma = new PrismaClient().$extends({
  model: {
    alertInfo: {
      
    },
    // Model AlertInfo sẽ được thêm khi prisma generate models
  },
});

export default AlertInfo;
