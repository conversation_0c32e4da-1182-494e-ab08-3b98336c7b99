import { PrismaClient } from "@prisma/client";
import <PERSON><PERSON> from "joi";

/**
 * StatReason Model
 * Tracks chargeback statistics by reason for analytics and reporting
 * Groups chargeback data by user, store, provider, and reason code
 */

// StatReason interface
export interface IStatReason {
  id: string;
  userId: string;
  linkedStoreId: string;
  provider: string; // 'shopify', 'stripe', etc.
  reasonCode: string; // Chargeback reason code
  reasonName: string; // Human-readable reason name
  count: number; // Number of chargebacks with this reason
  createdAt: Date;
  updatedAt: Date;
}

// Create stats input interface
export interface ICreateStatReasonInput {
  userId: string;
  linkedStoreId: string;
  provider: string;
  reasonCode: string;
  reasonName: string;
  count?: number; // Optional, defaults to 0
}

// Update stats input interface
export interface IUpdateStatReasonInput {
  count?: number;
  reasonName?: string; // Allow updating reason name if needed
}

// Joi validation schema
export const statReasonSchema = Joi.object({
  userId: Joi.string().required(),
  linkedStoreId: Joi.string().required(),
  provider: Joi.string().valid('shopify', 'stripe').required(),
  reasonCode: Joi.string().min(1).max(50).required(),
  reasonName: Joi.string().min(1).max(200).required(),
  count: Joi.number().integer().min(0).default(0)
});

// Update validation schema
export const updateStatReasonSchema = Joi.object({
  count: Joi.number().integer().min(0),
  reasonName: Joi.string().min(1).max(200)
}).min(1); // At least one field must be provided

// Export prisma client for use in services
export const prisma = new PrismaClient().$extends({
  model: {
    statReason: {
      // Custom method to increment count
      async incrementCount(where: { userId: string; linkedStoreId: string; provider: string; reasonCode: string }) {
        return prisma.statReason.upsert({
          where: {
            userId_linkedStoreId_provider_reasonCode: where
          },
          update: {
            count: { increment: 1 },
            updatedAt: new Date()
          },
          create: {
            id: crypto.randomUUID(),
            userId: where.userId,
            linkedStoreId: where.linkedStoreId,
            provider: where.provider,
            reasonCode: where.reasonCode,
            reasonName: '', // Will need to be filled by service
            count: 1,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });
      }
    }
  }
});

export default IStatReason;