import Joi from "joi";

// Store Settings interface (single setting)
export interface IStoreSetting {
  id: string;
  storeId: string;
  name: string;
  value: string;
  createdAt: Date;
  updatedAt: Date;
}

// Grouped store settings interface
export interface IStoreSettings {
  [key: string]: string;
}

// Request interfaces
export interface CreateStoreSettingRequest {
  storeId: string;
  name: string;
  value: string;
}

export interface UpdateStoreSettingRequest {
  value: string;
}

export interface UpdateStoreSettingsRequest {
  settings: { [key: string]: string };
}

// Common setting names
export enum SettingNames {
  MONTHLY_GOAL = "monthly_goal",
  CURRENCY = "currency",
  TIMEZONE = "timezone",
  NOTIFICATION_EMAIL = "notification_email",
  AUTO_REFUND = "auto_refund",
}

// Validation schemas
export const createStoreSettingSchema = Joi.object({
  storeId: Joi.string().required(),
  name: Joi.string().required(),
  value: Joi.string().required(),
});

export const updateStoreSettingSchema = Joi.object({
  value: Joi.string().required(),
});

export const updateStoreSettingsSchema = Joi.object({
  settings: Joi.object().pattern(Joi.string(), Joi.string()).required(),
}); 