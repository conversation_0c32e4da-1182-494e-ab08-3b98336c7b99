import { connectRedis, disconnectRedis } from "@/config/redis";
import express, { Request, Response } from "express";
import morgan from "morgan";
import routes from "@/routes";
import cors from "cors";
import helmet from "helmet";
import { env } from "@/config/environment";
import { connectDB, disconnectDB } from "@/config/database";
import exitHook from "async-exit-hook";
import ErrorMiddleware from "@/middlewares/error.middleware";
import compression from "compression";
import ResponseHandler from "@/utils/response-handler.util";
import { StatusCodes } from "http-status-codes";
import requestIp from "request-ip";
import http from "http";
import SocketService from "@/services/socket.service";
import { createLogger } from "./config/logger";
import { languageMiddleware } from "@/middlewares/language.middleware";
import { getShopifyJobProcessor } from "@/services/shopify-job-processor.service";

const logger = createLogger(
  "logs/server/error.log",
  "logs/server/combined.log"
);

const start = () => {
  const app = express();
  app.use(helmet());
  app.use(compression() as any);
  app.use(
    cors({
      origin: "*",
    })
  );
  app.use(express.json());
  app.use(express.urlencoded({ extended: false }));
  app.use(requestIp.mw());

  app.get("/", async (req: Request, res: Response) => {
    const ip = req.clientIp;
    return ResponseHandler.sendSuccess(res, {
      status: StatusCodes.OK,
      message: env.SERVER_GREETING,
      data: {
        ip: ip,
        type: env.ENV,
        time: new Date().toString(),
      },
    });
  });

  app.get("/health", (req: Request, res: Response) => {
    return ResponseHandler.sendSuccess(res, {
      status: StatusCodes.OK,
      message: "API is healthy.",
    });
  });

  app.use(languageMiddleware);
  app.use(routes);
  app.use(ErrorMiddleware.error404);
  app.use(ErrorMiddleware.errorHandling);

  // log only 4xx and 5xx responses to access.log in combined format
  app.use(
    morgan("combined", {
      skip: (req: Request, res: Response) => res.statusCode < 400,
      stream: {
        write: (message) => logger.error(message.trim()),
      },
    })
  );

  const server = http.createServer(app);
  const serverSocket = http.createServer();
  // Initialize Socket.IO
  SocketService.getInstance();
  SocketService.initSocket(serverSocket);

  server.listen(env.SERVER_PORT, () => {
    console.log(
      `Server listening on port: ${env.SERVER_PORT}. Environment: ${env.ENV}`
    );
    
    getShopifyJobProcessor();
    console.log("Shopify job processor initialized");
  });

  //Handle exit
  exitHook(() => {
    disconnectDB();
    console.log("Disconnected from database");
    disconnectRedis();
    console.log("Disconnected from Redis");
  });
};

connectDB()
  .then(() => console.log("Connected to database"))
  .then(() => connectRedis())
  .then(() => console.log("Connected to Redis"))
  .then(() => start())
  .catch((error) => {
    console.error(error);
    process.exit(0);
  });
