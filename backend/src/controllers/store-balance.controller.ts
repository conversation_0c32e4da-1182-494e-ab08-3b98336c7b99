import { Request, Response } from 'express';
import StoreBalanceService from '@/services/store-balance.service';
import { StatusCodes } from 'http-status-codes';
import ResponseHandler from '@/utils/response-handler.util';
import logger from '@/utils/logger.util';
import { z } from 'zod';

// Validation schemas
const manualTopupSchema = z.object({
  amount: z.number().min(1000).max(100000), // $10 to $1000
  paymentMethod: z.enum(['PAYPAL', 'STRIPE'])
});

export class StoreBalanceController {
  /**
   * Get current balance for a store
   */
  static async getCurrentBalance(req: Request, res: Response) {
    try {
      const { storeId } = req.params;
      
      if (!storeId) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: 'Store ID is required'
        });
      }
      
      const balance = await StoreBalanceService.getCurrentBalance(storeId);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: 'Balance retrieved successfully',
        data: { 
          balance,
          currency: 'USD',
          formatted: `$${(balance / 100).toFixed(2)}`
        }
      });
    } catch (error: any) {
      logger.error('Error getting balance:', error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || 'Failed to retrieve balance'
      });
    }
  }

  /**
   * Get available payment methods for a store
   */
  static async getPaymentMethods(req: Request, res: Response) {
    try {
      const { storeId } = req.params;
      
      if (!storeId) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: 'Store ID is required'
        });
      }
      
      const paymentMethods = await StoreBalanceService.getAvailablePaymentMethods(storeId);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: 'Payment methods retrieved successfully',
        data: paymentMethods
      });
    } catch (error: any) {
      logger.error('Error getting payment methods:', error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || 'Failed to retrieve payment methods'
      });
    }
  }

  /**
   * Perform manual topup
   */
  static async manualTopup(req: Request, res: Response) {
    try {
      const { storeId } = req.params;
      
      if (!storeId) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: 'Store ID is required'
        });
      }
      
      // Validate request body
      const validationResult = manualTopupSchema.safeParse(req.body);
      if (!validationResult.success) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: 'Invalid request data',
          data: validationResult.error.errors
        });
      }
      
      const { amount, paymentMethod } = validationResult.data;
      
      // Check if payment method is available
      const availableMethods = await StoreBalanceService.getAvailablePaymentMethods(storeId);
      if (paymentMethod === 'PAYPAL' && !availableMethods.paypal) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: 'PayPal payment method is not configured for this store'
        });
      }
      if (paymentMethod === 'STRIPE' && !availableMethods.stripe) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: 'Stripe payment method is not configured for this store'
        });
      }
      
      // Perform manual topup
      const success = await StoreBalanceService.manualTopup(storeId, amount, paymentMethod);
      
      if (success) {
        // Get updated balance
        const newBalance = await StoreBalanceService.getCurrentBalance(storeId);
        
        return ResponseHandler.sendSuccess(res, {
          status: StatusCodes.OK,
          message: 'Top-up completed successfully',
          data: {
            amount,
            paymentMethod,
            newBalance,
            formatted: `$${(newBalance / 100).toFixed(2)}`
          }
        });
      } else {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.PAYMENT_REQUIRED,
          message: 'Top-up failed. Please check your payment method and try again.'
        });
      }
    } catch (error: any) {
      logger.error('Error performing manual topup:', error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || 'Failed to process top-up'
      });
    }
  }

  /**
   * Get balance history for a store
   */
  static async getBalanceHistory(req: Request, res: Response) {
    try {
      const { storeId } = req.params;
      const { page = 1, limit = 20 } = req.query;
      
      if (!storeId) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: 'Store ID is required'
        });
      }
      
      // Get bills that represent balance changes (negative amounts are deductions, positive are topups)
      const BillService = (await import('@/services/bill.service')).default;
      const bills = await BillService.getBillsByStoreId(
        storeId, 
        Number(page), 
        Number(limit)
      );
      
      // Transform bills to balance history
      const balanceHistory = bills.items.map(bill => ({
        id: bill.id,
        type: bill.amount > 0 ? 'TOPUP' : 'DEDUCTION',
        amount: Math.abs(bill.amount),
        description: bill.description,
        status: bill.status,
        createdAt: bill.createdAt,
        paymentMethod: (bill as any).paymentHistories?.[0]?.paymentMethod || null
      }));
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: 'Balance history retrieved successfully',
        data: {
          items: balanceHistory,
          total: bills.total,
          page: bills.page,
          limit: bills.limit
        }
      });
    } catch (error: any) {
      logger.error('Error getting balance history:', error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || 'Failed to retrieve balance history'
      });
    }
  }

  /**
   * Check and maintain minimum balances for all stores or a specific store
   * This triggers proactive topups for stores with low balance
   */
  static async checkAndMaintainBalances(req: Request, res: Response) {
    try {
      const { storeId } = req.params;
      
      if (storeId) {
        logger.info(`Manual trigger: Starting balance maintenance check for store ${storeId}`);
        
        // Call the service method to check and maintain balance for specific store
        await StoreBalanceService.checkAndMaintainMinimumBalances(storeId);
        
        return ResponseHandler.sendSuccess(res, {
          status: StatusCodes.OK,
          message: `Balance maintenance check completed successfully for store ${storeId}`
        });
      } else {
        logger.info('Manual trigger: Starting balance maintenance check for all stores');
        
        // Call the service method to check and maintain balances for all stores
        await StoreBalanceService.checkAndMaintainMinimumBalances();
        
        return ResponseHandler.sendSuccess(res, {
          status: StatusCodes.OK,
          message: 'Balance maintenance check completed successfully for all stores'
        });
      }
    } catch (error: any) {
      logger.error('Error in balance maintenance check:', error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || 'Failed to complete balance maintenance check'
      });
    }
  }
}