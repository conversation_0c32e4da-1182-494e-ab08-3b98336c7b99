import { Request, Response, NextFunction } from "express";
import MatchedBlocksService from "@/services/matched-blocks.service";
import ResponseHandler from "@/utils/response-handler.util";
import { StatusCodes } from "http-status-codes";
import logger from "@/utils/logger.util";

class MatchedBlocksController {
  /**
   * Get chargeback rate chart data for matched blocks
   * Retrieves chargeback rate chart data based on matched blocks for a specific user
   */
  static async getChargebackRateChart(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = req.params;
      const { ...filters } = req.query;

      if (!userId) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: "User ID is required",
        });
      }

      // Parse filters
      const parsedFilters: Record<string, any> = {};
      for (const [key, value] of Object.entries(filters)) {
        if (typeof value === 'string') {
          const parts = value
            .split(',')
            .map((v) => v.replace(/\+/g, ' ').trim())
            .filter(Boolean);
          parsedFilters[key] = parts.length > 1 ? parts : parts[0];
        } else {
          parsedFilters[key] = value;
        }
      }

      const result = await MatchedBlocksService.getChargebackRateChart(userId, parsedFilters);

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      logger.error("Error in getChargebackRateChart controller:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || "Internal server error",
      });
    }
  }

  /**
   * Get matched blocks by user ID
   * Retrieves matched blocks for a specific user with filtering and pagination
   */
  static async getBlocksByUserId(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = req.params;
      const { page = "1", limit = "10", ...filters } = req.query;

      if (!userId) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: "User ID is required",
        });
      }

      // Parse filters
      const parsedFilters: Record<string, any> = {};
      for (const [key, value] of Object.entries(filters)) {
        if (typeof value === 'string') {
          const parts = value
            .split(',')
            .map((v) => v.replace(/\+/g, ' ').trim())
            .filter(Boolean);
          parsedFilters[key] = parts.length > 1 ? parts : parts[0];
        } else {
          parsedFilters[key] = value;
        }
      }

      const result = await MatchedBlocksService.getBlocksByUserId(
        userId,
        parseInt(page as string, 10),
        parseInt(limit as string, 10),
        parsedFilters
      );

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      logger.error("Error in getBlocksByUserId controller:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || "Internal server error",
      });
    }
  }

}

export default MatchedBlocksController;