import ResponseHandler from "@/utils/response-handler.util";
import { Request, Response, NextFunction } from "express";
import { StatusCodes } from "http-status-codes";

import { createLogger } from "@/config/logger";
import { PrismaClient } from "@prisma/client";
import PrismaService from "@/services/prisma.service";
import ShopifyService from "@/services/shopify.service";
import { shopify } from "@/config/shopify";

const logger = createLogger(
  "logs/middleware/auth/error.log",
  "logs/middleware/auth/combined.log"
);

class TestController {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = PrismaService.getInstance().getClient();
  }

  static createGraphQLClient(shop: string, accessToken: string) {
    return new shopify.clients.Graphql({
      session: {
        shop,
        accessToken,
      } as any,
    });
  }

  static createTest = async (req: Request, res: Response, next: NextFunction) => {
    console.log("getTest function called");
    logger.info("getTest function called");

    // Initialize the GraphQL client
    const client = this.createGraphQLClient("bec4c7-4b.myshopify.com", "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");

    const { orderId } = req.body;

    try {
      // Step 1: Get order details using GraphQL
      logger.info(`[Shopify Refund] Step 1: Fetching order details for ${orderId}`);
      const orderQuery = `
        query getOrder($id: ID!) {
          order(id: $id) {
            id
            name
            totalPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
            lineItems(first: 50) {
              edges {
                node {
                  id
                  quantity
                  name
                  refundableQuantity
                }
              }
            }
            transactions(first: 50) {
              id
              kind
              gateway
              amount
              status
            }
            refunds(first: 50) {
              id
              createdAt
              refundLineItems(first: 50) {
                edges {
                  node {
                    quantity
                    lineItem {
                      id
                    }
                  }
                }
              }
            }
          }
        }
      `;

      const orderResponse = await client.request(orderQuery, {
        variables: {
          id: `gid://shopify/Order/${orderId}`
        }
      });

      const orderData = orderResponse.data?.order;
      if (!orderData) {
        throw new Error(`Order ${orderId} not found`);
      }

      logger.info(`[Shopify Refund] Order found: ${orderData.name}, Total: ${orderData.totalPriceSet.shopMoney.amount} ${orderData.totalPriceSet.shopMoney.currencyCode}, Line items: ${orderData.lineItems.edges.length}`);

      // Step 2: Calculate refundable line items
      const refundableLineItems = orderData.lineItems.edges
        .filter((edge: any) => edge.node.refundableQuantity > 0)
        .map((edge: any) => ({
          lineItemId: edge.node.id,
          quantity: edge.node.refundableQuantity,
          restockType: "NO_RESTOCK"
        }));

      if (refundableLineItems.length === 0) {
        logger.info(`[Shopify Refund] No refundable items found for order ${orderId}`);
        throw new Error("No refundable items found - order may already be fully refunded");
      }

      logger.info(`[Shopify Refund] Step 2: Prepared ${refundableLineItems.length} line items for refund`);

      // Step 3: Create refund using GraphQL mutation
      logger.info(`[Shopify Refund] Step 3: Creating refund`);
      const refundMutation = `
        mutation refundCreate($input: RefundInput!) {
          refundCreate(input: $input) {
            refund {
              id
              createdAt
              note
              totalRefundedSet {
                presentmentMoney {
                  amount
                  currencyCode
                }
              }
              refundLineItems(first: 10) {
                edges {
                  node {
                    quantity
                    lineItem {
                      id
                      name
                    }
                  }
                }
              }
            }
            userErrors {
              field
              message
            }
          }
        }
      `;

      const refundResponse = await client.request(refundMutation, {
        variables: {
          input: {
            orderId: `gid://shopify/Order/${orderId}`,
            note: "Automatic refund processed via chargeback system",
            notify: true,
            refundLineItems: refundableLineItems,
            shipping: {
              fullRefund: true
            },
            transactions: [
              {
                orderId: `gid://shopify/Order/${orderId}`,
                kind: "REFUND",
                gateway: orderData.transactions.find((t: any) => t.kind === "SALE")?.gateway || "cash",
                amount: orderData.totalPriceSet.shopMoney.amount,
                parentId: orderData.transactions.find((t: any) => t.kind === "SALE")?.id
              }
            ]
          }
        }
      });

      const refundResult = refundResponse.data?.refundCreate;
      if (!refundResult) {
        throw new Error('Invalid GraphQL response');
      }
      
      if (refundResult.userErrors && refundResult.userErrors.length > 0) {
        const errors = refundResult.userErrors.map((error: any) => `${error.field}: ${error.message}`).join(', ');
        throw new Error(`GraphQL Refund Errors: ${errors}`);
      }

      logger.info(`[Shopify Refund] Refund created successfully for order ${orderId}. Refund ID: ${refundResult.refund.id}`);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Refund created successfully",
        data: refundResult.refund
      });
    } catch (error) {
      logger.error(`[Shopify Refund] Error creating refund for order ${orderId}:`, error);
      return ResponseHandler.sendCatchError(res, error);
    }
  };

  static getTest = async (_req: Request, res: Response) => {
    try {
      return ResponseHandler.sendSuccess(res, { status: StatusCodes.OK, message: "Get test endpoint" });
    } catch (error) {
      return ResponseHandler.sendCatchError(res, error);
    }
  };

  static deleteTest = async (_req: Request, res: Response) => {
    try {
      return ResponseHandler.sendSuccess(res, { status: StatusCodes.OK, message: "Delete test endpoint" });
    } catch (error) {
      return ResponseHandler.sendCatchError(res, error);
    }
  };
}

export default TestController;