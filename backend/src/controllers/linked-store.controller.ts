import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import LinkedStoreService from "../services/linked-store.service";
import ResponseHandler from "../utils/response-handler.util";

class LinkedStoreController {
  /**
   * Get all linked stores for a specific user by ID (all providers)
   */
  public async getLinkedStoresByUserId(req: Request, res: Response): Promise<void> {
    try {
      const { id: userId } = req.params;

      console.log("🔍 [LinkedStoreController.getLinkedStoresByUserId] Request params:", req.params);
      console.log("🔍 [LinkedStoreController.getLinkedStoresByUserId] User ID from params:", userId);

      if (!userId) {
        console.log("❌ [LinkedStoreController.getLinkedStoresByUserId] No user ID found in params");
        ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: "User ID parameter is required",
        });
        return;
      }

      console.log("🔍 [LinkedStoreController.getLinkedStoresByUserId] Calling service with userId:", userId);
      const result = await LinkedStoreService.getLinkedStoresByUserId(userId);
      
      console.log("✅ [LinkedStoreController.getLinkedStoresByUserId] Service result:", result);

      if (result.success) {
        ResponseHandler.sendSuccess(res, {
          status: result.status ?? StatusCodes.OK,
          message: result.message,
          data: result.data,
        });
      } else {
        ResponseHandler.sendError(res, {
          status: result.status ?? StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }
    } catch (error) {
      console.error("❌ [LinkedStoreController.getLinkedStoresByUserId] Error fetching linked stores:", error);
      ResponseHandler.sendCatchError(res, error);
    }
  }

}

export default LinkedStoreController;