import { Request, Response } from 'express';
import { paypalService } from '../services/paypal.service';
import PrismaService from '../services/prisma.service';

export class PayPalController {
  private prisma: any;

  constructor() {
    this.prisma = PrismaService.getInstance().getClient();
  }
  async createAgreement(req: Request, res: Response) {
    try {
      const { linkedStoreId, returnUrl, cancelUrl } = req.body;
      
      const order = await paypalService.createBillingOrder(
        linkedStoreId, 
        returnUrl, 
        cancelUrl
      );
      
      res.json({ success: true, data: order });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  }

  async executeAgreement(req: Request, res: Response) {
    try {
      const { orderId, linkedStoreId } = req.body;
      
      const result = await paypalService.executeAgreement(orderId, linkedStoreId);
      
      res.json({ success: true, data: result });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  }

  async getAgreements(req: Request, res: Response) {
    try {
      const { linkedStoreId } = req.query;
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔍 [Backend] Fetching PayPal agreements for store: ${linkedStoreId}`);
      }
      
      // Get ALL agreements for the store, not just ACTIVE ones
      // Frontend will filter as needed
      const agreements = await this.prisma.payPalAgreement.findMany({
        where: { linkedStoreId: linkedStoreId as string, status: 'ACTIVE' },
        include: {
          payments: {
            orderBy: { createdAt: 'desc' },
            take: 5
          }
        },
        orderBy: { createdAt: 'desc' }
      });
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔍 [Backend] Found ${agreements.length} PayPal agreements`);
        console.log(`🔍 [Backend] Agreement statuses:`, agreements.map((a: any) => ({ id: a.id, status: a.status })));
      }
      
      res.json({ success: true, data: agreements });
    } catch (error: any) {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ [Backend] Error fetching PayPal agreements:', error);
      }
      res.status(500).json({ success: false, error: error.message });
    }
  }

  async cancelAgreement(req: Request, res: Response) {
    try {
      const { agreementId } = req.params;
      
      await paypalService.cancelAgreement(agreementId);
      
      res.json({ success: true, message: 'Agreement cancelled' });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  }

  async processPayment(req: Request, res: Response) {
    try {
      const { agreementId, amount, billId } = req.body;
      
      const result = await paypalService.chargeAgreement(agreementId, amount, billId);
      
      res.json({ success: true, data: result });
    } catch (error: any) {
      res.status(500).json({ success: false, error: error.message });
    }
  }
}

export const paypalController = new PayPalController();