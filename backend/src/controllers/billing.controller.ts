import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import PrismaService from '../services/prisma.service';
import ShopifyService from '../services/shopify.service';
import { BILLING_PLANS, SUBSCRIPTION_STATUS } from '../constants/billing';
import { AuthenticatedUser } from '@/models/user.model';
import logger from '../utils/logger.util';

class BillingController {
  private prisma: PrismaClient;
  private shopifyService: ShopifyService;

  constructor() {
    this.prisma = PrismaService.getInstance().getClient();
    this.shopifyService = new ShopifyService();
  }

  /**
   * Create a new Shopify subscription
   */
  public async createSubscription(req: Request, res: Response): Promise<void> {
    try {
      const { linkedStoreId } = req.body;
      const user: AuthenticatedUser = (req as any).user;

      if (!linkedStoreId) {
        res.status(400).json({ 
          success: false, 
          error: 'linkedStoreId is required' 
        });
        return;
      }

      // Verify store belongs to user
      const linkedStore = await (this.prisma as any).linkedStore.findFirst({
        where: {
          id: linkedStoreId,
          userId: user.id
        }
      });

      if (!linkedStore) {
        res.status(404).json({ 
          success: false, 
          error: 'Store not found or access denied' 
        });
        return;
      }

      const result = await this.shopifyService.createSubscription(linkedStoreId);

      if (result.success) {
        res.status(200).json({
          success: true,
          message: 'Subscription created successfully',
          confirmationUrl: result.confirmationUrl
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error
        });
      }
    } catch (error) {
      logger.error('Error creating subscription:', { error: error instanceof Error ? error.message : String(error) });
      res.status(500).json({ 
        success: false, 
        error: 'Failed to create subscription' 
      });
    }
  }

  /**
   * Get subscription status for a store
   */
  public async getSubscriptionStatus(req: Request, res: Response): Promise<void> {
    try {
      const { linkedStoreId } = req.params;
      const user: AuthenticatedUser = (req as any).user;

      // Verify store belongs to user
      const linkedStore = await (this.prisma as any).linkedStore.findFirst({
        where: {
          id: linkedStoreId,
          userId: user.id
        }
      });

      if (!linkedStore) {
        res.status(404).json({ 
          success: false, 
          error: 'Store not found or access denied' 
        });
        return;
      }

      const subscription = await this.shopifyService.getSubscription(linkedStoreId);

      if (subscription) {
        res.status(200).json({
          success: true,
          data: {
            id: subscription.id,
            planName: subscription.planName,
            status: subscription.status,
            currentUsage: subscription.currentUsage,
            cappedAmount: subscription.cappedAmount,
            currency: subscription.currency,
            activatedAt: subscription.activatedAt,
            lastBilledAt: subscription.lastBilledAt,
            nextBillingAt: subscription.nextBillingAt,
            usageRecords: subscription.usageRecords
          }
        });
      } else {
        res.status(404).json({
          success: false,
          error: 'Subscription not found'
        });
      }
    } catch (error) {
      logger.error('Error getting subscription status:', { error: error instanceof Error ? error.message : String(error) });
      res.status(500).json({ 
        success: false, 
        error: 'Failed to get subscription status' 
      });
    }
  }

  /**
   * Cancel a subscription
   */
  public async cancelSubscription(req: Request, res: Response): Promise<void> {
    try {
      const { linkedStoreId } = req.params;
      const user: AuthenticatedUser = (req as any).user;

      // Verify store belongs to user
      const linkedStore = await (this.prisma as any).linkedStore.findFirst({
        where: {
          id: linkedStoreId,
          userId: user.id
        }
      });

      if (!linkedStore) {
        res.status(404).json({ 
          success: false, 
          error: 'Store not found or access denied' 
        });
        return;
      }

      const result = await this.shopifyService.cancelSubscription(linkedStoreId);

      if (result.success) {
        res.status(200).json({
          success: true,
          message: 'Subscription cancelled successfully'
        });
      } else {
        res.status(400).json({
          success: false,
          error: result.error
        });
      }
    } catch (error) {
      logger.error('Error cancelling subscription:', { error: error instanceof Error ? error.message : String(error) });
      res.status(500).json({ 
        success: false, 
        error: 'Failed to cancel subscription' 
      });
    }
  }

  /**
   * Handle billing callback after subscription approval
   */
  public async handleBillingCallback(req: Request, res: Response): Promise<void> {
    try {
      const { shop, store_id, charge_id } = req.query;

      if (!shop || !store_id || !charge_id) {
        res.status(400).json({ 
          success: false, 
          error: 'Missing required parameters' 
        });
        return;
      }

      // Update subscription status to ACTIVE
      await this.shopifyService.updateSubscriptionStatus(
        store_id as string, 
        SUBSCRIPTION_STATUS.ACTIVE
      );

      // Redirect to frontend success page
      const frontendUrl = process.env.FRONTEND_URL || '';

      // const redirectUrl = new URL(`${frontendUrl}/callback/billing/success`);
      // redirectUrl.searchParams.append('store_id', store_id as string);
      // redirectUrl.searchParams.append('status', 'active');
      
      const redirectUrl = new URL(`${frontendUrl}/dashboard`);

      res.redirect(redirectUrl.toString());
    } catch (error) {
      logger.error('Error handling billing callback:', { error: error instanceof Error ? error.message : String(error) });
      
      // Redirect to error page
      const frontendUrl = process.env.FRONTEND_URL || '';
      const errorUrl = new URL(`${frontendUrl}/callback/billing/error`);
      errorUrl.searchParams.append('error', 'billing_callback_failed');
      
      res.redirect(errorUrl.toString());
    }
  }

  /**
   * Handle Shopify billing webhooks
   */
  public async handleBillingWebhook(req: Request, res: Response): Promise<void> {
    try {
      const webhookData = req.body;
      const { app_subscription } = webhookData;

      if (!app_subscription) {
        res.status(400).json({ 
          success: false, 
          error: 'Invalid webhook data' 
        });
        return;
      }

      // Find subscription by Shopify subscription ID
      const subscription = await (this.prisma as any).shopifySubscription.findUnique({
        where: { shopifySubscriptionId: app_subscription.id }
      });

      if (!subscription) {
        logger.warn(`Webhook received for unknown subscription: ${app_subscription.id}`);
        res.status(404).json({ 
          success: false, 
          error: 'Subscription not found' 
        });
        return;
      }

      // Update subscription status based on webhook
      let status = subscription.status;
      if (app_subscription.status === 'ACTIVE') {
        status = SUBSCRIPTION_STATUS.ACTIVE;
      } else if (app_subscription.status === 'CANCELLED') {
        status = SUBSCRIPTION_STATUS.CANCELLED;
      } else if (app_subscription.status === 'EXPIRED') {
        status = SUBSCRIPTION_STATUS.EXPIRED;
      }

      await this.shopifyService.updateSubscriptionStatus(
        subscription.linkedStoreId,
        status,
        webhookData
      );

      logger.info(`Webhook processed for subscription ${app_subscription.id}: ${status}`);

      res.status(200).json({ 
        success: true, 
        message: 'Webhook processed successfully' 
      });
    } catch (error) {
      logger.error('Error handling billing webhook:', { error: error instanceof Error ? error.message : String(error) });
      res.status(500).json({ 
        success: false, 
        error: 'Failed to process webhook' 
      });
    }
  }

  /**
   * Get billing plans
   */
  public async getBillingPlans(req: Request, res: Response): Promise<void> {
    try {
      res.status(200).json({
        success: true,
        data: BILLING_PLANS
      });
    } catch (error) {
      logger.error('Error getting billing plans:', { error: error instanceof Error ? error.message : String(error) });
      res.status(500).json({ 
        success: false, 
        error: 'Failed to get billing plans' 
      });
    }
  }

  /**
   * Create usage record (for internal usage tracking)
   */
  public async createUsageRecord(req: Request, res: Response): Promise<void> {
    try {
      const { linkedStoreId, description, quantity, price } = req.body;
      const user: AuthenticatedUser = (req as any).user;

      if (!linkedStoreId || !description || !quantity || !price) {
        res.status(400).json({ 
          success: false, 
          error: 'Missing required parameters' 
        });
        return;
      }

      // Verify store belongs to user
      const linkedStore = await (this.prisma as any).linkedStore.findFirst({
        where: {
          id: linkedStoreId,
          userId: user.id
        }
      });

      if (!linkedStore) {
        res.status(404).json({ 
          success: false, 
          error: 'Store not found or access denied' 
        });
        return;
      }

      const usageRecord = await this.shopifyService.createUsageRecord(
        linkedStoreId,
        description,
        quantity,
        price
      );

      res.status(201).json({
        success: true,
        message: 'Usage record created successfully',
        data: usageRecord
      });
    } catch (error) {
      logger.error('Error creating usage record:', { error: error instanceof Error ? error.message : String(error) });
      res.status(500).json({ 
        success: false, 
        error: 'Failed to create usage record' 
      });
    }
  }
}

export default BillingController; 