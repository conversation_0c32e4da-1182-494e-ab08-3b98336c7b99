import { Request, Response } from 'express';
import ShopifyTransactionService from '../services/shopify-transaction.service';
import { createShopifyTransactionSchema } from '../models/shopify-transaction.model';

class ShopifyTransactionController {
  /**
   * Create a new Shopify transaction
   * @param req Express Request
   * @param res Express Response
   */
  static async createTransaction(req: Request, res: Response) {
    try {
      const { error, value } = createShopifyTransactionSchema.validate(req.body);
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const transaction = await ShopifyTransactionService.createTransaction(value);
      return res.status(201).json(transaction);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Create multiple Shopify transactions
   * @param req Express Request
   * @param res Express Response
   */
  static async createManyTransactions(req: Request, res: Response) {
    try {
      if (!Array.isArray(req.body)) {
        return res.status(400).json({ error: 'Request body must be an array of transactions' });
      }
      
      // Validate each transaction
      for (let i = 0; i < req.body.length; i++) {
        const { error } = createShopifyTransactionSchema.validate(req.body[i]);
        if (error) {
          return res.status(400).json({ 
            error: `Transaction at index ${i} is invalid: ${error.details[0].message}` 
          });
        }
      }

      const result = await ShopifyTransactionService.createManyTransactions(req.body);
      return res.status(201).json({ created: result });
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get all Shopify transactions with optional filtering
   * @param req Express Request
   * @param res Express Response
   */
  static async getAllTransactions(req: Request, res: Response) {
    try {
      const query = req.query;
      
      // Handle pagination
      if (req.query.page || req.query.limit) {
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 10;
        
        // Remove pagination params from query
        delete (query as any).page;
        delete (query as any).limit;
        
        const result = await ShopifyTransactionService.getPaginatedTransactions(page, limit, query);
        return res.status(200).json(result);
      }
      
      const transactions = await ShopifyTransactionService.getAllTransactions(query);
      return res.status(200).json(transactions);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get Shopify transactions for a specific linked store
   * @param req Express Request
   * @param res Express Response
   */
  static async getTransactionsByLinkedStoreId(req: Request, res: Response) {
    try {
      const { linkedStoreId } = req.params;
      
      if (!linkedStoreId) {
        return res.status(400).json({ error: 'Linked Store ID is required' });
      }
      
      const transactions = await ShopifyTransactionService.getTransactionsByLinkedStoreId(linkedStoreId);
      return res.status(200).json(transactions);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get Shopify transactions for a specific order
   * @param req Express Request
   * @param res Express Response
   */
  static async getTransactionsByOrderId(req: Request, res: Response) {
    try {
      const { orderId } = req.params;
      
      if (!orderId) {
        return res.status(400).json({ error: 'Order ID is required' });
      }
      
      const transactions = await ShopifyTransactionService.getTransactionsByOrderId(BigInt(orderId));
      return res.status(200).json(transactions);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get Shopify transactions for a specific payout
   * @param req Express Request
   * @param res Express Response
   */
  static async getTransactionsByPayoutId(req: Request, res: Response) {
    try {
      const { payoutId } = req.params;
      
      if (!payoutId) {
        return res.status(400).json({ error: 'Payout ID is required' });
      }
      
      const transactions = await ShopifyTransactionService.getTransactionsByPayoutId(BigInt(payoutId));
      return res.status(200).json(transactions);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get a specific Shopify transaction by ID
   * @param req Express Request
   * @param res Express Response
   */
  static async getTransactionById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ error: 'Transaction ID is required' });
      }
      
      const transaction = await ShopifyTransactionService.getTransactionById(BigInt(id));
      
      if (!transaction) {
        return res.status(404).json({ error: 'Transaction not found' });
      }
      
      return res.status(200).json(transaction);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Update an existing Shopify transaction
   * @param req Express Request
   * @param res Express Response
   */
  static async updateTransaction(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ error: 'Transaction ID is required' });
      }
      
      const transaction = await ShopifyTransactionService.updateTransaction(BigInt(id), req.body);
      return res.status(200).json(transaction);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Delete a Shopify transaction
   * @param req Express Request
   * @param res Express Response
   */
  static async deleteTransaction(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ error: 'Transaction ID is required' });
      }
      
      await ShopifyTransactionService.deleteTransaction(BigInt(id));
      return res.status(204).end();
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }
}

export default ShopifyTransactionController;
