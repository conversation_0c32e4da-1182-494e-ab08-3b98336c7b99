import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import AuthService from "@/services/auth.service";
import User, { AuthenticatedUser } from "@/models/user.model";
import ResponseHandler from "@/utils/response-handler.util";
import { StatusCodes } from "http-status-codes";
import { env } from "@/config/environment";
import { getI18n } from "@/middlewares/language.middleware";
import Convert from "@/utils/convert.util";

class AuthController {
  static async register(req: Request, res: Response, next: NextFunction) {
    try {
      const candidateUser = req.body;
      // Register user with the new schema
      const {
        data: result,
        success,
        message,
        status,
      } = await AuthService.register(candidateUser);

      if (!success) {
        return ResponseHandler.sendError(res, {
          status: status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: status || StatusCodes.CREATED,
        message,
        data: {
          user: {
            id: result.id,
            code: result.code,
            email: result.email,
            phoneNumber: result.phoneNumber,
          },
        },
      });
    } catch (error) {
      return ResponseHandler.sendCatchError(res, error);
    }
  }

  static async create(req: Request, res: Response, next: NextFunction) {
    try {
      const candidateUser = req.body;
      // Create user with the new schema
      const {
        data: result,
        success,
        message,
        status,
      } = await AuthService.register(candidateUser);

      if (!success) {
        return ResponseHandler.sendError(res, {
          status: status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: status || StatusCodes.CREATED,
        message,
        data: {
          user: {
            id: result.id,
            code: result.code,
            email: result.email,
            phoneNumber: result.phoneNumber,
          },
        },
      });
    } catch (error) {
      return ResponseHandler.sendCatchError(res, error);
    }
  }

  static async login(req: Request, res: Response, next: NextFunction) {
    try {
      const { email, password } = req.body;

      // Use the updated auth service login method
      const {
        data: result,
        success,
        message,
        status,
      } = await AuthService.login({ email, password });
      if (!success) {
        return ResponseHandler.sendError(res, {
          status: status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: status || StatusCodes.OK,
        message,
        data: {
          ...result?.user,
          token: {
            accessToken: result?.accessToken,
            refreshToken: result?.refreshToken,
            accessTokenExpiresAt: result?.accessTokenExpiresAt,
            refreshTokenExpiresAt: result?.refreshTokenExpiresAt,
          },
        },
      });
    } catch (error: any) {
      return ResponseHandler.sendCatchError(res, error);
    }
  }

  static async logout(req: Request, res: Response, next: NextFunction) {
    try {
      const { refreshToken } = req.body;

      const { success, message, status } = await AuthService.logout(
        refreshToken
      );
      if (!success) {
        return ResponseHandler.sendError(res, {
          status: status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: message,
        });
      }
      return ResponseHandler.sendSuccess(res, {
        status: status || StatusCodes.OK,
        message: message,
      });
    } catch (error) {
      return ResponseHandler.sendCatchError(res, error);
    }
  }

  static async getAccessToken(req: Request, res: Response, next: NextFunction) {
    const i18n = getI18n();
    try {
      const { refreshToken } = req.body;
      if (!refreshToken) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: i18n.t("auth.token.refreshTokenRequired"),
        });
      }
      if (!(await AuthService.isRefreshTokenValid(refreshToken))) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.UNAUTHORIZED,
          message: i18n.t("auth.token.refreshTokenInvalid"),
        });
      }
      jwt.verify(refreshToken, env.JWT_SECRET, (err: any, user: any) => {
        if (err)
          return ResponseHandler.sendError(res, {
            status: StatusCodes.UNAUTHORIZED,
            message: i18n.t("auth.token.refreshTokenInvalid"),
          });
        const payload = { ...user };
        delete payload.exp;
        const accessTokenResponse = AuthService.generateAccessToken(payload);

        return ResponseHandler.sendSuccess(res, {
          status: StatusCodes.OK,
          message: i18n.t("auth.token.success"),
          data: {
            accessToken: accessTokenResponse.token,
            accessTokenExpiresAt: accessTokenResponse.expiresAt,
          },
        });
      });
    } catch (error) {
      return ResponseHandler.sendCatchError(res, error);
    }
  }

  static changePassword = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const user: AuthenticatedUser = (req as any).user;
      const { newPassword, oldPassword } = req.body;
      const { success, message, status } = await AuthService.changePassword(
        user.id,
        newPassword,
        oldPassword
      );
      if (!success) {
        return ResponseHandler.sendError(res, {
          status: status || StatusCodes.INTERNAL_SERVER_ERROR,
          message,
        });
      }
      return ResponseHandler.sendSuccess(res, {
        status: status || StatusCodes.OK,
        message,
      });
    } catch (error) {
      return ResponseHandler.sendCatchError(res, error);
    }
  };
}

export default AuthController;
