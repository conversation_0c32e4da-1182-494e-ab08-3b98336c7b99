import { Request, Response } from "express";
import ShopifyService from "../services/shopify.service";
import { env } from "../config/environment";
import { AuthenticatedUser } from "../models/user.model";
import { StatusCodes } from "http-status-codes";
import BlockRefundService from "@/services/block-refund.service";
import logger from "@/utils/logger.util";
import { PrismaClient } from "@prisma/client";

class ShopifyController {
  private shopifyService: ShopifyService;
  private blockRefundService: BlockRefundService;
  private prisma: PrismaClient;

  constructor() {
    this.shopifyService = new ShopifyService();
    this.blockRefundService = new BlockRefundService();
    this.prisma = new PrismaClient();
  }

  /**
   * Generate authorization URL for Shopify integration
   */
  public async generateAuthUrl(req: Request, res: Response): Promise<void> {
    try {
      const { shop } = req.query;

      if (!shop || typeof shop !== "string") {
        res.status(400).json({ error: "Shop parameter is required" });
        return;
      }
      const authUrl = this.shopifyService.beginAuth(shop, "/shopify/auth/callback");
      res.status(200).json({ authUrl });
    } catch (error) {
      console.error("Error generating auth URL:", error);
      res.status(500).json({ error: "Failed to generate authorization URL" });
    }
  }

  /**
   * Connect a shop using an access token
   */
  public async connectWithToken(req: Request, res: Response): Promise<void> {
    try {
      const { shop, accessToken } = req.body;
      const user: AuthenticatedUser = (req as any).user;
      const userId = user.id;

      if (!shop || typeof shop !== "string") {
        res.status(400).json({ error: "Shop parameter is required" });
        return;
      }

      if (!accessToken || typeof accessToken !== "string") {
        res.status(400).json({ error: "Access token is required" });
        return;
      }

      if (!userId) {
        res.status(401).json({ error: "User authentication required" });
        return;
      }

      const linkedStore = await this.shopifyService.connectWithToken(
        shop,
        accessToken,
        userId
      );
      res.status(200).json(linkedStore);
    } catch (error) {
      console.error("Error connecting shop with token:", error);
      res.status(500).json({
        error: "Failed to connect shop with token",
        message: (error as Error).message,
      });
    }
  }

  /**
   * Handle OAuth callback from Shopify
   */
  public async handleCallback(req: Request, res: Response): Promise<void> {
    try {
      // Log all received parameters for debugging
      console.log("Shopify callback received:", {
        query: req.query,
        headers: req.headers,
        url: req.url,
        method: req.method
      });

      const { shop, code, hmac, host, timestamp } = req.query;

      // Step 1: Check required parameters with detailed logging
      const missingParams = [];
      if (!shop || typeof shop !== "string") missingParams.push("shop");
      if (!code || typeof code !== "string") missingParams.push("code");
      if (!hmac || typeof hmac !== "string") missingParams.push("hmac");
      
      // Host and timestamp are sometimes optional or in different formats
      const hostParam = host || req.query.embedded;
      const timestampParam = timestamp || req.query.timestamp;

      if (missingParams.length > 0) {
        console.error("Missing required parameters:", missingParams);
        console.error("Received parameters:", req.query);
        throw new Error(`missing_parameters: ${missingParams.join(", ")}`);
      }

      // Step 1: Check isValidShopifyHmac
      if (!this.shopifyService.isValidShopifyHmac(req.query)) {
        throw new Error("hmac_validation_failed");
      }

      // Step 1: Check isTimestampValid (use timestampParam if available)
      if (timestampParam && typeof timestampParam === 'string') {
        if (!this.shopifyService.isTimestampValid(timestampParam)) {
          throw new Error("timestamp_validation_failed");
        }
      }

      // Step 2: Get access token from code and shop
      const callbackResult = await this.shopifyService.handleShopifyCallback(code as string, shop as string);
      const accessToken = callbackResult.session.accessToken || "";

      if (!accessToken) {
        throw new Error("access_token_failed");
      }

      // Step 3: Perform Auth
      const linkedStore = await this.shopifyService.handleAuthCallback(shop as string, accessToken);
      
      console.log("✅ LinkedStore created:", {
        id: linkedStore.id,
        storeName: linkedStore.storeName,
        userId: linkedStore.userId
      });
      
      // Step 4: Check billing status and redirect accordingly
      console.log("🔍 Checking billing status for store:", linkedStore.id);
      let subscription;
      try {
        subscription = await this.shopifyService.getSubscription(linkedStore.id);
        console.log("💰 Subscription status:", subscription ? subscription.status : 'NO_SUBSCRIPTION');
      } catch (error) {
        console.error("❌ Error checking subscription:", error);
        subscription = null;
      }
      
      // Extract store data from the linked store
      const storeData = linkedStore.data as any;
      const shopData = storeData?.shop || {};
      
      // Prepare redirect URL with parameters
      const frontendUrl = env.FRONTEND_URL || '';
      
      // Always redirect to the callback page, let frontend handle billing flow
      console.log("🔀 Redirecting to callback page with billing status:", subscription ? subscription.status : 'NO_SUBSCRIPTION');
      const redirectUrl = new URL(`${frontendUrl}/auth/connect/callback`);
      
      // Add billing status parameter for frontend to handle
      if (subscription && subscription.status === 'ACTIVE') {
        redirectUrl.searchParams.append('billing_status', 'active');
      } else {
        redirectUrl.searchParams.append('billing_status', 'pending');
      }
      
      // Add common query parameters expected by frontend
      redirectUrl.searchParams.append('id', linkedStore.id || ''); // LinkedStore ID
      redirectUrl.searchParams.append('user_id', linkedStore.userId || ''); // User ID for auth
      redirectUrl.searchParams.append('shopify_store_id', shopData.id?.toString() || ''); // Shopify store ID
      redirectUrl.searchParams.append('name', shopData.name || '');
      redirectUrl.searchParams.append('email', shopData.email || '');
      redirectUrl.searchParams.append('domain', storeData?.domain || shop as string);
      redirectUrl.searchParams.append('myshopify_domain', shop as string);
      redirectUrl.searchParams.append('access_token', accessToken)
      
      console.log("🔀 Redirecting to:", redirectUrl.toString());
      
      // Redirect to appropriate URL
      res.redirect(redirectUrl.toString());
    } catch (error) {
      console.error("Error handling callback:", error);
      
      // Extract error key from error message or use default
      const errorKey = error instanceof Error ? error.message : 'oauth_error';
      
      // Redirect to frontend with error key
      const frontendUrl = env.FRONTEND_URL || '';
      const errorUrl = new URL(`${frontendUrl}/auth/connect`);
      errorUrl.searchParams.append('error', errorKey);
      
      res.redirect(errorUrl.toString());
    }
  }


  /**
   * Delete a linked store
   */
  public async deleteLinkedStore(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const user: AuthenticatedUser = (req as any).user;
      const userId = user.id;

      if (!userId) {
        res.status(401).json({ error: "User authentication required" });
        return;
      }

      await this.shopifyService.deleteLinkedStore(id, userId);
      res.status(200).json({ message: "Store unlinked successfully" });
    } catch (error) {
      console.error("Error unlinking store:", error);
      res.status(500).json({ error: "Failed to unlink store" });
    }
  }

  /**
   * Handle OAuth callback from Shopify with userId
   */
  public async handleCallbackWithUserId(
    req: Request,
    res: Response
  ): Promise<void> {
    try {
      const { shop, code, hmac, host, timestamp, userId } = req.body;

      if (
        !shop ||
        !code ||
        !hmac ||
        !host ||
        !timestamp ||
        !userId ||
        typeof shop !== "string" ||
        typeof code !== "string" ||
        typeof hmac !== "string" ||
        typeof host !== "string" ||
        typeof timestamp !== "string" ||
        typeof userId !== "string"
      ) {
        res.status(400).json({
          error: "Missing required parameters",
          message:
            "All required parameters must be provided and have the correct type",
        });
        return;
      }

      if (
        !this.shopifyService.isValidShopifyHmac({
          shop,
          code,
          hmac,
          host,
          timestamp,
        })
      ) {
        res.status(400).json({
          error: "HMAC validation failed",
          message: "Request signature verification failed",
        });
        return;
      }

      if (!this.shopifyService.isTimestampValid(timestamp as string)) {
        res.status(400).json({
          error: "Timestamp validation failed",
          message: "Request has expired or timestamp is invalid",
        });
        return;
      }

      const linkedStore = await this.shopifyService.handleCallbackWithUserId(
        shop,
        code,
        userId
      );

      res.status(200).json(linkedStore);
    } catch (error) {
      console.error("Error handling callback with userId:", error);
      res.status(500).json({
        error: "Failed to handle callback with userId",
        message: (error as Error).message,
      });
    }
  }

  /**
   * Get user's stores
   */
  public async getUserStores(req: Request, res: Response): Promise<void> {
    try {
      const user: AuthenticatedUser = (req as any).user;
      const userId = user.id;

      // Add debugging
      console.log("🔍 [getUserStores] Request headers:", req.headers);
      console.log("🔍 [getUserStores] Authenticated user:", user);
      console.log("🔍 [getUserStores] User ID:", userId);

      if (!userId) {
        console.log("❌ [getUserStores] No user ID found");
        res.status(401).json({ error: "User authentication required" });
        return;
      }

      console.log("🔍 [getUserStores] Calling service with userId:", userId);
      const stores = await this.shopifyService.getUserStores(userId);
      console.log("✅ [getUserStores] Stores found:", stores?.length || 0);
      console.log("✅ [getUserStores] Stores data:", stores);
      
      res.status(200).json(stores);
    } catch (error) {
      console.error("❌ [getUserStores] Error fetching user stores:", error);
      res.status(500).json({
        error: "Failed to fetch stores",
        message: (error as Error).message,
      });
    }
  }

  /**
   * Get store by ID
   */
  public async getStoreById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(400).json({ error: "Store ID is required" });
        return;
      }

      const store = await this.shopifyService.getStoreById(id);
      
      if (!store) {
        res.status(404).json({ error: "Store not found" });
        return;
      }
      
      res.status(200).json(store);
    } catch (error) {
      console.error("Error fetching store by ID:", error);
      res.status(500).json({ error: "Failed to fetch store by ID" });
    }
  }

  /**
   * Refresh store details
   */
  public async refreshStoreDetails(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const storeDetails = await this.shopifyService.getStoreDetails(
        id,
      );
      res.status(200).json(storeDetails);
    } catch (error) {
      console.error("Error refreshing store details:", error);
      res.status(500).json({ error: "Failed to refresh store details" });
    }
  }

  /**
   * Get order transactions
   */
  public async getOrderTransactions(
    req: Request,
    res: Response
  ): Promise<void> {
    try {
      const { storeId, orderId } = req.params;
      const user: AuthenticatedUser = (req as any).user;
      const userId = user.id;

      if (!userId) {
        res.status(401).json({ error: "User authentication required" });
        return;
      }

      const transactions = await this.shopifyService.getOrderTransactions(
        storeId,
        orderId,
        userId
      );
      res.status(200).json(transactions);
    } catch (error) {
      console.error("Error fetching order transactions:", error);
      res.status(500).json({
        error: "Failed to fetch order transactions",
        message: (error as Error).message,
      });
    }
  }

  /**
   * Create a refund for an order
   */
  public async createRefund(req: Request, res: Response): Promise<void> {
    try {
      const { storeId, orderId } = req.params;
      const { blockId} = req.body;

      // Validate required parameters
      if (!blockId) {
        res.status(400).json({
          error: "Bad Request",
          message: "blockId is required",
        });
        return;
      }

      // Create refund with BlockRefund record
      const result = await this.shopifyService.createRefund(
        storeId,
        orderId,
        blockId
      );

      // Check if there was an error
      if (!result.success) {
        res.status(500).json({
          error: "Shopify refund failed",
          message: result.message,
          blockRefund: result.blockRefund,
        });
        return;
      }

      // Success response
      res.status(200).json({
        shopifyRefund: result.data.shopifyRefund,
        blockRefund: result.data.blockRefund,
      });
    } catch (error) {
      console.error("Error creating refund:", error);
      res.status(500).json({
        error: "Failed to create refund",
        message: (error as Error).message,
      });
    }
  }

  /**
   * Cancel an order
   */
  public async cancelOrder(req: Request, res: Response): Promise<void> {
    try {
      const { storeId, orderId } = req.params;

      const result = await this.shopifyService.cancelOrder(
        storeId,
        orderId
      );
      res.status(200).json(result);
    } catch (error) {
      console.error("Error canceling order:", error);
      res.status(500).json({
        error: "Failed to cancel order",
        message: (error as Error).message,
      });
    }
  }

  /**
   * Calculate refund for an order
   */
  public async calculateRefund(req: Request, res: Response): Promise<void> {
    try {
      const { storeId, orderId } = req.params;

      const calculation = await this.shopifyService.calculateRefund(
        storeId,
        orderId
      );
      res.status(200).json(calculation);
    } catch (error) {
      console.error("Error calculating refund:", error);
      res.status(500).json({
        error: "Failed to calculate refund",
        message: (error as Error).message,
      });
    }
  }

  /**
   * Find orders related to a block
   */
  public async findRelatedOrders(req: Request, res: Response): Promise<void> {
    try {
      const user: AuthenticatedUser = (req as any).user;
      const userId = user.id;
      const { storeId } = req.params;
      const blockInfo = req.body;

      if (!userId) {
        res.status(401).json({ error: "User authentication required" });
        return;
      }

      if (!storeId) {
        res.status(400).json({ error: "Store ID is required" });
        return;
      }

      if (!blockInfo) {
        res.status(400).json({ error: "Block information is required" });
        return;
      }

      const result = await this.shopifyService.findOrdersByBlockInfo(
        storeId,
        userId,
        blockInfo
      );

      res.status(200).json(result);
    } catch (error) {
      console.error("Error finding related orders:", error);
      res.status(500).json({
        error: "Failed to find related orders",
        message: (error as Error).message,
      });
    }
  }
}

export default ShopifyController;