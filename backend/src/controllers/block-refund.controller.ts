import { Request, Response, NextFunction } from "express";
import BlockRefundService from "@/services/block-refund.service";
import ResponseHand<PERSON> from "@/utils/response-handler.util";
import { StatusCodes } from "http-status-codes";
import { getI18n } from "@/middlewares/language.middleware";
import logger from "@/utils/logger.util";
import { refundRequestSchema } from "@/models/block-refund.model";

class BlockRefundController {
  private static blockRefundService = new BlockRefundService();

  /**
   * Create a refund
   */
  static async create(req: Request, res: Response, next: NextFunction) {
    try {
      const i18n = getI18n();
      
      // Validate request body
      const { error, value } = refundRequestSchema.validate(req.body);
      if (error) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: error.details[0].message,
        });
      }

      // Convert orderId to BigInt
      const refundRequest = {
        ...value,
        orderId: BigInt(value.orderId),
      };

      logger.info(`Creating refund for block ${refundRequest.blockId}`);

      const refund = await BlockRefundController.blockRefundService.create(refundRequest);

      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.CREATED,
        message: i18n.t("success.refund.created"),
        data: {
          ...refund,
          orderId: refund.orderId.toString(),
          refundId: refund.refundId?.toString(),
          parentId: refund.parentId?.toString(),
          userId: refund.userId?.toString(),
        },
      });
    } catch (error: any) {
      logger.error("Error creating refund:", error);
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.refund.createFailed"),
      });
    }
  }

  /**
   * Get refund by ID
   */
  static async findById(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const i18n = getI18n();

      const refund = await BlockRefundController.blockRefundService.findById(id);

      if (!refund) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.NOT_FOUND,
          message: i18n.t("error.refund.notFound"),
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: i18n.t("success.refund.fetched"),
        data: {
          ...refund,
          orderId: refund.orderId.toString(),
          refundId: refund.refundId?.toString(),
          parentId: refund.parentId?.toString(),
          userId: refund.userId?.toString(),
        },
      });
    } catch (error: any) {
      logger.error("Error fetching refund:", error);
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.refund.fetchFailed"),
      });
    }
  }

  /**
   * Get all refunds with filters
   */
  static async findAll(req: Request, res: Response, next: NextFunction) {
    try {
      const i18n = getI18n();
      const { blockId, storeId, orderId } = req.query;

      const filters: any = {};
      if (blockId) filters.blockId = blockId as string;
      if (storeId) filters.storeId = storeId as string;
      if (orderId) filters.orderId = BigInt(orderId as string);

      const refunds = await BlockRefundController.blockRefundService.findAll(filters);

      const formattedRefunds = refunds.map(refund => ({
        ...refund,
        orderId: refund.orderId.toString(),
        refundId: refund.refundId?.toString(),
        parentId: refund.parentId?.toString(),
        userId: refund.userId?.toString(),
      }));

      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: i18n.t("success.refund.fetched"),
        data: formattedRefunds,
      });
    } catch (error: any) {
      logger.error("Error fetching refunds:", error);
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.refund.fetchFailed"),
      });
    }
  }

  /**
   * Update refund
   */
  static async update(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const i18n = getI18n();

      // Validate request body
      const { error, value } = refundRequestSchema.validate(req.body, { stripUnknown: true });
      if (error) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: error.details[0].message,
        });
      }

      logger.info(`Updating refund ${id}`);

      const refund = await BlockRefundController.blockRefundService.update(id, value);

      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: i18n.t("success.refund.updated"),
        data: {
          ...refund,
          orderId: refund.orderId.toString(),
          refundId: refund.refundId?.toString(),
          parentId: refund.parentId?.toString(),
          userId: refund.userId?.toString(),
        },
      });
    } catch (error: any) {
      logger.error("Error updating refund:", error);
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.refund.updateFailed"),
      });
    }
  }

  /**
   * Delete refund
   */
  static async delete(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const i18n = getI18n();

      logger.info(`Deleting refund ${id}`);

      await BlockRefundController.blockRefundService.delete(id);

      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: i18n.t("success.refund.deleted"),
      });
    } catch (error: any) {
      logger.error("Error deleting refund:", error);
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.refund.deleteFailed"),
      });
    }
  }
}

export default BlockRefundController;