import type { Request, Response } from "express";
import StoreSettingsService from "../services/store-settings.service";
import ResponseHandler from "../utils/response-handler.util";

class StoreSettingsController {
  private storeSettingsService: StoreSettingsService;

  constructor() {
    this.storeSettingsService = new StoreSettingsService();
  }

  /**
   * Get all store settings by store ID
   */
  getByStoreId = async (req: Request, res: Response): Promise<void> => {
    try {
      const { storeId } = req.params;

      if (!storeId) {
        ResponseHandler.sendError(res, {
          status: 400,
          message: "Store ID is required"
        });
        return;
      }



      const storeSettings = await this.storeSettingsService.getByStoreId(storeId);
      ResponseHandler.sendSuccess(res, {
        status: 200,
        message: "Store settings retrieved successfully",
        data: storeSettings
      });
    } catch (error) {
      console.error("Error getting store settings:", error);
      ResponseHandler.sendError(res, {
        status: 500,
        message: (error as Error).message
      });
    }
  };

  /**
   * Get a specific setting by name
   */
  getSetting = async (req: Request, res: Response): Promise<void> => {
    try {
      const { storeId, name } = req.params;

      if (!storeId || !name) {
        ResponseHandler.sendError(res, {
          status: 400,
          message: "Store ID and setting name are required"
        });
        return;
      }

      const setting = await this.storeSettingsService.getSetting(storeId, name);
      
      if (!setting) {
        // Return default value for all_time_goal if not found
        if (name === "all_time_goal") {
          ResponseHandler.sendSuccess(res, {
            status: 200,
            message: "Store setting retrieved successfully",
            data: { value: "1" }
          });
          return;
        }
        
        ResponseHandler.sendError(res, {
          status: 404,
          message: "Setting not found"
        });
        return;
      }

      ResponseHandler.sendSuccess(res, {
        status: 200,
        message: "Store setting retrieved successfully",
        data: setting
      });
    } catch (error) {
      console.error("Error getting store setting:", error);
      ResponseHandler.sendError(res, {
        status: 500,
        message: (error as Error).message
      });
    }
  };

  /**
   * Create or update a single setting
   */
  upsertSetting = async (req: Request, res: Response): Promise<void> => {
    try {
      const { storeId, name } = req.params;
      const { value } = req.body;

      if (!storeId || !name) {
        ResponseHandler.sendError(res, {
          status: 400,
          message: "Store ID and setting name are required"
        });
        return;
      }

      if (value === undefined || value === null) {
        ResponseHandler.sendError(res, {
          status: 400,
          message: "Setting value is required"
        });
        return;
      }

      const setting = await this.storeSettingsService.upsertSetting({
        storeId,
        name,
        value: String(value),
      });

      ResponseHandler.sendSuccess(res, {
        status: 200,
        message: "Store setting saved successfully",
        data: setting
      });
    } catch (error) {
      console.error("Error saving store setting:", error);
      ResponseHandler.sendError(res, {
        status: 500,
        message: (error as Error).message
      });
    }
  };

  /**
   * Update multiple settings at once
   */
  updateSettings = async (req: Request, res: Response): Promise<void> => {
    try {
      const { storeId } = req.params;
      const { settings } = req.body;

      if (!storeId) {
        ResponseHandler.sendError(res, {
          status: 400,
          message: "Store ID is required"
        });
        return;
      }

      if (!settings || typeof settings !== 'object') {
        ResponseHandler.sendError(res, {
          status: 400,
          message: "Settings object is required"
        });
        return;
      }

      const updatedSettings = await this.storeSettingsService.updateSettings(storeId, {
        settings,
      });

      ResponseHandler.sendSuccess(res, {
        status: 200,
        message: "Store settings updated successfully",
        data: updatedSettings
      });
    } catch (error) {
      console.error("Error updating store settings:", error);
      ResponseHandler.sendError(res, {
        status: 500,
        message: (error as Error).message
      });
    }
  };

  /**
   * Delete a specific setting
   */
  deleteSetting = async (req: Request, res: Response): Promise<void> => {
    try {
      const { storeId, name } = req.params;

      if (!storeId || !name) {
        ResponseHandler.sendError(res, {
          status: 400,
          message: "Store ID and setting name are required"
        });
        return;
      }

      await this.storeSettingsService.deleteSetting(storeId, name);

      ResponseHandler.sendSuccess(res, {
        status: 200,
        message: "Store setting deleted successfully"
      });
    } catch (error) {
      console.error("Error deleting store setting:", error);
      ResponseHandler.sendError(res, {
        status: 500,
        message: (error as Error).message
      });
    }
  };

  /**
   * Delete all settings for a store
   */
  deleteAllSettings = async (req: Request, res: Response): Promise<void> => {
    try {
      const { storeId } = req.params;

      if (!storeId) {
        ResponseHandler.sendError(res, {
          status: 400,
          message: "Store ID is required"
        });
        return;
      }

      await this.storeSettingsService.deleteAllSettings(storeId);

      ResponseHandler.sendSuccess(res, {
        status: 200,
        message: "Store settings deleted successfully"
      });
    } catch (error) {
      console.error("Error deleting store settings:", error);
      ResponseHandler.sendError(res, {
        status: 500,
        message: (error as Error).message
      });
    }
  };
}

export default StoreSettingsController; 