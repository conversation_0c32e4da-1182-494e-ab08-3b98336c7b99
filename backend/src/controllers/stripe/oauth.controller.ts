import { Request, Response } from "express";
import { StripeOAuthService } from "../../services/stripe/oauth.service";
import { env } from "../../config/environment";
import logger from "../../utils/logger.util";
import { StatusCodes } from "http-status-codes";

export class StripeOAuthController {
  private stripeOAuthService: StripeOAuthService;

  constructor() {
    this.stripeOAuthService = new StripeOAuthService();
  }

  /**
   * Get OAuth URL for Stripe Connect
   */
  public async getOAuthUrl(req: Request, res: Response): Promise<Response> {
    try {
      const { user_id } = req.query;

      if (
        env.STRIPE_APP_DISTRIBUTION === "public" &&
        env.STRIPE_APP_MARKETPLACE_URL
      ) {
        logger.info("Public Stripe App detected, returning marketplace URL", {
          marketplaceUrl: env.STRIPE_APP_MARKETPLACE_URL,
          user_id,
        });

        let authUrl = env.STRIPE_APP_MARKETPLACE_URL;

        // Add user_id to state parameter if provided
        if (user_id && typeof user_id === "string") {
          const state = JSON.stringify({ user_id });
          const url = new URL(authUrl);
          url.searchParams.append("state", state);
          authUrl = url.toString();
        }

        return res.status(StatusCodes.OK).json({
          success: true,
          authUrl,
        });
      }

      const isAppPlatform = env.STRIPE_APP_DISTRIBUTION === "private";

      let clientId: string;
      let redirectUri: string;
      let baseUrl: string;

      if (isAppPlatform) {
        clientId = env.STRIPE_APP_CLIENT_ID;
        redirectUri =
          env.STRIPE_APP_REDIRECT_URI ||
          `${env.SERVER_PUBLIC_URL}/stripe/oauth/callback`;
        baseUrl = "https://connect.stripe.com/oauth/authorize";

        logger.info("Using Stripe App Platform OAuth configuration (private)", {
          distribution: env.STRIPE_APP_DISTRIBUTION,
          note: "Using standard OAuth URL",
          user_id,
        });
      } else {
        clientId = env.STRIPE_APP_CLIENT_ID;
        redirectUri =
          env.STRIPE_APP_REDIRECT_URI ||
          `${env.SERVER_PUBLIC_URL}/stripe/oauth/callback`;
        baseUrl = "https://connect.stripe.com/oauth/authorize";

        logger.info("Using standard Stripe Connect OAuth configuration", {
          user_id,
        });
      }

      if (!clientId) {
        throw new Error(
          "Stripe Client ID is not configured. Please set STRIPE_CLIENT_ID or STRIPE_APP_CLIENT_ID in environment variables."
        );
      }

      const params = new URLSearchParams({
        response_type: "code",
        client_id: clientId,
        scope: "read_write",
        redirect_uri: redirectUri,
      });

      // Add user_id to state parameter if provided
      if (user_id && typeof user_id === "string") {
        const state = JSON.stringify({ user_id });
        params.append("state", state);
      }

      const stripeOAuthUrl = `${baseUrl}?${params.toString()}`;

      logger.info("Generated Stripe OAuth URL", {
        clientId: clientId.substring(0, 10) + "...",
        redirectUri,
        isAppPlatform,
        user_id,
        hasState: !!user_id,
      });

      return res.status(StatusCodes.OK).json({
        success: true,
        authUrl: stripeOAuthUrl,
      });
    } catch (error: any) {
      logger.error("Error generating Stripe OAuth URL:", {
        error: error.message,
        stack: error.stack,
      });

      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: "Failed to generate Stripe OAuth URL",
        error: error.message,
      });
    }
  }

  /**
   * Handle OAuth callback from Stripe App Platform
   */
  public async handleCallback(
    req: Request,
    res: Response
  ): Promise<Response | void> {
    logger.info("--------------------------------");
    logger.info("Stripe OAuth callback received:", { query: req.query });
    logger.info("--------------------------------");
    const frontendUrl = env.FRONTEND_URL || "http://localhost:3000";

    // Declare userId at function level so it's accessible in catch block
    let userId: string | undefined;

    try {
      const { code, state } = req.query;

      logger.info("Stripe OAuth callback received:", {
        code: code,
        state: state,
      });

      // Validate required parameters
      if (!code || typeof code !== "string") {
        throw new Error("Authorization code is required");
      }

      // Extract user_id from state if present
      if (state && typeof state === "string") {
        try {
          const stateData = JSON.parse(state);
          userId = stateData.user_id;
          logger.info("Extracted user_id from state:", { userId });
        } catch (error: any) {
          logger.warn("Failed to parse state parameter:", {
            state,
            error: error.message,
          });
        }
      } else {
        logger.info("No state parameter provided");
        const redirectPath = "/auth/connect/stripe/marketplace";

        const redirectUrl = new URL(`${frontendUrl}${redirectPath}`);

        // Add common query parameters expected by frontend
        redirectUrl.searchParams.append("code", code || "");

        logger.info("🔀 Redirecting to:", {
          redirectUrl: redirectUrl.toString(),
        });

        return res.redirect(redirectUrl.toString());
      }

      // Exchange code for tokens
      const tokens = await this.stripeOAuthService.exchangeCodeForTokens(
        code as string
      );

      logger.info("Tokens:", { tokens });

      // Get account details
      const accountDetails = await this.stripeOAuthService.getStripeAccountById(
        tokens.stripeUserId as string,
        tokens.accessToken
      );

      logger.info("Account details:", { accountDetails });

      if (!accountDetails) {
        throw new Error("Failed to retrieve Stripe account details");
      }

      // Create or update linked store record (tương tự Shopify)
      const linkedStore = await this.stripeOAuthService.handleAuthCallback(
        tokens,
        accountDetails,
        userId
      );

      logger.info("Stripe account linked successfully:", {
        storeId: linkedStore.id,
        stripeAccountId: accountDetails.id,
        storeName: linkedStore.storeName,
        userId: linkedStore.userId,
      });

      // Determine redirect URL based on whether userId was provided
      let redirectPath: string;
      if (userId) {
        // If userId was provided, this is an integration from settings - redirect to integration callback
        redirectPath = "/callback/integrations/stripe";
      } else {
        // If no userId, this is a new user signup - redirect to auth callback
        redirectPath = "/auth/connect/callback/stripe";
      }

      const redirectUrl = new URL(`${frontendUrl}${redirectPath}`);

      // Generate fallback email based on user ID if accountDetails.email is empty
      const email = accountDetails.email || `${accountDetails.id}@stripe.com`;

      // Add common query parameters expected by frontend
      redirectUrl.searchParams.append("id", linkedStore.id || ""); // LinkedStore ID
      redirectUrl.searchParams.append("user_id", linkedStore.userId || ""); // User ID for auth
      redirectUrl.searchParams.append(
        "stripe_account_id",
        accountDetails.id || ""
      ); // Stripe account ID
      redirectUrl.searchParams.append("name", linkedStore.storeName || "");
      redirectUrl.searchParams.append("email", email);
      redirectUrl.searchParams.append("access_token", tokens.accessToken || "");

      logger.info("🔀 Redirecting to:", {
        redirectUrl: redirectUrl.toString(),
      });

      return res.redirect(redirectUrl.toString());
    } catch (error: any) {
      logger.error("Error handling Stripe OAuth callback:", {
        error: error.message,
        stack: error.stack,
      });

      // Extract error key from error message or use default
      const errorKey = error instanceof Error ? error.message : "oauth_error";

      // Determine error redirect URL based on whether userId was provided
      let errorRedirectPath: string;
      if (userId) {
        // If userId was provided, this was an integration attempt - redirect to integration callback with error
        errorRedirectPath = "/callback/integrations/stripe";
      } else {
        // If no userId, this was a new user signup attempt - redirect to auth connect page
        errorRedirectPath = "/auth/connect";
      }

      const errorUrl = new URL(`${frontendUrl}${errorRedirectPath}`);
      errorUrl.searchParams.append("error", errorKey);

      return res.redirect(errorUrl.toString());
    }
  }

  /**
   * Disconnect Stripe integration
   */
  public async disconnect(req: Request, res: Response): Promise<Response> {
    try {
      const { storeId } = req.params;

      if (!storeId) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          success: false,
          message: "Store ID is required",
        });
      }

      logger.info("Disconnecting Stripe integration:", { storeId });

      // Call service to handle disconnect
      await this.stripeOAuthService.disconnectStore(storeId);

      logger.info("Stripe integration disconnected successfully:", { storeId });

      return res.status(StatusCodes.OK).json({
        success: true,
        message: "Stripe integration disconnected successfully",
      });
    } catch (error: any) {
      logger.error("Error disconnecting Stripe integration:", {
        error: error.message,
        stack: error.stack,
        storeId: req.params.storeId,
      });

      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: "Failed to disconnect Stripe integration",
        error: error.message,
      });
    }
  }
}

export default StripeOAuthController;
