import { Request, Response } from "express";
import StripeStoreService from "../../services/stripe/store.service";
import ResponseHandler from "../../utils/response-handler.util";
import { StatusCodes } from "http-status-codes";
import logger from "../../utils/logger.util";

export class StripeStoreController {
  private stripeStoreService: StripeStoreService;

  constructor() {
    this.stripeStoreService = new StripeStoreService();
  }

  /**
   * Get store by ID
   */
  public async getStoreById(req: Request, res: Response) {
    try {
      const { id } = req.params;

      if (!id) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: "Store ID is required"
        });
      }

      const store = await this.stripeStoreService.getStoreById(id);
      
      if (!store) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.NOT_FOUND,
          message: "Store not found"
        });
      }
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Store retrieved successfully",
        data: store
      });
    } catch (error: any) {
      logger.error(`Error fetching store by ID: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Failed to fetch store by ID"
      });
    }
  }

  /**
   * Refresh store details
   */
  public async refreshStoreDetails(req: Request, res: Response) {
    try {
      const { id } = req.params;

      if (!id) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: "Store ID is required"
        });
      }

      const refreshedStore = await this.stripeStoreService.getStoreDetails(id);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Store details refreshed successfully",
        data: refreshedStore
      });
    } catch (error: any) {
      logger.error(`Error refreshing store details: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Failed to refresh store details"
      });
    }
  }

  /**
   * Disconnect store
   */
  public async disconnectStore(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const user = (req as any).user;

      if (!id) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: "Store ID is required"
        });
      }

      if (!user) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.UNAUTHORIZED,
          message: "User authentication required"
        });
      }

      await this.stripeStoreService.deleteLinkedStore(id, user.id);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Store disconnected successfully"
      });
    } catch (error: any) {
      logger.error(`Error disconnecting store: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Failed to disconnect store"
      });
    }
  }
}

export default StripeStoreController;