import { Request, Response, NextFunction } from "express";
import BillService from "../services/bill.service";
import ResponseHand<PERSON> from "../utils/response-handler.util";
import { StatusCodes } from "http-status-codes";
import logger from "../utils/logger.util";

class BillController {
  /**
   * Create a new bill
   */
  static async createBill(req: Request, res: Response, next: NextFunction) {
    try {
      const createBillData = req.body;
      const bill = await BillService.createBill(createBillData);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.CREATED,
        message: "Bill created successfully",
        data: bill
      });
    } catch (error: any) {
      logger.error(`Error creating bill: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error creating bill"
      });
    }
  }

  /**
   * Get all bills
   */
  static async getAllBills(req: Request, res: Response, next: NextFunction) {
    try {
      const bills = await BillService.getAllBills();
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Bills retrieved successfully",
        data: bills
      });
    } catch (error: any) {
      logger.error(`Error getting bills: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error getting bills"
      });
    }
  }

  /**
   * Get bills by store ID
   */
  static async getBillsByStoreId(req: Request, res: Response, next: NextFunction) {
    try {
      const storeId = req.params.storeId;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      
      const bills = await BillService.getBillsByStoreId(storeId, page, limit);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Bills retrieved successfully",
        data: bills
      });
    } catch (error: any) {
      logger.error(`Error getting bills by store ID: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error getting bills"
      });
    }
  }

  /**
   * Get bill by ID
   */
  static async getBill(req: Request, res: Response, next: NextFunction) {
    try {
      const id = req.params.id;
      const bill = await BillService.getBillById(id);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Bill retrieved successfully",
        data: bill
      });
    } catch (error: any) {
      logger.error(`Error getting bill: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.NOT_FOUND,
        message: error.message || "Bill not found"
      });
    }
  }

  /**
   * Update bill
   */
  static async updateBill(req: Request, res: Response, next: NextFunction) {
    try {
      const id = req.params.id;
      const updateBillData = req.body;
      const bill = await BillService.updateBill(id, updateBillData);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Bill updated successfully",
        data: bill
      });
    } catch (error: any) {
      logger.error(`Error updating bill: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error updating bill"
      });
    }
  }

  /**
   * Delete bill
   */
  static async deleteBill(req: Request, res: Response, next: NextFunction) {
    try {
      const id = req.params.id;
      await BillService.deleteBill(id);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Bill deleted successfully"
      });
    } catch (error: any) {
      logger.error(`Error deleting bill: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.NOT_FOUND,
        message: error.message || "Error deleting bill"
      });
    }
  }

  /**
   * Generate payment link for bill
   */
  static async generatePaymentLink(req: Request, res: Response, next: NextFunction) {
    try {
      const billId = req.params.billId;
      const { stripeAccountId, amount, currency, description } = req.query;
      
      // Validate required query parameters
      if (!stripeAccountId || !amount) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: "Missing required query parameters: stripeAccountId and amount"
        });
      }
      
      const paymentUrl = await BillService.generatePaymentLink({
        stripeAccountId: stripeAccountId as string,
        amount: parseFloat(amount as string),
        billId,
        currency: currency as string || 'USD',
        description: description as string
      });
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Payment link generated successfully",
        data: { url: paymentUrl }
      });
    } catch (error: any) {
      logger.error(`Error generating payment link: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error generating payment link"
      });
    }
  }

  /**
   * Get payment histories for a specific bill
   */
  static async getBillPaymentHistories(req: Request, res: Response, next: NextFunction) {
    try {
      const billId = req.params.billId;
      
      // Get bill with payment histories
      const bill = await BillService.getBillById(billId);
      
      if (!bill) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.NOT_FOUND,
          message: "Bill not found"
        });
      }
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Bill payment histories retrieved successfully",
        data: {
          bill,
        }
      });
    } catch (error: any) {
      logger.error(`Error getting bill payment histories: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error getting payment histories"
      });
    }
  }
}

export default BillController;
export { BillController };