import { Request, Response, NextFunction } from "express";
import {
  statisticsService,
  GetStatsParams,
} from "@/services/statistics.service";
import ResponseHandler from "@/utils/response-handler.util";
import { StatusCodes } from "http-status-codes";
import { getI18n } from "@/middlewares/language.middleware";
import logger from "@/utils/logger.util";
import { AuthenticatedUser } from "@/models/user.model";

class StatisticsController {
  /**
   * Get top chargeback countries for authenticated user
   */
  static async getTopChargebackCountries(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const user = (req as any).user as AuthenticatedUser;
      if (!user) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.UNAUTHORIZED,
          message: "Authentication required",
        });
      }

      const { limit, storeId, provider } = req.query;

      // Validate and parse parameters
      const params: GetStatsParams = {};

      if (limit) {
        const parsedLimit = parseInt(limit as string);
        if (isNaN(parsedLimit) || parsedLimit < 1 || parsedLimit > 50) {
          return ResponseHandler.sendError(res, {
            status: StatusCodes.BAD_REQUEST,
            message: "Limit must be a number between 1 and 50",
          });
        }
        params.limit = parsedLimit;
      }

      if (storeId) {
        params.storeId = storeId as string;
      }

      if (provider) {
        if (!["shopify", "stripe"].includes(provider as string)) {
          return ResponseHandler.sendError(res, {
            status: StatusCodes.BAD_REQUEST,
            message: "Provider must be either 'shopify' or 'stripe'",
          });
        }
        params.provider = provider as string;
      }

      const result = await statisticsService.getTopChargebackCountries(
        user.id,
        params
      );

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      logger.error("Error in getTopChargebackCountries:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.statistics.internalError"),
      });
    }
  }

  /**
   * Get top chargeback reasons for authenticated user
   */
  static async getTopChargebackReasons(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const user = (req as any).user as AuthenticatedUser;
      if (!user) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.UNAUTHORIZED,
          message: "Authentication required",
        });
      }

      const { limit, storeId, provider } = req.query;

      // Validate and parse parameters
      const params: GetStatsParams = {};

      if (limit) {
        const parsedLimit = parseInt(limit as string);
        if (isNaN(parsedLimit) || parsedLimit < 1 || parsedLimit > 50) {
          return ResponseHandler.sendError(res, {
            status: StatusCodes.BAD_REQUEST,
            message: "Limit must be a number between 1 and 50",
          });
        }
        params.limit = parsedLimit;
      }

      if (storeId) {
        params.storeId = storeId as string;
      }

      if (provider) {
        if (!["shopify", "stripe"].includes(provider as string)) {
          return ResponseHandler.sendError(res, {
            status: StatusCodes.BAD_REQUEST,
            message: "Provider must be either 'shopify' or 'stripe'",
          });
        }
        params.provider = provider as string;
      }

      const result = await statisticsService.getTopChargebackReasons(
        user.id,
        params
      );

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      logger.error("Error in getTopChargebackReasons:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.statistics.internalError"),
      });
    }
  }

  /**
   * Get combined chargeback statistics overview for authenticated user
   */
  static async getChargebackStatsOverview(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const user = (req as any).user as AuthenticatedUser;
      if (!user) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.UNAUTHORIZED,
          message: "Authentication required",
        });
      }

      const { limit, storeId, provider } = req.query;

      // Validate and parse parameters
      const params: GetStatsParams = {};

      if (limit) {
        const parsedLimit = parseInt(limit as string);
        if (isNaN(parsedLimit) || parsedLimit < 1 || parsedLimit > 50) {
          return ResponseHandler.sendError(res, {
            status: StatusCodes.BAD_REQUEST,
            message: "Limit must be a number between 1 and 50",
          });
        }
        params.limit = parsedLimit;
      }

      if (storeId) {
        params.storeId = storeId as string;
      }

      if (provider) {
        if (!["shopify", "stripe"].includes(provider as string)) {
          return ResponseHandler.sendError(res, {
            status: StatusCodes.BAD_REQUEST,
            message: "Provider must be either 'shopify' or 'stripe'",
          });
        }
        params.provider = provider as string;
      }

      const result = await statisticsService.getChargebackStatsOverview(
        user.id,
        params
      );

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      logger.error("Error in getChargebackStatsOverview:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.statistics.internalError"),
      });
    }
  }

  /**
   * Reset statistics for authenticated user (for testing purposes)
   */
  static async resetUserStatistics(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const user = (req as any).user as AuthenticatedUser;
      if (!user) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.UNAUTHORIZED,
          message: "Authentication required",
        });
      }

      const { storeId } = req.query;

      const result = await statisticsService.resetUserStatistics(
        user.id,
        storeId as string | undefined
      );

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      logger.error("Error in resetUserStatistics:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.statistics.internalError"),
      });
    }
  }
}

export const statisticsController = StatisticsController;
