import { Request, Response, NextFunction } from "express";
import BlockService from "@/services/block.service";
import ResponseHand<PERSON> from "@/utils/response-handler.util";
import { StatusCodes } from "http-status-codes";
import { getI18n } from "@/middlewares/language.middleware";
import logger from "@/utils/logger.util";

class BlockController {

  /**
   * Process Ethoca block webhook
   * Handles incoming Ethoca block notifications
   */
  static async processEthocaBlock(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const blockData = req.body;
      logger.info("Ethoca block data received", blockData);

      const result = await BlockService.processEthocaBlock(blockData);

      if (!result.success) {
        logger.error(`Ethoca block processing failed: ${result.message}`);
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      const block = result.data;

      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: result.message,
        data: {
          id: block.id,
          alertId: block.alertId
        },
      });
    } catch (error: any) {
      logger.error("Ethoca block processing error:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || "Internal server error processing Ethoca block",
      });
    }
  }

  /**
   * Process RDR block webhook
   * Handles incoming RDR block notifications
   */
  static async processRdrBlock(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const blockData = req.body;
      logger.info("RDR block data received", blockData);

      const result = await BlockService.processRdrBlock(blockData);

      if (!result.success) {
        logger.error(`RDR block processing failed: ${result.message}`);
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      const block = result.data;

      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: result.message,
        data: {
          id: block.id,
          alertId: block.alertId
        },
      });
    } catch (error: any) {
      logger.error("RDR block processing error:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || "Internal server error processing RDR block",
      });
    }
  }

  /**
   * Send block feedback
   * Sends feedback about a block
   */
  static async sendBlockFeedback(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { id } = req.params;
      const feedbackData = req.body;
      const result = await BlockService.sendBlockFeedback(id, feedbackData);

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.block.internalError"),
      });
    }
  }

  /**
   * List blocks
   * Retrieves a paginated list of blocks
   */
  static async getBlocks(req: Request, res: Response, next: NextFunction) {
    try {
      const { page = "1", limit = "10", ...filters } = req.query;

      const parsedFilters: Record<string, any> = {};
      for (const [key, value] of Object.entries(filters)) {
        if (typeof value === 'string') {
          const parts = value
            .split(',')
            .map((v) => v.replace(/\+/g, ' ').trim())
            .filter(Boolean);
          parsedFilters[key] = parts.length > 1 ? parts : parts[0];
        } else {
          parsedFilters[key] = value;
        }
      }

      // Set default linkedStoreId to 99999 if not provided
      if (!parsedFilters.linkedStoreId) {
        parsedFilters.linkedStoreId = '99999';
      }

      const result = await BlockService.getBlocks(
        parseInt(page as string, 10),
        parseInt(limit as string, 10),
        parsedFilters as Record<string, any>
      );

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.block.internalError"),
      });
    }
  }

  /**
   * Get a single block by ID
   */
  static async getBlock(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const result = await BlockService.getBlock(id);

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.block.internalError"),
      });
    }
  }

  /**
   * Delete a block
   */
  static async deleteBlock(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const result = await BlockService.deleteBlock(id);

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.block.internalError"),
      });
    }
  }

  /**
   * Find matched orders for a block
   * Finds orders that match the block data based on amount, currency, and card number
   */
  static async findMatchedOrder(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    try {
      const { id, linkedStoreId } = req.params;
      
      if (!id) {
        const i18n = getI18n();
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: i18n.t("error.block.invalidId") || "Block ID is required",
        });
      }

      if (!linkedStoreId) {
        const i18n = getI18n();
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: i18n.t("error.store.invalidId") || "LinkedStore ID is required",
        });
      }

      const result = await BlockService.findMatchedOrder(id, linkedStoreId);

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      logger.error("Error in findMatchedOrder controller:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.block.internalError") || "Internal server error",
      });
    }
  }

  /**
   * Clear block cache
   * Clears specific cache or all block-related caches
   */
  static async clearCache(req: Request, res: Response, next: NextFunction) {
    try {
      const { type } = req.query;
      const i18n = getI18n();
      
      if (type === 'chart') {
        await BlockService.clearSpecificCache('BLOCK_CHARGEBACK_CHART');
        return ResponseHandler.sendSuccess(res, {
          status: StatusCodes.OK,
          message: "Chart cache cleared successfully",
        });
      } else if (type === 'grouped') {
        await BlockService.clearSpecificCache('BLOCK_GROUPED');
        return ResponseHandler.sendSuccess(res, {
          status: StatusCodes.OK,
          message: "Grouped blocks cache cleared successfully",
        });
      } else {
        // Clear all block caches
        await BlockService.clearSpecificCache('BLOCK_CHARGEBACK_CHART');
        await BlockService.clearSpecificCache('BLOCK_GROUPED');
        return ResponseHandler.sendSuccess(res, {
          status: StatusCodes.OK,
          message: "All block caches cleared successfully",
        });
      }
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || "Failed to clear cache",
      });
    }
  }
  
  /**
   * Get chargeback rate chart data
   * Retrieves daily actual chargeback rates for the current month
   */
  static async getChargebackRateChart(req: Request, res: Response, next: NextFunction) {
    try {
      const { ...filters } = req.query;

      const parsedFilters: Record<string, any> = {};
      for (const [key, value] of Object.entries(filters)) {
        if (typeof value === 'string') {
          const parts = value
            .split(',')
            .map((v) => v.replace(/\+/g, ' ').trim())
            .filter(Boolean);
          parsedFilters[key] = parts.length > 1 ? parts : parts[0];
        } else {
          parsedFilters[key] = value;
        }
      }

      // Set default linkedStoreId to 99999 if not provided
      if (!parsedFilters.linkedStoreId) {
        parsedFilters.linkedStoreId = '99999';
      }

      const result = await BlockService.getChargebackRateChart(parsedFilters);

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t("error.block.internalError"),
      });
    }
  }
}

export default BlockController; 