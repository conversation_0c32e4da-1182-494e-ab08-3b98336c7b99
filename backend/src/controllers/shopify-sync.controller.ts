import { Request, Response } from "express";
import { getShopifyJobProcessor } from "../services/shopify-job-processor.service";
import { AuthenticatedUser } from "../models/user.model";
import { StatusCodes } from "http-status-codes";

class ShopifySyncController {
  private shopifyJobProcessor = getShopifyJobProcessor();

  public async triggerStoreSync(req: Request, res: Response): Promise<void> {
    try {
      const { storeId } = req.params;
      const { syncTypes, forceSync } = req.body;
      const user: AuthenticatedUser = (req as any).user;

      if (!user?.id) {
        res.status(StatusCodes.UNAUTHORIZED).json({ 
          error: "User authentication required" 
        });
        return;
      }

      if (!storeId) {
        res.status(StatusCodes.BAD_REQUEST).json({ 
          error: "Store ID is required" 
        });
        return;
      }

      let jobId: string;
      
      if (forceSync) {
        jobId = await this.shopifyJobProcessor.scheduleForceSyncByStoreId(storeId, syncTypes);
      } else {
        jobId = await this.shopifyJobProcessor.scheduleRegularSyncByStoreId(storeId, syncTypes);
      }

      res.status(StatusCodes.OK).json({
        message: "Sync job scheduled successfully",
        jobId,
        storeId,
        syncTypes: syncTypes || ['orders', 'payouts', 'disputes', 'transactions'],
        forceSync: forceSync || false
      });
    } catch (error) {
      console.error("Error triggering store sync:", error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        error: "Failed to trigger store sync",
        message: (error as Error).message,
      });
    }
  }

  public async triggerBulkSync(req: Request, res: Response): Promise<void> {
    try {
      const { syncTypes } = req.body;
      const user: AuthenticatedUser = (req as any).user;

      if (!user?.id) {
        res.status(StatusCodes.UNAUTHORIZED).json({ 
          error: "User authentication required" 
        });
        return;
      }

      const jobId = await this.shopifyJobProcessor.scheduleBulkSync(syncTypes);

      res.status(StatusCodes.OK).json({
        message: "Bulk sync job scheduled successfully",
        jobId,
        syncTypes: syncTypes || ['orders', 'payouts', 'disputes', 'transactions']
      });
    } catch (error) {
      console.error("Error triggering bulk sync:", error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        error: "Failed to trigger bulk sync",
        message: (error as Error).message,
      });
    }
  }

  public async getSyncStatus(req: Request, res: Response): Promise<void> {
    try {
      const user: AuthenticatedUser = (req as any).user;

      if (!user?.id) {
        res.status(StatusCodes.UNAUTHORIZED).json({ 
          error: "User authentication required" 
        });
        return;
      }

      const stats = this.shopifyJobProcessor.getJobStats();
      const jobs = this.shopifyJobProcessor.getShopifyJobs();

      res.status(StatusCodes.OK).json({
        stats,
        recentJobs: jobs.slice(-10),
        totalJobs: jobs.length
      });
    } catch (error) {
      console.error("Error getting sync status:", error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        error: "Failed to get sync status",
        message: (error as Error).message,
      });
    }
  }

  public async getJobDetails(req: Request, res: Response): Promise<void> {
    try {
      const { jobId } = req.params;
      const user: AuthenticatedUser = (req as any).user;

      if (!user?.id) {
        res.status(StatusCodes.UNAUTHORIZED).json({ 
          error: "User authentication required" 
        });
        return;
      }

      if (!jobId) {
        res.status(StatusCodes.BAD_REQUEST).json({ 
          error: "Job ID is required" 
        });
        return;
      }

      const jobs = this.shopifyJobProcessor.getShopifyJobs();
      const job = jobs.find(j => j.id === jobId);

      if (!job) {
        res.status(StatusCodes.NOT_FOUND).json({ 
          error: "Job not found" 
        });
        return;
      }

      res.status(StatusCodes.OK).json(job);
    } catch (error) {
      console.error("Error getting job details:", error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        error: "Failed to get job details",
        message: (error as Error).message,
      });
    }
  }

  public async clearJobs(req: Request, res: Response): Promise<void> {
    try {
      const { type = 'completed' } = req.body;
      const user: AuthenticatedUser = (req as any).user;

      if (!user?.id) {
        res.status(StatusCodes.UNAUTHORIZED).json({ 
          error: "User authentication required" 
        });
        return;
      }

      let clearedCount = 0;
      
      if (type === 'completed') {
        clearedCount = this.shopifyJobProcessor.clearCompletedJobs();
      } else if (type === 'failed') {
        clearedCount = this.shopifyJobProcessor.clearFailedJobs();
      } else {
        res.status(StatusCodes.BAD_REQUEST).json({ 
          error: "Invalid type. Use 'completed' or 'failed'" 
        });
        return;
      }

      res.status(StatusCodes.OK).json({
        message: `Cleared ${clearedCount} ${type} jobs`,
        clearedCount,
        type
      });
    } catch (error) {
      console.error("Error clearing jobs:", error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        error: "Failed to clear jobs",
        message: (error as Error).message,
      });
    }
  }
}

export default ShopifySyncController;