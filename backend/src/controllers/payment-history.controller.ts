import { Request, Response, NextFunction } from "express";
import PaymentHistoryService from "../services/payment-history.service";
import ResponseHandler from "../utils/response-handler.util";
import { StatusCodes } from "http-status-codes";
import logger from "../utils/logger.util";

class PaymentHistoryController {
  /**
   * Create a new payment history record
   */
  static async createPaymentHistory(req: Request, res: Response, next: NextFunction) {
    try {
      const createPaymentHistoryData = req.body;
      const paymentHistory = await PaymentHistoryService.createPaymentHistory(createPaymentHistoryData);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.CREATED,
        message: "Payment history created successfully",
        data: paymentHistory
      });
    } catch (error: any) {
      logger.error(`Error creating payment history: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error creating payment history"
      });
    }
  }

  /**
   * Get all payment history
   */
  static async getAllPaymentHistory(req: Request, res: Response, next: NextFunction) {
    try {
      const paymentHistory = await PaymentHistoryService.getAllPaymentHistory();
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Payment history retrieved successfully",
        data: paymentHistory
      });
    } catch (error: any) {
      logger.error(`Error getting payment history: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error getting payment history"
      });
    }
  }

  /**
   * Get payment history by store ID
   */
  static async getPaymentHistoryByStoreId(req: Request, res: Response, next: NextFunction) {
    try {
      const storeId = req.params.storeId;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      
      const paymentHistory = await PaymentHistoryService.getPaymentHistoryByStoreId(storeId, page, limit);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Payment history retrieved successfully",
        data: paymentHistory
      });
    } catch (error: any) {
      logger.error(`Error getting payment history: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error getting payment history"
      });
    }
  }

  /**
   * Get payment history by ID
   */
  static async getPaymentHistory(req: Request, res: Response, next: NextFunction) {
    try {
      const id = req.params.id;
      const paymentHistory = await PaymentHistoryService.getPaymentHistoryById(id);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Payment history retrieved successfully",
        data: paymentHistory
      });
    } catch (error: any) {
      logger.error(`Error getting payment history: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.NOT_FOUND,
        message: error.message || "Payment history not found"
      });
    }
  }

  /**
   * Update payment history
   */
  static async updatePaymentHistory(req: Request, res: Response, next: NextFunction) {
    try {
      const id = req.params.id;
      const updatePaymentHistoryData = req.body;
      const paymentHistory = await PaymentHistoryService.updatePaymentHistory(id, updatePaymentHistoryData);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Payment history updated successfully",
        data: paymentHistory
      });
    } catch (error: any) {
      logger.error(`Error updating payment history: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error updating payment history"
      });
    }
  }

  /**
   * Delete payment history
   */
  static async deletePaymentHistory(req: Request, res: Response, next: NextFunction) {
    try {
      const id = req.params.id;
      await PaymentHistoryService.deletePaymentHistory(id);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Payment history deleted successfully"
      });
    } catch (error: any) {
      logger.error(`Error deleting payment history: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.NOT_FOUND,
        message: error.message || "Error deleting payment history"
      });
    }
  }
}

export default PaymentHistoryController;
export { PaymentHistoryController };