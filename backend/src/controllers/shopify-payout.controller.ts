import { Request, Response } from 'express';
import ShopifyPayoutService from '../services/shopify-payout.service';
import { createShopifyPayoutSchema } from '../models/shopify-payout.model';

class ShopifyPayoutController {
  /**
   * Create a new Shopify payout
   * @param req Express Request
   * @param res Express Response
   */
  static async createPayout(req: Request, res: Response) {
    try {
      const { error, value } = createShopifyPayoutSchema.validate(req.body);
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const payout = await ShopifyPayoutService.createPayout(value);
      return res.status(201).json(payout);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Create multiple Shopify payouts
   * @param req Express Request
   * @param res Express Response
   */
  static async createManyPayouts(req: Request, res: Response) {
    try {
      if (!Array.isArray(req.body)) {
        return res.status(400).json({ error: 'Request body must be an array of payouts' });
      }
      
      // Validate each payout
      for (let i = 0; i < req.body.length; i++) {
        const { error } = createShopifyPayoutSchema.validate(req.body[i]);
        if (error) {
          return res.status(400).json({ 
            error: `Payout at index ${i} is invalid: ${error.details[0].message}` 
          });
        }
      }

      const result = await ShopifyPayoutService.createManyPayouts(req.body);
      return res.status(201).json({ created: result });
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get all Shopify payouts with optional filtering
   * @param req Express Request
   * @param res Express Response
   */
  static async getAllPayouts(req: Request, res: Response) {
    try {
      const query = req.query;
      
      // Parse date ranges if provided
      if (query.dateFrom) {
        (query as any).dateFrom = new Date(query.dateFrom as string);
      }
      
      if (query.dateTo) {
        (query as any).dateTo = new Date(query.dateTo as string);
      }
      
      // Handle pagination
      if (req.query.page || req.query.limit) {
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 10;
        
        // Remove pagination params from query
        delete (query as any).page;
        delete (query as any).limit;
        
        const result = await ShopifyPayoutService.getPaginatedPayouts(page, limit, query);
        return res.status(200).json(result);
      }
      
      const payouts = await ShopifyPayoutService.getAllPayouts(query);
      return res.status(200).json(payouts);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get Shopify payouts for a specific linked store
   * @param req Express Request
   * @param res Express Response
   */
  static async getPayoutsByLinkedStoreId(req: Request, res: Response) {
    try {
      const { linkedStoreId } = req.params;
      
      if (!linkedStoreId) {
        return res.status(400).json({ error: 'Linked Store ID is required' });
      }
      
      const payouts = await ShopifyPayoutService.getPayoutsByLinkedStoreId(linkedStoreId);
      return res.status(200).json(payouts);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get a specific Shopify payout by ID
   * @param req Express Request
   * @param res Express Response
   */
  static async getPayoutById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ error: 'Payout ID is required' });
      }
      
      const payout = await ShopifyPayoutService.getPayoutById(BigInt(id));
      
      if (!payout) {
        return res.status(404).json({ error: 'Payout not found' });
      }
      
      return res.status(200).json(payout);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Update an existing Shopify payout
   * @param req Express Request
   * @param res Express Response
   */
  static async updatePayout(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ error: 'Payout ID is required' });
      }
      
      const payout = await ShopifyPayoutService.updatePayout(BigInt(id), req.body);
      return res.status(200).json(payout);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Delete a Shopify payout
   * @param req Express Request
   * @param res Express Response
   */
  static async deletePayout(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ error: 'Payout ID is required' });
      }
      
      await ShopifyPayoutService.deletePayout(BigInt(id));
      return res.status(204).end();
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }
}

export default ShopifyPayoutController;
