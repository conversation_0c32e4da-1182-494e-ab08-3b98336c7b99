import { Request, Response } from 'express';
import ShopifyOrderService from '../services/shopify-order.service';
import { createShopifyOrderSchema } from '../models/shopify-order.model';

class ShopifyOrderController {
  /**
   * Create a new Shopify order
   * @param req Express Request
   * @param res Express Response
   */
  static async createOrder(req: Request, res: Response) {
    try {
      const { error, value } = createShopifyOrderSchema.validate(req.body);
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const order = await ShopifyOrderService.createOrder(value);
      return res.status(201).json(order);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }
  
  /**
   * Create multiple Shopify orders
   * @param req Express Request
   * @param res Express Response
   */
  static async createManyOrders(req: Request, res: Response) {
    try {
      if (!Array.isArray(req.body)) {
        return res.status(400).json({ error: 'Request body must be an array of orders' });
      }
      
      // Validate each order
      for (let i = 0; i < req.body.length; i++) {
        const { error } = createShopifyOrderSchema.validate(req.body[i]);
        if (error) {
          return res.status(400).json({ 
            error: `Order at index ${i} is invalid: ${error.details[0].message}` 
          });
        }
      }

      const result = await ShopifyOrderService.createManyOrders(req.body);
      return res.status(201).json({ created: result });
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get all Shopify orders with optional filtering
   * @param req Express Request
   * @param res Express Response
   */
  static async getAllOrders(req: Request, res: Response) {
    try {
      const query = req.query;
      
      // Handle pagination
      if (req.query.page || req.query.limit) {
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 10;
        
        // Remove pagination params from query
        delete (query as any).page;
        delete (query as any).limit;
        
        const result = await ShopifyOrderService.getPaginatedOrders(page, limit, query);
        return res.status(200).json(result);
      }
      
      const orders = await ShopifyOrderService.getAllOrders(query);
      return res.status(200).json(orders);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get Shopify orders for a specific linked store
   * @param req Express Request
   * @param res Express Response
   */
  static async getOrdersByLinkedStoreId(req: Request, res: Response) {
    try {
      const { linkedStoreId } = req.params;
      
      if (!linkedStoreId) {
        return res.status(400).json({ error: 'Linked Store ID is required' });
      }
      
      const orders = await ShopifyOrderService.getOrdersByLinkedStoreId(linkedStoreId);
      return res.status(200).json(orders);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get a specific Shopify order by ID
   * @param req Express Request
   * @param res Express Response
   */
  static async getOrderById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ error: 'Order ID is required' });
      }
      
      const order = await ShopifyOrderService.getOrderById(BigInt(id));
      
      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }
      
      return res.status(200).json(order);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Update an existing Shopify order
   * @param req Express Request
   * @param res Express Response
   */
  static async updateOrder(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ error: 'Order ID is required' });
      }

      // Partial validation can be added here if needed
      
      const order = await ShopifyOrderService.updateOrder(BigInt(id), req.body);
      return res.status(200).json(order);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Delete a Shopify order
   * @param req Express Request
   * @param res Express Response
   */
  static async deleteOrder(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ error: 'Order ID is required' });
      }
      
      await ShopifyOrderService.deleteOrder(BigInt(id));
      return res.status(204).end();
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }
}

export default ShopifyOrderController;
