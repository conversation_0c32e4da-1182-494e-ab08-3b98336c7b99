import ResponseHandler from "@/utils/response-handler.util";
import { Request, Response, NextFunction } from "express";
import { StatusCodes } from "http-status-codes";
import UserService from "@/services/user.service";
import { AuthenticatedUser } from "@/models/user.model";

import { createLogger } from "@/config/logger";

const logger = createLogger(
  "logs/middleware/auth/error.log",
  "logs/middleware/auth/combined.log"
);

class UserController {
  static async createUser(req: Request, res: Response, next: NextFunction) {
    try {
      const candidateUser = req.body;
      // Register user with the new schema
      const {
        data: result,
        success,
        message,
        status,
      } = await UserService.createUser(candidateUser);

      if (!success) {
        return ResponseHandler.sendError(res, {
          status: status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: status || StatusCodes.CREATED,
        message,
        data: {
          user: {
            id: result.id,
            code: result.code,
            email: result.email,
            phoneNumber: result.phoneNumber,
          },
        },
      });
    } catch (error) {
      return ResponseHandler.sendCatchError(res, error);
    }
  }

  static getUser = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const user: AuthenticatedUser = (req as any).user;
      const { id: userId } = req.params;
      const {
        success,
        message,
        status,
        data: result,
      } = await UserService.getUser(userId ?? user.id);
      if (!success) {
        return ResponseHandler.sendError(res, {
          status: status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: message,
        });
      }
      return ResponseHandler.sendSuccess(res, {
        status: status || StatusCodes.OK,
        message: message,
        data: result,
      });
    } catch (error) {
      return ResponseHandler.sendCatchError(res, error);
    }
  };

  static getUsers = async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Get all users if pageNumber is 0, default is 1
      const pageNumber = Number(req.query?.pageNumber || 1) ?? 1;
      const pageSize = Number(req.query?.pageSize) || 10;
      const keyword = req.query?.keyword?.toString() || "";
      const status = req.query?.status?.toString();
      const genders = req.query?.genders?.toString();
      const {
        data: result,
        status: responseStatus,
        message,
        success,
      } = await UserService.getUsers(
        pageNumber,
        pageSize,
        keyword,
        status,
        genders
      );

      if (!success) {
        return ResponseHandler.sendError(res, {
          status: responseStatus || StatusCodes.INTERNAL_SERVER_ERROR,
          message,
        });
      }
      return ResponseHandler.sendSuccess(res, {
        status: responseStatus || StatusCodes.OK,
        message,
        data: {
          items: result?.items,
          total: result?.total,
        },
      });
    } catch (error) {
      return ResponseHandler.sendCatchError(res, error);
    }
  };

  static updateUser = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const user: AuthenticatedUser = (req as any).user;
      const { id: userId } = req.params;
      // Update user profile
      const {
        data: result,
        status,
        message,
        success,
      } = await UserService.updateUser(userId ?? user.id, req.body);

      if (!success) {
        return ResponseHandler.sendError(res, {
          status: status || StatusCodes.INTERNAL_SERVER_ERROR,
          message,
        });
      }
      return ResponseHandler.sendSuccess(res, {
        status: status || StatusCodes.OK,
        message,
        data: result,
      });
    } catch (error) {
      return ResponseHandler.sendCatchError(res, error);
    }
  };

  static deleteUser = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { id: userId } = req.params;

      const {
        data: result,
        status,
        message,
        success,
      } = await UserService.deleteUser(userId);

      if (!success) {
        return ResponseHandler.sendError(res, {
          status: status || StatusCodes.INTERNAL_SERVER_ERROR,
          message,
        });
      }
      return ResponseHandler.sendSuccess(res, {
        status: status || StatusCodes.OK,
        message,
        data: result,
      });
    } catch (error) {
      return ResponseHandler.sendCatchError(res, error);
    }
  };
}

export default UserController;
