import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import PrismaService from "@/services/prisma.service";
import { StatusCodes } from "http-status-codes";

class ShopifyOrderDetailController {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = PrismaService.getInstance().getClient();
  }

  public async getOrderDetail(req: Request, res: Response): Promise<void> {
    try {
      const { orderId } = req.params;

      if (!orderId) {
        res.status(StatusCodes.BAD_REQUEST).json({
          error: "Order ID is required"
        });
        return;
      }

      // Convert orderId to BigInt for Shopify order ID
      let orderIdBigInt: bigint;
      try {
        orderIdBigInt = BigInt(orderId);
      } catch (error) {
        res.status(StatusCodes.BAD_REQUEST).json({
          error: "Invalid order ID format"
        });
        return;
      }

      // Get order with all related data
      const order = await (this.prisma as any).shopifyOrder.findUnique({
        where: { id: orderIdBigInt },
        include: {
          linkedStore: {
            include: {
              alertInfos: true,
            },
            // select: {
            //   id: true,
            //   userId: true,
            //   storeName: true,
            //   provider: true,
            //   providerStoreId: true,
            //   createdAt: true,
            //   updatedAt: true,
            //   alertInfos: true,
            // },
          },
          transactions: {
            include: {
              payout: true,
              disputes: true
            }
          }
        }
      });

      if (!order) {
        res.status(StatusCodes.NOT_FOUND).json({
          error: "Order not found"
        });
        return;
      }

      res.setHeader('Content-Type', 'application/json');
      res.status(StatusCodes.OK).send(
        JSON.stringify(order, (_key, value) =>
          typeof value === 'bigint' ? value.toString() : value
        )
      );

    } catch (error) {
      console.error("Error getting order detail:", error);
      res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        error: "Failed to get order detail",
        message: (error as Error).message,
      });
    }
  }
}

export default ShopifyOrderDetailController;