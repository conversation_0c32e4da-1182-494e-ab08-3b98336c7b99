import { Request, Response, NextFunction } from "express";
import CompensationService from "@/services/compensation.service";
import ResponseHand<PERSON> from "@/utils/response-handler.util";
import { StatusCodes } from "http-status-codes";
import { getI18n } from "@/middlewares/language.middleware";

class CompensationController {
  /**
   * Submit compensation request
   * Initiates a compensation request for a transaction
   */
  static async submitCompensationRequest(req: Request, res: Response, next: NextFunction) {
    try {
      const compensationData = req.body;
      const result = await CompensationService.submitCompensationRequest(compensationData);

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.compensation.internalError'),
      });
    }
  }

  /**
   * Check compensation status
   * Retrieves the current status of a previously submitted compensation request
   */
  static async checkCompensationStatus(req: Request, res: Response, next: NextFunction) {
    try {
      const { requestId } = req.params;
      const result = await CompensationService.checkCompensationStatus(requestId);

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.compensation.internalError'),
      });
    }
  }

  /**
   * Submit additional evidence
   * Adds supplementary evidence to an existing compensation request
   */
  static async submitAdditionalEvidence(req: Request, res: Response, next: NextFunction) {
    try {
      const { requestId } = req.params;
      const { evidenceFiles } = req.body;
      const result = await CompensationService.submitAdditionalEvidence(
        requestId,
        evidenceFiles
      );

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.compensation.internalError'),
      });
    }
  }

  /**
   * List compensation requests
   * Retrieves a paginated list of compensation requests with optional filters
   */
  static async listCompensationRequests(req: Request, res: Response, next: NextFunction) {
    try {
      const { page = '1', limit = '10', ...filters } = req.query;
      const result = await CompensationService.listCompensationRequests(
        parseInt(page as string, 10),
        parseInt(limit as string, 10),
        filters as Record<string, any>
      );

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.compensation.internalError'),
      });
    }
  }
}

export default CompensationController;
