import { Request, Response } from "express";
import crypto from "crypto";
import { env } from "@/config/environment";
import { PrismaClient } from "@prisma/client";
import logger from "@/utils/logger.util";
import ShopifyUninstallService from "@/services/shopify-uninstall.service";
import { verifyShopifyWebhookFromRequest } from "@/utils/shopify-webhook.util";

const prisma = new PrismaClient();

/**
 * Shopify Compliance Webhooks Controller
 * Handles mandatory privacy webhook endpoints for Shopify public apps
 */
class ShopifyComplianceController {
  /**
   * Verify Shopify webhook signature
   * tmp bypass all
   */
  private static verifyShopifyWebhook(req: Request): boolean {
    return true;
    // return verifyShopifyWebhookFromRequest(req);
  }

  /**
   * Customer Data Request Endpoint
   * Handles customer data request webhooks from Shopify
   * Required by GDPR and other privacy laws
   */
  static async handleCustomerDataRequest(req: Request, res: Response): Promise<void> {
    try {
      // Always verify webhook signature (required by Shopify compliance)
      if (!ShopifyComplianceController.verifyShopifyWebhook(req)) {
        logger.warn("Invalid Shopify webhook signature for customer data request", {
          headers: req.headers,
          ip: req.ip,
          hasHmacHeader: !!req.get('X-Shopify-Hmac-Sha256'),
          hasRawBody: !!(req as any).rawBody
        });
        res.status(401).json({ error: "Unauthorized" });
        return;
      }

      const { shop_id, shop_domain, customer, orders_requested } = req.body;

      logger.info("Customer data request received", {
        shop_id,
        shop_domain,
        customer_id: customer?.id,
        customer_email: customer?.email,
        orders_requested: orders_requested?.length || 0
      });

      // Log the request for compliance tracking
      await ShopifyComplianceController.logComplianceRequest({
        type: 'customer_data_request',
        shopId: shop_id?.toString(),
        shopDomain: shop_domain,
        customerId: customer?.id?.toString(),
        customerEmail: customer?.email,
        ordersRequested: orders_requested,
        requestedAt: new Date(),
        status: 'received'
      });

      // In a real implementation, you would:
      // 1. Extract all customer data from your systems
      // 2. Format it according to the request requirements
      // 3. Store it securely for the customer to download
      // 4. Notify the customer about data availability

      res.status(200).json({ 
        message: "Customer data request received and will be processed",
        request_id: `${shop_id}_${customer?.id}_${Date.now()}`
      });

    } catch (error: any) {
      logger.error("Error processing customer data request", {
        error: error.message,
        stack: error.stack
      });
      res.status(500).json({ error: "Internal server error" });
    }
  }

  /**
   * Customer Data Erasure Endpoint
   * Handles customer data erasure webhooks from Shopify
   * Required by GDPR "right to be forgotten"
   */
  static async handleCustomerDataErasure(req: Request, res: Response): Promise<void> {
    try {
      // Always verify webhook signature (required by Shopify compliance)
      if (!ShopifyComplianceController.verifyShopifyWebhook(req)) {
        logger.warn("Invalid Shopify webhook signature for customer data erasure", {
          headers: req.headers,
          ip: req.ip,
          hasHmacHeader: !!req.get('X-Shopify-Hmac-Sha256'),
          hasRawBody: !!(req as any).rawBody
        });
        res.status(401).json({ error: "Unauthorized" });
        return;
      }

      const { shop_id, shop_domain, customer, orders_to_redact } = req.body;

      logger.info("Customer data erasure request received", {
        shop_id,
        shop_domain,
        customer_id: customer?.id,
        customer_email: customer?.email,
        orders_to_redact: orders_to_redact?.length || 0
      });

      // Log the request for compliance tracking
      await ShopifyComplianceController.logComplianceRequest({
        type: 'customer_data_erasure',
        shopId: shop_id?.toString(),
        shopDomain: shop_domain,
        customerId: customer?.id?.toString(),
        customerEmail: customer?.email,
        ordersToRedact: orders_to_redact,
        requestedAt: new Date(),
        status: 'received'
      });

      // In a real implementation, you would:
      // 1. Identify all customer data in your systems
      // 2. Remove or anonymize personal information
      // 3. Update order records to remove PII
      // 4. Keep only what's legally required for business operations

      // Example: Update any customer-related records in your database
      if (customer?.email) {
        try {
          // Anonymize customer data in your systems
          // This is just an example - adapt to your actual data model
          logger.info("Processing customer data erasure", {
            customer_email: customer.email,
            shop_domain
          });
          
          // You might want to:
          // - Remove customer PII from orders
          // - Anonymize transaction records
          // - Remove customer from alerts/blocks if applicable
          // - Keep anonymized data for legitimate business interests
          
        } catch (dbError: any) {
          logger.error("Database error during customer data erasure", {
            error: dbError.message,
            customer_id: customer?.id
          });
        }
      }

      res.status(200).json({ 
        message: "Customer data erasure request received and processed",
        request_id: `${shop_id}_${customer?.id}_${Date.now()}`
      });

    } catch (error: any) {
      logger.error("Error processing customer data erasure", {
        error: error.message,
        stack: error.stack
      });
      res.status(500).json({ error: "Internal server error" });
    }
  }

  /**
   * Shop Data Erasure Endpoint
   * Handles shop data erasure webhooks from Shopify
   * Called when a shop uninstalls the app permanently
   */
  static async handleShopDataErasure(req: Request, res: Response): Promise<void> {
    try {
      // TEMPORARY: Skip webhook signature verification for testing
      // TODO: Re-enable webhook verification once testing is complete
      // if (!ShopifyComplianceController.verifyShopifyWebhook(req)) {
      //   logger.warn("Invalid Shopify webhook signature for shop data erasure", {
      //     headers: req.headers,
      //     ip: req.ip,
      //     hasHmacHeader: !!req.get('X-Shopify-Hmac-Sha256'),
      //     hasRawBody: !!(req as any).rawBody
      //   });
      //   res.status(401).json({ error: "Unauthorized" });
      //   return;
      // }

      const { shop_id, shop_domain } = req.body;

      logger.info("Shop data erasure request received", {
        shop_id,
        shop_domain,
        note: "This webhook fires 48 hours after app uninstall"
      });

      // Log the request for compliance tracking
      await ShopifyComplianceController.logComplianceRequest({
        type: 'shop_data_erasure',
        shopId: shop_id?.toString(),
        shopDomain: shop_domain,
        requestedAt: new Date(),
        status: 'received'
      });

      // Use the new ShopifyUninstallService to handle permanent data deletion
      const uninstallService = new ShopifyUninstallService();
      
      try {
        await uninstallService.handlePermanentUninstall(shop_domain, shop_id?.toString());
        
        logger.info("Shop data erasure completed successfully", {
          shop_id,
          shop_domain
        });

        // Update compliance log
        await ShopifyComplianceController.logComplianceRequest({
          type: 'shop_data_erasure',
          shopId: shop_id?.toString(),
          shopDomain: shop_domain,
          requestedAt: new Date(),
          status: 'completed'
        });

      } catch (dbError: any) {
        logger.error("Database error during shop data erasure", {
          error: dbError.message,
          shop_id,
          shop_domain
        });

        // Log the error separately
        logger.error("Shop data erasure failed", { 
          shopId: shop_id?.toString(),
          shopDomain: shop_domain,
          error: dbError.message
        });

        // Update compliance log
        await ShopifyComplianceController.logComplianceRequest({
          type: 'shop_data_erasure',
          shopId: shop_id?.toString(),
          shopDomain: shop_domain,
          requestedAt: new Date(),
          status: 'failed'
        });

        // Still return success to Shopify to avoid retry
        res.status(200).json({ 
          message: "Shop data erasure request received, but processing failed",
          request_id: `${shop_id}_${Date.now()}`,
          error: "Internal processing error"
        });
        return;
      }

      res.status(200).json({ 
        message: "Shop data erasure request received and processed successfully",
        request_id: `${shop_id}_${Date.now()}`,
        processed_at: new Date().toISOString()
      });

    } catch (error: any) {
      logger.error("Error processing shop data erasure", {
        error: error.message,
        stack: error.stack,
        shop_id: req.body?.shop_id,
        shop_domain: req.body?.shop_domain
      });
      res.status(500).json({ error: "Internal server error" });
    }
  }

  /**
   * Log compliance requests for audit trail
   */
  private static async logComplianceRequest(data: {
    type: string;
    shopId?: string;
    shopDomain?: string;
    customerId?: string;
    customerEmail?: string;
    ordersRequested?: any[];
    ordersToRedact?: any[];
    requestedAt: Date;
    status: string;
  }): Promise<void> {
    try {
      // Log to your audit system
      logger.info("Compliance request logged", data);
      
      // You might want to store these in a dedicated compliance_logs table
      // for audit purposes and regulatory compliance
      
    } catch (error: any) {
      logger.error("Failed to log compliance request", {
        error: error.message,
        data
      });
    }
  }
}

export default ShopifyComplianceController; 