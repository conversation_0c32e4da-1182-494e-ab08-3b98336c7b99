import { Request, Response, NextFunction } from "express";
import StripeService from "../services/stripe.service";
import ResponseHandler from "../utils/response-handler.util";
import { StatusCodes } from "http-status-codes";
import logger from "../utils/logger.util";
import { getStripeInstance } from "../config/stripe";
import { env } from '../config/environment';
import Stripe from 'stripe';

class StripeController {
  // ===== Stripe Account Methods =====

  /**
   * Create a new Stripe account for a store
   */
  static async createStripeAccount(req: Request, res: Response, next: NextFunction) {
    try {
      const createStripeAccountData = req.body;
      const stripeAccount = await StripeService.createStripeAccount(createStripeAccountData);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.CREATED,
        message: "Stripe account created successfully",
        data: stripeAccount
      });
    } catch (error: any) {
      logger.error(`Error creating Stripe account: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error creating Stripe account"
      });
    }
  }

  /**
   * Generate OAuth URL for Standard account connection
   */
  static async generateOAuthUrl(req: Request, res: Response, next: NextFunction) {
    try {
      const { storeId, state } = req.body;
      
      if (!storeId) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: "Store ID is required"
        });
      }
      
      const oauthUrl = StripeService.generateOAuthUrl(storeId, state);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "OAuth URL generated successfully",
        data: { url: oauthUrl }
      });
    } catch (error: any) {
      logger.error(`Error generating OAuth URL: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error generating OAuth URL"
      });
    }
  }

  /**
   * Handle OAuth callback from Stripe
   */
  static async handleOAuthCallback(req: Request, res: Response, next: NextFunction) {
    const frontendUrl = env.FRONTEND_URL || 'http://localhost:3000';
    
    try {
      const { code, state, error } = req.query;
      
      if (error) {
        logger.error(`OAuth error: ${error}`);
        // Redirect to frontend with error
        return res.redirect(`${frontendUrl}/settings/payment?stripe_error=${encodeURIComponent(error as string)}`);
      }
      
      if (!code || !state) {
        logger.error('Missing required OAuth parameters');
        // Redirect to frontend with error
        return res.redirect(`${frontendUrl}/settings/payment?stripe_error=${encodeURIComponent('Missing required OAuth parameters')}`);
      }
      
      const stripeAccount = await StripeService.handleOAuthCallback(code as string, state as string);
      
      logger.info('Stripe account connected successfully:', {stripeAccountId: stripeAccount.id});
      
      // Redirect to frontend with success
      return res.redirect(`${frontendUrl}/settings/payment?stripe_connected=true&store_id=${state}`);
      
    } catch (error: any) {
      logger.error(`Error handling OAuth callback: ${error.message}`);
      // Redirect to frontend with error
      return res.redirect(`${frontendUrl}/settings/payment?stripe_error=${encodeURIComponent(error.message || 'Connection failed')}`);
    }
  }

  /**
   * Get all Stripe accounts
   */
  static async getAllStripeAccounts(req: Request, res: Response, next: NextFunction) {
    try {
      const stripeAccounts = await StripeService.getAllStripeAccounts();
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Stripe accounts retrieved successfully",
        data: stripeAccounts
      });
    } catch (error: any) {
      logger.error(`Error getting Stripe accounts: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error getting Stripe accounts"
      });
    }
  }

  /**
   * Get Stripe account by ID
   */
  static async getStripeAccount(req: Request, res: Response, next: NextFunction) {
    try {
      const id = req.params.id;
      const stripeAccount = await StripeService.getStripeAccountById(id);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Stripe account retrieved successfully",
        data: stripeAccount
      });
    } catch (error: any) {
      logger.error(`Error getting Stripe account: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.NOT_FOUND,
        message: error.message || "Stripe account not found"
      });
    }
  }

  /**
   * Get Stripe account by store ID
   */
  static async getStripeAccountByStoreId(req: Request, res: Response, next: NextFunction) {
    try {
      const storeId = req.params.storeId;
      const stripeAccount = await StripeService.getStripeAccountByStoreId(storeId);
      
      if (!stripeAccount) {
        return ResponseHandler.sendSuccess(res, {
          status: StatusCodes.OK,
          message: "No Stripe account found for this store",
          data: null
        });
      }
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Stripe account retrieved successfully",
        data: stripeAccount
      });
    } catch (error: any) {
      logger.error(`Error getting Stripe account by store ID: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error retrieving Stripe account"
      });
    }
  }

  /**
   * Get comprehensive Stripe information by store ID
   */
  static async getStripeInfoByStoreId(req: Request, res: Response, next: NextFunction) {
    try {
      const storeId = req.params.storeId;
      const stripeAccount = await StripeService.getStripeAccountByStoreId(storeId);
      
      if (!stripeAccount) {
        return ResponseHandler.sendSuccess(res, {
          status: StatusCodes.OK,
          message: "No Stripe account found for this store",
          data: {
            isConnected: false,
            stripeAccountId: null,
            status: null,
            accountDetails: null,
            requirements: null
          }
        });
      }

      // Get additional Stripe account details if connected
      let accountDetails = null;
      let requirements = null;
      
      if (stripeAccount.stripeAccountId) {
        try {
          accountDetails = await StripeService.getStripeAccountDetails(stripeAccount.stripeAccountId);
          requirements = await StripeService.getAccountRequirements(stripeAccount.stripeAccountId);
        } catch (detailsError: any) {
          logger.warn(`Could not fetch Stripe details for account ${stripeAccount.stripeAccountId}: ${detailsError.message}`);
        }
      }
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Stripe information retrieved successfully",
        data: {
          isConnected: stripeAccount.status === 'active',
          stripeAccountId: stripeAccount.stripeAccountId,
          status: stripeAccount.status,
          localAccount: stripeAccount,
          accountDetails,
          requirements
        }
      });
    } catch (error: any) {
      logger.error(`Error getting Stripe info by store ID: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error retrieving Stripe information"
      });
    }
  }

  /**
   * Update Stripe account
   */
  static async updateStripeAccount(req: Request, res: Response, next: NextFunction) {
    try {
      const id = req.params.id;
      const updateStripeAccountData = req.body;
      
      const stripeAccount = await StripeService.updateStripeAccount(id, updateStripeAccountData);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Stripe account updated successfully",
        data: stripeAccount
      });
    } catch (error: any) {
      logger.error(`Error updating Stripe account: ${error.message}`);
      
      const status = error.message.includes('not found') 
        ? StatusCodes.NOT_FOUND 
        : StatusCodes.BAD_REQUEST;
      
      return ResponseHandler.sendError(res, {
        status,
        message: error.message || "Error updating Stripe account"
      });
    }
  }

  /**
   * Generate onboarding link for Stripe account
   */
  static async generateOnboardingLink(req: Request, res: Response, next: NextFunction) {
    try {
      const id = req.params.id;
      const frontendUrl = env.FRONTEND_URL;
      const refreshUrl = `${frontendUrl}/settings?linkExpired=true`;
      const returnUrl = `${frontendUrl}/settings?accountLinked=true`;

      const stripeAccount = await StripeService.getStripeAccountById(id);
      if (!stripeAccount || !stripeAccount.stripeAccountId) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.NOT_FOUND,
          message: "Stripe account not found"
        });
      }
      
      const linkUrl = await StripeService.generateOnboardingLink(
        stripeAccount.stripeAccountId,
        refreshUrl,
        returnUrl
      );
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Onboarding link generated successfully",
        data: { url: linkUrl }
      });
    } catch (error: any) {
      logger.error(`Error generating onboarding link: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error generating onboarding link"
      });
    }
  }

  /**
   * Delete Stripe account
   */
  static async deleteStripeAccount(req: Request, res: Response, next: NextFunction) {
    try {
      const id = req.params.id;
      await StripeService.deleteStripeAccount(id);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Stripe account deleted successfully"
      });
    } catch (error: any) {
      logger.error(`Error deleting Stripe account: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.NOT_FOUND,
        message: error.message || "Error deleting Stripe account"
      });
    }
  }

  /**
   * Get Stripe account details from Stripe API
   */
  static async getStripeAccountDetails(req: Request, res: Response, next: NextFunction) {
    try {
      const id = req.params.id;
      const stripeAccount = await StripeService.getStripeAccountById(id);
      
      if (!stripeAccount || !stripeAccount.stripeAccountId) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.NOT_FOUND,
          message: "Stripe account not found"
        });
      }

      const accountDetails = await StripeService.getStripeAccountDetails(stripeAccount.stripeAccountId);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Stripe account details retrieved successfully",
        data: accountDetails
      });
    } catch (error: any) {
      logger.error(`Error getting Stripe account details: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.NOT_FOUND,
        message: error.message || "Stripe account details not found"
      });
    }
  }

  /**
   * Sync Stripe account status
   */
  static async syncAccountStatus(req: Request, res: Response, next: NextFunction) {
    try {
      const id = req.params.id;
      const stripeAccount = await StripeService.getStripeAccountById(id);
      
      if (!stripeAccount || !stripeAccount.stripeAccountId) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.NOT_FOUND,
          message: "Stripe account not found"
        });
      }

      const syncedAccount = await StripeService.syncAccountStatus(stripeAccount.stripeAccountId);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Account status synced successfully",
        data: syncedAccount
      });
    } catch (error: any) {
      logger.error(`Error syncing account status: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error syncing account status"
      });
    }
  }

  /**
   * Create dashboard link for Stripe account
   */
  static async createDashboardLink(req: Request, res: Response, next: NextFunction) {
    try {
      const id = req.params.id;
      const stripeAccount = await StripeService.getStripeAccountById(id);
      
      if (!stripeAccount || !stripeAccount.stripeAccountId) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.NOT_FOUND,
          message: "Stripe account not found"
        });
      }

      const dashboardUrl = await StripeService.createDashboardLink(stripeAccount.stripeAccountId);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Dashboard link created successfully",
        data: { url: dashboardUrl }
      });
    } catch (error: any) {
      logger.error(`Error creating dashboard link: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error creating dashboard link"
      });
    }
  }

  /**
   * Get account requirements
   */
  static async getAccountRequirements(req: Request, res: Response, next: NextFunction) {
    try {
      const id = req.params.id;
      const stripeAccount = await StripeService.getStripeAccountById(id);
      
      if (!stripeAccount || !stripeAccount.stripeAccountId) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.NOT_FOUND,
          message: "Stripe account not found"
        });
      }

      const requirements = await StripeService.getAccountRequirements(stripeAccount.stripeAccountId);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Account requirements retrieved successfully",
        data: requirements
      });
    } catch (error: any) {
      logger.error(`Error getting account requirements: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error getting account requirements"
      });
    }
  }


  // ===== CARD SETUP ENDPOINTS =====

  /**
   * Create setup intent for card collection
   */
  static async createSetupIntent(req: Request, res: Response, next: NextFunction) {
    try {
      const { storeId, provider } = req.body;
      
      logger.info(`Creating setup intent for storeId: ${storeId}, provider: ${provider}`);
      
      if (!storeId) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: "Store ID is required"
        });
      }

      // Get or create stripe account for the store
      let stripeAccount = await StripeService.getStripeAccountByStoreId(storeId);
      
      if (!stripeAccount) {
        // Create a basic Stripe account record for billing purposes
        try {
          stripeAccount = await StripeService.createStripeAccount({
            storeId,
            stripeAccountId: null, // Will be set later when Connect account is created
            status: "pending"
          });
          logger.info(`Created new Stripe account record for store ${storeId}`);
        } catch (createError: any) {
          logger.error(`Failed to create Stripe account for store ${storeId}: ${createError.message}`);
          return ResponseHandler.sendError(res, {
            status: StatusCodes.INTERNAL_SERVER_ERROR,
            message: "Failed to initialize billing setup for this store"
          });
        }
      }

      // Get store information to create customer
      const { PrismaClient } = require("@prisma/client");
      const prisma = new PrismaClient();
      
      const store = await prisma.linkedStore.findUnique({
        where: { id: storeId },
        include: { user: true }
      });
      
      logger.info(`Found store: ${store ? store.storeName : 'null'}, provider: ${store ? store.provider : 'null'}`);
      
      if (!store) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.NOT_FOUND,
          message: "Store information not found"
        });
      }

      let customerId = stripeAccount.stripeCustomerId;

      // Create Stripe customer if not exists
      if (!customerId) {
        const storeData = store.data as any;
        const email = storeData?.shop?.email || store.user?.email || '<EMAIL>';
        const name = storeData?.shop?.shop_owner || store.user?.fullName || store.storeName;
        
        customerId = await StripeService.createCustomer(email, name, {
          storeId,
          storeName: store.storeName
        });

        // Update stripe account with customer ID
        stripeAccount = await StripeService.updateStripeAccount(stripeAccount.id, {
          stripeCustomerId: customerId
        });
      }

      // Create setup intent
      const setupIntent = await StripeService.createSetupIntent(customerId, storeId);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Setup intent created successfully",
        data: setupIntent
      });
    } catch (error: any) {
      logger.error(`Error creating setup intent: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error creating setup intent"
      });
    }
  }

  /**
   * Confirm card setup and save payment method
   */
  static async confirmCardSetup(req: Request, res: Response, next: NextFunction) {
    try {
      const { storeId, paymentMethodId, cardData } = req.body;
      
      if (!storeId || !paymentMethodId || !cardData) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: "Store ID, payment method ID, and card data are required"
        });
      }

      // Get stripe account
      const stripeAccount = await StripeService.getStripeAccountByStoreId(storeId);
      
      if (!stripeAccount || !stripeAccount.stripeCustomerId) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.NOT_FOUND,
          message: "Stripe account or customer not found"
        });
      }

      // Attach payment method to customer
      await StripeService.attachPaymentMethod(paymentMethodId, stripeAccount.stripeCustomerId);

      // Get payment method details from Stripe
      const paymentMethodDetails = await StripeService.getPaymentMethod(paymentMethodId);

      // Update stripe account with card information
      await StripeService.updateWithCardInfo(stripeAccount.id, {
        stripeCustomerId: stripeAccount.stripeCustomerId,
        paymentMethodId,
        cardLast4: paymentMethodDetails.last4,
        cardBrand: paymentMethodDetails.brand,
        cardCountry: paymentMethodDetails.country,
        cardholderName: cardData.cardholderName,
        billingEmail: cardData.billingEmail,
      });
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Card setup completed successfully",
        data: { success: true }
      });
    } catch (error: any) {
      logger.error(`Error confirming card setup: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error confirming card setup"
      });
    }
  }

  /**
   * Get card setup status for a store
   */
  static async getCardSetupStatus(req: Request, res: Response, next: NextFunction) {
    try {
      const { storeId } = req.params;
      
      const stripeAccount = await StripeService.getStripeAccountByStoreId(storeId);
      
      if (!stripeAccount) {
        return ResponseHandler.sendSuccess(res, {
          status: StatusCodes.OK,
          message: "Card setup status retrieved",
          data: {
            setupCompleted: false,
            cardInfo: null
          }
        });
      }

      const response = {
        setupCompleted: stripeAccount.setupCompleted || false,
        cardInfo: stripeAccount.setupCompleted ? {
          last4: stripeAccount.cardLast4,
          brand: stripeAccount.cardBrand,
          cardholderName: stripeAccount.cardholderName
        } : null
      };
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Card setup status retrieved successfully",
        data: response
      });
    } catch (error: any) {
      logger.error(`Error getting card setup status: ${error.message}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.message || "Error getting card setup status"
      });
    }
  }

  // ===== Webhook Handler =====

  /**
   * Handle Stripe webhooks
   */
  static async handleWebhook(req: Request, res: Response) {
    const sig = req.headers['stripe-signature'] as string;
    const stripe = getStripeInstance();
    
    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(req.body, sig, env.STRIPE_WEBHOOK_SECRET);
    } catch (err: any) {
      console.error('Webhook signature verification failed:', err.message);
      return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    try {
      switch (event.type) {
        case 'account.updated':
          await StripeController.handleAccountUpdated(event.data.object as Stripe.Account);
          break;
        
        case 'account.application.deauthorized':
          await StripeController.handleAccountDeauthorized(event.data.object as any);
          break;
        
        case 'capability.updated':
          await StripeController.handleCapabilityUpdated(event.data.object as Stripe.Capability);
          break;
        
        case 'payment_intent.succeeded':
        case 'payment_intent.payment_failed':
          await StripeController.handlePaymentUpdate(event.data.object as Stripe.PaymentIntent, event.type);
          break;
        
        default:
          console.log(`Unhandled event type: ${event.type}`);
      }

      res.json({ received: true });
    } catch (error: any) {
      console.error('Error processing webhook:', error);
      res.status(500).json({ error: 'Webhook processing failed' });
    }
  }

  private static async handleAccountUpdated(account: Stripe.Account) {
    try {
      console.log(`Processing account.updated for account: ${account.id}`);
      
      // Sync the account status in our database
      await StripeService.syncAccountStatus(account.id);
      
      console.log(`Successfully synced account status for: ${account.id}`);
    } catch (error: any) {
      console.error(`Failed to sync account ${account.id}:`, error.message);
    }
  }

  private static async handleAccountDeauthorized(data: any) {
    try {
      const accountId = data.account;
      console.log(`Processing account.application.deauthorized for account: ${accountId}`);
      
      // Find and update the account status to deactivated
      const localAccount = await StripeService.getAllStripeAccounts({ 
        stripeAccountId: accountId 
      });
      
      if (localAccount.length > 0) {
        await StripeService.updateStripeAccount(localAccount[0].id, {
          status: 'deactivated'
        });
        console.log(`Deactivated account: ${accountId}`);
      }
    } catch (error: any) {
      console.error(`Failed to deactivate account:`, error.message);
    }
  }

  private static async handleCapabilityUpdated(capability: Stripe.Capability) {
    try {
      console.log(`Processing capability.updated for account: ${capability.account}`);
      
      // Sync the account to get updated capability status
      await StripeService.syncAccountStatus(capability.account as string);
      
      console.log(`Successfully synced capabilities for account: ${capability.account}`);
    } catch (error: any) {
      console.error(`Failed to sync capabilities for account ${capability.account}:`, error.message);
    }
  }

  private static async handlePaymentUpdate(paymentIntent: Stripe.PaymentIntent, eventType: string) {
    try {
      const accountId = paymentIntent.on_behalf_of as string;
      if (!accountId) return;

      console.log(`Processing ${eventType} for account: ${accountId}`);
      
      // Extract billId from metadata
      const billId = paymentIntent.metadata?.billId;
      if (!billId) {
        console.log(`No billId found in payment metadata for payment ${paymentIntent.id}`);
        return;
      }

      // Import required services
      const BillService = (await import('../services/bill.service')).default;
      const PaymentHistoryService = (await import('../services/payment-history.service')).default;

      // Get the bill
      const bill = await BillService.getBillById(billId);
      if (!bill) {
        console.error(`Bill ${billId} not found for payment ${paymentIntent.id}`);
        return;
      }

      // Create payment history record
      const paymentStatus = eventType === 'payment_intent.succeeded' ? 'SUCCEEDED' : 'FAILED';
      await PaymentHistoryService.createPaymentHistory({
        linkedStoreId: bill.linkedStoreId,
        billId: billId,
        amount: paymentIntent.amount / 100, // Convert from cents
        currency: paymentIntent.currency.toUpperCase(),
        description: `Payment for bill ${billId}`,
        status: paymentStatus,
        paymentDate: new Date()
      });

      // Update bill status if payment succeeded
      if (eventType === 'payment_intent.succeeded') {
        await BillService.updateBill(billId, {
          status: 'PAID'
        });
        console.log(`Bill ${billId} marked as PAID`);
      }

      console.log(`Payment history created for bill ${billId} with status ${paymentStatus}`);
    } catch (error: any) {
      console.error(`Failed to process payment update:`, error.message);
    }
  }
}

export default StripeController;
export { StripeController };