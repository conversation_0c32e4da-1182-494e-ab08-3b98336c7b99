import { Request, Response } from 'express';
import OrderService from '../services/order.service';
import { orderSchema, updateOrderSchema } from '../models/order.model';

class OrderController {
  /**
   * Create a new order
   * @param req Express Request
   * @param res Express Response
   */
  static async createOrder(req: Request, res: Response) {
    try {
      const { error, value } = orderSchema.validate(req.body);
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const order = await OrderService.createOrder(value);
      return res.status(201).json(order);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get all orders with optional filtering
   * @param req Express Request
   * @param res Express Response
   */
  static async getAllOrders(req: Request, res: Response) {
    try {
      const query = req.query;
      const orders = await OrderService.getAllOrders(query);
      return res.status(200).json(orders);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get orders for a specific store
   * @param req Express Request
   * @param res Express Response
   */
  static async getOrdersByStoreId(req: Request, res: Response) {
    try {
      const { storeId } = req.params;
      
      if (!storeId) {
        return res.status(400).json({ error: 'Store ID is required' });
      }
      
      const orders = await OrderService.getOrdersByStoreId(storeId);
      return res.status(200).json(orders);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get a specific order by ID
   * @param req Express Request
   * @param res Express Response
   */
  static async getOrderById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ error: 'Order ID is required' });
      }
      
      const order = await OrderService.getOrderById(id);
      
      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }
      
      return res.status(200).json(order);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get a specific order by store ID and order ID
   * @param req Express Request
   * @param res Express Response
   */
  static async getOrderByStoreIdAndOrderId(req: Request, res: Response) {
    try {
      const { storeId, orderId } = req.params;
      
      if (!storeId || !orderId) {
        return res.status(400).json({ error: 'Store ID and Order ID are required' });
      }
      
      const order = await OrderService.getOrderByStoreIdAndOrderId(storeId, orderId);
      
      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }
      
      return res.status(200).json(order);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Update an existing order
   * @param req Express Request
   * @param res Express Response
   */
  static async updateOrder(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ error: 'Order ID is required' });
      }

      const { error, value } = updateOrderSchema.validate(req.body);
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      // Check if order exists
      const existingOrder = await OrderService.getOrderById(id);
      if (!existingOrder) {
        return res.status(404).json({ error: 'Order not found' });
      }

      const order = await OrderService.updateOrder(id, value);
      return res.status(200).json(order);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Delete an order
   * @param req Express Request
   * @param res Express Response
   */
  static async deleteOrder(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ error: 'Order ID is required' });
      }

      // Check if order exists
      const existingOrder = await OrderService.getOrderById(id);
      if (!existingOrder) {
        return res.status(404).json({ error: 'Order not found' });
      }

      await OrderService.deleteOrder(id);
      return res.status(204).send();
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }
}

export default OrderController;
