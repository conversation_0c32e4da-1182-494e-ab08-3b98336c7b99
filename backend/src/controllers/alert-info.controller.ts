import { Request, Response } from 'express';
import {
  createAlertInfo,
  getAlertInfosByStoreId,
  getAlertInfoById,
  updateAlertInfo,
  deleteAlertInfo,
  handleRegistrationStatusNotification
} from '../services/alert-info.service';
import logger from '@/utils/logger.util';

/**
 * Create a new alert info
 * @param req - request
 * @param res - response
 */
export const createAlertInfoController = async (req: Request, res: Response) => {
  try {
    const result = await createAlertInfo(req.body);
    return res.status(201).json({ success: true, data: result });
  } catch (error: any) {
    return res.status(400).json({ success: false, message: error.message });
  }
};

/**
 * Get all alert info for a store
 * @param req - request
 * @param res - response
 */
export const getAlertInfosByStoreIdController = async (req: Request, res: Response) => {
  try {
    const { storeId } = req.params;
    const result = await getAlertInfosByStoreId(storeId);
    return res.status(200).json({ success: true, data: result });
  } catch (error: any) {
    return res.status(400).json({ success: false, message: error.message });
  }
};

/**
 * Get alert info by id
 * @param req - request
 * @param res - response
 */
export const getAlertInfoByIdController = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await getAlertInfoById(id);
    return res.status(200).json({ success: true, data: result });
  } catch (error: any) {
    return res.status(400).json({ success: false, message: error.message });
  }
};

/**
 * Update alert info
 * @param req - request
 * @param res - response
 */
export const updateAlertInfoController = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const result = await updateAlertInfo(id, req.body);
    return res.status(200).json({ success: true, data: result });
  } catch (error: any) {
    return res.status(400).json({ success: false, message: error.message });
  }
};

/**
 * Delete alert info
 * @param req - request
 * @param res - response
 */
export const deleteAlertInfoController = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    await deleteAlertInfo(id);
    return res.status(200).json({ success: true, message: 'Alert info deleted successfully' });
  } catch (error: any) {
    return res.status(400).json({ success: false, message: error.message });
  }
};



/**
 * Get alert info registration status
 * @param req - request
 * @param res - response
 */
export const getRegistrationStatusController = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const alertInfo = await getAlertInfoById(id);

    if (!alertInfo) {
      return res.status(404).json({
        success: false,
        message: 'Alert info not found'
      });
    }

    return res.status(200).json({
      success: true,
      data: {
        id: alertInfo.id,
        arn: alertInfo.arn,
        registrationStatus: alertInfo.registrationStatus,
        registrationMessage: alertInfo.registrationMessage,
        registeredAt: alertInfo.registeredAt,
        closedAt: alertInfo.closedAt
      }
    });

  } catch (error: any) {
    console.error('Get registration status error:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get registration status'
    });
  }
};

/**
 * Handle TradeDefensor webhook for merchant registration status notifications
 * 商户卡账单状态结果通知 webhook handler
 */
export const handleMerchantRegistrationWebhook = async (req: Request, res: Response) => {
  try {
    logger.info('Received merchant registration webhook', {
      timestamp: new Date().toISOString(),
      alertInfoId: req.params.alertInfoId,
      data: req.body
    });
    // Get alertInfoId from either URL params or request body
    const alertInfoId = req.params.alertInfoId || req.body.alertInfoId;
    const { status, message, alertType, descriptor, dba, mcc, cardBin, caid, website, arn } = req.body;
    
    if (!alertInfoId) {
      return res.status(400).json({
        success: false,
        message: 'alertInfoId is required either in URL params or request body'
      });
    }
    
    // Log webhook for debugging
    console.log('Received merchant registration webhook:', {
      timestamp: new Date().toISOString(),
      alertInfoId,
      data: { status, message, alertType, descriptor, dba, mcc, cardBin, caid, website, arn },
    });

    // Process the webhook notification
    const result = await handleRegistrationStatusNotification({
      alertInfoId,
      status,
      message,
      alertType,
      descriptor,
      dba,
      mcc,
      cardBin,
      caid,
      website,
      arn
    });

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Webhook processed successfully',
      data: result
    });

  } catch (error: any) {
    console.error('Webhook processing error:', {
      timestamp: new Date().toISOString(),
      error: error.message,
      stack: error.stack,
      webhookData: req.body
    });

    // Return error response
    return res.status(400).json({
      success: false,
      message: error.message || 'Webhook processing failed',
      error: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

/**
 * Handle general TradeDefensor webhooks
 */
export const handleTradeDefensorWebhook = async (req: Request, res: Response) => {
  try {
    const { type, data } = req.body;

    console.log('Received TradeDefensor webhook:', {
      type,
      timestamp: new Date().toISOString(),
      data
    });

    // Route to appropriate handler based on webhook type
    switch (type) {
      case 'merchant_registration_status':
        return await handleMerchantRegistrationWebhook(req, res);

      case 'alert_notification':
        // Handle alert notifications if needed
        return res.status(200).json({ success: true, message: 'Alert notification received' });

      default:
        console.warn('Unknown webhook type:', type);
        return res.status(200).json({ success: true, message: 'Webhook received but not processed' });
    }

  } catch (error: any) {
    console.error('General webhook error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error processing webhook'
    });
  }
};
