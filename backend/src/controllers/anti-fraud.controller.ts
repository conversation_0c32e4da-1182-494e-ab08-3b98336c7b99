import { Request, Response, NextFunction } from "express";
import AntiFraudService from "@/services/anti-fraud.service";
import ResponseHand<PERSON> from "@/utils/response-handler.util";
import { StatusCodes } from "http-status-codes";
import { getI18n } from "@/middlewares/language.middleware";

class AntiFraudController {
  /**
   * Check transaction for risk
   * Performs a risk assessment on a transaction
   */
  static async checkTransaction(req: Request, res: Response, next: NextFunction) {
    try {
      const transactionData = req.body;
      const result = await AntiFraudService.checkTransaction(transactionData);

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.antiFraud.internalError'),
      });
    }
  }

  /**
   * Notify transaction result
   * Notifies the anti-fraud service about the result of a transaction
   */
  static async notifyTransactionResult(req: Request, res: Response, next: NextFunction) {
    try {
      const notificationData = req.body;
      const result = await AntiFraudService.notifyTransactionResult(notificationData);

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.antiFraud.internalError'),
      });
    }
  }
}

export default AntiFraudController;
