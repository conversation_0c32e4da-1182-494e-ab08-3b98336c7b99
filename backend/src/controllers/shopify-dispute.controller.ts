import { Request, Response } from 'express';
import ShopifyDisputeService from '../services/shopify-dispute.service';
import { createShopifyDisputeSchema } from '../models/shopify-dispute.model';

class ShopifyDisputeController {
  /**
   * Create a new Shopify dispute
   * @param req Express Request
   * @param res Express Response
   */
  static async createDispute(req: Request, res: Response) {
    try {
      const { error, value } = createShopifyDisputeSchema.validate(req.body);
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const dispute = await ShopifyDisputeService.createDispute(value);
      return res.status(201).json(dispute);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Create multiple Shopify disputes
   * @param req Express Request
   * @param res Express Response
   */
  static async createManyDisputes(req: Request, res: Response) {
    try {
      if (!Array.isArray(req.body)) {
        return res.status(400).json({ error: 'Request body must be an array of disputes' });
      }
      
      // Validate each dispute
      for (let i = 0; i < req.body.length; i++) {
        const { error } = createShopifyDisputeSchema.validate(req.body[i]);
        if (error) {
          return res.status(400).json({ 
            error: `Dispute at index ${i} is invalid: ${error.details[0].message}` 
          });
        }
      }

      const result = await ShopifyDisputeService.createManyDisputes(req.body);
      return res.status(201).json({ created: result });
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get all Shopify disputes with optional filtering
   * @param req Express Request
   * @param res Express Response
   */
  static async getAllDisputes(req: Request, res: Response) {
    try {
      const query = req.query;
      
      // Parse date ranges if provided
      if (query.initiatedFrom) {
        (query as any).initiatedFrom = new Date(query.initiatedFrom as string);
      }
      
      if (query.initiatedTo) {
        (query as any).initiatedTo = new Date(query.initiatedTo as string);
      }
      
      if (query.evidenceDueFrom) {
        (query as any).evidenceDueFrom = new Date(query.evidenceDueFrom as string);
      }
      
      if (query.evidenceDueTo) {
        (query as any).evidenceDueTo = new Date(query.evidenceDueTo as string);
      }
      
      // Handle pagination
      if (req.query.page || req.query.limit) {
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 10;
        
        // Remove pagination params from query
        delete (query as any).page;
        delete (query as any).limit;
        
        const result = await ShopifyDisputeService.getPaginatedDisputes(page, limit, query);
        return res.status(200).json(result);
      }
      
      const disputes = await ShopifyDisputeService.getAllDisputes(query);
      return res.status(200).json(disputes);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get Shopify disputes for a specific linked store
   * @param req Express Request
   * @param res Express Response
   */
  static async getDisputesByLinkedStoreId(req: Request, res: Response) {
    try {
      const { linkedStoreId } = req.params;
      
      if (!linkedStoreId) {
        return res.status(400).json({ error: 'Linked Store ID is required' });
      }
      
      const disputes = await ShopifyDisputeService.getDisputesByLinkedStoreId(linkedStoreId);
      return res.status(200).json(disputes);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get Shopify disputes for a specific transaction
   * @param req Express Request
   * @param res Express Response
   */
  static async getDisputesByTransactionId(req: Request, res: Response) {
    try {
      const { transactionId } = req.params;
      
      if (!transactionId) {
        return res.status(400).json({ error: 'Transaction ID is required' });
      }
      
      const disputes = await ShopifyDisputeService.getDisputesByTransactionId(BigInt(transactionId));
      return res.status(200).json(disputes);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get a specific Shopify dispute by ID
   * @param req Express Request
   * @param res Express Response
   */
  static async getDisputeById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ error: 'Dispute ID is required' });
      }
      
      const dispute = await ShopifyDisputeService.getDisputeById(BigInt(id));
      
      if (!dispute) {
        return res.status(404).json({ error: 'Dispute not found' });
      }
      
      return res.status(200).json(dispute);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Update an existing Shopify dispute
   * @param req Express Request
   * @param res Express Response
   */
  static async updateDispute(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ error: 'Dispute ID is required' });
      }
      
      const dispute = await ShopifyDisputeService.updateDispute(BigInt(id), req.body);
      return res.status(200).json(dispute);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }

  /**
   * Delete a Shopify dispute
   * @param req Express Request
   * @param res Express Response
   */
  static async deleteDispute(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ error: 'Dispute ID is required' });
      }
      
      await ShopifyDisputeService.deleteDispute(BigInt(id));
      return res.status(204).end();
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }
}

export default ShopifyDisputeController;
