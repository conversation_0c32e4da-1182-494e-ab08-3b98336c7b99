import { Request, Response, NextFunction } from "express";
import <PERSON><PERSON><PERSON><PERSON> from "../utils/response-handler.util";
import { StatusCodes } from "http-status-codes";
import ClientService from "../services/client.service";

class ClientController {
  static async create(req: Request, res: Response, next: NextFunction) {
    try {
      const client = req.body;
      if (await ClientService.isClientExists(client.email)) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.CONFLICT,
          message: "Client already exists",
        });
      }
      const newClient = await ClientService.create(client);
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.CREATED,
        message: "Client created successfully",
        data: {
          client: newClient,
        },
      });
    } catch (error) {
      return ResponseHandler.sendCatchError(res, error);
    }
  }
}

export default ClientController;
