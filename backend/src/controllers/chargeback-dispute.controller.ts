import { Request, Response, NextFunction } from "express";
import ChargebackDisputeService from "@/services/chargeback-dispute.service";
import ResponseHandler from "@/utils/response-handler.util";
import { StatusCodes } from "http-status-codes";
import { getI18n } from "@/middlewares/language.middleware";

class ChargebackDisputeController {
  /**
   * Submit dispute
   * Submits a detailed dispute case to challenge a chargeback
   */
  static async submitDispute(req: Request, res: Response, next: NextFunction) {
    try {
      const disputeData = req.body;
      const result = await ChargebackDisputeService.submitDispute(disputeData);

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.chargebackDispute.internalError'),
      });
    }
  }

  /**
   * Check dispute status
   * Retrieves the current status of a previously submitted dispute
   */
  static async checkDisputeStatus(req: Request, res: Response, next: NextFunction) {
    try {
      const { merchantOrderNo } = req.params;
      const result = await ChargebackDisputeService.checkDisputeStatus(merchantOrderNo);

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.chargebackDispute.internalError'),
      });
    }
  }

  /**
   * Submit additional materials
   * Adds supplementary materials to an existing dispute case
   */
  static async submitAdditionalMaterials(req: Request, res: Response, next: NextFunction) {
    try {
      const { merchantOrderNo } = req.params;
      const materials = req.body;
      const result = await ChargebackDisputeService.submitAdditionalMaterials(
        merchantOrderNo,
        materials
      );

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.chargebackDispute.internalError'),
      });
    }
  }

  /**
   * Get chargebacks by store
   * Retrieves all chargebacks for a specific linked store
   */
  static async getChargebacksByStore(req: Request, res: Response, next: NextFunction) {
    try {
      const { storeId } = req.params;
      const { page, pageSize, status, startDate, endDate } = req.query;
      
      const options = {
        page: page ? parseInt(page as string) : undefined,
        pageSize: pageSize ? parseInt(pageSize as string) : undefined,
        status: status as string,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
      };
      
      const result = await ChargebackDisputeService.getChargebacksByStoreId(storeId, options);

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.chargebackDispute.internalError'),
      });
    }
  }

  /**
   * Get chargeback statistics
   * Retrieves statistics for chargebacks of a specific linked store
   */
  static async getChargebackStats(req: Request, res: Response, next: NextFunction) {
    try {
      const { storeId } = req.params;
      const result = await ChargebackDisputeService.getChargebackStats(storeId);

      if (!result.success) {
        return ResponseHandler.sendError(res, {
          status: result.status || StatusCodes.INTERNAL_SERVER_ERROR,
          message: result.message,
        });
      }

      return ResponseHandler.sendSuccess(res, {
        status: result.status || StatusCodes.OK,
        message: result.message,
        data: result.data,
      });
    } catch (error: any) {
      const i18n = getI18n();
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || i18n.t('error.chargebackDispute.internalError'),
      });
    }
  }
}

export default ChargebackDisputeController;
