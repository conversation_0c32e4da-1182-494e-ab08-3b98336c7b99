import { Request, Response } from "express";
import { usageTrackingService } from "../services/usage-tracking.service";
import StoreBalanceService from "../services/store-balance.service";
import ResponseHandler from "../utils/response-handler.util";
import { StatusCodes } from "http-status-codes";
import logger from "../utils/logger.util";

export class UsageTrackingController {
  /**
   * Get current period usage
   */
  static async getCurrentUsage(req: Request, res: Response) {
    try {
      const { storeId } = req.params;
      
      const usage = await usageTrackingService.getCurrentPeriodUsage(storeId);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Current usage retrieved",
        data: usage,
      });
    } catch (error: any) {
      logger.error("Error getting current usage:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || "Failed to get current usage",
      });
    }
  }

  /**
   * Get billing history
   */
  static async getBillingHistory(req: Request, res: Response) {
    try {
      const { storeId } = req.params;
      const limit = parseInt(req.query.limit as string) || 12;
      
      const history = await usageTrackingService.getBillingHistory(storeId, limit);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Billing history retrieved",
        data: history,
      });
    } catch (error: any) {
      logger.error("Error getting billing history:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || "Failed to get billing history",
      });
    }
  }

  /**
   * Get period details
   */
  static async getPeriodDetails(req: Request, res: Response) {
    try {
      const { storeId, period } = req.params;
      
      // Validate period format
      if (!/^\d{4}-\d{2}$/.test(period)) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.BAD_REQUEST,
          message: "Invalid period format. Use YYYY-MM",
        });
      }
      
      const details = await usageTrackingService.getPeriodDetails(storeId, period);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Period details retrieved",
        data: details,
      });
    } catch (error: any) {
      logger.error("Error getting period details:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || "Failed to get period details",
      });
    }
  }

  /**
   * Get plan info
   */
  static async getPlanInfo(req: Request, res: Response) {
    try {
      const { storeId } = req.params;
      
      const planConfig = usageTrackingService.getDefaultPlanConfig();
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Plan info retrieved",
        data: {
          planName: usageTrackingService.getPlanDisplayName(),
          planTier: planConfig.planTier,
          description: planConfig.description
        },
      });
    } catch (error: any) {
      logger.error("Error getting plan info:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || "Failed to get plan info",
      });
    }
  }

  /**
   * Get account balance
   */
  static async getAccountBalance(req: Request, res: Response) {
    try {
      const { storeId } = req.params;
      
      const balance = await StoreBalanceService.getCurrentBalance(storeId);
      
      return ResponseHandler.sendSuccess(res, {
        status: StatusCodes.OK,
        message: "Account balance retrieved",
        data: {
          balance,
          formattedBalance: `$${(balance / 100).toFixed(2)}`
        },
      });
    } catch (error: any) {
      logger.error("Error getting account balance:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: error.message || "Failed to get account balance",
      });
    }
  }
}