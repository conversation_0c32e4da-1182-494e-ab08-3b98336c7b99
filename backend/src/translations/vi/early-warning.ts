export default {
  error: {
    earlyWarning: {
      internalError: '<PERSON><PERSON> xảy ra lỗi nội bộ khi xử lý yêu cầu của bạn',
      invalidSignature: 'Chữ ký không hợp lệ trong phản hồi',
      invalidAlertSignature: 'Chữ ký không hợp lệ trong dữ liệu cảnh báo'
    }
  },
  success: {
    earlyWarning: {
      activated: 'Dịch vụ cảnh báo sớm đã được kích hoạt thành công',
      statusRetrieved: 'Trạng thái cảnh báo sớm đã được truy xuất thành công',
      callbackUpdated: 'URL Callback đã được cập nhật thành công',
      feedbackSent: 'Phản hồi đã được gửi thành công',
      alertProcessed: 'Cảnh báo đã được xử lý thành công',
      alertsRetrieved: '<PERSON><PERSON><PERSON> cảnh báo sớm đã được truy xuất thành công'
    }
  }
};
