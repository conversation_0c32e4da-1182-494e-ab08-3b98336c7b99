export default {
  // Common messages
  common: {
    notFound: "Không tìm thấy vai trò",
    permissionDenied: "Từ chối quyền truy cập",
  },

  // CRUD operations
  crud: {
    // Create
    createSuccess: "Tạo vai trò thành công",
    createFailed: "Tạo vai trò thất bại",

    // Read
    fetchSuccess: "L<PERSON>y danh sách vai trò thành công",
    fetchFailed: "Lấy danh sách vai trò thất bại",
    getRoleSuccess: "Lấy thông tin vai trò thành công",
    getRoleFailed: "L<PERSON>y thông tin vai trò thất bại",

    // Update
    updateSuccess: "Cập nhật vai trò thành công",
    updateFailed: "Cập nhật vai trò thất bại",

    // Delete
    deleteSuccess: "Xóa vai trò thành công",
    deleteFailed: "Xóa vai trò thất bại",
  },

  // Validation messages
  validation: {
    roleExists: "Vai trò đã tồn tại",
    invalidRole: "Định dạng vai trò không hợp lệ",
    requiredFields: "Vui lòng điền đầy đủ các trường bắt buộc",
    cannotDeleteSystemRole: "Không thể xóa vai trò hệ thống",
    roleInUse: "Vai trò đang được sử dụng bởi người dùng",
  },

  // Status messages
  status: {
    active: "Vai trò đang hoạt động",
    inactive: "Vai trò không hoạt động",
    system: "Vai trò hệ thống",
    custom: "Vai trò tùy chỉnh",
  },

  // Permission messages
  permission: {
    invalidPermission: "Quyền không hợp lệ",
    permissionGranted: "Cấp quyền thành công",
    permissionRevoked: "Thu hồi quyền thành công",
    cannotModifySystemPermissions:
      "Không thể thay đổi quyền của vai trò hệ thống",
  },
};
