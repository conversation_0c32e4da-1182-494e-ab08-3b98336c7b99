export default {
  // Common messages
  common: {
    notFound: "Không tìm thấy người dùng",
    permissionDenied: "Từ chối quyền truy cập",
  },

  // CRUD operations
  crud: {
    // Create
    createSuccess: "Tạo người dùng thành công",
    createFailed: "Tạo người dùng thất bại",

    // Read
    fetchSuccess: "L<PERSON>y danh sách người dùng thành công",
    fetchFailed: "Lấy danh sách người dùng thất bại",
    getUserSuccess: "Lấy thông tin người dùng thành công",
    getUserFailed: "Lấy thông tin người dùng thất bại",

    // Update
    updateSuccess: "Cập nhật người dùng thành công",
    updateFailed: "Cập nhật người dùng thất bại",

    // Delete
    deleteSuccess: "Xóa người dùng thành công",
    deleteFailed: "Xóa người dùng thất bại",
  },

  // Validation messages
  validation: {
    emailPhoneExists: "Email hoặc số điện thoại đã được sử dụng",
    invalidEmail: "Định dạng email không hợp lệ",
    invalidPhone: "Định dạng số điện thoại không hợp lệ",
    requiredFields: "Vui lòng điền đầy đủ các trường bắt buộc",
  },

  // Password related
  password: {
    invalidPassword: "Mật khẩu cũ không chính xác",
    changePasswordSuccess: "Thay đổi mật khẩu thành công",
    changePasswordFailed: "Thay đổi mật khẩu thất bại",
    passwordTooWeak: "Mật khẩu quá yếu",
    passwordMismatch: "Mật khẩu không khớp",
  },

  // Status messages
  status: {
    active: "Người dùng đang hoạt động",
    inactive: "Người dùng không hoạt động",
    blocked: "Người dùng đã bị khóa",
  },
};
