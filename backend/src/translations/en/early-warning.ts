export default {
  error: {
    earlyWarning: {
      internalError: 'An internal error occurred while processing your request',
      invalidSignature: 'Invalid signature in response',
      invalidAlertSignature: 'Invalid signature in alert data'
    }
  },
  success: {
    earlyWarning: {
      activated: 'Early warning service activated successfully',
      statusRetrieved: 'Early warning status retrieved successfully',
      callbackUpdated: 'Callback URL updated successfully',
      feedbackSent: 'Feedback sent successfully',
      alertProcessed: '<PERSON><PERSON> processed successfully',
      alertsRetrieved: 'Early warning alerts retrieved successfully'
    }
  }
};
