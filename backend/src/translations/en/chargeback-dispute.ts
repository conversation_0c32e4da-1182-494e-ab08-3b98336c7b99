export default {
  error: {
    chargebackDispute: {
      internalError: 'An internal error occurred while processing your request',
      invalidSignature: 'Invalid signature in response',
    }
  },
  success: {
    chargebackDispute: {
      documentReady: 'Dispute document successfully prepared',
      waitingForMaterials: 'Dispute submitted, waiting for additional materials',
      statusRetrieved: 'Dispute status retrieved successfully',
      materialsSubmitted: 'Additional materials submitted successfully'
    }
  }
};
