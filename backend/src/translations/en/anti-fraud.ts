export default {
  error: {
    antiFraud: {
      internalError: 'An internal error occurred while processing your request',
      requestFailed: 'Risk check request failed',
      notifyFailed: 'Transaction notification failed'
    }
  },
  success: {
    antiFraud: {
      riskCheckCompleted: 'Risk assessment completed successfully',
      notifyCompleted: 'Transaction notification sent successfully'
    }
  }
};
