export default {
  // Common messages
  common: {
    notFound: "Role not found",
    permissionDenied: "Permission denied",
  },

  // CRUD operations
  crud: {
    // Create
    createSuccess: "Role created successfully",
    createFailed: "Failed to create role",

    // Read
    fetchSuccess: "Role list fetched successfully",
    fetchFailed: "Failed to fetch role list",
    getRoleSuccess: "Role details fetched successfully",
    getRoleFailed: "Failed to fetch role details",

    // Update
    updateSuccess: "Role updated successfully",
    updateFailed: "Failed to update role",

    // Delete
    deleteSuccess: "Role deleted successfully",
    deleteFailed: "Failed to delete role",
  },

  // Validation messages
  validation: {
    roleExists: "Role already exists",
    invalidRole: "Invalid role format",
    requiredFields: "Please fill in all required fields",
    cannotDeleteSystemRole: "Cannot delete system role",
    roleInUse: "Role is currently in use by users",
  },

  // Status messages
  status: {
    active: "Role is active",
    inactive: "Role is inactive",
    system: "System role",
    custom: "Custom role",
  },

  // Permission messages
  permission: {
    invalidPermission: "Invalid permission",
    permissionGranted: "Permission granted successfully",
    permissionRevoked: "Permission revoked successfully",
    cannotModifySystemPermissions: "Cannot modify system role permissions",
  },
};
