export default {
  // Common messages
  common: {
    notFound: "User not found",
    permissionDenied: "Permission denied",
  },

  // CRUD operations
  crud: {
    // Create
    createSuccess: "User created successfully",
    createFailed: "Failed to create user",

    // Read
    fetchSuccess: "User list fetched successfully",
    fetchFailed: "Failed to fetch user list",
    getUserSuccess: "User details fetched successfully",
    getUserFailed: "Failed to fetch user details",

    // Update
    updateSuccess: "User updated successfully",
    updateFailed: "Failed to update user",

    // Delete
    deleteSuccess: "User deleted successfully",
    deleteFailed: "Failed to delete user",
  },

  // Validation messages
  validation: {
    emailPhoneExists: "Email or phone number already in use",
    invalidEmail: "Invalid email format",
    invalidPhone: "Invalid phone number format",
    requiredFields: "Please fill in all required fields",
  },

  // Password related
  password: {
    invalidPassword: "Old password is incorrect",
    changePasswordSuccess: "Password changed successfully",
    changePasswordFailed: "Failed to change password",
    passwordTooWeak: "Password is too weak",
    passwordMismatch: "Passwords do not match",
  },

  // Status messages
  status: {
    active: "User is active",
    inactive: "User is inactive",
    blocked: "User is blocked",
  },
};
