import <PERSON><PERSON>and<PERSON> from "@/utils/response-handler.util";
import { Request, Response, NextFunction } from "express";
import { StatusCodes } from "http-status-codes";
import <PERSON><PERSON> from "joi";

class ValidationMiddleware {
  static register = async (req: Request, res: Response, next: NextFunction) => {
    const schema = Joi.object({
      fullName: Joi.string().required(),
      phoneNumber: Joi.string().required().min(10).max(10),
      email: Joi.string().required().email(),
      address: Joi.string().required(),
      password: Joi.string().required().min(8),
      note: Joi.string().allow("", null),
      avatar: Joi.string().allow("", null),
      gender: Joi.string().valid("male", "female", "other").required(),
      birthDate: Joi.date().required(),
    });

    const { error } = schema.validate(req.body);
    if (error) {
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.details[0].message,
      });
    }
    next();
  };

  static create = async (req: Request, res: Response, next: NextFunction) => {
    const schema = Joi.object({
      fullName: Joi.string().required(),
      phoneNumber: Joi.string().required().min(10).max(10),
      email: Joi.string().required().email(),
      address: Joi.string().required(),
      password: Joi.string().required().min(8),
      status: Joi.boolean().default(true),
      note: Joi.string().allow("", null),
      avatar: Joi.string().allow("", null),
      gender: Joi.string().valid("male", "female", "other").required(),
      birthDate: Joi.date().required(),
      role: Joi.string().required(),
    });

    const { error } = schema.validate(req.body);
    if (error) {
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: error.details[0].message,
      });
    }
    next();
  };
}

export default ValidationMiddleware;
