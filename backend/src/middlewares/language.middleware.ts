import { Request, Response, NextFunction } from "express";
import i18next, { i18n } from "i18next";
import { createNamespace, getNamespace } from "cls-hooked";
import { LRUCache } from "lru-cache";
import enTranslations from "@/translations/en";
import viTranslations from "@/translations/vi";

// Định nghĩa interface cho Pool
interface I18nInstancePool {
  acquire(lang: string): Promise<i18n>;
  release(instance: i18n): void;
}

const LANGUAGE_SUPPORTED = ["en", "vi"];
const DEFAULT_LANGUAGE = "en";

// Class quản lý pool của i18n instances
class I18nPool implements I18nInstancePool {
  private readonly pool: Map<string, i18n[]>;
  private readonly maxPoolSize: number;
  private readonly minInstances: number;
  private readonly resources: { [key: string]: any };

  constructor(maxPoolSize = 50, minInstances = 5) {
    this.pool = new Map();
    this.maxPoolSize = maxPoolSize;
    this.minInstances = minInstances;

    // Load all translations
    this.resources = {
      en: { translation: enTranslations },
      vi: { translation: viTranslations },
    };

    // Khởi tạo pool với số lượng instance tối thiểu cho mỗi ngôn ngữ
    LANGUAGE_SUPPORTED.forEach((lang) => {
      const instances = Array(minInstances)
        .fill(null)
        .map(() => this.createInstance(lang));
      this.pool.set(lang, instances);
    });
  }

  private createInstance(lang: string): i18n {
    const instance = i18next.createInstance();
    instance.init({
      lng: lang,
      resources: this.resources,
      fallbackLng: DEFAULT_LANGUAGE,
      interpolation: {
        escapeValue: false,
      },
      returnNull: false,
      returnEmptyString: false,
      parseMissingKeyHandler: (key) => `${key}`,
    });
    return instance;
  }

  async acquire(lang: string): Promise<i18n> {
    const instances = this.pool.get(lang) || [];

    // Nếu có sẵn instance trong pool thì lấy ra
    if (instances.length > 0) {
      return instances.pop()!;
    }

    // Nếu pool của ngôn ngữ này chưa đạt max size thì tạo mới
    if (instances.length < this.maxPoolSize) {
      return this.createInstance(lang);
    }

    // Nếu đã đạt max size thì đợi và thử lại
    await new Promise((resolve) => setTimeout(resolve, 100));
    return this.acquire(lang);
  }

  release(instance: i18n): void {
    const lang = instance.language;
    const instances = this.pool.get(lang) || [];

    // Chỉ trả instance về pool nếu chưa đạt max size
    if (instances.length < this.maxPoolSize) {
      instances.push(instance);
      this.pool.set(lang, instances);
    }
  }
}

// Cache cho các bản dịch đã được xử lý
class TranslationCache {
  private cache: LRUCache<string, string>;

  constructor(maxSize = 1000) {
    this.cache = new LRUCache({
      max: maxSize,
      ttl: 1000 * 60 * 60, // Cache 1 giờ
    });
  }

  getKey(lang: string, key: string, params?: Record<string, any>): string {
    return `${lang}:${key}:${JSON.stringify(params || {})}`;
  }

  get(
    lang: string,
    key: string,
    params?: Record<string, any>
  ): string | undefined {
    return this.cache.get(this.getKey(lang, key, params));
  }

  set(
    lang: string,
    key: string,
    value: string,
    params?: Record<string, any>
  ): void {
    this.cache.set(this.getKey(lang, key, params), value);
  }
}

// Khởi tạo namespace, pool và cache
const i18nNamespace = createNamespace("i18n");
const pool = new I18nPool();
const cache = new TranslationCache();

// Middleware xử lý ngôn ngữ với pool và cache
export const languageMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  i18nNamespace.run(async () => {
    const lang =
      req.headers["accept-language"] || req.query.lang || DEFAULT_LANGUAGE;
    const supportedLangs = LANGUAGE_SUPPORTED;
    const currentLang = supportedLangs.includes(lang.toString())
      ? lang
      : DEFAULT_LANGUAGE;

    // Lấy instance từ pool
    const i18nInstance = await pool.acquire(currentLang.toString());

    // Wrap instance với cache
    const wrappedInstance = new Proxy(i18nInstance, {
      get(target, prop) {
        if (prop === "t") {
          return (key: string, params?: Record<string, any>) => {
            // Kiểm tra cache trước
            const cachedValue = cache.get(currentLang.toString(), key, params);
            if (cachedValue) return cachedValue;

            // Nếu không có trong cache thì dịch và lưu cache
            const translation = target.t(key, params);
            cache.set(currentLang.toString(), key, translation, params);
            return translation;
          };
        }
        return target[prop as keyof typeof target];
      },
    });

    // Lưu wrapped instance vào namespace
    i18nNamespace.set("i18nInstance", wrappedInstance);

    // Cleanup khi request kết thúc
    res.on("finish", () => {
      const instance = i18nNamespace.get("i18nInstance");
      if (instance) {
        pool.release(instance);
      }
    });

    next();
  });
};

// Helper function để lấy i18n instance
export const getI18n = (): i18n => {
  const namespace = getNamespace("i18n");
  if (!namespace) {
    throw new Error("i18n namespace not found");
  }

  const i18nInstance = namespace.get("i18nInstance");
  if (!i18nInstance) {
    throw new Error("i18n instance not found");
  }

  return i18nInstance;
};
