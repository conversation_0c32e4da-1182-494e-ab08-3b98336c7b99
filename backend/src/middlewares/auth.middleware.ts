import jwt from "jsonwebtoken";
import { Request, Response, NextFunction } from "express";
import { env } from "@/config/environment";
import redisClient from "@/config/redis";
import ResponseHandler from "@/utils/response-handler.util";
import { StatusCodes } from "http-status-codes";
import { RedisPrefix } from "@/constants/prefix";
import { AuthenticatedUser } from "@/models/user.model";
import { createLogger } from "@/config/logger";
import ClientService from "@/services/client.service";
import { RoleType } from "@/constants/type";
import ShopifyService from "@/services/shopify.service";
import PrismaService from "@/services/prisma.service";

const logger = createLogger(
  "logs/middleware/auth/error.log",
  "logs/middleware/auth/combined.log"
);

class AuthMiddleware {
  /**
   * Validate API key from request header
   */
  static validateApiKey = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    // const apiKey = req.header("x-api-key");
    // logger.info(`Validating API key: ${apiKey ? "Present" : "Missing"}`);

    // if (!apiKey) {
    //   logger.warn("API key missing in request");
    //   return ResponseHandler.sendError(res, {
    //     status: StatusCodes.FORBIDDEN,
    //     message: "API key is missing",
    //   });
    // }

    // const isValidApiKey = await ClientService.isAPIKeyValid(apiKey);
    // if (!isValidApiKey) {
    //   logger.warn(`Invalid API key attempt: ${apiKey}`);
    //   return ResponseHandler.sendError(res, {
    //     status: StatusCodes.FORBIDDEN,
    //     message: "Invalid API key",
    //   });
    // }

    logger.info("API key tmp all validation successful");
    next();
  };

  /**
   * Validate Shopify access token and attach user to request
   */
  static validateShopifyToken = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    const authorization = req.header("Authorization");
    logger.info(`Validating Shopify token: ${authorization ? "Present" : "Missing"}`);

    if (!authorization) {
      logger.warn("Authorization header missing");
      return ResponseHandler.sendError(res, {
        status: StatusCodes.UNAUTHORIZED,
        message: "Authorization header is missing",
      });
    }

    const token = authorization.replace("Bearer ", "");
    if (!token) {
      logger.warn("Access token missing");
      return ResponseHandler.sendError(res, {
        status: StatusCodes.UNAUTHORIZED,
        message: "Access token is missing",
      });
    }

    try {
      const prisma = PrismaService.getInstance().getClient();
      
      logger.info(`Searching for token: ${token}`);
      
      // Try different approaches to find the linked store
      // First, let's try to find all shopify stores to debug
      const allShopifyStores = await (prisma as any).linkedStore.findMany({
        where: {
          provider: 'shopify'
        }
      });
      
      logger.info(`Found ${allShopifyStores.length} Shopify stores`);
      
      // Debug: Log the actual JSON data to see what's in there
      allShopifyStores.forEach((store: any, index: number) => {
        logger.info(`Store ${index + 1}: ${store.id}`);
        logger.info(`Data: ${JSON.stringify(store.data)}`);
        if (store.data && store.data.accessToken) {
          logger.info(`Access token in store: ${store.data.accessToken}`);
          logger.info(`Token match: ${store.data.accessToken === token}`);
        }
      });
      
      // Find the linked store with this access token using string contains (including inactive stores)
      let linkedStore = await (prisma as any).linkedStore.findFirst({
        where: {
          provider: 'shopify',
          data: {
            path: ['accessToken'],
            equals: token
          }
        },
        include: {
          user: {
            select: {
              id: true,
              code: true,
              email: true,
              phoneNumber: true,
              fullName: true,
              isActive: true
            }
          }
        }
      });
      
      // If that doesn't work, try with string_contains
      if (!linkedStore) {
        logger.info("Trying alternative query method...");
        linkedStore = await (prisma as any).linkedStore.findFirst({
          where: {
            provider: 'shopify',
            data: {
              string_contains: token
            }
          },
          include: {
            user: {
              select: {
                id: true,
                code: true,
                email: true,
                phoneNumber: true,
                fullName: true,
                isActive: true
              }
            }
          }
        });
        
        if (linkedStore) {
          logger.info("Found store using alternative method");
        }
      }
      
      // If both Prisma methods fail, try JavaScript approach
      if (!linkedStore) {
        logger.info("Trying JavaScript-based search...");
        const matchingStore = allShopifyStores.find((store: any) => {
          return store.data && store.data.accessToken === token;
        });
        
        if (matchingStore) {
          logger.info("Found store using JavaScript method");
          // Now get the full store with user relationship
          linkedStore = await (prisma as any).linkedStore.findFirst({
            where: {
              id: matchingStore.id
            },
            include: {
              user: {
                select: {
                  id: true,
                  code: true,
                  email: true,
                  phoneNumber: true,
                  fullName: true,
                  isActive: true
                }
              }
            }
          });
        }
      }

      logger.info(`Linked store found: ${linkedStore ? 'Yes' : 'No'}`);
      if (linkedStore) {
        logger.info(`User attached: ${linkedStore.user ? 'Yes' : 'No'}`);
        logger.info(`Store ID: ${linkedStore.id}, User ID: ${linkedStore.userId}`);
      }

      if (!linkedStore || !linkedStore.user) {
        logger.warn("Shopify token not found or no associated user");
        return ResponseHandler.sendError(res, {
          status: StatusCodes.UNAUTHORIZED,
          message: "Invalid Shopify access token",
        });
      }

      // Check if user is active and not uninstalled
      if (!linkedStore.user.isActive || linkedStore.user.uninstalledAt) {
        const uninstalledAt = linkedStore.user.uninstalledAt;
        const isTemporaryUninstall = uninstalledAt && 
          (new Date().getTime() - uninstalledAt.getTime()) < (48 * 60 * 60 * 1000); // Less than 48 hours
        
        logger.warn(`User ${linkedStore.user.id} is inactive/uninstalled - access denied`, {
          isActive: linkedStore.user.isActive,
          uninstalledAt: uninstalledAt,
          isTemporaryUninstall
        });
        
        return ResponseHandler.sendError(res, {
          status: StatusCodes.UNAUTHORIZED,
          message: isTemporaryUninstall 
            ? "App appears to be uninstalled. Please reinstall the app to continue."
            : "User account is inactive. Please reinstall the app.",
          data: {
            requiresReinstall: true,
            uninstalledAt: uninstalledAt,
            isTemporaryUninstall
          }
        });
      }

      // Create user object similar to JWT token payload
      const user: AuthenticatedUser = {
        id: linkedStore.user.id,
        code: linkedStore.user.code,
        email: linkedStore.user.email,
        phoneNumber: linkedStore.user.phoneNumber,
        roles: [] // Default empty roles array since User model doesn't have roles field
      };

      logger.info(`Shopify token validated successfully for user: ${user.id}`);
      (req as any).user = user;
      next();
    } catch (error) {
      logger.error("Shopify token validation error:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.UNAUTHORIZED,
        message: "Invalid Shopify access token",
      });
    }
  };

  /**
   * Validate JWT token and attach user to request
   */
  static validateToken = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    const authorization = req.header("Authorization");
    logger.info(`Validating token: ${authorization ? "Present" : "Missing"}`);

    if (!authorization) {
      logger.warn("Authorization header missing");
      return ResponseHandler.sendError(res, {
        status: StatusCodes.UNAUTHORIZED,
        message: "Authorization header is missing",
      });
    }

    const token = authorization.replace("Bearer ", "");
    if (!token) {
      logger.warn("Access token missing");
      return ResponseHandler.sendError(res, {
        status: StatusCodes.UNAUTHORIZED,
        message: "Access token is missing",
      });
    }

    try {
      const tokenExists = await redisClient.get(
        `${RedisPrefix.ACCESS_TOKEN}_${token}`
      );
      if (!tokenExists) {
        logger.warn("Token not found in Redis");
        return ResponseHandler.sendError(res, {
          status: StatusCodes.UNAUTHORIZED,
          message: "Invalid access token",
        });
      }

      const decoded = jwt.verify(token, env.JWT_SECRET, {
        algorithms: ["HS256"],
      }) as AuthenticatedUser;

      // Check if user is still active (not uninstalled)
      const prisma = PrismaService.getInstance().getClient();
      const user = await (prisma as any).user.findUnique({
        where: { id: decoded.id },
        select: {
          id: true,
          isActive: true,
          uninstalledAt: true,
        },
      });

      if (!user) {
        logger.warn(`User ${decoded.id} not found - token invalid`);
        return ResponseHandler.sendError(res, {
          status: StatusCodes.UNAUTHORIZED,
          message: "Invalid access token - user not found",
        });
      }

      if (!user.isActive || user.uninstalledAt) {
        const uninstalledAt = user.uninstalledAt;
        const isTemporaryUninstall = uninstalledAt && 
          (new Date().getTime() - uninstalledAt.getTime()) < (48 * 60 * 60 * 1000); // Less than 48 hours
        
        logger.warn(`User ${decoded.id} is inactive/uninstalled - token invalid`, {
          isActive: user.isActive,
          uninstalledAt: uninstalledAt,
          isTemporaryUninstall
        });
        
        return ResponseHandler.sendError(res, {
          status: StatusCodes.UNAUTHORIZED,
          message: isTemporaryUninstall 
            ? "App appears to be uninstalled. Please reinstall the app to continue."
            : "User account is inactive. Please reinstall the app.",
          data: {
            requiresReinstall: true,
            uninstalledAt: uninstalledAt,
            isTemporaryUninstall
          }
        });
      }

      logger.info(
        `Token validated successfully for user: ${decoded.id}`
      );
      (req as any).user = decoded as AuthenticatedUser;
      next();
    } catch (error) {
      logger.error("Token validation error:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.UNAUTHORIZED,
        message: "Invalid access token",
      });
    }
  };

  /**
   * Check if user has admin role
   */
  static isAdmin = async (req: Request, res: Response, next: NextFunction) => {
    const user = (req as any).user as AuthenticatedUser;
    logger.info(`Checking admin role for user: ${user?.id}`);

    if (!user || !user.roles.includes(RoleType.ADMIN)) {
      logger.warn(`Admin access denied for user: ${user?.id}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.FORBIDDEN,
        message: "Admin access required",
      });
    }

    logger.info(`Admin access granted for user: ${user.id}`);
    next();
  };

  /**
   * Check if user has required role
   */
  static hasRole = (roles: string[]) => {
    return async (req: Request, res: Response, next: NextFunction) => {
      const user = (req as any).user as AuthenticatedUser;

      if (!user || !user.roles.some((role) => roles.includes(role))) {
        return ResponseHandler.sendError(res, {
          status: StatusCodes.FORBIDDEN,
          message: "Insufficient permissions",
        });
      }

      next();
    };
  };

  /**
   * Check if user has active billing subscription (for Shopify stores)
   */
  static hasBillingAccess = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    const user = (req as any).user as AuthenticatedUser;
    logger.info(`Checking billing access for user: ${user?.id}`);

    if (!user) {
      logger.warn("User not authenticated for billing access check");
      return ResponseHandler.sendError(res, {
        status: StatusCodes.UNAUTHORIZED,
        message: "User authentication required",
      });
    }

    try {
      const prisma = PrismaService.getInstance().getClient();
      
      // Get user's Shopify stores
      const shopifyStores = await (prisma as any).linkedStore.findMany({
        where: {
          userId: user.id,
          provider: 'shopify'
        },
        include: {
          shopifySubscription: true
        }
      });

      // Check if user has any active subscriptions
      const hasActiveSubscription = shopifyStores.some((store: any) => 
        store.shopifySubscription && store.shopifySubscription.status === 'ACTIVE'
      );

      if (!hasActiveSubscription) {
        logger.warn(`Billing access denied for user: ${user.id} - No active subscriptions`);
        return ResponseHandler.sendError(res, {
          status: StatusCodes.PAYMENT_REQUIRED,
          message: "Active billing subscription required to access this resource",
        });
      }

      logger.info(`Billing access granted for user: ${user.id}`);
      next();
    } catch (error) {
      logger.error("Error checking billing access:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: "Failed to verify billing access",
      });
    }
  };

  /**
   * Check if specific store has active billing subscription
   */
  static hasStoreBillingAccess = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    const user = (req as any).user as AuthenticatedUser;
    const storeId = req.params.storeId || req.params.linkedStoreId || req.body.linkedStoreId;
    
    logger.info(`Checking store billing access for user: ${user?.id}, store: ${storeId}`);

    if (!user || !storeId) {
      logger.warn("User or store ID missing for billing access check");
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: "User authentication and store ID required",
      });
    }

    try {
      const prisma = PrismaService.getInstance().getClient();
      
      // Get specific store with subscription
      const store = await (prisma as any).linkedStore.findFirst({
        where: {
          id: storeId,
          userId: user.id,
          provider: 'shopify'
        },
        include: {
          shopifySubscription: true
        }
      });

      if (!store) {
        logger.warn(`Store not found or access denied for user: ${user.id}, store: ${storeId}`);
        return ResponseHandler.sendError(res, {
          status: StatusCodes.NOT_FOUND,
          message: "Store not found or access denied",
        });
      }

      if (!store.shopifySubscription || store.shopifySubscription.status !== 'ACTIVE') {
        logger.warn(`Store billing access denied for user: ${user.id}, store: ${storeId} - No active subscription`);
        return ResponseHandler.sendError(res, {
          status: StatusCodes.PAYMENT_REQUIRED,
          message: "Active billing subscription required for this store",
        });
      }

      logger.info(`Store billing access granted for user: ${user.id}, store: ${storeId}`);
      next();
    } catch (error) {
      logger.error("Error checking store billing access:", error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        message: "Failed to verify store billing access",
      });
    }
  };

  /**
   * Check if store requires billing setup (allows basic operations, warns about billing)
   */
  static requiresBillingSetup = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    const user = (req as any).user as AuthenticatedUser;
    const storeId = req.params.storeId || req.params.linkedStoreId || req.body.linkedStoreId;
    
    logger.info(`Checking billing setup requirement for user: ${user?.id}, store: ${storeId}`);

    if (!user || !storeId) {
      return next(); // Allow request to proceed, let individual controllers handle
    }

    try {
      const prisma = PrismaService.getInstance().getClient();
      
      // Get specific store with subscription
      const store = await (prisma as any).linkedStore.findFirst({
        where: {
          id: storeId,
          userId: user.id,
          provider: 'shopify'
        },
        include: {
          shopifySubscription: true
        }
      });

      if (!store) {
        return next(); // Store validation will be handled by individual controllers
      }

      // Add billing status to request for controllers to use
      (req as any).billingStatus = {
        hasActiveSubscription: store.shopifySubscription && store.shopifySubscription.status === 'ACTIVE',
        subscriptionStatus: store.shopifySubscription?.status || 'NONE',
        requiresSetup: !store.shopifySubscription || store.shopifySubscription.status !== 'ACTIVE'
      };

      logger.info(`Billing setup status for store ${storeId}: ${(req as any).billingStatus.subscriptionStatus}`);
      next();
    } catch (error) {
      logger.error("Error checking billing setup requirement:", error);
      // Allow request to proceed even if billing check fails
      next();
    }
  };

  /**
   * Combine multiple middleware
   */
  static compose = (middlewares: Array<any>) => {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        for (const middleware of middlewares) {
          await new Promise((resolve, reject) => {
            middleware(req, res, (error: any) => {
              if (error) reject(error);
              resolve(true);
            });
          });
        }
        next();
      } catch (error) {
        next(error);
      }
    };
  };
}

// Preset middleware combinations
export const publicRoute = AuthMiddleware.validateApiKey;
export const userRoute = AuthMiddleware.compose([
  AuthMiddleware.validateApiKey,
  AuthMiddleware.validateToken,
]);
export const shopifyRoute = AuthMiddleware.compose([
  // AuthMiddleware.validateApiKey,
  AuthMiddleware.validateShopifyToken,
  // AuthMiddleware.validateToken,
]);
export const adminRoute = AuthMiddleware.compose([
  AuthMiddleware.validateApiKey,
  AuthMiddleware.validateToken,
  AuthMiddleware.isAdmin,
]);
export const billingRoute = AuthMiddleware.compose([
  AuthMiddleware.validateApiKey,
  AuthMiddleware.validateToken,
  AuthMiddleware.hasBillingAccess,
]);
export const storeBillingRoute = AuthMiddleware.compose([
  AuthMiddleware.validateApiKey,
  AuthMiddleware.validateToken,
  AuthMiddleware.hasStoreBillingAccess,
]);
export const storeRoute = AuthMiddleware.compose([
  AuthMiddleware.validateApiKey,
  AuthMiddleware.validateToken,
  AuthMiddleware.requiresBillingSetup,
]);

// Export individual middleware for custom usage
export const authenticate = userRoute;

export default AuthMiddleware;

// import express from 'express';
// import { publicRoute, userRoute, adminRoute } from '@/middlewares/auth.middleware';

// const router = express.Router();

// Example routes with authentication:
// // Public routes - chỉ cần API key
// router.get('/users', publicRoute, UserController.getUsers);

// // User routes - cần API key và token user
// router.get('/profile', userRoute, UserController.getUserProfile);

// // Admin routes - cần API key, token và quyền admin
// router.post('/users', adminRoute, UserController.createUser);

// // Custom role check
// router.put('/users/:id',
//   AuthMiddleware.compose([
//     AuthMiddleware.validateApiKey,
//     AuthMiddleware.validateToken,
//     AuthMiddleware.hasRole(['admin', 'manager'])
//   ]),
//   UserController.updateUser
// );

// export default router;
