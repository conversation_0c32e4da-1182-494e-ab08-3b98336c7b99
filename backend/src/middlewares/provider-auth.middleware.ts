import { Request, Response, NextFunction } from "express";
import { StatusCodes } from "http-status-codes";
import ResponseHandler from "../utils/response-handler.util";
import { AuthenticatedUser } from "../models/user.model";
import { createLogger } from "../config/logger";
import PrismaService from "../services/prisma.service";

const logger = createLogger(
  "logs/middleware/provider-auth/error.log",
  "logs/middleware/provider-auth/combined.log"
);

type SupportedProvider = 'shopify' | 'stripe';

interface ProviderTokenValidationContext {
  provider: SupportedProvider;
  token: string;
  req: Request;
}

class ProviderAuthMiddleware {
  /**
   * Validate access token for multiple providers (Shopify, Stripe)
   * Uses x-provider header to determine which provider to validate against
   */
  static validateProviderToken = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    const authorization = req.header("Authorization");
    const provider = req.header("x-provider") as SupportedProvider;

    logger.info(`Provider auth validation started`, { 
      provider: provider || 'none',
      hasAuth: authorization ? 'Present' : 'Missing' 
    });

    // Validate required headers
    if (!authorization) {
      logger.warn("Authorization header missing");
      return ResponseHandler.sendError(res, {
        status: StatusCodes.UNAUTHORIZED,
        message: "Authorization header is missing",
      });
    }

    if (!provider) {
      logger.warn("Provider header missing");
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: "x-provider header is required. Supported providers: shopify, stripe",
      });
    }

    if (!['shopify', 'stripe'].includes(provider)) {
      logger.warn(`Unsupported provider: ${provider}`);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.BAD_REQUEST,
        message: "Unsupported provider. Supported providers: shopify, stripe",
      });
    }

    const token = authorization.replace("Bearer ", "");
    if (!token) {
      logger.warn("Access token missing");
      return ResponseHandler.sendError(res, {
        status: StatusCodes.UNAUTHORIZED,
        message: "Access token is missing",
      });
    }

    try {
      const context: ProviderTokenValidationContext = {
        provider,
        token,
        req
      };

      // Route to appropriate provider validation
      let linkedStore;
      switch (provider) {
        case 'shopify':
          linkedStore = await this.validateShopifyToken(context);
          break;
        case 'stripe':
          linkedStore = await this.validateStripeToken(context);
          break;
        default:
          throw new Error(`Unsupported provider: ${provider}`);
      }

      if (!linkedStore || !linkedStore.user) {
        logger.warn(`${provider} token not found or no associated user`);
        return ResponseHandler.sendError(res, {
          status: StatusCodes.UNAUTHORIZED,
          message: `Invalid ${provider} access token`,
        });
      }

      // Common user validation (applies to all providers)
      const validationResult = await this.validateUser(linkedStore.user, provider);
      if (!validationResult.isValid) {
        return ResponseHandler.sendError(res, validationResult.error!);
      }

      // Create user object and attach to request
      const user: AuthenticatedUser = {
        id: linkedStore.user.id,
        code: linkedStore.user.code,
        email: linkedStore.user.email,
        phoneNumber: linkedStore.user.phoneNumber,
        roles: []
      };

      logger.info(`${provider} token validated successfully`, { 
        userId: user.id, 
        linkedStoreId: linkedStore.id,
        provider 
      });

      (req as any).user = user;
      (req as any).linkedStore = linkedStore;
      (req as any).provider = provider;
      next();

    } catch (error) {
      logger.error(`${provider} token validation error:`, error);
      return ResponseHandler.sendError(res, {
        status: StatusCodes.UNAUTHORIZED,
        message: `Invalid ${provider} access token`,
      });
    }
  };

  /**
   * Validate Shopify access token using existing logic
   */
  private static async validateShopifyToken(context: ProviderTokenValidationContext) {
    const { token } = context;
    const prisma = PrismaService.getInstance().getClient();

    logger.info(`Validating Shopify token: ${token.substring(0, 10)}...`);

    // Method 1: JSON Path Query
    let linkedStore = await (prisma as any).linkedStore.findFirst({
      where: {
        provider: 'shopify',
        data: {
          path: ['accessToken'],
          equals: token
        }
      },
      include: {
        user: {
          select: {
            id: true,
            code: true,
            email: true,
            phoneNumber: true,
            fullName: true,
            isActive: true,
            uninstalledAt: true
          }
        }
      }
    });

    // Method 2: String Contains Query (fallback)
    if (!linkedStore) {
      logger.info("Trying alternative Shopify query method...");
      linkedStore = await (prisma as any).linkedStore.findFirst({
        where: {
          provider: 'shopify',
          data: {
            string_contains: token
          }
        },
        include: {
          user: {
            select: {
              id: true,
              code: true,
              email: true,
              phoneNumber: true,
              fullName: true,
              isActive: true,
              uninstalledAt: true
            }
          }
        }
      });
    }

    // Method 3: JavaScript-based Search (fallback)
    if (!linkedStore) {
      logger.info("Trying JavaScript-based Shopify search...");
      const allShopifyStores = await (prisma as any).linkedStore.findMany({
        where: {
          provider: 'shopify'
        },
        include: {
          user: {
            select: {
              id: true,
              code: true,
              email: true,
              phoneNumber: true,
              fullName: true,
              isActive: true,
              uninstalledAt: true
            }
          }
        }
      });

      linkedStore = allShopifyStores.find((store: any) => {
        return store.data && store.data.accessToken === token;
      });
    }

    return linkedStore;
  }

  /**
   * Validate Stripe access token
   */
  private static async validateStripeToken(context: ProviderTokenValidationContext) {
    const { token } = context;
    const prisma = PrismaService.getInstance().getClient();

    logger.info(`Validating Stripe token: ${token.substring(0, 10)}...`);

    // Method 1: JSON Path Query
    let linkedStore = await (prisma as any).linkedStore.findFirst({
      where: {
        provider: 'stripe',
        data: {
          path: ['accessToken'],
          equals: token
        }
      },
      include: {
        user: {
          select: {
            id: true,
            code: true,
            email: true,
            phoneNumber: true,
            fullName: true,
            isActive: true,
            uninstalledAt: true
          }
        }
      }
    });

    // Method 2: String Contains Query (fallback)
    if (!linkedStore) {
      logger.info("Trying alternative Stripe query method...");
      linkedStore = await (prisma as any).linkedStore.findFirst({
        where: {
          provider: 'stripe',
          data: {
            string_contains: token
          }
        },
        include: {
          user: {
            select: {
              id: true,
              code: true,
              email: true,
              phoneNumber: true,
              fullName: true,
              isActive: true,
              uninstalledAt: true
            }
          }
        }
      });
    }

    // Method 3: JavaScript-based Search (fallback)
    if (!linkedStore) {
      logger.info("Trying JavaScript-based Stripe search...");
      const allStripeStores = await (prisma as any).linkedStore.findMany({
        where: {
          provider: 'stripe'
        },
        include: {
          user: {
            select: {
              id: true,
              code: true,
              email: true,
              phoneNumber: true,
              fullName: true,
              isActive: true,
              uninstalledAt: true
            }
          }
        }
      });

      linkedStore = allStripeStores.find((store: any) => {
        return store.data && store.data.accessToken === token;
      });
    }

    return linkedStore;
  }

  /**
   * Common user validation logic for all providers
   */
  private static async validateUser(user: any, provider: SupportedProvider) {
    // Check if user is active and not uninstalled
    if (!user.isActive || user.uninstalledAt) {
      const uninstalledAt = user.uninstalledAt;
      const isTemporaryUninstall = uninstalledAt && 
        (new Date().getTime() - uninstalledAt.getTime()) < (48 * 60 * 60 * 1000); // Less than 48 hours

      logger.warn(`User ${user.id} is inactive/uninstalled - access denied`, {
        provider,
        isActive: user.isActive,
        uninstalledAt: uninstalledAt,
        isTemporaryUninstall
      });

      const errorMessage = provider === 'shopify' 
        ? (isTemporaryUninstall 
          ? "App appears to be uninstalled. Please reinstall the app to continue."
          : "User account is inactive. Please reinstall the app.")
        : (isTemporaryUninstall
          ? "Account appears to be disconnected. Please reconnect your account to continue."
          : "User account is inactive. Please reconnect your account.");

      return {
        isValid: false,
        error: {
          status: StatusCodes.UNAUTHORIZED,
          message: errorMessage,
          data: {
            requiresReinstall: true,
            provider,
            uninstalledAt: uninstalledAt,
            isTemporaryUninstall
          }
        }
      };
    }

    return { isValid: true };
  }


}

// Export the main multi-provider middleware
export const providerAuthRoute = ProviderAuthMiddleware.validateProviderToken;

export default ProviderAuthMiddleware;