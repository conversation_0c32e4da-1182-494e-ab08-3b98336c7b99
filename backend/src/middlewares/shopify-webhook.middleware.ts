import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { env } from '@/config/environment';

/**
 * Middleware to capture raw body for Shopify webhook verification
 * This must be applied before express.json() middleware
 */
export const captureRawBody = (req: Request, res: Response, next: NextFunction) => {
  let data = '';
  
  req.on('data', (chunk) => {
    data += chunk;
  });
  
  req.on('end', () => {
    (req as any).rawBody = data;
    next();
  });
};

/**
 * Middleware to verify Shopify webhook signatures
 */
export const verifyShopifyWebhook = (req: Request, res: Response, next: NextFunction) => {
  try {
    const hmac = req.get('X-Shopify-Hmac-Sha256');
    const rawBody = (req as any).rawBody;
    
    if (!hmac) {
      return res.status(401).json({ error: 'Missing X-Shopify-Hmac-Sha256 header' });
    }

    if (!rawBody) {
      return res.status(400).json({ error: 'Missing request body' });
    }

    const calculatedHmac = crypto
      .createHmac('sha256', env.SHOPIFY_API_SECRET)
      .update(rawBody, 'utf8')
      .digest('base64');

    const isValid = crypto.timingSafeEqual(
      Buffer.from(hmac, 'base64'),
      Buffer.from(calculatedHmac, 'base64')
    );

    if (!isValid) {
      return res.status(401).json({ error: 'Invalid webhook signature' });
    }

    next();
  } catch (error) {
    console.error('Webhook verification error:', error);
    return res.status(500).json({ error: 'Webhook verification failed' });
  }
};

/**
 * Combined middleware for Shopify webhooks
 * Use this for routes that need raw body + verification
 */
export const shopifyWebhookMiddleware = [captureRawBody, verifyShopifyWebhook]; 