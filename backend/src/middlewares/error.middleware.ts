import { Request, Response, NextFunction } from "express";
import { StatusCodes } from "http-status-codes";
import { env } from "@/config/environment";
import ResponseHandler from "@/utils/response-handler.util";

class ErrorMiddleware {
  static error404 = (req: Request, res: Response, next: NextFunction) => {
    return ResponseHandler.sendError(res, {
      status: StatusCodes.NOT_FOUND,
      message: "API endpoint not found",
    });
  };
  static errorHandling = (
    error: any,
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    const message =
      ((error as any).response &&
        (error as any).response.data &&
        (error as any).response.data.message) ||
      (error as any).message ||
      (error as any).toString();

    // In production
    if (env.ENV !== "development") {
      return ResponseHandler.sendError(res, {
        status:
          error.status || error.statusCode || StatusCodes.INTERNAL_SERVER_ERROR,
        message: message,
      });
    }

    return ResponseHandler.sendError(res, {
      status:
        error.status || error.statusCode || StatusCodes.INTERNAL_SERVER_ERROR,
      message: message,
      data: {
        stack: error.stack,
      },
    });
  };
}

export default ErrorMiddleware;
