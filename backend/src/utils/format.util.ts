import { format } from "date-fns";
import { vi } from "date-fns/locale";

interface AccentsMap {
  [key: string]: string;
}

class FormatHelper {
  static removeVietnameseAccents(text: string): string {
    const accentsMap: AccentsMap = {
      à: "a",
      á: "a",
      ã: "a",
      ả: "a",
      ạ: "a",
      ă: "a",
      ằ: "a",
      ắ: "a",
      ẳ: "a",
      ẵ: "a",
      ặ: "a",
      â: "a",
      ầ: "a",
      ấ: "a",
      ẩ: "a",
      ẫ: "a",
      ậ: "a",
      è: "e",
      é: "e",
      ẻ: "e",
      ẽ: "e",
      ẹ: "e",
      ê: "e",
      ề: "e",
      ế: "e",
      ể: "e",
      ễ: "e",
      ệ: "e",
      đ: "d",
      ù: "u",
      ú: "u",
      ủ: "u",
      ũ: "u",
      ụ: "u",
      ư: "u",
      ừ: "u",
      ứ: "u",
      ử: "u",
      ữ: "u",
      ự: "u",
      ò: "o",
      ó: "o",
      ỏ: "o",
      õ: "o",
      ọ: "o",
      ô: "o",
      ồ: "o",
      ố: "o",
      ổ: "o",
      ỗ: "o",
      ộ: "o",
      ơ: "o",
      ờ: "o",
      ớ: "o",
      ở: "o",
      ỡ: "o",
      ợ: "o",
      ì: "i",
      í: "i",
      ỉ: "i",
      ĩ: "i",
      ị: "i",
      ỳ: "y",
      ý: "y",
      ỷ: "y",
      ỹ: "y",
      ỵ: "y",
      À: "A",
      Á: "A",
      Ã: "A",
      Ả: "A",
      Ạ: "A",
      Ă: "A",
      Ằ: "A",
      Ắ: "A",
      Ẳ: "A",
      Ẵ: "A",
      Ặ: "A",
      Â: "A",
      Ầ: "A",
      Ấ: "A",
      Ẩ: "A",
      Ẫ: "A",
      Ậ: "A",
      È: "E",
      É: "E",
      Ẻ: "E",
      Ẽ: "E",
      Ẹ: "E",
      Ê: "E",
      Ề: "E",
      Ế: "E",
      Ể: "E",
      Ễ: "E",
      Ệ: "E",
      Đ: "D",
      Ù: "U",
      Ú: "U",
      Ủ: "U",
      Ũ: "U",
      Ụ: "U",
      Ư: "U",
      Ừ: "U",
      Ứ: "U",
      Ử: "U",
      Ữ: "U",
      Ự: "U",
      Ò: "O",
      Ó: "O",
      Ỏ: "O",
      Õ: "O",
      Ọ: "O",
      Ô: "O",
      Ồ: "O",
      Ố: "O",
      Ổ: "O",
      Ỗ: "O",
      Ộ: "O",
      Ơ: "O",
      Ờ: "O",
      Ớ: "O",
      Ở: "O",
      Ỡ: "O",
      Ợ: "O",
      Ì: "I",
      Í: "I",
      Ỉ: "I",
      Ĩ: "I",
      Ị: "I",
      Ỳ: "Y",
      Ý: "Y",
      Ỷ: "Y",
      Ỹ: "Y",
      Ỵ: "Y",
    };

    return text
      .split("")
      .map((char) => accentsMap[char] || char)
      .join("");
  }

  static formatDate = (date: Date) => {
    return format(date, "dd/MM/yyyy", { locale: vi });
  };
}

export default FormatHelper;
