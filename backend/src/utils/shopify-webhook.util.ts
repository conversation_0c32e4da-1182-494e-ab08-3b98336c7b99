import crypto from 'crypto';
import { env } from '@/config/environment';
import logger from '@/utils/logger.util';

/**
 * Verify Shopify webhook signature using HMAC-SHA256
 * 
 * @param rawBody - Raw body of the webhook request
 * @param signature - X-Shopify-Hmac-Sha256 header value
 * @returns boolean indicating if signature is valid
 */
export function verifyShopifyWebhook(rawBody: string, signature: string): boolean {
  try {
    if (!signature) {
      logger.warn('Missing X-Shopify-Hmac-Sha256 header');
      return false;
    }

    if (!env.SHOPIFY_API_SECRET) {
      logger.error('SHOPIFY_API_SECRET environment variable is not set');
      return false;
    }

    // Create HMAC using the webhook secret
    const hmac = crypto
      .createHmac('sha256', env.SHOPIFY_API_SECRET)
      .update(rawBody, 'utf8')
      .digest('base64');

    // Compare the computed HMAC with the provided signature
    const isValid = hmac === signature;
    
    if (!isValid) {
      logger.warn('Invalid Shopify webhook signature', {
        expected: hmac,
        received: signature,
        bodyLength: rawBody.length
      });
    }

    return isValid;
  } catch (error) {
    logger.error('Error verifying Shopify webhook signature:', { error: error instanceof Error ? error.message : String(error) });
    return false;
  }
}

/**
 * Verify Shopify webhook signature from Express request
 * 
 * @param req - Express request object with rawBody attached
 * @returns boolean indicating if signature is valid
 */
export function verifyShopifyWebhookFromRequest(req: any): boolean {
  const signature = req.get('X-Shopify-Hmac-Sha256');
  const rawBody = req.rawBody;

  if (!rawBody) {
    logger.warn('No raw body available for webhook verification');
    return false;
  }

  // Convert buffer to string if necessary
  const bodyString = typeof rawBody === 'string' ? rawBody : rawBody.toString('utf8');
  
  return verifyShopifyWebhook(bodyString, signature);
}

/**
 * Middleware to capture raw body for webhook verification
 * Should be used before express.json() middleware
 */
export function captureRawBody(req: any, res: any, next: any) {
  let data = '';
  
  req.on('data', (chunk: any) => {
    data += chunk;
  });
  
  req.on('end', () => {
    req.rawBody = data;
    next();
  });
}

/**
 * Express middleware for webhook verification
 * Use this middleware on webhook routes to automatically verify signatures
 */
export function webhookVerificationMiddleware(req: any, res: any, next: any) {
  if (!verifyShopifyWebhookFromRequest(req)) {
    logger.warn('Webhook signature verification failed', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path
    });
    
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid webhook signature'
    });
  }
  
  next();
} 