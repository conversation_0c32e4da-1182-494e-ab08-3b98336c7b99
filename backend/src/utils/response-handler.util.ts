import { Response } from "express";
import { StatusCodes } from "http-status-codes";

export interface DataResponse {
  status: number;
  message: string;
  data?: any;
}

class ResponseHandler {
  static sendSuccess(res: Response, data: DataResponse) {
    return res.status(data.status).json({
      success: true,
      ...data,
    });
  }

  static sendError(res: Response, data: DataResponse) {
    return res.status(data.status).json({
      success: false,
      ...data,
    });
  }

  static sendCatchError(res: Response, error: any) {
    const message =
      ((error as any).response &&
        (error as any).response.data &&
        (error as any).response.data.message) ||
      (error as any).message ||
      (error as any).toString();

    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      // console.error("Response data:", error.response.data);
      // console.error("Response status:", error.response.status);
      // console.error("Response headers:", error.response.headers);
      return this.sendError(res, {
        status: error.response.status || error.response.code || StatusCodes.INTERNAL_SERVER_ERROR,
        message: message,
        data: error.response.data,
      });
    } else if (error.request) {
      // The request was made but no response was received
      return this.sendError(res, {
        status:
          error.status || error.statusCode || StatusCodes.INTERNAL_SERVER_ERROR,
        message: message || "Internal Server Error",
      });
    } else {
      // Something happened in setting up the request that triggered an Error
      // console.error("Error message:", error.message);
      return this.sendError(res, {
        status:
          error.status || error.statusCode || StatusCodes.INTERNAL_SERVER_ERROR,
        message: message || "Internal Server Error",
      });
    }
  }
}

export default ResponseHandler;
