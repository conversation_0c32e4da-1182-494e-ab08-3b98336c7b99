class Convert {
  static TTLToSecond(ttl: string): number {
    const value = parseInt(ttl);
    const unit = ttl.slice(-1).toLowerCase();

    switch (unit) {
      case "s":
        return value;
      case "m":
        return value * 60;
      case "h":
        return value * 60 * 60;
      case "d":
        return value * 24 * 60 * 60;
      case "w":
        return value * 7 * 24 * 60 * 60;
      case "y":
        return value * 365 * 24 * 60 * 60;
      default:
        throw new Error("Invalid TTL format");
    }
  }

  static convertQueryToArray = (query: string): string[] => {
    return query
      .split(".")
      .map((item) => item.trim())
      .filter((item) => item !== "");
  };
}

export default Convert;
