/**
 * Signature Utils
 * Utility functions for signature generation and verification
 */

/**
 * Generate signature based on parameters and secret
 * This method follows the same logic as the Java implementation
 */
export const sign = (
  paramValues: Record<string, any>,
  secret: string
): string => {
  const sb: string[] = [];

  console.log("paramValues", paramValues);
  const paramNames = Object.keys(paramValues).sort();

  for (const key of paramNames) {
    const value = paramValues[key];
    if (
      value === null ||
      value === undefined ||
      value.toString().trim() === ""
    ) {
      continue;
    }

    const valueStr = value.toString();
    if (valueStr.startsWith("[")) {
      sb.push(`&${key}=${parseJsonArray(valueStr)}`);
    } else if (valueStr.startsWith("{")) {
      const requestMap = JSON.parse(valueStr);
      sb.push(`&${key}=${sign(requestMap, secret)}`);
    } else {
      sb.push(`&${key}=${valueStr}`);
    }
  }

  console.log("sb", sb);

  sb.push(`&${secret}`);
  const str = sb.join("").substring(1);

  return disguiseMD5(str, "UTF-8");
};

/**
 * Helper method to parse and sort JSON array for signature
 */
const parseJsonArray = (jsonArrayString: string): string => {
  try {
    const list = JSON.parse(jsonArrayString) as Record<string, any>[];
    if (!Array.isArray(list) || list.length === 0) {
      return "[]";
    }

    const result: Record<string, any>[] = [];

    for (const map of list) {
      const paramNames = Object.keys(map).sort();
      const jsonObject: Record<string, any> = {};

      for (const key of paramNames) {
        const value = map[key];
        if (
          value === null ||
          value === undefined ||
          value.toString().trim() === ""
        ) {
          continue;
        }

        jsonObject[key] = value;
      }

      result.push(jsonObject);
    }

    // Sort by index if available
    result.sort((o1, o2) => {
      const o1Index = o1.index;
      const o2Index = o2.index;

      if (o1Index === undefined || o2Index === undefined) {
        throw new Error("Index must be specified in parameter details");
      }

      return o1Index - o2Index;
    });

    return JSON.stringify(result);
  } catch (e) {
    // Return original string if parsing fails
    return jsonArrayString;
  }
};

/**
 * Create MD5 hash of a string with specified encoding
 */
const disguiseMD5 = (message: string, encoding: string): string => {
  if (message === null || encoding === null) {
    return "";
  }

  message = message.trim();

  // Use require for crypto to avoid type issues
  const crypto = require("crypto");
  const md5Hash = crypto.createHash("md5").update(message, encoding).digest();

  return toHex(md5Hash);
};

/**
 * Convert byte array to hexadecimal string
 */
const toHex = (input: Uint8Array): string => {
  if (input === null) {
    return "";
  }

  const output: string[] = [];

  for (let i = 0; i < input.length; i++) {
    const current = input[i] & 0xff;
    if (current < 16) {
      output.push("0");
    }
    output.push(current.toString(16));
  }

  return output.join("");
};

/**
 * Verify signature of the response or webhook data
 * @param data Data with signature to verify
 * @param secret Secret key
 * @returns True if signature is valid
 */
export const verifySignature = (data: any, secret: string): boolean => {
  // Extract and remove the sign from data
  const { sign: receivedSign, ...dataWithoutSign } = data;

  // Calculate signature for the data
  const calculatedSign = sign(dataWithoutSign, secret);

  // Compare calculated sign with the one in the response
  return calculatedSign === receivedSign;
};

/**
 * Add signature to request data
 * @param data Request data
 * @param secret Secret key
 * @returns Data with added signature
 */
export const addSignature = (data: any, secret: string): any => {
  // Create a copy of the data
  const signedData = { ...data };

  // Calculate signature
  const signature = sign(signedData, secret);

  // Add sign to the data
  signedData.sign = signature;

  return signedData;
};
