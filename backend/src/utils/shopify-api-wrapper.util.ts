import { PrismaClient } from '@prisma/client';
import logger from '@/utils/logger.util';
import ShopifyUninstallService from '@/services/shopify-uninstall.service';

const prisma = new PrismaClient();

interface ShopifyApiError {
  response?: {
    status?: number;
    statusText?: string;
    data?: any;
  };
  message?: string;
  code?: string;
}

interface ShopifyStoreContext {
  storeId?: string;
  shop?: string;
  accessToken?: string;
}

/**
 * Utility class to wrap Shopify API calls and detect app uninstallation
 */
export class ShopifyApiWrapper {
  
  /**
   * Detects if an error indicates the app has been uninstalled
   */
  private static isUninstallError(error: ShopifyApiError): boolean {
    const status = error.response?.status;
    const message = error.message?.toLowerCase() || '';
    const errorData = error.response?.data;

    // Common HTTP status codes for uninstalled apps
    if (status === 401 || status === 403) {
      return true;
    }

    // Check for specific Shopify error messages that indicate uninstallation
    const uninstallIndicators = [
      'invalid api access',
      'unauthorized',
      'access denied',
      'invalid access token',
      'app not installed',
      'permission denied',
      'insufficient permissions'
    ];

    const hasUninstallMessage = uninstallIndicators.some(indicator => 
      message.includes(indicator)
    );

    if (hasUninstallMessage) {
      return true;
    }

    // Check GraphQL errors that might indicate uninstallation
    if (errorData?.errors) {
      const graphqlErrors = Array.isArray(errorData.errors) ? errorData.errors : [errorData.errors];
      const hasAuthError = graphqlErrors.some((err: any) => {
        const errMessage = err.message?.toLowerCase() || '';
        return uninstallIndicators.some(indicator => errMessage.includes(indicator));
      });
      
      if (hasAuthError) {
        return true;
      }
    }

    return false;
  }

  /**
   * Handles store uninstallation using the ShopifyUninstallService
   */
  private static async handleStoreUninstall(context: ShopifyStoreContext, errorCode: number): Promise<void> {
    try {
      const { shop, accessToken } = context;
      
      if (!shop || !accessToken) {
        logger.warn('Insufficient context for uninstall detection', { context });
        return;
      }

      const uninstallService = new ShopifyUninstallService();
      
      // Use the uninstall service to detect and handle the uninstall
      const result = await uninstallService.detectUninstallFromApiError(shop, accessToken, errorCode);
      
      if (result.wasUninstalled) {
        logger.info('Successfully processed app uninstallation via API error', {
          shop,
          userId: result.userId,
          storeId: result.storeId,
          message: result.message,
          detectionMethod: 'api_error'
        });
      } else {
        logger.debug('API error did not indicate uninstallation', {
          shop,
          errorCode,
          message: result.message
        });
      }

    } catch (error) {
      logger.error('Error handling store uninstall', { 
        error: error instanceof Error ? error.message : String(error),
        context 
      });
    }
  }

  /**
   * Wraps a Shopify REST API call with uninstall detection
   */
  static async wrapRestCall<T>(
    apiCall: () => Promise<T>,
    context: ShopifyStoreContext,
    operationName: string = 'shopify_api_call'
  ): Promise<T> {
    try {
      const result = await apiCall();
      return result;
    } catch (error: any) {
      const errorCode = error.response?.status;
      
      logger.debug(`Shopify API call failed: ${operationName}`, {
        error: error.message,
        status: errorCode,
        context
      });

      // Check if this error indicates app uninstallation
      if (this.isUninstallError(error)) {
        logger.warn(`Detected app uninstallation via API error in ${operationName}`, {
          error: error.message,
          status: errorCode,
          context
        });

        // Handle store uninstall in background
        this.handleStoreUninstall(context, errorCode).catch(err => {
          logger.error('Failed to handle store uninstall', { error: err instanceof Error ? err.message : String(err) });
        });
      }

      // Re-throw the original error
      throw error;
    }
  }

  /**
   * Wraps a Shopify GraphQL API call with uninstall detection
   */
  static async wrapGraphQLCall<T>(
    apiCall: () => Promise<T>,
    context: ShopifyStoreContext,
    operationName: string = 'shopify_graphql_call'
  ): Promise<T> {
    try {
      const result = await apiCall();
      return result;
    } catch (error: any) {
      const errorCode = error.response?.status || 401; // Default to 401 for GraphQL errors
      
      logger.debug(`Shopify GraphQL call failed: ${operationName}`, {
        error: error.message,
        status: errorCode,
        context
      });

      // Check if this error indicates app uninstallation
      if (this.isUninstallError(error)) {
        logger.warn(`Detected app uninstallation via GraphQL error in ${operationName}`, {
          error: error.message,
          status: errorCode,
          context
        });

        // Handle store uninstall in background
        this.handleStoreUninstall(context, errorCode).catch(err => {
          logger.error('Failed to handle store uninstall', { error: err instanceof Error ? err.message : String(err) });
        });
      }

      // Re-throw the original error
      throw error;
    }
  }
}

/**
 * Convenience function for REST API calls
 */
export const wrapShopifyRestCall = ShopifyApiWrapper.wrapRestCall.bind(ShopifyApiWrapper);

/**
 * Convenience function for GraphQL API calls  
 */
export const wrapShopifyGraphQLCall = ShopifyApiWrapper.wrapGraphQLCall.bind(ShopifyApiWrapper); 