export const generateRandomNumber = (length: number): string => {
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  const randomNum = Math.floor(Math.random() * (max - min + 1)) + min;
  return randomNum.toString().padStart(length, "0");
};

export const generateSingleRandomNumber = (length: number): string => {
  const excludedNumbers = new Set([
    1111, 2222, 3333, 4444, 5555, 6666, 7777, 8888, 9999, 1234, 2345, 3456,
    4567, 5678, 6789, 6868, 8686, 7979, 8989, 9898, 6969, 9696, 3838, 3939,
    9876, 8765, 7654, 6543, 5432, 4321,
  ]);
  let num: number;
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;

  do {
    num = Math.floor(Math.random() * (max - min + 1)) + min;
  } while (excludedNumbers.has(num));
  return num.toString().padStart(length, "0");
};

export const generateSpecialBIBNumbers = (prefix: string): string[] => {
  let list: string[] = [];
  const excludedNumbers = new Set([
    1111, 2222, 3333, 4444, 5555, 6666, 7777, 8888, 9999, 1234, 2345, 3456,
    4567, 5678, 6789, 6868, 8686, 7979, 8989, 9898, 6969, 9696, 3838, 3939,
    9876, 8765, 7654, 6543, 5432, 4321,
  ]);
  for (let num of excludedNumbers) {
    list.push(`${prefix}${num}`);
  }
  return list;
};

export const generateStrongPassword = (length: number = 12): string => {
  const uppercaseChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const lowercaseChars = "abcdefghijklmnopqrstuvwxyz";
  const numberChars = "0123456789";
  const specialChars = "!@#$%^&*";

  const allChars = uppercaseChars + lowercaseChars + numberChars + specialChars;
  let password = "";

  // Ensure at least one character from each category
  password += uppercaseChars[Math.floor(Math.random() * uppercaseChars.length)];
  password += lowercaseChars[Math.floor(Math.random() * lowercaseChars.length)];
  password += numberChars[Math.floor(Math.random() * numberChars.length)];
  password += specialChars[Math.floor(Math.random() * specialChars.length)];

  // Fill the rest of the password
  for (let i = password.length; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }

  // Shuffle the password to make it more random
  return password
    .split("")
    .sort(() => Math.random() - 0.5)
    .join("");
};
