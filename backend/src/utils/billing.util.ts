import { BILLING_PLANS, SUBSCRIPTION_STATUS } from '../constants/billing';
import logger from './logger.util';

/**
 * Billing utility functions for Shopify subscription management
 */
export class BillingUtils {
  /**
   * Calculate total usage amount for a billing period
   */
  static calculateUsageAmount(usageRecords: any[]): number {
    return usageRecords.reduce((total, record) => {
      return total + (record.totalAmount || 0);
    }, 0);
  }

  /**
   * Calculate remaining billing capacity
   */
  static calculateRemainingCapacity(currentUsage: number, cappedAmount: number): number {
    return Math.max(0, cappedAmount - currentUsage);
  }

  /**
   * Calculate usage percentage
   */
  static calculateUsagePercentage(currentUsage: number, cappedAmount: number): number {
    if (cappedAmount === 0) return 0;
    return Math.min(100, (currentUsage / cappedAmount) * 100);
  }

  /**
   * Check if usage is approaching the billing cap
   */
  static isApproachingCap(currentUsage: number, cappedAmount: number, threshold: number = 0.8): boolean {
    return currentUsage >= (cappedAmount * threshold);
  }

  /**
   * Check if usage has exceeded the billing cap
   */
  static hasExceededCap(currentUsage: number, cappedAmount: number): boolean {
    return currentUsage >= cappedAmount;
  }

  /**
   * Format amount in cents to dollars
   */
  static formatAmount(amountInCents: number, currency: string = 'USD'): string {
    const amount = amountInCents / 100;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }

  /**
   * Format usage summary for display
   */
  static formatUsageSummary(subscription: any): {
    planName: string;
    currentUsage: string;
    cappedAmount: string;
    remainingCapacity: string;
    usagePercentage: number;
    isApproachingCap: boolean;
    hasExceededCap: boolean;
    status: string;
  } {
    const currentUsage = subscription.currentUsage || 0;
    const cappedAmount = subscription.cappedAmount || 0;
    const currency = subscription.currency || 'USD';

    return {
      planName: subscription.planName || 'Unknown',
      currentUsage: this.formatAmount(currentUsage, currency),
      cappedAmount: this.formatAmount(cappedAmount, currency),
      remainingCapacity: this.formatAmount(this.calculateRemainingCapacity(currentUsage, cappedAmount), currency),
      usagePercentage: this.calculateUsagePercentage(currentUsage, cappedAmount),
      isApproachingCap: this.isApproachingCap(currentUsage, cappedAmount),
      hasExceededCap: this.hasExceededCap(currentUsage, cappedAmount),
      status: subscription.status || 'UNKNOWN'
    };
  }

  /**
   * Validate subscription data
   */
  static validateSubscription(subscription: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!subscription) {
      errors.push('Subscription data is required');
      return { isValid: false, errors };
    }

    if (!subscription.linkedStoreId) {
      errors.push('LinkedStore ID is required');
    }

    if (!subscription.planName) {
      errors.push('Plan name is required');
    }

    if (!subscription.currency) {
      errors.push('Currency is required');
    }

    if (subscription.cappedAmount === undefined || subscription.cappedAmount < 0) {
      errors.push('Capped amount must be a non-negative number');
    }

    if (!Object.values(SUBSCRIPTION_STATUS).includes(subscription.status)) {
      errors.push('Invalid subscription status');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate usage record data
   */
  static validateUsageRecord(usageRecord: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!usageRecord) {
      errors.push('Usage record data is required');
      return { isValid: false, errors };
    }

    if (!usageRecord.description) {
      errors.push('Description is required');
    }

    if (!usageRecord.quantity || usageRecord.quantity <= 0) {
      errors.push('Quantity must be a positive number');
    }

    if (!usageRecord.price || usageRecord.price <= 0) {
      errors.push('Price must be a positive number');
    }

    if (!usageRecord.billingDate) {
      errors.push('Billing date is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Calculate next billing date
   */
  static calculateNextBillingDate(currentDate: Date, interval: string): Date {
    const nextBillingDate = new Date(currentDate);

    switch (interval) {
      case 'EVERY_30_DAYS':
        nextBillingDate.setDate(nextBillingDate.getDate() + 30);
        break;
      case 'MONTHLY':
        nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
        break;
      case 'YEARLY':
        nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1);
        break;
      default:
        // Default to 30 days
        nextBillingDate.setDate(nextBillingDate.getDate() + 30);
        break;
    }

    return nextBillingDate;
  }

  /**
   * Check if subscription is active and not expired
   */
  static isSubscriptionActive(subscription: any): boolean {
    if (!subscription) return false;
    
    const now = new Date();
    const status = subscription.status;
    
    // Check status
    if (status !== SUBSCRIPTION_STATUS.ACTIVE) {
      return false;
    }

    // Check if subscription is expired
    if (subscription.nextBillingAt && new Date(subscription.nextBillingAt) < now) {
      return false;
    }

    return true;
  }

  /**
   * Get subscription health status
   */
  static getSubscriptionHealth(subscription: any): {
    status: 'healthy' | 'warning' | 'critical' | 'inactive';
    message: string;
    recommendations: string[];
  } {
    if (!subscription) {
      return {
        status: 'inactive',
        message: 'No subscription found',
        recommendations: ['Set up a billing subscription to continue using the service']
      };
    }

    const isActive = this.isSubscriptionActive(subscription);
    const currentUsage = subscription.currentUsage || 0;
    const cappedAmount = subscription.cappedAmount || 0;
    const usagePercentage = this.calculateUsagePercentage(currentUsage, cappedAmount);

    if (!isActive) {
      return {
        status: 'inactive',
        message: 'Subscription is not active',
        recommendations: ['Reactivate your subscription to continue using the service']
      };
    }

    if (usagePercentage >= 100) {
      return {
        status: 'critical',
        message: 'Usage cap exceeded',
        recommendations: [
          'Usage has reached the monthly cap',
          'Service may be limited until next billing cycle',
          'Consider upgrading to a higher plan'
        ]
      };
    }

    if (usagePercentage >= 80) {
      return {
        status: 'warning',
        message: 'Approaching usage cap',
        recommendations: [
          'Usage is approaching the monthly cap',
          'Monitor usage closely',
          'Consider upgrading to avoid service interruption'
        ]
      };
    }

    return {
      status: 'healthy',
      message: 'Subscription is active and healthy',
      recommendations: []
    };
  }

  /**
   * Generate billing report data
   */
  static generateBillingReport(subscription: any, usageRecords: any[]): {
    subscription: any;
    usageSummary: any;
    usageBreakdown: any[];
    health: any;
    totalRecords: number;
    reportGeneratedAt: Date;
  } {
    const usageSummary = this.formatUsageSummary(subscription);
    const health = this.getSubscriptionHealth(subscription);
    
    const usageBreakdown = usageRecords.map(record => ({
      id: record.id,
      date: record.billingDate,
      description: record.description,
      quantity: record.quantity,
      price: this.formatAmount(record.price, subscription.currency),
      totalAmount: this.formatAmount(record.totalAmount, subscription.currency),
      status: record.status
    }));

    return {
      subscription: {
        id: subscription.id,
        planName: subscription.planName,
        status: subscription.status,
        activatedAt: subscription.activatedAt,
        nextBillingAt: subscription.nextBillingAt
      },
      usageSummary,
      usageBreakdown,
      health,
      totalRecords: usageRecords.length,
      reportGeneratedAt: new Date()
    };
  }

  /**
   * Check if billing is in test mode
   */
  static isTestMode(): boolean {
    return process.env.NODE_ENV === 'development' || process.env.SHOPIFY_BILLING_TEST_MODE === 'true';
  }

  /**
   * Log billing event
   */
  static logBillingEvent(event: string, data: any): void {
    const testModeFlag = this.isTestMode() ? '[TEST]' : '[LIVE]';
    logger.info(`${testModeFlag} Billing Event: ${event}`, {
      event,
      data,
      testMode: this.isTestMode(),
      timestamp: new Date().toISOString()
    });
  }
}

export default BillingUtils; 