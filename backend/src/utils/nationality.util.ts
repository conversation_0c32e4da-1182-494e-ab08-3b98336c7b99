export const countries = [
  {
    code: 'AFG',
    name: 'Afghanistan',
    nationality: 'Afghan'
  },
  {
    code: 'ALA',
    name: 'Åland Islands',
    nationality: 'Åland Island'
  },
  {
    code: 'ALB',
    name: 'Albania',
    nationality: 'Albanian'
  },
  {
    code: 'DZA',
    name: 'Algeria',
    nationality: 'Algerian'
  },
  {
    code: 'ASM',
    name: 'American Samoa',
    nationality: 'American Samoan'
  },
  {
    code: 'AND',
    name: 'Andorra',
    nationality: 'Andorran'
  },
  {
    code: 'AGO',
    name: 'Angola',
    nationality: 'Angolan'
  },
  {
    code: 'AIA',
    name: '<PERSON><PERSON><PERSON>',
    nationality: 'Anguil<PERSON>'
  },
  {
    code: 'ATA',
    name: 'Antarctica',
    nationality: 'Antarctic'
  },
  {
    code: 'ATG',
    name: 'Antigua and Barbuda',
    nationality: 'Antiguan or Barbudan'
  },
  {
    code: 'ARG',
    name: 'Argentina',
    nationality: 'Argentine'
  },
  {
    code: 'ARM',
    name: 'Armenia',
    nationality: 'Armenian'
  },
  {
    code: 'ABW',
    name: 'Aruba',
    nationality: 'Aruban'
  },
  {
    code: 'AUS',
    name: 'Australia',
    nationality: 'Australian'
  },
  {
    code: 'AUT',
    name: 'Austria',
    nationality: 'Austrian'
  },
  {
    code: 'AZE',
    name: 'Azerbaijan',
    nationality: 'Azerbaijani, Azeri'
  },
  {
    code: 'BHS',
    name: 'Bahamas',
    nationality: 'Bahamian'
  },
  {
    code: 'BHR',
    name: 'Bahrain',
    nationality: 'Bahraini'
  },
  {
    code: 'BGD',
    name: 'Bangladesh',
    nationality: 'Bangladeshi'
  },
  {
    code: 'BRB',
    name: 'Barbados',
    nationality: 'Barbadian'
  },
  {
    code: 'BLR',
    name: 'Belarus',
    nationality: 'Belarusian'
  },
  {
    code: 'BEL',
    name: 'Belgium',
    nationality: 'Belgian'
  },
  {
    code: 'BLZ',
    name: 'Belize',
    nationality: 'Belizean'
  },
  {
    code: 'BEN',
    name: 'Benin',
    nationality: 'Beninese, Beninois'
  },
  {
    code: 'BMU',
    name: 'Bermuda',
    nationality: 'Bermudian, Bermudan'
  },
  {
    code: 'BTN',
    name: 'Bhutan',
    nationality: 'Bhutanese'
  },
  {
    code: 'BOL',
    name: 'Bolivia (Plurinational State of)',
    nationality: 'Bolivian'
  },
  {
    code: 'BES',
    name: 'Bonaire, Sint Eustatius and Saba',
    nationality: 'Bonaire'
  },
  {
    code: 'BIH',
    name: 'Bosnia and Herzegovina',
    nationality: 'Bosnian or Herzegovinian'
  },
  {
    code: 'BWA',
    name: 'Botswana',
    nationality: 'Motswana, Botswanan'
  },
  {
    code: 'BVT',
    name: 'Bouvet Island',
    nationality: 'Bouvet Island'
  },
  {
    code: 'BRA',
    name: 'Brazil',
    nationality: 'Brazilian'
  },
  {
    code: 'IOT',
    name: 'British Indian Ocean Territory',
    nationality: 'BIOT'
  },
  {
    code: 'BRN',
    name: 'Brunei Darussalam',
    nationality: 'Bruneian'
  },
  {
    code: 'BGR',
    name: 'Bulgaria',
    nationality: 'Bulgarian'
  },
  {
    code: 'BFA',
    name: 'Burkina Faso',
    nationality: 'Burkinabé'
  },
  {
    code: 'BDI',
    name: 'Burundi',
    nationality: 'Burundian'
  },
  {
    code: 'CPV',
    name: 'Cabo Verde',
    nationality: 'Cabo Verdean'
  },
  {
    code: 'KHM',
    name: 'Cambodia',
    nationality: 'Cambodian'
  },
  {
    code: 'CMR',
    name: 'Cameroon',
    nationality: 'Cameroonian'
  },
  {
    code: 'CAN',
    name: 'Canada',
    nationality: 'Canadian'
  },
  {
    code: 'CYM',
    name: 'Cayman Islands',
    nationality: 'Caymanian'
  },
  {
    code: 'CAF',
    name: 'Central African Republic',
    nationality: 'Central African'
  },
  {
    code: 'TCD',
    name: 'Chad',
    nationality: 'Chadian'
  },
  {
    code: 'CHL',
    name: 'Chile',
    nationality: 'Chilean'
  },
  {
    code: 'CHN',
    name: 'China',
    nationality: 'Chinese'
  },
  {
    code: 'CXR',
    name: 'Christmas Island',
    nationality: 'Christmas Island'
  },
  {
    code: 'CCK',
    name: 'Cocos (Keeling) Islands',
    nationality: 'Cocos Island'
  },
  {
    code: 'COL',
    name: 'Colombia',
    nationality: 'Colombian'
  },
  {
    code: 'COM',
    name: 'Comoros',
    nationality: 'Comoran, Comorian'
  },
  {
    code: 'COG',
    name: 'Congo (Republic of the)',
    nationality: 'Congolese'
  },
  {
    code: 'COD',
    name: 'Congo (Democratic Republic of the)',
    nationality: 'Congolese'
  },
  {
    code: 'COK',
    name: 'Cook Islands',
    nationality: 'Cook Island'
  },
  {
    code: 'CRI',
    name: 'Costa Rica',
    nationality: 'Costa Rican'
  },
  {
    code: 'CIV',
    name: "Côte d'Ivoire",
    nationality: 'Ivorian'
  },
  {
    code: 'HRV',
    name: 'Croatia',
    nationality: 'Croatian'
  },
  {
    code: 'CUB',
    name: 'Cuba',
    nationality: 'Cuban'
  },
  {
    code: 'CUW',
    name: 'Curaçao',
    nationality: 'Curaçaoan'
  },
  {
    code: 'CYP',
    name: 'Cyprus',
    nationality: 'Cypriot'
  },
  {
    code: 'CZE',
    name: 'Czech Republic',
    nationality: 'Czech'
  },
  {
    code: 'DNK',
    name: 'Denmark',
    nationality: 'Danish'
  },
  {
    code: 'DJI',
    name: 'Djibouti',
    nationality: 'Djiboutian'
  },
  {
    code: 'DMA',
    name: 'Dominica',
    nationality: 'Dominican'
  },
  {
    code: 'DOM',
    name: 'Dominican Republic',
    nationality: 'Dominican'
  },
  {
    code: 'ECU',
    name: 'Ecuador',
    nationality: 'Ecuadorian'
  },
  {
    code: 'EGY',
    name: 'Egypt',
    nationality: 'Egyptian'
  },
  {
    code: 'SLV',
    name: 'El Salvador',
    nationality: 'Salvadoran'
  },
  {
    code: 'GNQ',
    name: 'Equatorial Guinea',
    nationality: 'Equatorial Guinean, Equatoguinean'
  },
  {
    code: 'ERI',
    name: 'Eritrea',
    nationality: 'Eritrean'
  },
  {
    code: 'EST',
    name: 'Estonia',
    nationality: 'Estonian'
  },
  {
    code: 'ETH',
    name: 'Ethiopia',
    nationality: 'Ethiopian'
  },
  {
    code: 'FLK',
    name: 'Falkland Islands (Malvinas)',
    nationality: 'Falkland Island'
  },
  {
    code: 'FRO',
    name: 'Faroe Islands',
    nationality: 'Faroese'
  },
  {
    code: 'FJI',
    name: 'Fiji',
    nationality: 'Fijian'
  },
  {
    code: 'FIN',
    name: 'Finland',
    nationality: 'Finnish'
  },
  {
    code: 'FRA',
    name: 'France',
    nationality: 'French'
  },
  {
    code: 'GUF',
    name: 'French Guiana',
    nationality: 'French Guianese'
  },
  {
    code: 'PYF',
    name: 'French Polynesia',
    nationality: 'French Polynesian'
  },
  {
    code: 'ATF',
    name: 'French Southern Territories',
    nationality: 'French Southern Territories'
  },
  {
    code: 'GAB',
    name: 'Gabon',
    nationality: 'Gabonese'
  },
  {
    code: 'GMB',
    name: 'Gambia',
    nationality: 'Gambian'
  },
  {
    code: 'GEO',
    name: 'Georgia',
    nationality: 'Georgian'
  },
  {
    code: 'DEU',
    name: 'Germany',
    nationality: 'German'
  },
  {
    code: 'GHA',
    name: 'Ghana',
    nationality: 'Ghanaian'
  },
  {
    code: 'GIB',
    name: 'Gibraltar',
    nationality: 'Gibraltar'
  },
  {
    code: 'GRC',
    name: 'Greece',
    nationality: 'Greek, Hellenic'
  },
  {
    code: 'GRL',
    name: 'Greenland',
    nationality: 'Greenlandic'
  },
  {
    code: 'GRD',
    name: 'Grenada',
    nationality: 'Grenadian'
  },
  {
    code: 'GLP',
    name: 'Guadeloupe',
    nationality: 'Guadeloupe'
  },
  {
    code: 'GUM',
    name: 'Guam',
    nationality: 'Guamanian, Guambat'
  },
  {
    code: 'GTM',
    name: 'Guatemala',
    nationality: 'Guatemalan'
  },
  {
    code: 'GGY',
    name: 'Guernsey',
    nationality: 'Channel Island'
  },
  {
    code: 'GIN',
    name: 'Guinea',
    nationality: 'Guinean'
  },
  {
    code: 'GNB',
    name: 'Guinea-Bissau',
    nationality: 'Bissau-Guinean'
  },
  {
    code: 'GUY',
    name: 'Guyana',
    nationality: 'Guyanese'
  },
  {
    code: 'HTI',
    name: 'Haiti',
    nationality: 'Haitian'
  },
  {
    code: 'HMD',
    name: 'Heard Island and McDonald Islands',
    nationality: 'Heard Island or McDonald Islands'
  },
  {
    code: 'VAT',
    name: 'Vatican City State',
    nationality: 'Vatican'
  },
  {
    code: 'HND',
    name: 'Honduras',
    nationality: 'Honduran'
  },
  {
    code: 'HKG',
    name: 'Hong Kong',
    nationality: 'Hong Kong, Hong Kongese'
  },
  {
    code: 'HUN',
    name: 'Hungary',
    nationality: 'Hungarian, Magyar'
  },
  {
    code: 'ISL',
    name: 'Iceland',
    nationality: 'Icelandic'
  },
  {
    code: 'IND',
    name: 'India',
    nationality: 'Indian'
  },
  {
    code: 'IDN',
    name: 'Indonesia',
    nationality: 'Indonesian'
  },
  {
    code: 'IRN',
    name: 'Iran',
    nationality: 'Iranian, Persian'
  },
  {
    code: 'IRQ',
    name: 'Iraq',
    nationality: 'Iraqi'
  },
  {
    code: 'IRL',
    name: 'Ireland',
    nationality: 'Irish'
  },
  {
    code: 'IMN',
    name: 'Isle of Man',
    nationality: 'Manx'
  },
  {
    code: 'ISR',
    name: 'Israel',
    nationality: 'Israeli'
  },
  {
    code: 'ITA',
    name: 'Italy',
    nationality: 'Italian'
  },
  {
    code: 'JAM',
    name: 'Jamaica',
    nationality: 'Jamaican'
  },
  {
    code: 'JPN',
    name: 'Japan',
    nationality: 'Japanese'
  },
  {
    code: 'JEY',
    name: 'Jersey',
    nationality: 'Channel Island'
  },
  {
    code: 'JOR',
    name: 'Jordan',
    nationality: 'Jordanian'
  },
  {
    code: 'KAZ',
    name: 'Kazakhstan',
    nationality: 'Kazakhstani, Kazakh'
  },
  {
    code: 'KEN',
    name: 'Kenya',
    nationality: 'Kenyan'
  },
  {
    code: 'KIR',
    name: 'Kiribati',
    nationality: 'I-Kiribati'
  },
  {
    code: 'PRK',
    name: "Korea (Democratic People's Republic of)",
    nationality: 'North Korean'
  },
  {
    code: 'KOR',
    name: 'Korea (Republic of)',
    nationality: 'South Korean'
  },
  {
    code: 'KWT',
    name: 'Kuwait',
    nationality: 'Kuwaiti'
  },
  {
    code: 'KGZ',
    name: 'Kyrgyzstan',
    nationality: 'Kyrgyzstani, Kyrgyz, Kirgiz, Kirghiz'
  },
  {
    code: 'LAO',
    name: "Lao People's Democratic Republic",
    nationality: 'Lao, Laotian'
  },
  {
    code: 'LVA',
    name: 'Latvia',
    nationality: 'Latvian'
  },
  {
    code: 'LBN',
    name: 'Lebanon',
    nationality: 'Lebanese'
  },
  {
    code: 'LSO',
    name: 'Lesotho',
    nationality: 'Basotho'
  },
  {
    code: 'LBR',
    name: 'Liberia',
    nationality: 'Liberian'
  },
  {
    code: 'LBY',
    name: 'Libya',
    nationality: 'Libyan'
  },
  {
    code: 'LIE',
    name: 'Liechtenstein',
    nationality: 'Liechtenstein'
  },
  {
    code: 'LTU',
    name: 'Lithuania',
    nationality: 'Lithuanian'
  },
  {
    code: 'LUX',
    name: 'Luxembourg',
    nationality: 'Luxembourg, Luxembourgish'
  },
  {
    code: 'MAC',
    name: 'Macao',
    nationality: 'Macanese, Chinese'
  },
  {
    code: 'MKD',
    name: 'Macedonia (the former Yugoslav Republic of)',
    nationality: 'Macedonian'
  },
  {
    code: 'MDG',
    name: 'Madagascar',
    nationality: 'Malagasy'
  },
  {
    code: 'MWI',
    name: 'Malawi',
    nationality: 'Malawian'
  },
  {
    code: 'MYS',
    name: 'Malaysia',
    nationality: 'Malaysian'
  },
  {
    code: 'MDV',
    name: 'Maldives',
    nationality: 'Maldivian'
  },
  {
    code: 'MLI',
    name: 'Mali',
    nationality: 'Malian, Malinese'
  },
  {
    code: 'MLT',
    name: 'Malta',
    nationality: 'Maltese'
  },
  {
    code: 'MHL',
    name: 'Marshall Islands',
    nationality: 'Marshallese'
  },
  {
    code: 'MTQ',
    name: 'Martinique',
    nationality: 'Martiniquais, Martinican'
  },
  {
    code: 'MRT',
    name: 'Mauritania',
    nationality: 'Mauritanian'
  },
  {
    code: 'MUS',
    name: 'Mauritius',
    nationality: 'Mauritian'
  },
  {
    code: 'MYT',
    name: 'Mayotte',
    nationality: 'Mahoran'
  },
  {
    code: 'MEX',
    name: 'Mexico',
    nationality: 'Mexican'
  },
  {
    code: 'FSM',
    name: 'Micronesia (Federated States of)',
    nationality: 'Micronesian'
  },
  {
    code: 'MDA',
    name: 'Moldova (Republic of)',
    nationality: 'Moldovan'
  },
  {
    code: 'MCO',
    name: 'Monaco',
    nationality: 'Monégasque, Monacan'
  },
  {
    code: 'MNG',
    name: 'Mongolia',
    nationality: 'Mongolian'
  },
  {
    code: 'MNE',
    name: 'Montenegro',
    nationality: 'Montenegrin'
  },
  {
    code: 'MSR',
    name: 'Montserrat',
    nationality: 'Montserratian'
  },
  {
    code: 'MAR',
    name: 'Morocco',
    nationality: 'Moroccan'
  },
  {
    code: 'MOZ',
    name: 'Mozambique',
    nationality: 'Mozambican'
  },
  {
    code: 'MMR',
    name: 'Myanmar',
    nationality: 'Burmese'
  },
  {
    code: 'NAM',
    name: 'Namibia',
    nationality: 'Namibian'
  },
  {
    code: 'NRU',
    name: 'Nauru',
    nationality: 'Nauruan'
  },
  {
    code: 'NPL',
    name: 'Nepal',
    nationality: 'Nepali, Nepalese'
  },
  {
    code: 'NLD',
    name: 'Netherlands',
    nationality: 'Dutch, Netherlandic'
  },
  {
    code: 'NCL',
    name: 'New Caledonia',
    nationality: 'New Caledonian'
  },
  {
    code: 'NZL',
    name: 'New Zealand',
    nationality: 'New Zealand, NZ'
  },
  {
    code: 'NIC',
    name: 'Nicaragua',
    nationality: 'Nicaraguan'
  },
  {
    code: 'NER',
    name: 'Niger',
    nationality: 'Nigerien'
  },
  {
    code: 'NGA',
    name: 'Nigeria',
    nationality: 'Nigerian'
  },
  {
    code: 'NIU',
    name: 'Niue',
    nationality: 'Niuean'
  },
  {
    code: 'NFK',
    name: 'Norfolk Island',
    nationality: 'Norfolk Island'
  },
  {
    code: 'MNP',
    name: 'Northern Mariana Islands',
    nationality: 'Northern Marianan'
  },
  {
    code: 'NOR',
    name: 'Norway',
    nationality: 'Norwegian'
  },
  {
    code: 'OMN',
    name: 'Oman',
    nationality: 'Omani'
  },
  {
    code: 'PAK',
    name: 'Pakistan',
    nationality: 'Pakistani'
  },
  {
    code: 'PLW',
    name: 'Palau',
    nationality: 'Palauan'
  },
  {
    code: 'PSE',
    name: 'Palestine, State of',
    nationality: 'Palestinian'
  },
  {
    code: 'PAN',
    name: 'Panama',
    nationality: 'Panamanian'
  },
  {
    code: 'PNG',
    name: 'Papua New Guinea',
    nationality: 'Papua New Guinean, Papuan'
  },
  {
    code: 'PRY',
    name: 'Paraguay',
    nationality: 'Paraguayan'
  },
  {
    code: 'PER',
    name: 'Peru',
    nationality: 'Peruvian'
  },
  {
    code: 'PHL',
    name: 'Philippines',
    nationality: 'Philippine, Filipino'
  },
  {
    code: 'PCN',
    name: 'Pitcairn',
    nationality: 'Pitcairn Island'
  },
  {
    code: 'POL',
    name: 'Poland',
    nationality: 'Polish'
  },
  {
    code: 'PRT',
    name: 'Portugal',
    nationality: 'Portuguese'
  },
  {
    code: 'PRI',
    name: 'Puerto Rico',
    nationality: 'Puerto Rican'
  },
  {
    code: 'QAT',
    name: 'Qatar',
    nationality: 'Qatari'
  },
  {
    code: 'REU',
    name: 'Réunion',
    nationality: 'Réunionese, Réunionnais'
  },
  {
    code: 'ROU',
    name: 'Romania',
    nationality: 'Romanian'
  },
  {
    code: 'RUS',
    name: 'Russian Federation',
    nationality: 'Russian'
  },
  {
    code: 'RWA',
    name: 'Rwanda',
    nationality: 'Rwandan'
  },
  {
    code: 'BLM',
    name: 'Saint Barthélemy',
    nationality: 'Barthélemois'
  },
  {
    code: 'SHN',
    name: 'Saint Helena, Ascension and Tristan da Cunha',
    nationality: 'Saint Helenian'
  },
  {
    code: 'KNA',
    name: 'Saint Kitts and Nevis',
    nationality: 'Kittitian or Nevisian'
  },
  {
    code: 'LCA',
    name: 'Saint Lucia',
    nationality: 'Saint Lucian'
  },
  {
    code: 'MAF',
    name: 'Saint Martin (French part)',
    nationality: 'Saint-Martinoise'
  },
  {
    code: 'SPM',
    name: 'Saint Pierre and Miquelon',
    nationality: 'Saint-Pierrais or Miquelonnais'
  },
  {
    code: 'VCT',
    name: 'Saint Vincent and the Grenadines',
    nationality: 'Saint Vincentian, Vincentian'
  },
  {
    code: 'WSM',
    name: 'Samoa',
    nationality: 'Samoan'
  },
  {
    code: 'SMR',
    name: 'San Marino',
    nationality: 'Sammarinese'
  },
  {
    code: 'STP',
    name: 'Sao Tome and Principe',
    nationality: 'São Toméan'
  },
  {
    code: 'SAU',
    name: 'Saudi Arabia',
    nationality: 'Saudi, Saudi Arabian'
  },
  {
    code: 'SEN',
    name: 'Senegal',
    nationality: 'Senegalese'
  },
  {
    code: 'SRB',
    name: 'Serbia',
    nationality: 'Serbian'
  },
  {
    code: 'SYC',
    name: 'Seychelles',
    nationality: 'Seychellois'
  },
  {
    code: 'SLE',
    name: 'Sierra Leone',
    nationality: 'Sierra Leonean'
  },
  {
    code: 'SGP',
    name: 'Singapore',
    nationality: 'Singaporean'
  },
  {
    code: 'SXM',
    name: 'Sint Maarten (Dutch part)',
    nationality: 'Sint Maarten'
  },
  {
    code: 'SVK',
    name: 'Slovakia',
    nationality: 'Slovak'
  },
  {
    code: 'SVN',
    name: 'Slovenia',
    nationality: 'Slovenian, Slovene'
  },
  {
    code: 'SLB',
    name: 'Solomon Islands',
    nationality: 'Solomon Island'
  },
  {
    code: 'SOM',
    name: 'Somalia',
    nationality: 'Somali, Somalian'
  },
  {
    code: 'ZAF',
    name: 'South Africa',
    nationality: 'South African'
  },
  {
    code: 'SGS',
    name: 'South Georgia and the South Sandwich Islands',
    nationality: 'South Georgia or South Sandwich Islands'
  },
  {
    code: 'SSD',
    name: 'South Sudan',
    nationality: 'South Sudanese'
  },
  {
    code: 'ESP',
    name: 'Spain',
    nationality: 'Spanish'
  },
  {
    code: 'LKA',
    name: 'Sri Lanka',
    nationality: 'Sri Lankan'
  },
  {
    code: 'SDN',
    name: 'Sudan',
    nationality: 'Sudanese'
  },
  {
    code: 'SUR',
    name: 'Suriname',
    nationality: 'Surinamese'
  },
  {
    code: 'SJM',
    name: 'Svalbard and Jan Mayen',
    nationality: 'Svalbard'
  },
  {
    code: 'SWZ',
    name: 'Swaziland',
    nationality: 'Swazi'
  },
  {
    code: 'SWE',
    name: 'Sweden',
    nationality: 'Swedish'
  },
  {
    code: 'CHE',
    name: 'Switzerland',
    nationality: 'Swiss'
  },
  {
    code: 'SYR',
    name: 'Syrian Arab Republic',
    nationality: 'Syrian'
  },
  {
    code: 'TWN',
    name: 'Taiwan, Province of China',
    nationality: 'Chinese, Taiwanese'
  },
  {
    code: 'TJK',
    name: 'Tajikistan',
    nationality: 'Tajikistani'
  },
  {
    code: 'TZA',
    name: 'Tanzania, United Republic of',
    nationality: 'Tanzanian'
  },
  {
    code: 'THA',
    name: 'Thailand',
    nationality: 'Thai'
  },
  {
    code: 'TLS',
    name: 'Timor-Leste',
    nationality: 'Timorese'
  },
  {
    code: 'TGO',
    name: 'Togo',
    nationality: 'Togolese'
  },
  {
    code: 'TKL',
    name: 'Tokelau',
    nationality: 'Tokelauan'
  },
  {
    code: 'TON',
    name: 'Tonga',
    nationality: 'Tongan'
  },
  {
    code: 'TTO',
    name: 'Trinidad and Tobago',
    nationality: 'Trinidadian or Tobagonian'
  },
  {
    code: 'TUN',
    name: 'Tunisia',
    nationality: 'Tunisian'
  },
  {
    code: 'TUR',
    name: 'Turkey',
    nationality: 'Turkish'
  },
  {
    code: 'TKM',
    name: 'Turkmenistan',
    nationality: 'Turkmen'
  },
  {
    code: 'TCA',
    name: 'Turks and Caicos Islands',
    nationality: 'Turks and Caicos Island'
  },
  {
    code: 'TUV',
    name: 'Tuvalu',
    nationality: 'Tuvaluan'
  },
  {
    code: 'UGA',
    name: 'Uganda',
    nationality: 'Ugandan'
  },
  {
    code: 'UKR',
    name: 'Ukraine',
    nationality: 'Ukrainian'
  },
  {
    code: 'ARE',
    name: 'United Arab Emirates',
    nationality: 'Emirati, Emirian, Emiri'
  },
  {
    code: 'GBR',
    name: 'United Kingdom of Great Britain and Northern Ireland',
    nationality: 'British, UK'
  },
  {
    code: 'UMI',
    name: 'United States Minor Outlying Islands',
    nationality: 'American'
  },
  {
    code: 'USA',
    name: 'United States of America',
    nationality: 'American'
  },
  {
    code: 'URY',
    name: 'Uruguay',
    nationality: 'Uruguayan'
  },
  {
    code: 'UZB',
    name: 'Uzbekistan',
    nationality: 'Uzbekistani, Uzbek'
  },
  {
    code: 'VUT',
    name: 'Vanuatu',
    nationality: 'Ni-Vanuatu, Vanuatuan'
  },
  {
    code: 'VEN',
    name: 'Venezuela (Bolivarian Republic of)',
    nationality: 'Venezuelan'
  },
  {
    code: 'VNM',
    name: 'Vietnam',
    nationality: 'Vietnamese'
  },
  {
    code: 'VGB',
    name: 'Virgin Islands (British)',
    nationality: 'British Virgin Island'
  },
  {
    code: 'VIR',
    name: 'Virgin Islands (U.S.)',
    nationality: 'U.S. Virgin Island'
  },
  {
    code: 'WLF',
    name: 'Wallis and Futuna',
    nationality: 'Wallis and Futuna, Wallisian or Futunan'
  },
  {
    code: 'ESH',
    name: 'Western Sahara',
    nationality: 'Sahrawi, Sahrawian, Sahraouian'
  },
  {
    code: 'YEM',
    name: 'Yemen',
    nationality: 'Yemeni'
  },
  {
    code: 'ZMB',
    name: 'Zambia',
    nationality: 'Zambian'
  },
  {
    code: 'ZWE',
    name: 'Zimbabwe',
    nationality: 'Zimbabwean'
  }
]

export const nationalitiesSorted = [
  // Đặt Vietnam lên đầu
  {
    code: 'VNM',
    name: 'Vietnam',
    nationality: 'Vietnamese'
  }
  // ... các nước còn lại được sắp xếp theo alphabet của nationality
].concat(
  countries
    .filter(country => country.code !== 'VNM')
    .sort((a, b) => a.nationality.localeCompare(b.nationality))
)

export const isValidCode = (code: string): boolean => {
  return countries.some(country => country.code === code)
}

export const getNationalityByCode = (code: string): string | null => {
  const country = countries.find(country => country.code === code)
  return country ? country.nationality : null
}
