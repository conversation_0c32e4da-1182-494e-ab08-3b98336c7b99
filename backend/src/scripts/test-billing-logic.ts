/**
 * Test billing logic without database dependencies
 * This simulates the billing flows to help understand how they work
 */

import { BLOCK_PRICING } from '../config/block-pricing.config';

// Constants
const MIN_BALANCE_THRESHOLD = 1000; // $10 in cents
const DEFAULT_TOPUP_AMOUNT = 10000; // $100 in cents
const MAX_MONTHLY_CHARGE = 79999; // $799.99 in cents

// Simulation state
let accountBalance = 0;
let monthlyUsage = 0;
let transactionLog: string[] = [];

function log(message: string) {
  console.log(message);
  transactionLog.push(message);
}

function formatCurrency(cents: number): string {
  return `$${(cents / 100).toFixed(2)}`;
}

function simulateInitialState() {
  log('\n🏁 INITIAL STATE');
  log('================');
  log(`Account Balance: ${formatCurrency(accountBalance)}`);
  log(`Monthly Usage: ${formatCurrency(monthlyUsage)}`);
  log(`Minimum Balance Threshold: ${formatCurrency(MIN_BALANCE_THRESHOLD)}`);
  log(`Default Topup Amount: ${formatCurrency(DEFAULT_TOPUP_AMOUNT)}`);
  log(`Monthly Usage Limit: ${formatCurrency(MAX_MONTHLY_CHARGE)}`);
}

function simulateTopup(amount: number = DEFAULT_TOPUP_AMOUNT): boolean {
  log(`\n💳 TOPUP ATTEMPT: ${formatCurrency(amount)}`);
  
  // Check if topup would exceed monthly limit
  if (monthlyUsage + amount > MAX_MONTHLY_CHARGE) {
    log(`❌ TOPUP BLOCKED: Would exceed monthly limit`);
    log(`   Current usage: ${formatCurrency(monthlyUsage)}`);
    log(`   Topup amount: ${formatCurrency(amount)}`);
    log(`   Would total: ${formatCurrency(monthlyUsage + amount)}`);
    log(`   Monthly limit: ${formatCurrency(MAX_MONTHLY_CHARGE)}`);
    return false;
  }
  
  // Process topup
  accountBalance += amount;
  monthlyUsage += amount;
  
  log(`✅ TOPUP SUCCESSFUL`);
  log(`   New balance: ${formatCurrency(accountBalance)}`);
  log(`   Monthly usage: ${formatCurrency(monthlyUsage)}`);
  
  return true;
}

function simulateBlockDeduction(blockType: 'RDR' | 'ETHOCA'): boolean {
  const fee = BLOCK_PRICING[blockType];
  
  log(`\n📦 BLOCK PROCESSING: ${blockType}`);
  log(`   Fee: ${formatCurrency(fee)}`);
  log(`   Current balance: ${formatCurrency(accountBalance)}`);
  
  // Check if balance is sufficient
  if (accountBalance < fee) {
    log(`⚠️  INSUFFICIENT BALANCE - Triggering auto-topup...`);
    
    // Check if we can do auto-topup
    if (monthlyUsage + DEFAULT_TOPUP_AMOUNT > MAX_MONTHLY_CHARGE) {
      log(`❌ AUTO-TOPUP FAILED: Monthly limit reached`);
      log(`   Service would be paused until next billing cycle`);
      return false;
    }
    
    // Perform auto-topup
    if (simulateTopup()) {
      log(`🔄 AUTO-TOPUP COMPLETED - Retrying deduction...`);
    } else {
      return false;
    }
  }
  
  // Deduct the fee
  accountBalance -= fee;
  log(`✅ BLOCK PROCESSED`);
  log(`   New balance: ${formatCurrency(accountBalance)}`);
  
  // Check if balance fell below minimum threshold
  if (accountBalance < MIN_BALANCE_THRESHOLD && accountBalance >= 0) {
    log(`⚠️  BALANCE BELOW MINIMUM - Scheduling proactive topup...`);
    // In real system, this would happen asynchronously
    setTimeout(() => {
      log(`\n🔄 PROACTIVE TOPUP (Background Process)`);
      simulateTopup();
    }, 100);
  }
  
  return true;
}

function simulateMonthlyLimitReached() {
  log('\n🚫 SIMULATING MONTHLY LIMIT SCENARIO');
  log('=====================================');
  
  // Set usage near limit
  monthlyUsage = 75000; // $750
  accountBalance = 5000; // $50
  
  log(`Current state:`);
  log(`   Balance: ${formatCurrency(accountBalance)}`);
  log(`   Monthly usage: ${formatCurrency(monthlyUsage)}`);
  
  // Try to process expensive blocks
  simulateBlockDeduction('ETHOCA');
  simulateBlockDeduction('ETHOCA');
  simulateBlockDeduction('ETHOCA');
  
  log(`\n📊 FINAL STATE:`);
  log(`   Balance: ${formatCurrency(accountBalance)}`);
  log(`   Monthly usage: ${formatCurrency(monthlyUsage)}`);
  log(`   Can process more: ${monthlyUsage < MAX_MONTHLY_CHARGE ? 'Yes' : 'No'}`);
  
  if (monthlyUsage >= MAX_MONTHLY_CHARGE) {
    log(`\n🔄 ACTION REQUIRED: Start new subscription cycle to continue`);
  }
}

function simulateTypicalUserJourney() {
  log('\n🚶 TYPICAL USER JOURNEY');
  log('=======================');
  
  // User starts with no balance
  log('\n1️⃣ New user signs up');
  simulateInitialState();
  
  // User processes first block (triggers auto-topup)
  log('\n2️⃣ User processes first chargeback');
  simulateBlockDeduction('RDR');
  
  // User processes several more blocks
  log('\n3️⃣ User processes more chargebacks');
  simulateBlockDeduction('ETHOCA');
  simulateBlockDeduction('RDR');
  simulateBlockDeduction('RDR');
  
  // Simulate time passing and balance getting low
  log('\n4️⃣ Balance gets low over time');
  accountBalance = 800; // Set to $8
  log(`   Balance manually set to: ${formatCurrency(accountBalance)}`);
  simulateBlockDeduction('RDR');
  
  // Show final state
  log('\n📊 JOURNEY SUMMARY');
  log(`   Final balance: ${formatCurrency(accountBalance)}`);
  log(`   Total spent (monthly usage): ${formatCurrency(monthlyUsage)}`);
  log(`   Remaining until limit: ${formatCurrency(MAX_MONTHLY_CHARGE - monthlyUsage)}`);
}

// Run simulations
console.log('🧪 BILLING SYSTEM SIMULATION');
console.log('============================');

// Reset state
accountBalance = 0;
monthlyUsage = 0;
transactionLog = [];

// Run the simulation you want to test
const scenario = process.argv[2] || 'journey';

switch (scenario) {
  case 'topup':
    simulateInitialState();
    simulateTopup();
    break;
    
  case 'deduct':
    simulateInitialState();
    simulateTopup(); // Add initial balance
    simulateBlockDeduction('RDR');
    simulateBlockDeduction('ETHOCA');
    break;
    
  case 'limit':
    simulateMonthlyLimitReached();
    break;
    
  case 'journey':
  default:
    simulateTypicalUserJourney();
    break;
}

// Print transaction log
console.log('\n📝 TRANSACTION LOG');
console.log('==================');
transactionLog.forEach((log, index) => {
  console.log(`${index + 1}. ${log}`);
});

console.log('\n💡 Usage:');
console.log('  npx ts-node src/scripts/test-billing-logic.ts          # Run typical journey');
console.log('  npx ts-node src/scripts/test-billing-logic.ts topup    # Test topup only');
console.log('  npx ts-node src/scripts/test-billing-logic.ts deduct   # Test deduction');
console.log('  npx ts-node src/scripts/test-billing-logic.ts limit    # Test monthly limit');