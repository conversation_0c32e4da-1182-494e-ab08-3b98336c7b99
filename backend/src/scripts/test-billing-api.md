# Billing System API Testing Guide

This guide helps you test the billing flows using API calls.

## Prerequisites

1. Get your API key from the system
2. Get a valid store ID
3. Set the base URL (e.g., `http://localhost:4000/api`)

## Headers Required for All Requests

```
x-api-key: YOUR_API_KEY
x-store-id: YOUR_STORE_ID
Content-Type: application/json
```

## 1. Check Current Balance

```bash
GET /usage/{storeId}/balance
```

Example:
```bash
curl -X GET "http://localhost:4000/api/usage/YOUR_STORE_ID/balance" \
  -H "x-api-key: YOUR_API_KEY" \
  -H "x-store-id: YOUR_STORE_ID"
```

## 2. Check Current Usage

```bash
GET /usage/{storeId}/current
```

Example:
```bash
curl -X GET "http://localhost:4000/api/usage/YOUR_STORE_ID/current" \
  -H "x-api-key: YOUR_API_KEY" \
  -H "x-store-id: YOUR_STORE_ID"
```

## 3. Simulate Block Processing (Deduct Balance)

To test deduction, you need to process a block through the system. This would normally happen when a chargeback is processed.

## 4. Check Subscription Cycle Status

```bash
GET /subscription-cycle/status
```

Example:
```bash
curl -X GET "http://localhost:4000/api/subscription-cycle/status" \
  -H "x-api-key: YOUR_API_KEY" \
  -H "x-store-id: YOUR_STORE_ID"
```

## 5. Start New Subscription Cycle (when limit is reached)

```bash
POST /subscription-cycle/new
```

Example:
```bash
curl -X POST "http://localhost:4000/api/subscription-cycle/new" \
  -H "x-api-key: YOUR_API_KEY" \
  -H "x-store-id: YOUR_STORE_ID" \
  -H "Content-Type: application/json"
```

## Testing Scenarios

### Scenario 1: Normal Flow
1. Check initial balance (should be $0)
2. Process a block (will trigger auto-topup)
3. Check balance again (should be ~$80-85 after topup and deduction)
4. Check usage tracking (should show $100 topup)

### Scenario 2: Low Balance Auto-Topup
1. Set balance to $8 (requires database access)
2. Process a block
3. System should automatically topup when balance falls below $10

### Scenario 3: Monthly Limit
1. Process many blocks until usage reaches $799.99
2. Try to process another block (should fail)
3. Check subscription cycle status
4. Start new subscription cycle
5. Continue processing

## Manual Database Commands (for testing)

If you have database access, you can manually set balances for testing:

```sql
-- Set balance to specific amount (in cents)
UPDATE store_settings 
SET value = '800' 
WHERE store_id = 'YOUR_STORE_ID' 
AND name = 'current_balance';

-- Check current balance
SELECT * FROM store_settings 
WHERE store_id = 'YOUR_STORE_ID' 
AND name = 'current_balance';

-- Check usage tracking
SELECT * FROM usage_tracking 
WHERE store_id = 'YOUR_STORE_ID' 
ORDER BY timestamp DESC;

-- Clear usage for fresh testing
DELETE FROM usage_tracking 
WHERE store_id = 'YOUR_STORE_ID';
```

## Expected Responses

### Balance Response
```json
{
  "success": true,
  "status": 200,
  "message": "Account balance retrieved",
  "data": {
    "balance": 8500,
    "formattedBalance": "$85.00"
  }
}
```

### Current Usage Response
```json
{
  "success": true,
  "status": 200,
  "message": "Current usage retrieved",
  "data": {
    "period": "2025-07",
    "totalBlocks": 5,
    "totalAmount": 10000,
    "ethokaBlocks": 2,
    "rdrBlocks": 3,
    "planLimit": 999999,
    "planTier": "free",
    "usagePercentage": 0,
    "isOverLimit": false,
    "remainingBlocks": 999999
  }
}
```

### Subscription Cycle Status Response
```json
{
  "success": true,
  "data": {
    "currentPeriod": "2025-07",
    "currentUsage": 75000,
    "usageLimit": 79999,
    "canStartNewCycle": false,
    "remainingAmount": 4999,
    "usagePercentage": 93.75
  }
}
```