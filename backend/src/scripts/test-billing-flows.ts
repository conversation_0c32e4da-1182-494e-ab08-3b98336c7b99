import PrismaService from '../services/prisma.service';
import StoreBalanceService from '../services/store-balance.service';
import { usageTrackingService } from '../services/usage-tracking.service';
import { BLOCK_PRICING } from '../config/block-pricing.config';
import logger from '../utils/logger.util';

const prisma = PrismaService.getInstance().getClient();

// Test configuration
const TEST_STORE_ID = 'test-store-' + Date.now(); // Use a unique test store ID

async function setupTestStore() {
  console.log('\n🔧 Setting up test store...');
  
  try {
    // First, try to find an existing user or create a test user
    let testUser = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      }
    });
    
    if (!testUser) {
      console.log('Creating test user...');
      testUser = await prisma.user.create({
        data: {
          id: 'test-user-' + Date.now(),
          code: 'TEST-BILLING-' + Date.now(),
          fullName: 'Test Billing User',
          phoneNumber: '+1000000' + Date.now(),
          email: '<EMAIL>',
          password: 'hashed-password', // In real app, this would be properly hashed
          gender: 'other',
          birthDate: new Date(),
          status: true,
          note: 'Test user for billing flow testing'
        }
      });
    }
    
    // Create a test linked store
    const testStore = await (prisma as any).linkedStore.create({
      data: {
        id: TEST_STORE_ID,
        userId: testUser.id,
        storeName: 'Test Store for Billing',
        provider: 'shopify',
        providerStoreId: 'test-shopify-id',
        data: {
          shop: { name: 'Test Shop' },
          accessToken: 'test-token',
          domain: 'test-shop.myshopify.com'
        }
      }
    });
    
    console.log('✅ Test store created:', testStore.id);
    return testStore;
  } catch (error: any) {
    if (error.code === 'P2002') {
      console.log('Test user already exists, using existing one...');
      // If user exists, just find it and use it
      const testUser = await prisma.user.findFirst({
        where: {
          email: '<EMAIL>'
        }
      });
      
      if (testUser) {
        const testStore = await (prisma as any).linkedStore.create({
          data: {
            id: TEST_STORE_ID,
            userId: testUser.id,
            storeName: 'Test Store for Billing',
            provider: 'shopify',
            providerStoreId: 'test-shopify-id',
            data: {
              shop: { name: 'Test Shop' },
              accessToken: 'test-token',
              domain: 'test-shop.myshopify.com'
            }
          }
        });
        
        console.log('✅ Test store created:', testStore.id);
        return testStore;
      }
    }
    throw error;
  }
}

async function testInitialBalance() {
  console.log('\n💰 Testing Initial Balance...');
  
  const balance = await StoreBalanceService.getCurrentBalance(TEST_STORE_ID);
  console.log('Initial balance:', `$${balance / 100}`);
  
  return balance;
}

async function testManualTopup() {
  console.log('\n💳 Testing Manual Topup...');
  
  // Simulate a manual topup of $100
  const topupAmount = 10000; // $100 in cents
  
  console.log('Adding $100 to balance...');
  const currentBalance = await StoreBalanceService.getCurrentBalance(TEST_STORE_ID);
  await StoreBalanceService.updateBalance(TEST_STORE_ID, currentBalance + topupAmount);
  
  // Track the topup for Shopify billing
  await usageTrackingService.trackTopup(TEST_STORE_ID, topupAmount);
  
  const newBalance = await StoreBalanceService.getCurrentBalance(TEST_STORE_ID);
  console.log('✅ New balance after topup:', `$${newBalance / 100}`);
  
  return newBalance;
}

async function testBlockDeduction(blockType: 'RDR' | 'ETHOCA') {
  console.log(`\n🔽 Testing ${blockType} Block Deduction...`);
  
  const fee = BLOCK_PRICING[blockType];
  console.log(`${blockType} block fee: $${fee / 100}`);
  
  const beforeBalance = await StoreBalanceService.getCurrentBalance(TEST_STORE_ID);
  console.log('Balance before deduction:', `$${beforeBalance / 100}`);
  
  const result = await StoreBalanceService.deductBalance(
    TEST_STORE_ID,
    fee,
    `Processing ${blockType} block`
  );
  
  if (result.success) {
    console.log('✅ Deduction successful');
    console.log('New balance:', `$${result.newBalance! / 100}`);
    
    // Track the block processing
    await usageTrackingService.trackBlockProcessed(TEST_STORE_ID, blockType);
  } else {
    console.log('❌ Deduction failed:', result.error);
  }
  
  return result;
}

async function testAutoTopupTrigger() {
  console.log('\n🔄 Testing Auto-Topup Trigger...');
  console.log('Setting balance to $5 (below $10 threshold)...');
  
  await StoreBalanceService.updateBalance(TEST_STORE_ID, 500); // $5
  
  // This should trigger auto-topup
  console.log('Balance updated. Check logs for auto-topup trigger.');
  
  // Wait a bit for async topup to process
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  const newBalance = await StoreBalanceService.getCurrentBalance(TEST_STORE_ID);
  console.log('Balance after auto-topup trigger:', `$${newBalance / 100}`);
  
  if (newBalance > 500) {
    console.log('✅ Auto-topup was triggered successfully!');
  } else {
    console.log('⚠️  Auto-topup may not have completed yet or was not triggered');
  }
}

async function testInsufficientBalance() {
  console.log('\n💸 Testing Insufficient Balance Scenario...');
  
  // Set a low balance
  await StoreBalanceService.updateBalance(TEST_STORE_ID, 1000); // $10
  
  // Try to deduct more than available
  const largeDeduction = 2000; // $20
  console.log(`Attempting to deduct $${largeDeduction / 100} from $10 balance...`);
  
  const result = await StoreBalanceService.deductBalance(
    TEST_STORE_ID,
    largeDeduction,
    'Large deduction test'
  );
  
  if (!result.success) {
    console.log('✅ Correctly prevented overdraft');
  } else {
    console.log('✅ Auto-topup handled the insufficient balance');
    console.log('New balance:', `$${result.newBalance! / 100}`);
  }
}

async function testUsageTracking() {
  console.log('\n📊 Testing Usage Tracking...');
  
  const usage = await usageTrackingService.getCurrentPeriodUsage(TEST_STORE_ID);
  
  console.log('Current period:', usage.period);
  console.log('Total amount (topups):', `$${usage.totalAmount / 100}`);
  console.log('Blocks processed:');
  console.log('  - RDR blocks:', usage.rdrBlocks);
  console.log('  - ETHOCA blocks:', usage.ethokaBlocks);
  console.log('Can process more?', await usageTrackingService.canProcessBlock(TEST_STORE_ID));
}

async function testMonthlyLimitScenario() {
  console.log('\n🚫 Testing Monthly Limit ($799.99) Scenario...');
  
  // This would require many topups to reach the limit
  // For testing, we'll just check the logic
  const currentUsage = await usageTrackingService.getCurrentPeriodUsage(TEST_STORE_ID);
  const remainingUntilLimit = 79999 - currentUsage.totalAmount;
  
  console.log('Current usage:', `$${currentUsage.totalAmount / 100}`);
  console.log('Remaining until limit:', `$${remainingUntilLimit / 100}`);
  
  if (remainingUntilLimit <= 0) {
    console.log('✅ Monthly limit reached! New subscription cycle needed.');
  }
}

async function testProactiveBalanceCheck() {
  console.log('\n🔍 Testing Proactive Balance Check...');
  
  // This simulates the scheduled job that checks all stores
  await StoreBalanceService.checkAndMaintainMinimumBalances();
  
  console.log('✅ Proactive balance check completed. Check logs for details.');
}

async function cleanupTestData() {
  console.log('\n🧹 Cleaning up test data...');
  
  try {
    // Delete test usage tracking records
    await prisma.usageTracking.deleteMany({
      where: { storeId: TEST_STORE_ID }
    });
    
    // Delete test store settings
    await prisma.storeSettings.deleteMany({
      where: { storeId: TEST_STORE_ID }
    });
    
    // Delete test bills
    await (prisma as any).bill.deleteMany({
      where: { linkedStoreId: TEST_STORE_ID }
    });
    
    // Delete test payment history
    await prisma.paymentHistory.deleteMany({
      where: { linkedStoreId: TEST_STORE_ID }
    });
    
    // Delete test store
    await (prisma as any).linkedStore.delete({
      where: { id: TEST_STORE_ID }
    });
    
    // Optionally delete test user (comment out if you want to keep it)
    // await prisma.user.deleteMany({
    //   where: { email: '<EMAIL>' }
    // });
    
    console.log('✅ Test data cleaned up');
  } catch (error) {
    console.error('Error cleaning up:', error);
  }
}

async function runAllTests() {
  console.log('🧪 Starting Billing Flow Tests...');
  console.log('================================');
  
  try {
    // Setup
    await setupTestStore();
    
    // Test flows
    await testInitialBalance();
    await testManualTopup();
    await testBlockDeduction('RDR');
    await testBlockDeduction('ETHOCA');
    await testAutoTopupTrigger();
    await testInsufficientBalance();
    await testUsageTracking();
    await testMonthlyLimitScenario();
    await testProactiveBalanceCheck();
    
    console.log('\n✅ All tests completed!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
  } finally {
    // Cleanup
    await cleanupTestData();
    await prisma.$disconnect();
  }
}

// Run specific test based on command line argument
const testToRun = process.argv[2];

switch (testToRun) {
  case 'balance':
    setupTestStore().then(() => testInitialBalance()).then(cleanupTestData);
    break;
  case 'topup':
    setupTestStore().then(() => testManualTopup()).then(cleanupTestData);
    break;
  case 'deduct':
    setupTestStore().then(() => testManualTopup()).then(() => testBlockDeduction('RDR')).then(cleanupTestData);
    break;
  case 'auto-topup':
    setupTestStore().then(() => testAutoTopupTrigger()).then(cleanupTestData);
    break;
  case 'usage':
    setupTestStore().then(() => testManualTopup()).then(() => testUsageTracking()).then(cleanupTestData);
    break;
  case 'all':
  default:
    runAllTests();
}

console.log('\n💡 Usage:');
console.log('  npm run test:billing         # Run all tests');
console.log('  npm run test:billing balance # Test balance only');
console.log('  npm run test:billing topup   # Test topup only');
console.log('  npm run test:billing deduct  # Test deduction only');
console.log('  npm run test:billing auto-topup # Test auto-topup');
console.log('  npm run test:billing usage   # Test usage tracking');