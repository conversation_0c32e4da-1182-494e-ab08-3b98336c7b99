ENV=development
SERVER_GREETING="Welcome to API"
SERVER_CODE=api-ledinhcuong.com
SERVER_PUBLIC_URL=http://localhost:9000
SERVER_DOMAIN=ledinhcuong.com
SERVER_VERSION=1
SERVER_PORT=29000

# AUTHENTICATION
JWT_SECRET=mXx99d
ACCESS_TOKEN_TTL=1y
REFRESH_TOKEN_TTL=1y

# REDIS
REDIS_HOST=************
REDIS_PORT=6379
REDIS_PASSWORD="StrongRedisPassword123"

# DATABASE
DATABASE_URL="postgresql://rw_user:Vn)<EMAIL>:5432/chargeback?schema=development"
SHADOW_DATABASE_URL="postgresql://rw_user:Vn)<EMAIL>:5432/chargeback?schema=development_shadow"

# Anti-Fraud Service
ANTI_FRAUD_SECRET_ID="your-secret-id"
ANTI_FRAUD_SECRET_KEY="your-secret-key"
ANTI_FRAUD_DOMAIN="rce.na-siliconvalley.wetech-rc.com"
ANTI_FRAUD_REGION="na-siliconvalley"
ANTI_FRAUD_CLIENT_ID="your-client-id"

# Chargeback Dispute Service
CHARGEBACK_MERCHANT_NO="your-merchant-no"
CHARGEBACK_SIGN_KEY="your-sign-key"
CHARGEBACK_API_ENDPOINT="https://mer.tradefensor.com"

# Compensation Service
COMPENSATION_MERCHANT_NO="your-merchant-no"
COMPENSATION_SIGN_KEY="your-sign-key"
COMPENSATION_API_ENDPOINT="https://mer.tradefensor.com"

# Early Warning Service
EARLY_WARNING_MERCHANT_NO="your-merchant-no"
EARLY_WARNING_SIGN_KEY="your-sign-key"
EARLY_WARNING_API_ENDPOINT="https://mer.tradefensor.com"
EARLY_WARNING_CALLBACK_URL="https://your-domain.com/api/blocks/webhook"

# Webhook log
WEBHOOK_LOG_URL=https://logger.ledinhcuong.com/api/webhook
WEBHOOK_LOG_TOKEN=04c0fabd-f6da-42ea-8f97-1dbee8797457

# Shopify Integration
SHOPIFY_API_KEY=9c3258bd80d1d1f907644bc8f8715743
SHOPIFY_API_SECRET=ccd4f7c0c9615dba193952705559c4aa
SHOPIFY_SCOPES=read_orders,read_all_orders,read_custom_pixels,read_pixels,write_orders,read_shopify_payments_disputes
SHOPIFY_HOST_NAME=localhost:29000
SHOPIFY_API_VERSION=2023-04
FRONTEND_URL=http://localhost:3000

# Stripe
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key_here"
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key_here"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret_here"
STRIPE_API_VERSION="2023-11-15"
STRIPE_CLIENT_ID="ca_YOUR_STRIPE_CLIENT_ID_HERE"
STRIPE_OAUTH_REDIRECT_URI="https://api.quantchargeback.com/stripe/oauth/callback"

# Stripe App Platform OAuth (from stripe apps upload)
STRIPE_APP_CLIENT_ID="sk_test_your_app_client_id_here"
STRIPE_APP_SECRET_KEY="sk_test_your_app_secret_key_here"
STRIPE_APP_REDIRECT_URI="https://api.quantchargeback.com/stripe/oauth/callback"
STRIPE_APP_WEBHOOK_SECRET="whsec_your_app_webhook_secret"
STRIPE_APP_MODE="test"
STRIPE_APP_DISTRIBUTION="private" # Options: private, public
STRIPE_APP_API_VERSION="2025-05-28.basil"
# For public apps, this should be the Stripe Marketplace URL of your app
# For private apps, this can be your API URL
STRIPE_APP_MARKETPLACE_URL="https://marketplace.stripe.com/apps/your-app-id"

SYNC_SERVICE_URL=http://ec2.x.amazonaws.com

PAYPAL_CLIENT_ID=
PAYPAL_CLIENT_SECRET=
PAYPAL_MODE="sandbox"
PAYPAL_WEBHOOK_ID=

