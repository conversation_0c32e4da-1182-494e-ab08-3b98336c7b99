---
description:
globs:
alwaysApply: false
---
# X<PERSON> lý lỗi

## Nguy<PERSON>n tắc chung
- Sử dụng try/catch cho tất cả các xử lý bất đồng bộ
- Tạo và sử dụng các custom error classes
- Log tất cả các lỗi với đầy đủ thông tin
- Trả về response lỗi theo format chuẩn

## Custom Error Classes
- Tạo các custom error classes kế thừa từ Error
- Đ<PERSON><PERSON> nghĩa các thuộc tính: code, message, status, data
- Sử dụng custom error classes để phân loại lỗi

## Format Response Lỗi
```typescript
{
  success: false,
  message: string, // Thông báo lỗi
  errors: {
    [field]: string[] // Danh sách lỗi cho từng trường
  }
}
```

## Middleware xử lý lỗi
- Tạo middleware xử lý lỗi ở tầng cuối cùng của Express
- Phân loại và xử lý từng loại lỗi
- Ghi log chi tiết
- Tr<PERSON> về response lỗi theo format chuẩn

## Xử lý lỗi Prisma
- Catch PrismaClientKnownRequestError và PrismaClientValidationError
- Map các lỗi Prisma sang custom errors phù hợp
- Xử lý các lỗi unique constraint violation (P2002)
- Xử lý các lỗi foreign key constraint (P2003)
- Xử lý các lỗi record not found (P2025)

## Xử lý lỗi Validation
- Sử dụng Joi để validate dữ liệu đầu vào
- Map các lỗi Joi sang format lỗi chuẩn
- Trả về HTTP status 400 cho lỗi validation

## Xử lý lỗi Xác thực
- Trả về HTTP status 401 cho lỗi xác thực
- Trả về HTTP status 403 cho lỗi phân quyền
- Kèm theo thông báo lỗi rõ ràng

## Logging
- Sử dụng Winston cho logging
- Log các lỗi với mức độ error
- Log request/response với mức độ info
- Bao gồm trace ID trong log để theo dõi request
