# Cấu trúc dự án

Dự án tuân theo mô hình MVC (Model-View-Controller) với các thư mục chính:

## Main Entry Point
- [src/server.ts](mdc:src/server.ts): File khởi động ứng dụng Express

## Core Directories
- `src/config/`: Ch<PERSON>a cấu hình ứng dụng, database, logging, etc.
- `src/controllers/`: Xử lý logic nghiệp vụ, nhận request và trả về response
- `src/middlewares/`: Chứa các middleware xác thực, xử lý lỗi, logging
- `src/models/`: Định nghĩa các model và interfaces
- `src/routes/`: Định nghĩa các API endpoints và mapping tới controllers
- `src/services/`: Chứa business logic phức tạp, tương tác với database
- `src/utils/`: <PERSON><PERSON><PERSON> hàm tiện ích sử dụng trong ứng dụng
- `src/constants/`: <PERSON><PERSON><PERSON> hằng số được sử dụng xuyên suốt ứng dụng
- `src/translations/`: Các file ngôn ngữ cho i18n

## Database
- [prisma/schema.prisma](mdc:prisma/schema.prisma): Định nghĩa schema database
- `prisma/migrations/`: Chứa các migration files
- `prisma/seeds/`: Chứa dữ liệu mẫu cho database

## Documentation
- `docs/`: Tất cả tài liệu dự án phải được tạo và lưu trữ trong thư mục này
- Bao gồm API documentation, deployment guides, technical specifications
- Sử dụng định dạng Markdown (.md) cho tài liệu
- Đặt tên file theo kebab-case (ví dụ: `api-documentation.md`, `deployment-guide.md`)

## Documentation Generation Rule
**IMPORTANT**: NEVER create, generate, or write documentation files (*.md, README files, API docs, etc.) unless explicitly requested by the user. Only create documentation when the user specifically asks for it. Focus on implementing functionality first.

## Quy ước đặt tên file
- Controller: `*.controller.ts`
- Route: `*.route.ts`
- Service: `*.service.ts`
- Middleware: `*.middleware.ts`
- Utility: `*.util.ts`
- Type/Interface: `*.type.ts` hoặc `*.interface.ts`
- Constants: `*.constant.ts`
- Documentation: `*.md` (trong thư mục `docs/`)
