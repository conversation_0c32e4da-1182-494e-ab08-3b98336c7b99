---
description: 
globs: 
alwaysApply: false
---
# Prisma ORM và PostgreSQL

## Schema Design
- [prisma/schema.prisma](mdc:prisma/schema.prisma): <PERSON><PERSON><PERSON> nghĩa cấu trúc database
- Tất cả model phải có các trường `created_at` và `updated_at`
- <PERSON><PERSON> dụng UUID cho khóa chính
- Đị<PERSON> nghĩa rõ ràng mối quan hệ giữa các bảng (relations)
- Sử dụng indexes cho các trường thường xuyên truy vấn
- <PERSON><PERSON> dụng @@map để định nghĩa tên bảng trong database (dạng snake_case và số nhiều)
- Sử dụng @map để định nghĩa tên cột trong database (dạng snake_case)

## Quy ước đặt tên
- Tên model: <PERSON><PERSON><PERSON>, số <PERSON>t (ví dụ: `User`, `Role`)
- Tên trường: camelCase (ví dụ: `firstName`, `createdAt`)
- Tên bảng trong DB: snake_case, số nhiều (ví dụ: `users`, `user_roles`)
- Tên cột trong DB: snake_case (ví dụ: `first_name`, `created_at`)

## Migrations
- Không chỉnh sửa migration files sau khi đã được tạo
- Tạo migration mới cho mỗi thay đổi schema
- Kiểm tra migration trước khi áp dụng vào production

## Querying Best Practices
- Sử dụng transactions khi thực hiện nhiều thao tác liên quan
- Tránh N+1 queries bằng cách sử dụng include
- Sử dụng select chỉ lấy các trường cần thiết
- Sử dụng where, orderBy, take, skip cho queries phức tạp
- Xử lý lỗi Prisma một cách nhất quán

## Prisma Client
- Tạo Prisma client instance một lần và sử dụng lại
- Đóng kết nối Prisma khi ứng dụng shutdown
- Sử dụng middleware Prisma khi cần xử lý logic trước/sau khi tương tác với database

## Models trong project
- `User`: Người dùng của hệ thống
- `Role`: Vai trò của người dùng
- `Client`: Khách hàng sử dụng API
- `Token`: JWT tokens
- `LinkedStore`: Cửa hàng được liên kết với người dùng

## Services
- Mỗi model nên có một service riêng
- Service đảm nhiệm tương tác với database qua Prisma client
- Không gọi Prisma trực tiếp từ controllers, phải thông qua services
