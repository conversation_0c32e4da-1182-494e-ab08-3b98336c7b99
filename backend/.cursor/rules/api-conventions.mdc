# Quy ước thiết kế API

## RESTful API Design
- Sử dụng HTTP methods (GET, POST, PUT, PATCH, DELETE) theo đúng ngữ nghĩa
- Endpoints phải tuân theo kebab-case (ví dụ: `/api/user-profiles`)
- Sử dụng versioning cho API (ví dụ: `/api/v1/users`)
- Resource phải ở dạng số nhiều (ví dụ: `/api/users` thay vì `/api/user`)

## Cấu trúc Response
```typescript
{
  success: boolean;
  message: string;
  data?: any;
  errors?: any;
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    totalPages?: number;
  }
}
```

## HTTP Status Codes
- 200: OK (thành công)
- 201: Created (tạo mới thành công)
- 400: Bad Request (lỗi dữ liệu đầu vào)
- 401: Unauthorized (chưa xác thực)
- 403: Forbidden (không có quyền truy cập)
- 404: Not Found (không tìm thấy resource)
- 409: Conflict (xung đột dữ liệu)
- 422: Unprocessable Entity (dữ liệu hợp lệ nhưng không thể xử lý)
- 500: Internal Server Error (lỗi server)

## Xác thực và Phân quyền

### Quy tắc chung
- Tất cả API endpoints đều phải đi qua middleware xác thực trừ khi được đặc biệt loại trừ
- Sử dụng RBAC (Role-Based Access Control) cho phân quyền

### Các loại xác thực

#### 1. JWT Authentication (`userRoute`)
- Sử dụng cho các endpoints dành cho users thông thường
- Header: `Authorization: Bearer <jwt_token>`
- Middleware: `auth.middleware.ts`
- Dùng cho: user management, profile endpoints

#### 2. API Key Authentication (`publicRoute`)
- Sử dụng cho webhook endpoints từ external services
- Header: `x-api-key: <api_key>`
- Middleware: `auth.middleware.ts`
- Dùng cho: webhooks từ Ethoca, RDR, Alert-info

#### 3. Multi-Provider Token Authentication (`providerAuthRoute`)
- Sử dụng cho Shopify và Stripe integration endpoints
- Headers: 
  - `Authorization: Bearer <access_token>`
  - `x-provider: shopify|stripe`
- Middleware: `provider-auth.middleware.ts`
- Validates token bằng cách tìm trong `linked_stores.data.accessToken` theo provider
- Attach real user từ `linked_stores.user_id` vào request
- Dùng cho: dashboard, blocks, orders từ Shopify/Stripe app

#### 4. Combined Authentication (Dual Auth)
- Sử dụng cho endpoints cần cả API key và provider token
- Pattern: `router.use(publicRoute, providerAuthRoute, controller)`
- Flow: API key validation → Provider token validation → Controller
- Dùng cho: user-specific endpoints trong provider apps

### Multi-Provider Authentication Flow
```typescript
// Frontend session structure
{
  user: {
    id: "linked_store_id",      // LinkedStore ID
    access_token: "shpat_..." | "rk_live_..."   // Provider access token
  }
}

// Backend authentication flow
1. API Key validation (publicRoute)
2. Provider token validation (providerAuthRoute)
   - Detect provider từ x-provider header
   - Tìm token trong linked_stores.data.accessToken theo provider
   - Attach real user_id vào req.user
3. Controller receives authenticated request
```

### Authentication Middleware Usage Examples
```typescript
// Public webhook endpoint
router.post('/webhook', publicRoute, controller);

// JWT protected endpoint
router.get('/profile', userRoute, controller);

// Multi-provider protected endpoint
router.get('/blocks', publicRoute, providerAuthRoute, controller);

// Combined authentication
router.use(publicRoute, providerAuthRoute);
router.get('/user-specific-data', controller);
```

### Frontend Authentication Implementation
```typescript
// For API key endpoints
const api = apiAxios();

// For multi-provider authenticated endpoints
const shopifyApi = createAuthenticatedApi(session?.user?.access_token, 'shopify');
const stripeApi = createAuthenticatedApi(session?.user?.access_token, 'stripe');

// Service layer should accept token and provider parameters
export const blockService = {
  getBlocks: (linkedStoreId: string, token?: string, provider?: 'shopify' | 'stripe') => {
    const api = token ? createAuthenticatedApi(token, provider) : apiAxios();
    return api.get(`/blocks?linkedStoreId=${linkedStoreId}`);
  }
};
```

## Validation
- Sử dụng Joi để validate dữ liệu đầu vào
- Validation rules phải được định nghĩa rõ ràng trong controllers hoặc middleware
- Trả về lỗi validation chi tiết trong response

## Rate Limiting
- Giới hạn số lượng request trong một khoảng thời gian
- Trả về HTTP status 429 (Too Many Requests) khi vượt quá giới hạn

## Logging
- Log tất cả API requests (URL, method, body, headers)
- Log tất cả API responses (status, body)
- Log tất cả lỗi với đầy đủ thông tin
- Log tất cả API requests (URL, method, body, headers)
- Log tất cả API responses (status, body)
- Log tất cả lỗi với đầy đủ thông tin
