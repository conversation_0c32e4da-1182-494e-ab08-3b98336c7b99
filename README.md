# Charge Back App - Shopify Integration

## Introduction

The QuantChargeback App allows you to connect with Shopify stores to track and manage disputes and refunds. This feature helps you easily view and manage all disputes from a centralized interface.

## Prerequisites

Before setting up the project, ensure you have the following installed:

- Node.js (v18 or higher)
- npm or yarn
- PostgreSQL database
- Redis server (optional, for caching)

## Project Structure

The project consists of two main components:

- **Backend**: Express.js API with Prisma ORM
- **Frontend**: Next.js application with Tailwind CSS

## Installation and Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd charge-back-app
```

### 2. Backend Setup

```bash
cd backend

# Install dependencies
npm install
# or
yarn install

# Configure environment variables
cp .env.example .env.development
```

Edit the `.env.development` file with your configuration:

- Database connection string
- JWT secret
- Redis configuration (if used)
- Shopify API credentials
- Other service credentials as needed

```bash
# Generate Prisma client
npm run prisma:generate
# or
yarn prisma:generate

# Run database migrations
npm run prisma:migrate:dev
# or
yarn prisma:migrate:dev

# Start the development server
npm run development
# or
yarn development
```

The backend server will run on `http://localhost:29000` by default.

### 3. Frontend Setup

```bash
cd frontend

# Install dependencies
npm install
# or
yarn install

# Configure environment variables
cp .env.example .env.local
```

Edit the `.env.local` file with your configuration:

- `NEXT_PUBLIC_API_URL`: Backend API URL
- `NEXT_PUBLIC_APP_URL`: Frontend application URL
- `NEXT_PUBLIC_API_KEY`: API key for authentication
- `AUTH_SECRET`: Secret for NextAuth authentication
- Other service credentials as needed

```bash
# Start the development server
npm run dev
# or
yarn dev
```

The frontend application will run on `http://localhost:3000` by default.

## Usage

1. Access the user interface at `http://localhost:3000`
2. Log in to your account
3. Select "Shopify" from the left menu
4. Enter your Shopify store domain (e.g., your-store.myshopify.com)
5. Complete authentication with Shopify when prompted
6. After successful authentication, you will see your store in the list
7. Click on the store to view details and disputes

## Features

- Connect multiple Shopify stores
- View detailed store information
- Display payment disputes from Shopify
- Refresh data from Shopify
- Disconnect stores
- Interactive dashboard with modern charts for visualizing dispute data
- Flexible integration cards for connecting various services

## Deployment

### Backend Deployment

```bash
# Build the backend
npm run be-build
# or
yarn be-build

# Deploy to production
npm run deploy:be
# or
yarn deploy:be
```

### Frontend Deployment

```bash
# Deploy to production
npm run deploy:fe
# or
yarn deploy:fe
```

## Troubleshooting

If you encounter issues connecting to Shopify, check:

1. Environment variables are configured correctly
2. Shopify app permissions
3. Redirect URL is set correctly in Shopify Partner Dashboard
4. Your Shopify account supports Shopify Payments and has permission to view disputes

## Notes

- Payment disputes are only available with Shopify Payments
- You need permission to view disputes in your Shopify store
- The application uses modern React patterns with TypeScript
- The dashboard features interactive charts with scientific styling

## API Documentation

API documentation is available in the Postman collection located in the `backend/postman` directory.

## License

This project is proprietary and confidential.
