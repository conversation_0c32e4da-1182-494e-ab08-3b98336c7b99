"""
Task manager for background task tracking
"""

from typing import Dict, Any

# Global task storage (in production, use Redis or similar)
# This is shared between routes
_tasks: Dict[str, Any] = {}

def get_task(task_id: str) -> Any:
    """Get task by ID"""
    return _tasks.get(task_id)

def set_task(task_id: str, task_data: Any) -> None:
    """Set task data"""
    _tasks[task_id] = task_data

def task_exists(task_id: str) -> bool:
    """Check if task exists"""
    return task_id in _tasks

def get_all_tasks() -> Dict[str, Any]:
    """Get all tasks (for debugging)"""
    return _tasks.copy()