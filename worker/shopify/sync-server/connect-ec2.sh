#!/bin/bash

# EC2 SSH Connection Script
# Usage: ./connect-ec2.sh <ec2-host>

EC2_HOST=$1
EC2_USER="ubuntu"
SSH_KEY="./ssh/quantchargeback.pem"

# Validate arguments
if [ -z "$EC2_HOST" ]; then
    echo "Error: Please provide EC2 host as argument"
    echo "Usage: ./connect-ec2.sh <ec2-host>"
    exit 1
fi

# Check if SSH key exists
if [ ! -f "$SSH_KEY" ]; then
    echo "Error: SSH key not found at $SSH_KEY"
    exit 1
fi

# Set proper permissions for SSH key
chmod 400 "$SSH_KEY"

# Connect to EC2
echo "Connecting to EC2 instance: $EC2_HOST"
ssh -i "$SSH_KEY" "$EC2_USER@$EC2_HOST"