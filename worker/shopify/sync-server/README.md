# Sync Server

This is the synchronization server for QuantChargeBack, handling Shopify data sync and block processing.

## Structure

```
sync-server/
├── main.py              # FastAPI application entry point
├── routes/              # API route handlers
│   ├── __init__.py      # Route exports
│   ├── health.py        # Health check endpoints
│   ├── sync.py          # Shopify sync endpoints
│   └── blocks.py        # Block processing endpoints
├── services/            # Core service modules
│   ├── __init__.py      # Service module exports
│   ├── blocks.py        # Block processing logic
│   ├── db.py            # Database manager and models
│   ├── shopify.py       # Shopify synchronization logic
│   └── rate_limiter.py  # Rate limiting utilities
├── utils/               # Utility modules
│   ├── __init__.py      # Utils exports
│   └── task_manager.py  # Background task tracking
├── requirements.txt     # Python dependencies
├── .env                 # Environment variables (not in git)
└── README.md           # This file
```

## Routes

### health.py
- `GET /` - Root health check
- `GET /health` - Detailed health status

### sync.py
- `POST /sync` - Trigger Shopify data synchronization
- `GET /sync/task/{task_id}` - Check sync task status

### blocks.py
- `POST /blocks/auto` - Auto-process a block
- `GET /blocks/task/{task_id}` - Check block processing task status

## Services

### blocks.py
- `auto_process_block()` - Automatically process blocks through full workflow
- `find_matched_order()` - Find matching Shopify orders for a block
- `refund_order()` - Create refunds in Shopify
- `send_block_feedback()` - Send feedback to Early Warning system

### db.py
- `DatabaseManager` - PostgreSQL connection management
- `ShopifyStore` - Store data model
- Database query methods for blocks, orders, transactions

### shopify.py
- `ShopifyDataSynchronizer` - Main sync orchestrator
- `ShopifyAPI` - Shopify API client with rate limiting
- Batch processing for orders and transactions

### rate_limiter.py
- `RateLimiter` - Generic token bucket rate limiter
- `ShopifyRateLimiter` - Shopify-specific rate limits

## Utils

### task_manager.py
- `get_task()` - Get task by ID
- `set_task()` - Set task data
- `task_exists()` - Check if task exists


## Environment Variables

Required environment variables:
```
DATABASE_URL=postgresql://...
SHOPIFY_API_VERSION=2025-04
EARLY_WARNING_MERCHANT_NO=...
EARLY_WARNING_SIGN_KEY=...
EARLY_WARNING_API_ENDPOINT=...
API_PORT=8000
API_HOST=0.0.0.0
```

## Running

```bash
# Install dependencies
pip install -r requirements.txt

# Run the server
python main.py
```

The server will start on http://localhost:8000 by default.