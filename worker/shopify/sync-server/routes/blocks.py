"""
Block processing routes
"""

import uuid
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from services.blocks import auto_process_block
from services.shopify import logger
from utils.task_manager import get_task, set_task, task_exists

router = APIRouter(prefix="/blocks", tags=["blocks"])

# Request/Response models
class BlockAutoProcessRequest(BaseModel):
    block_id: str
    store_id: Optional[str] = None

@router.post("/auto")
async def auto_process_block_endpoint(
    request: BlockAutoProcessRequest,
    background_tasks: BackgroundTasks
):
    """
    Automatically process a block through the entire workflow in background:
    1. Automatically detect the store based on block type and data (if store_id not provided)
    2. Find matched orders in the detected/specified store
    3. Refund the first matched order (if any)
    4. Send feedback to Early Warning system
    
    - **block_id**: The ID of the block to process
    - **store_id**: Optional store ID (if not provided, will be auto-detected based on:
      - For RDR blocks: Matches AlertInfo by BIN and CAID
      - For ETHOCA blocks: Matches AlertInfo by Descriptor)
    
    Returns a task ID to track the background processing
    """
    try:
        # Generate a task ID
        task_id = str(uuid.uuid4())
        
        # Add the auto-process task to background tasks
        background_tasks.add_task(
            run_auto_process_task,
            task_id=task_id,
            block_id=request.block_id,
            store_id=request.store_id  # This can be None
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"Block auto-processing started for block {request.block_id}",
                "data": {
                    "taskId": task_id,
                    "blockId": request.block_id,
                    "storeId": request.store_id or "auto-detect",
                    "status": "processing"
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to start auto-process task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def run_auto_process_task(task_id: str, block_id: str, store_id: str):
    """Run block auto-processing task in background"""
    initial_task = {
        "status": "running",
        "blockId": block_id,
        "storeId": store_id,
        "message": "Block processing in progress",
        "startedAt": datetime.now().isoformat()
    }
    set_task(task_id, initial_task)
    
    try:
        # Call the auto_process_block function
        result = await auto_process_block(block_id, store_id)
        
        set_task(task_id, {
            "status": "completed" if result['success'] else "failed",
            "blockId": block_id,
            "storeId": store_id,
            "message": result['message'],
            "startedAt": initial_task["startedAt"],
            "completedAt": datetime.now().isoformat(),
            "result": result
        })
        
        logger.info(f"Block auto-processing completed for task {task_id}")
        
    except Exception as e:
        logger.error(f"Block auto-processing task failed: {e}")
        set_task(task_id, {
            "status": "failed",
            "blockId": block_id,
            "storeId": store_id,
            "message": str(e),
            "startedAt": initial_task["startedAt"],
            "completedAt": datetime.now().isoformat(),
            "error": str(e)
        })


@router.get("/task/{task_id}")
async def get_block_task_status(task_id: str):
    """Get the status of a block processing task"""
    if not task_exists(task_id):
        raise HTTPException(status_code=404, detail="Task not found")
    
    return get_task(task_id)