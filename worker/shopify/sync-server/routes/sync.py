"""
Shopify synchronization routes
"""

import uuid
from typing import Op<PERSON>
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from services.shopify import ShopifyDataSynchronizer, logger
from utils.task_manager import get_task, set_task, task_exists

router = APIRouter(prefix="/sync", tags=["sync"])

# Request/Response models
class SyncRequest(BaseModel):
    store_id: Optional[str] = None
    sync_disputes: Optional[bool] = None

class SyncResponse(BaseModel):
    message: str
    store_id: Optional[str] = None
    store_name: Optional[str] = None
    store_count: Optional[int] = None
    task_id: Optional[str] = None

@router.post("/", response_model=SyncResponse)
async def sync_shopify_data(
    request: SyncRequest,
    background_tasks: BackgroundTasks
):
    """
    Synchronize Shopify data
    
    - **store_id**: Optional store ID to sync a specific store
    - **sync_disputes**: 
        - If True: Only sync disputes (no orders/transactions)
        - If False or None: Sync all (orders + transactions + disputes)
    - If no store_id is provided, all stores will be synced
    """
    try:
        # Generate a task ID
        task_id = str(uuid.uuid4())
        
        # Get store information first
        synchronizer = ShopifyDataSynchronizer()
        
        if request.store_id:
            # Get specific store info
            store = synchronizer.db_manager.get_store_by_id(request.store_id)
            if not store:
                raise HTTPException(status_code=404, detail=f"Store with ID {request.store_id} not found")
            
            # Add the sync task to background tasks
            background_tasks.add_task(
                run_sync_task,
                task_id=task_id,
                store_id=request.store_id,
                sync_disputes=request.sync_disputes
            )
            
            return SyncResponse(
                message=f"Synchronization started for store: {store.store_name}",
                store_id=store.id,
                store_name=store.store_name,
                task_id=task_id
            )
        else:
            # Get all stores info
            stores = synchronizer.db_manager.get_linked_stores()
            store_count = len(stores)
            
            # Add the sync task to background tasks
            background_tasks.add_task(
                run_sync_task,
                task_id=task_id,
                store_id=None,
                sync_disputes=request.sync_disputes
            )
            
            return SyncResponse(
                message=f"Synchronization started for {store_count} stores",
                store_count=store_count,
                task_id=task_id
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start sync task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def run_sync_task(task_id: str, store_id: Optional[str] = None, sync_disputes: Optional[bool] = None):
    """Run synchronization task in background"""
    set_task(task_id, {
        "status": "running",
        "store_id": store_id,
        "sync_disputes": sync_disputes,
        "message": "Synchronization in progress"
    })
    
    try:
        synchronizer = ShopifyDataSynchronizer()
        
        if store_id:
            await synchronizer.sync_store_by_id(store_id, sync_disputes=sync_disputes)
        else:
            await synchronizer.sync_all_stores(sync_disputes=sync_disputes)
        
        set_task(task_id, {
            "status": "completed",
            "store_id": store_id,
            "sync_disputes": sync_disputes,
            "message": "Synchronization completed successfully"
        })
    except Exception as e:
        logger.error(f"Background sync task failed: {e}")
        set_task(task_id, {
            "status": "failed",
            "store_id": store_id,
            "sync_disputes": sync_disputes,
            "message": str(e)
        })


@router.get("/task/{task_id}")
async def get_task_status(task_id: str):
    """Get the status of a synchronization task"""
    if not task_exists(task_id):
        raise HTTPException(status_code=404, detail="Task not found")
    
    return get_task(task_id)