#!/usr/bin/env python3
"""
FastAPI application for Shopify data synchronization
"""

import os
from fastapi import FastAPI
import uvicorn
from dotenv import load_dotenv

# Import routers
from routes import health_router, sync_router, blocks_router

# Load environment variables
load_dotenv()

# Create FastAPI app
app = FastAPI(
    title="Shopify Data Sync API",
    description="API for synchronizing Shopify store data to PostgreSQL",
    version="1.0.0"
)

# Include routers
app.include_router(health_router)
app.include_router(sync_router)
app.include_router(blocks_router)

if __name__ == "__main__":
    # Run with uvicorn when executed directly
    port = int(os.getenv("API_PORT", 8000))
    host = os.getenv("API_HOST", "0.0.0.0")
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )