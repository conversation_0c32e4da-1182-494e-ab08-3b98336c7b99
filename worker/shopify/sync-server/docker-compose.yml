version: '3.8'

services:
  sync-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sync-server
    ports:
      - "80:8000"
    environment:
      # Override any environment variables if needed
      # Values from .env file will be used by default
      API_HOST: "0.0.0.0"
      API_PORT: "8000"
    volumes:
      # Mount logs directory for debugging
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - sync-server-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  sync-server-network:
    driver: bridge