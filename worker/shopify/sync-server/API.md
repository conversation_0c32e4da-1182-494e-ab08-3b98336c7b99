# Shopify Sync API Documentation

## Store Detection Logic

The sync server now supports automatic store detection based on block type:

- **RDR Blocks**: Store is detected by matching AlertInfo using BIN and CAID fields
- **ETHOCA Blocks**: Store is detected by matching AlertInfo using the Descriptor field

This allows blocks to be processed without explicitly providing a store_id.

## Base URL
```
http://localhost:8000
```

## Endpoints

### 1. Health Check
Check if the API service is running.

**GET** `/health`

**Response:**
```json
{
  "status": "healthy",
  "message": "Service is operational"
}
```

### 2. Sync Shopify Data
Start synchronization of Shopify store data in the background.

**POST** `/sync`

**Request Body:**
```json
{
  "store_id": "optional-store-id",
  "sync_disputes": true
}
```

**Parameters:**
- `store_id` (optional): Specific store ID to sync. If not provided, syncs all stores.
- `sync_disputes` (optional): Enable/disable dispute synchronization. Defaults to `true` for single store sync, `false` for all stores sync.

**Response (Single Store):**
```json
{
  "message": "Synchronization started for store: Store Name",
  "store_id": "store-123",
  "store_name": "Store Name",
  "task_id": "uuid-task-id"
}
```

**Response (All Stores):**
```json
{
  "message": "Synchronization started for 5 stores",
  "store_count": 5,
  "task_id": "uuid-task-id"
}
```

### 3. Check Task Status
Get the status of a background synchronization task.

**GET** `/task/{task_id}`

**Parameters:**
- `task_id`: The task ID returned from the sync endpoint

**Response:**
```json
{
  "status": "running|completed|failed",
  "store_id": "store-123",
  "message": "Synchronization in progress|completed successfully|error message"
}
```

## Environment Variables

Create a `.env` file with:
```
DATABASE_URL=postgresql://user:password@host:port/database?schema=your_schema
SHOPIFY_API_VERSION=2025-04
API_PORT=8000
API_HOST=0.0.0.0
```

## Running with Docker

```bash
# Start the service
docker-compose up -d

# View logs
docker-compose logs -f

# Stop the service
docker-compose down
```

## Example Usage

```bash
# Sync all stores (disputes disabled by default)
curl -X POST http://localhost:8000/sync/ \
  -H "Content-Type: application/json" \
  -d '{}'

# Sync all stores with disputes enabled
curl -X POST http://localhost:8000/sync/ \
  -H "Content-Type: application/json" \
  -d '{"sync_disputes": true}'

# Sync specific store (disputes enabled by default)
curl -X POST http://localhost:8000/sync/ \
  -H "Content-Type: application/json" \
  -d '{"store_id": "store-123"}'

# Sync specific store with disputes disabled
curl -X POST http://localhost:8000/sync/ \
  -H "Content-Type: application/json" \
  -d '{"store_id": "store-123", "sync_disputes": false}'

# Check task status
curl http://localhost:8000/task/uuid-task-id
```

## Blocks API Endpoint

### 4. Auto Process Block
Automatically process a block through the entire workflow in background:
1. Automatically detect the store based on block type and data (if store_id not provided)
2. Find matched orders in the detected/specified store
3. Refund the first matched order (if any)
4. Send feedback to Early Warning system

**POST** `/blocks/auto`

**Request Body:**
```json
{
  "block_id": "block-123",
  "store_id": "store-456"  // Optional - will auto-detect if not provided
}
```

**Parameters:**
- `block_id`: The ID of the block to process
- `store_id` (optional): Specific store ID. If not provided, will be auto-detected based on:
  - For RDR blocks: Matches AlertInfo by BIN and CAID
  - For ETHOCA blocks: Matches AlertInfo by Descriptor

**Initial Response:**
```json
{
  "success": true,
  "message": "Block auto-processing started for block block-123",
  "data": {
    "taskId": "uuid-task-id",
    "blockId": "block-123",
    "storeId": "store-456",  // or "auto-detect" if not provided
    "status": "processing"
  }
}
```

**Task Status Response (via GET /task/{task_id}):**
```json
{
  "status": "completed",
  "blockId": "block-123",
  "storeId": "store-456",
  "message": "Block processed successfully",
  "startedAt": "2024-01-15T10:30:00Z",
  "completedAt": "2024-01-15T10:31:00Z",
  "result": {
    "success": true,
    "status": 200,
    "message": "Block processed successfully",
    "data": {
      "blockId": "block-123",
      "storeId": "store-456",  // or auto-detected store ID
      "processingStatus": "success",
      "summary": {
        "ordersFound": 1,
        "transactionsFound": 1,
        "orderRefunded": true,
        "feedbackSent": true
      },
      "matchResult": {
        "block": {
          "id": "block-123",
          "amount": "100.00",
          "currency": "USD"
        },
        "matchedTransactions": [...],
        "matchedOrders": [...],
        "matchingSummary": {...}
      },
      "refundResult": {
        "success": true,
        "status": 200,
        "message": "Refund created successfully",
        "data": {...}
      },
      "feedbackResult": {
        "success": true,
        "status": 200,
        "message": "Feedback sent successfully",
        "data": {...}
      }
    }
  }
}
```

**Processing Flow:**
1. **Find Matched Orders**: Search for Shopify orders matching the block's transaction details
2. **Create Refund**: If orders are found, automatically refund the first matched order
3. **Send Feedback**: Report the processing result back to the Early Warning system

**Error Handling:**
- If no orders are found, feedback is still sent with `outcome: "NO_MATCH"`
- If refund fails, processing continues and feedback is sent with `outcome: "REFUND_FAILED"`
- All steps are logged for debugging purposes

## Updated Environment Variables

Update your `.env` file to include:
```
# Database and API Configuration
DATABASE_URL=postgresql://user:password@host:port/database?schema=your_schema
API_PORT=8000
API_HOST=127.0.0.1

# Shopify Integration
SHOPIFY_API_VERSION=2025-04

# Early Warning Service (for block feedback)
EARLY_WARNING_MERCHANT_NO=your-merchant-no
EARLY_WARNING_SIGN_KEY=your-sign-key
EARLY_WARNING_API_ENDPOINT=https://mer.tradefensor.com
```

## Additional Examples

```bash
# Auto-process a block with specific store
curl -X POST http://localhost:8000/blocks/auto \
  -H "Content-Type: application/json" \
  -d '{
    "block_id": "block-123",
    "store_id": "store-456"
  }'

# Auto-process a block with automatic store detection
curl -X POST http://localhost:8000/blocks/auto \
  -H "Content-Type: application/json" \
  -d '{
    "block_id": "block-123"
  }'

# Check block processing task status
curl http://localhost:8000/task/uuid-task-id
```