#!/bin/bash

# Sync Server Auto Deployment Script for EC2
# Usage: ./deploy.sh <ec2-host>

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Configuration
EC2_HOST=$1
EC2_USER="ubuntu"
SSH_KEY="./ssh/quantchargeback.pem"
REMOTE_APP_DIR="/home/<USER>/sync-server"
DOCKER_IMAGE_NAME="sync-server"
CONTAINER_NAME="sync-server"

# Validate arguments
if [ -z "$EC2_HOST" ]; then
    echo -e "${RED}Error: Please provide EC2 host as argument${NC}"
    echo "Usage: ./deploy.sh <ec2-host>"
    exit 1
fi

# Check if SSH key exists
if [ ! -f "$SSH_KEY" ]; then
    echo -e "${RED}Error: SSH key not found at $SSH_KEY${NC}"
    exit 1
fi

# Set proper permissions for SSH key
chmod 400 "$SSH_KEY"

echo -e "${GREEN}Starting deployment to EC2: $EC2_HOST${NC}"

# Step 1: Create remote directory if not exists
echo -e "${YELLOW}Creating remote application directory...${NC}"
ssh -i "$SSH_KEY" "$EC2_USER@$EC2_HOST" "mkdir -p $REMOTE_APP_DIR"

# Clean remote directory before copying
echo -e "${YELLOW}Cleaning remote directory...${NC}"
ssh -i "$SSH_KEY" "$EC2_USER@$EC2_HOST" "sudo rm -rf $REMOTE_APP_DIR/* || true"

# Step 2: Copy entire sync-server directory
echo -e "${YELLOW}Copying entire sync-server directory...${NC}"
# Create temporary directory and copy files
TEMP_DIR=$(mktemp -d)
cp -r . "$TEMP_DIR/sync-server"
# Remove unnecessary files
rm -rf "$TEMP_DIR/sync-server/venv"
rm -rf "$TEMP_DIR/sync-server/logs"
rm -rf "$TEMP_DIR/sync-server/.git"
rm -rf "$TEMP_DIR/sync-server/__pycache__"
rm -rf "$TEMP_DIR/sync-server/**/__pycache__"
# Copy to remote
scp -i "$SSH_KEY" -r "$TEMP_DIR/sync-server" "$EC2_USER@$EC2_HOST:/home/<USER>/"
# Clean up
rm -rf "$TEMP_DIR"

# Step 3: Setup Docker and deploy
echo -e "${YELLOW}Setting up Docker environment...${NC}"
ssh -i "$SSH_KEY" "$EC2_USER@$EC2_HOST" << 'ENDSSH'
cd /home/<USER>/sync-server

# Add user to docker group if not already added
if ! groups $USER | grep -q docker; then
    echo "Adding user to docker group..."
    sudo usermod -aG docker $USER
    echo "Note: You may need to log out and back in for docker permissions to take effect"
fi

# Use sudo for docker commands to ensure they work
echo "Stopping existing container..."
sudo docker compose down || true

# Remove old images
echo "Removing old images..."
sudo docker image prune -f

# Build new image
echo "Building new Docker image..."
sudo docker compose build --no-cache

# Start container
echo "Starting container..."
sudo docker compose up -d

# Check if container is running
echo "Checking container status..."
sleep 5
sudo docker ps | grep sync-server

# Show logs
echo "Recent logs:"
sudo docker compose logs --tail=50

# Test health endpoint
echo "Testing health endpoint..."
sleep 10
curl -f http://localhost:80/health || echo "Health check failed - container may still be starting"
ENDSSH

echo -e "${GREEN}Deployment completed!${NC}"
echo -e "${GREEN}API should be accessible at: http://$EC2_HOST:80${NC}"
echo ""
echo "Useful commands:"
echo "  View logs: ssh -i $SSH_KEY $EC2_USER@$EC2_HOST 'cd $REMOTE_APP_DIR && sudo docker compose logs -f'"
echo "  Restart: ssh -i $SSH_KEY $EC2_USER@$EC2_HOST 'cd $REMOTE_APP_DIR && sudo docker compose restart'"
echo "  Stop: ssh -i $SSH_KEY $EC2_USER@$EC2_HOST 'cd $REMOTE_APP_DIR && sudo docker compose down'"