# Sync Server Deployment Guide

## Quick Start

### 1. Connect to EC2
```bash
./connect-ec2.sh <your-ec2-host>
```

### 2. Deploy Application
```bash
./deploy.sh <your-ec2-host>
```

## Scripts Overview

### connect-ec2.sh
- Connects to EC2 instance via SSH
- Uses key: `./ssh/quantchargeback.pem`
- Default user: `ubuntu`

### deploy.sh
- Auto-deploys sync-server to EC2
- Copies all necessary files
- Builds and runs Docker container
- Performs health check

## Prerequisites

1. EC2 instance with Docker installed
2. SSH key in `./ssh/quantchargeback.pem`
3. Port 8000 open in EC2 security group
4. `.env` file configured

## Manual Commands

### View logs
```bash
ssh -i ./ssh/quantchargeback.pem ubuntu@<ec2-host> 'cd /home/<USER>/sync-server && docker-compose logs -f'
```

### Restart container
```bash
ssh -i ./ssh/quantchargeback.pem ubuntu@<ec2-host> 'cd /home/<USER>/sync-server && docker-compose restart'
```

### Stop container
```bash
ssh -i ./ssh/quantchargeback.pem ubuntu@<ec2-host> 'cd /home/<USER>/sync-server && docker-compose down'
```