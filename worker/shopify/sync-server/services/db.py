#!/usr/bin/env python3
"""
Database Manager for PostgreSQL operations
Handles all database connections and queries for the sync server
"""

import os
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timezone
from dateutil import parser
from psycopg2.extras import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, execute_batch
from psycopg2.pool import SimpleConnectionPool
from urllib.parse import urlparse, parse_qs
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class ShopifyStore:
    """Shopify store configuration"""
    id: str
    domain: str
    access_token: str
    user_id: str
    store_name: str

class DatabaseManager:
    """PostgreSQL database manager"""
    
    def __init__(self):
        self.database_url = os.getenv('DATABASE_URL')
        if not self.database_url:
            raise ValueError("DATABASE_URL environment variable is required")
        
        # Parse the database URL and extract schema parameter
        parsed = urlparse(self.database_url)
        query_params = parse_qs(parsed.query)
        self.schema = query_params.get('schema', ['public'])[0]
        
        # Remove schema from query parameters for psycopg2
        clean_params = {k: v for k, v in query_params.items() if k != 'schema'}
        clean_query = '&'.join([f"{k}={v[0]}" for k, v in clean_params.items()])
        
        # Reconstruct URL without schema parameter
        self.clean_database_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
        if clean_query:
            self.clean_database_url += f"?{clean_query}"
        
        # Create connection pool
        self.pool = SimpleConnectionPool(
            2, 10,  # min 2, max 10 connections
            self.clean_database_url,
            cursor_factory=RealDictCursor
        )
        
        # Prepare batch size
        self.batch_size = 100
    
    def get_connection(self):
        """Get connection from pool"""
        return self.pool.getconn()
    
    def put_connection(self, conn):
        """Return connection to pool"""
        self.pool.putconn(conn)
    
    def close_all_connections(self):
        """Close all connections in the pool"""
        self.pool.closeall()
    
    def get_linked_stores(self) -> List[ShopifyStore]:
        """Get all linked Shopify stores"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT id, user_id, store_name, data 
                    FROM {self.schema}.linked_stores 
                    WHERE provider = 'shopify'
                """)
                
                stores = []
                for row in cur.fetchall():
                    data = row['data']
                    if 'domain' in data and 'accessToken' in data:
                        stores.append(ShopifyStore(
                            id=row['id'],
                            domain=data['domain'],
                            access_token=data['accessToken'],
                            user_id=row['user_id'],
                            store_name=row['store_name']
                        ))
                
                return stores
        finally:
            self.put_connection(conn)
    
    def get_store_by_id(self, store_id: str) -> Optional[ShopifyStore]:
        """Get a specific Shopify store by ID"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT id, user_id, store_name, data 
                    FROM {self.schema}.linked_stores 
                    WHERE provider = 'shopify' AND id = %s
                """, (store_id,))
                
                row = cur.fetchone()
                if row:
                    data = row['data']
                    if 'domain' in data and 'accessToken' in data:
                        return ShopifyStore(
                            id=row['id'],
                            domain=data['domain'],
                            access_token=data['accessToken'],
                            user_id=row['user_id'],
                            store_name=row['store_name']
                        )
                
                return None
        finally:
            self.put_connection(conn)
    
    # Block-related queries
    async def get_block_by_id(self, block_id: str) -> Optional[Dict]:
        """Get block data from database"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT * FROM {self.schema}.blocks WHERE id = %s
                """, (block_id,))
                return cur.fetchone()
        finally:
            self.put_connection(conn)
    
    async def get_linked_store(self, store_id: str) -> Optional[Dict]:
        """Get linked store data from database"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT * FROM {self.schema}.linked_stores WHERE id = %s
                """, (store_id,))
                return cur.fetchone()
        finally:
            self.put_connection(conn)
    
    async def get_shopify_transactions(self, linked_store_id: str, conditions: Dict) -> List[Dict]:
        """Get Shopify transactions from database with filtering"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                # Build dynamic query based on conditions
                where_clauses = ["linked_store_id = %s"]
                params = [linked_store_id]
                
                if conditions.get('amount') is not None:
                    where_clauses.append("amount = %s")
                    params.append(conditions['amount'])
                
                if conditions.get('currency'):
                    where_clauses.append("currency = %s")
                    params.append(conditions['currency'])
                
                if conditions.get('card_last4'):
                    # Search in payment_details JSON for card last 4
                    where_clauses.append("payment_details->>'card_last4' = %s")
                    params.append(conditions['card_last4'])
                
                query = f"""
                    SELECT * FROM {self.schema}.shopify_transactions 
                    WHERE {' AND '.join(where_clauses)}
                    ORDER BY processed_at DESC
                    LIMIT 100
                """
                
                cur.execute(query, params)
                return cur.fetchall()
        finally:
            self.put_connection(conn)
    
    async def get_shopify_orders(self, order_ids: List[str], linked_store_id: str) -> List[Dict]:
        """Get Shopify orders from database"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                # Convert order_ids to integers
                order_ids_int = [int(oid) for oid in order_ids]
                
                cur.execute(f"""
                    SELECT * FROM {self.schema}.shopify_orders 
                    WHERE id = ANY(%s) AND linked_store_id = %s
                """, (order_ids_int, linked_store_id))
                return cur.fetchall()
        finally:
            self.put_connection(conn)
    
    async def update_block(self, block_id: str, data: Dict) -> Dict:
        """Update block in database"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                # Build update query dynamically
                update_fields = []
                params = []
                
                if 'feedbackStatus' in data:
                    update_fields.append("feedback_status = %s")
                    params.append(data['feedbackStatus'])
                
                if 'feedbackTime' in data:
                    update_fields.append("feedback_time = %s")
                    params.append(data['feedbackTime'])
                
                if 'feedbackData' in data:
                    update_fields.append("feedback_data = %s")
                    params.append(Json(data['feedbackData']))
                
                update_fields.append("updated_at = %s")
                params.append(datetime.now(timezone.utc))
                
                params.append(block_id)
                
                query = f"""
                    UPDATE {self.schema}.blocks 
                    SET {', '.join(update_fields)}
                    WHERE id = %s
                    RETURNING *
                """
                
                cur.execute(query, params)
                conn.commit()
                return cur.fetchone()
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            self.put_connection(conn)
    
    async def save_mapped_blocks(self, mapped_blocks: List[Dict]) -> None:
        """Save mapped blocks to database"""
        if not mapped_blocks:
            return
        
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                # Prepare values for batch insert
                values = []
                for mb in mapped_blocks:
                    values.append({
                        'block_id': mb['blockId'],
                        'linked_store_id': mb['linkedStoreId'],
                        'order_id': int(mb['orderId']) if mb.get('orderId') else None,
                        'transaction_id': int(mb['transactionId']) if mb.get('transactionId') else None,
                        'created_at': datetime.now(timezone.utc),
                        'updated_at': datetime.now(timezone.utc)
                    })
                
                query = f"""
                    INSERT INTO {self.schema}.mapped_blocks (
                        block_id, linked_store_id, order_id, transaction_id,
                        created_at, updated_at
                    ) VALUES (
                        %(block_id)s, %(linked_store_id)s, %(order_id)s, 
                        %(transaction_id)s, %(created_at)s, %(updated_at)s
                    )
                    ON CONFLICT (block_id, linked_store_id, order_id, transaction_id) 
                    DO UPDATE SET updated_at = EXCLUDED.updated_at
                """
                
                execute_batch(cur, query, values, page_size=self.batch_size)
                conn.commit()
                logger.info(f"Saved {len(values)} mapped blocks")
        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to save mapped blocks: {e}")
            raise e
        finally:
            self.put_connection(conn)
    
    async def get_mapped_blocks(self, block_id: str, store_id: str) -> List[Dict]:
        """Get cached mapped blocks for a block and store"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT mb.*, 
                           so.name as order_name, so.order_number, so.total_price as order_total,
                           st.amount as transaction_amount, st.status as transaction_status
                    FROM {self.schema}.mapped_blocks mb
                    LEFT JOIN {self.schema}.shopify_orders so ON mb.order_id = so.id
                    LEFT JOIN {self.schema}.shopify_transactions st ON mb.transaction_id = st.id
                    WHERE mb.block_id = %s AND mb.linked_store_id = %s
                """, (block_id, store_id))
                return cur.fetchall()
        finally:
            self.put_connection(conn)
    
    async def get_alert_info_by_bin_caid(self, bin: str, caid: str) -> Optional[Dict]:
        """Get alert info by BIN and CAID for RDR type"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT * FROM {self.schema}.alert_infos 
                    WHERE bin = %s AND caid = %s AND alert_type = 'RDR'
                    LIMIT 1
                """, (bin, caid))
                return cur.fetchone()
        finally:
            self.put_connection(conn)
    
    async def get_alert_info_by_descriptor(self, descriptor: str) -> Optional[Dict]:
        """Get alert info by descriptor for ETHOCA type"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT * FROM {self.schema}.alert_infos 
                    WHERE descriptor = %s AND alert_type = 'ETHOCA'
                    LIMIT 1
                """, (descriptor,))
                return cur.fetchone()
        finally:
            self.put_connection(conn)
    
    async def create_block_refund(self, refund_data: Dict) -> Dict:
        """Create a block refund record"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                query = f"""
                    INSERT INTO {self.schema}.block_refunds (
                        block_id, store_id, order_id, refund_id, amount, currency,
                        reason, note, notify, shipping, refund_line_items, transactions,
                        order_adjustments, duties, gateway, parent_id, processed_at,
                        restock, user_id, admin_graphql_api_id, created_at, updated_at
                    ) VALUES (
                        %(block_id)s, %(store_id)s, %(order_id)s, %(refund_id)s,
                        %(amount)s, %(currency)s, %(reason)s, %(note)s, %(notify)s,
                        %(shipping)s, %(refund_line_items)s, %(transactions)s,
                        %(order_adjustments)s, %(duties)s, %(gateway)s, %(parent_id)s,
                        %(processed_at)s, %(restock)s, %(user_id)s, %(admin_graphql_api_id)s,
                        %(created_at)s, %(updated_at)s
                    ) RETURNING *
                """
                
                now = datetime.now(timezone.utc)
                refund_data['created_at'] = now
                refund_data['updated_at'] = now
                
                cur.execute(query, refund_data)
                conn.commit()
                return cur.fetchone()
        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to create block refund: {e}")
            raise e
        finally:
            self.put_connection(conn)
    
    # Shopify-specific methods for compatibility
    def upsert_orders_batch(self, orders: List[Tuple[Dict, str]]):
        """Batch upsert orders for better performance"""
        if not orders:
            return
        
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                # Prepare data for batch insert
                values = [self._format_order_data(order, linked_store_id) 
                         for order, linked_store_id in orders]
                
                # Define the query once, will be used for both paths
                query = f"""
                    INSERT INTO {self.schema}.shopify_orders (
                        id, linked_store_id, name, note, tags, test, email, phone, token,
                        app_id, number, currency, customer_data, closed_at, confirmed,
                        device_id, po_number, reference, tax_data, total_tax, browser_ip,
                        cart_token, created_at, line_items, source_url, tax_exempt,
                        updated_at, checkout_id, location_id, source_name, total_price,
                        cancelled_at, fulfillments, landing_site, order_number,
                        processed_at, total_weight, cancel_reason, contact_email,
                        payment_terms, shipping_lines, subtotal_price, taxes_included,
                        billing_address, customer_locale, duties_included, estimated_taxes,
                        note_attributes, total_discounts, financial_status, landing_site_ref,
                        shipping_address, source_identifier, total_outstanding,
                        fulfillment_status, buyer_accepts_marketing, checkout_token,
                        client_details, company, confirmation_number, current_subtotal_price,
                        current_subtotal_price_set, current_total_additional_fees_set,
                        current_total_discounts, current_total_discounts_set,
                        current_total_duties_set, current_total_price, current_total_price_set,
                        customer, discount_applications, discount_codes,
                        merchant_business_entity_id, merchant_of_record_app_id,
                        original_total_additional_fees_set, original_total_duties_set,
                        payment_gateway_names, referring_site, refunds, subtotal_price_set,
                        tax_lines, total_cash_rounding_payment_adjustment_set,
                        total_cash_rounding_refund_adjustment_set, total_discounts_set,
                        total_line_items_price, total_line_items_price_set,
                        total_price_set, total_shipping_price_set, total_tax_set,
                        total_tip_received, user_id, order_status_url
                    ) VALUES (
                        %(id)s, %(linked_store_id)s, %(name)s, %(note)s, %(tags)s, %(test)s,
                        %(email)s, %(phone)s, %(token)s, %(app_id)s, %(number)s, %(currency)s,
                        %(customer_data)s, %(closed_at)s, %(confirmed)s, %(device_id)s,
                        %(po_number)s, %(reference)s, %(tax_data)s, %(total_tax)s,
                        %(browser_ip)s, %(cart_token)s, %(created_at)s, %(line_items)s,
                        %(source_url)s, %(tax_exempt)s, %(updated_at)s, %(checkout_id)s,
                        %(location_id)s, %(source_name)s, %(total_price)s, %(cancelled_at)s,
                        %(fulfillments)s, %(landing_site)s, %(order_number)s, %(processed_at)s,
                        %(total_weight)s, %(cancel_reason)s, %(contact_email)s,
                        %(payment_terms)s, %(shipping_lines)s, %(subtotal_price)s,
                        %(taxes_included)s, %(billing_address)s, %(customer_locale)s,
                        %(duties_included)s, %(estimated_taxes)s, %(note_attributes)s,
                        %(total_discounts)s, %(financial_status)s, %(landing_site_ref)s,
                        %(shipping_address)s, %(source_identifier)s, %(total_outstanding)s,
                        %(fulfillment_status)s, %(buyer_accepts_marketing)s, %(checkout_token)s,
                        %(client_details)s, %(company)s, %(confirmation_number)s,
                        %(current_subtotal_price)s, %(current_subtotal_price_set)s,
                        %(current_total_additional_fees_set)s, %(current_total_discounts)s,
                        %(current_total_discounts_set)s, %(current_total_duties_set)s,
                        %(current_total_price)s, %(current_total_price_set)s, %(customer)s,
                        %(discount_applications)s, %(discount_codes)s,
                        %(merchant_business_entity_id)s, %(merchant_of_record_app_id)s,
                        %(original_total_additional_fees_set)s, %(original_total_duties_set)s,
                        %(payment_gateway_names)s, %(referring_site)s, %(refunds)s,
                        %(subtotal_price_set)s, %(tax_lines)s,
                        %(total_cash_rounding_payment_adjustment_set)s,
                        %(total_cash_rounding_refund_adjustment_set)s, %(total_discounts_set)s,
                        %(total_line_items_price)s, %(total_line_items_price_set)s,
                        %(total_price_set)s, %(total_shipping_price_set)s, %(total_tax_set)s,
                        %(total_tip_received)s, %(user_id)s, %(order_status_url)s
                    )
                    ON CONFLICT (id) DO UPDATE SET
                        updated_at = EXCLUDED.updated_at,
                        financial_status = EXCLUDED.financial_status,
                        fulfillment_status = EXCLUDED.fulfillment_status,
                        cancelled_at = EXCLUDED.cancelled_at,
                        refunds = EXCLUDED.refunds
                """
                
                # Process in chunks of 1000 for large datasets
                if len(values) > 1000:
                    # Process in chunks of 1000
                    for i in range(0, len(values), 1000):
                        chunk = values[i:i + 1000]
                        execute_batch(cur, query, chunk, page_size=self.batch_size)
                        logger.info(f"Batch upserted chunk {i//1000 + 1} of {(len(values) + 999) // 1000} ({len(chunk)} orders)")
                else:
                    # Use execute_batch for smaller batches
                    execute_batch(cur, query, values, page_size=self.batch_size)
                
                conn.commit()
                logger.info(f"Batch upserted {len(values)} orders")
                
        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to batch upsert orders: {e}")
            raise
        finally:
            self.put_connection(conn)
    
    def upsert_transactions_batch(self, transactions: List[Tuple[Dict, str, int]]):
        """Batch upsert transactions for better performance"""
        if not transactions:
            return
        
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                # Prepare data for batch insert
                values = [self._format_transaction_data(transaction, linked_store_id, order_id) 
                         for transaction, linked_store_id, order_id in transactions]
                
                # Define the query once, will be used for both paths
                query = f"""
                    INSERT INTO {self.schema}.shopify_transactions (
                        id, linked_store_id, order_id, fee, net, test, type, amount, currency,
                        source_id, source_type, processed_at, payout_status, source_order_id,
                        adjustment_reason, source_order_transaction_id,
                        adjustment_order_transactions, amount_rounding, "authorization",
                        authorization_expires_at, currency_exchange_adjustment, device_id,
                        error_code, extended_authorization_attributes, gateway, kind,
                        location_id, manual_payment_gateway, message, parent_id,
                        payment_details, payments_refund_attributes, receipt, source_name,
                        status, total_unsettled_set, user_id, created_at, updated_at,
                        reference_authorization_code, reference_card_number, 
                        reference_transaction_time, reference_amount, reference_currency,
                        reference_arn
                    ) VALUES (
                        %(id)s, %(linked_store_id)s, %(order_id)s, %(fee)s, %(net)s, %(test)s,
                        %(type)s, %(amount)s, %(currency)s, %(source_id)s, %(source_type)s,
                        %(processed_at)s, %(payout_status)s, %(source_order_id)s,
                        %(adjustment_reason)s, %(source_order_transaction_id)s,
                        %(adjustment_order_transactions)s, %(amount_rounding)s, %(authorization)s,
                        %(authorization_expires_at)s, %(currency_exchange_adjustment)s,
                        %(device_id)s, %(error_code)s, %(extended_authorization_attributes)s,
                        %(gateway)s, %(kind)s, %(location_id)s, %(manual_payment_gateway)s,
                        %(message)s, %(parent_id)s, %(payment_details)s,
                        %(payments_refund_attributes)s, %(receipt)s, %(source_name)s,
                        %(status)s, %(total_unsettled_set)s, %(user_id)s, %(created_at)s,
                        %(updated_at)s, %(reference_authorization_code)s, %(reference_card_number)s,
                        %(reference_transaction_time)s, %(reference_amount)s, %(reference_currency)s,
                        %(reference_arn)s
                    )
                    ON CONFLICT (id) DO UPDATE SET
                        updated_at = EXCLUDED.updated_at,
                        status = EXCLUDED.status,
                        payout_status = EXCLUDED.payout_status,
                        reference_authorization_code = EXCLUDED.reference_authorization_code,
                        reference_card_number = EXCLUDED.reference_card_number,
                        reference_transaction_time = EXCLUDED.reference_transaction_time,
                        reference_amount = EXCLUDED.reference_amount,
                        reference_currency = EXCLUDED.reference_currency,
                        reference_arn = EXCLUDED.reference_arn
                """
                
                # Process in chunks of 1000 for large datasets
                if len(values) > 1000:
                    # Process in chunks of 1000
                    for i in range(0, len(values), 1000):
                        chunk = values[i:i + 1000]
                        execute_batch(cur, query, chunk, page_size=self.batch_size)
                        logger.info(f"Batch upserted chunk {i//1000 + 1} of {(len(values) + 999) // 1000} ({len(chunk)} transactions)")
                else:
                    # Use execute_batch for smaller batches
                    execute_batch(cur, query, values, page_size=self.batch_size)
                conn.commit()
                logger.info(f"Batch upserted {len(values)} transactions")
                
        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to batch upsert transactions: {e}")
            raise
        finally:
            self.put_connection(conn)
    
    def upsert_disputes_batch(self, disputes: List[Tuple[Dict, str]]):
        """Batch upsert disputes for better performance"""
        if not disputes:
            return
        
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                # Prepare data for batch insert
                values = [self._format_dispute_data(dispute, linked_store_id) 
                         for dispute, linked_store_id in disputes]
                
                query = f"""
                    INSERT INTO {self.schema}.shopify_disputes (
                        id, linked_store_id, transaction_id, type, amount, reason, status,
                        currency, order_id, finalized_on, initiated_at, evidence_due_by,
                        evidence_sent_on, network_reason_code, created_at, updated_at
                    ) VALUES (
                        %(id)s, %(linked_store_id)s, %(transaction_id)s, %(type)s,
                        %(amount)s, %(reason)s, %(status)s, %(currency)s, %(order_id)s,
                        %(finalized_on)s, %(initiated_at)s, %(evidence_due_by)s,
                        %(evidence_sent_on)s, %(network_reason_code)s, %(created_at)s,
                        %(updated_at)s
                    )
                    ON CONFLICT (id) DO UPDATE SET
                        updated_at = EXCLUDED.updated_at,
                        status = EXCLUDED.status,
                        finalized_on = EXCLUDED.finalized_on,
                        evidence_sent_on = EXCLUDED.evidence_sent_on
                """
                
                execute_batch(cur, query, values, page_size=self.batch_size)
                conn.commit()
                logger.info(f"Batch upserted {len(values)} disputes")
                
        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to batch upsert disputes: {e}")
            raise
        finally:
            self.put_connection(conn)
    
    def _format_order_data(self, order: Dict, linked_store_id: str) -> Dict:
        """Format Shopify order data for database insertion"""
        def safe_int(value):
            if value is None or value == '':
                return None
            try:
                # Convert to float first, then multiply by 100 to convert to cents
                return int(float(value) * 100)
            except (ValueError, TypeError):
                return None
        
        def safe_timestamptz(value):
            """Convert a datetime string to datetime object (UTC)."""
            if not value:
                return None
            dt = parser.parse(value)
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            return dt.astimezone(timezone.utc)
        
        def safe_json(value):
            return Json(value) if value is not None else None
        
        # Only format fields that might change for updates
        return {
            'id': order.get('id'),
            'linked_store_id': linked_store_id,
            'name': order.get('name'),
            'note': order.get('note'),
            'tags': order.get('tags'),
            'test': order.get('test', False),
            'email': order.get('email'),
            'phone': order.get('phone'),
            'token': order.get('token'),
            'app_id': order.get('app_id'),
            'number': order.get('number'),
            'currency': order.get('currency'),
            'customer_data': safe_json(order.get('customer')),
            'closed_at': safe_timestamptz(order.get('closed_at')),
            'confirmed': order.get('confirmed'),
            'device_id': order.get('device_id'),
            'po_number': order.get('po_number'),
            'reference': order.get('reference'),
            'tax_data': safe_json(order.get('tax_lines')),
            'total_tax': safe_int(order.get('total_tax')),
            'browser_ip': order.get('browser_ip'),
            'cart_token': order.get('cart_token'),
            'created_at': safe_timestamptz(order.get('created_at')),
            'line_items': safe_json(order.get('line_items')),
            'source_url': order.get('source_url'),
            'tax_exempt': order.get('tax_exempt'),
            'updated_at': safe_timestamptz(order.get('updated_at')),
            'checkout_id': order.get('checkout_id'),
            'location_id': order.get('location_id'),
            'source_name': order.get('source_name'),
            'total_price': safe_int(order.get('total_price')),
            'cancelled_at': safe_timestamptz(order.get('cancelled_at')),
            'fulfillments': safe_json(order.get('fulfillments')),
            'landing_site': order.get('landing_site'),
            'order_number': order.get('order_number'),
            'processed_at': safe_timestamptz(order.get('processed_at')),
            'total_weight': order.get('total_weight'),
            'cancel_reason': order.get('cancel_reason'),
            'contact_email': order.get('contact_email'),
            'payment_terms': safe_json(order.get('payment_terms')),
            'shipping_lines': safe_json(order.get('shipping_lines')),
            'subtotal_price': safe_int(order.get('subtotal_price')),
            'taxes_included': order.get('taxes_included'),
            'billing_address': safe_json(order.get('billing_address')),
            'customer_locale': order.get('customer_locale'),
            'duties_included': order.get('duties_included'),
            'estimated_taxes': order.get('estimated_taxes'),
            'note_attributes': safe_json(order.get('note_attributes')),
            'total_discounts': safe_int(order.get('total_discounts')),
            'financial_status': order.get('financial_status'),
            'landing_site_ref': order.get('landing_site_ref'),
            'shipping_address': safe_json(order.get('shipping_address')),
            'source_identifier': order.get('source_identifier'),
            'total_outstanding': safe_int(order.get('total_outstanding')),
            'fulfillment_status': order.get('fulfillment_status'),
            'buyer_accepts_marketing': order.get('buyer_accepts_marketing'),
            'checkout_token': order.get('checkout_token'),
            'client_details': safe_json(order.get('client_details')),
            'company': safe_json(order.get('company')),
            'confirmation_number': order.get('confirmation_number'),
            'current_subtotal_price': safe_int(order.get('current_subtotal_price')),
            'current_subtotal_price_set': safe_json(order.get('current_subtotal_price_set')),
            'current_total_additional_fees_set': safe_json(order.get('current_total_additional_fees_set')),
            'current_total_discounts': safe_int(order.get('current_total_discounts')),
            'current_total_discounts_set': safe_json(order.get('current_total_discounts_set')),
            'current_total_duties_set': safe_json(order.get('current_total_duties_set')),
            'current_total_price': safe_int(order.get('current_total_price')),
            'current_total_price_set': safe_json(order.get('current_total_price_set')),
            'customer': safe_json(order.get('customer')),
            'discount_applications': safe_json(order.get('discount_applications')),
            'discount_codes': safe_json(order.get('discount_codes')),
            'merchant_business_entity_id': order.get('merchant_business_entity_id'),
            'merchant_of_record_app_id': order.get('merchant_of_record_app_id'),
            'original_total_additional_fees_set': safe_json(order.get('original_total_additional_fees_set')),
            'original_total_duties_set': safe_json(order.get('original_total_duties_set')),
            'payment_gateway_names': safe_json(order.get('payment_gateway_names')),
            'referring_site': order.get('referring_site'),
            'refunds': safe_json(order.get('refunds')),
            'subtotal_price_set': safe_json(order.get('subtotal_price_set')),
            'tax_lines': safe_json(order.get('tax_lines')),
            'total_cash_rounding_payment_adjustment_set': safe_json(order.get('total_cash_rounding_payment_adjustment_set')),
            'total_cash_rounding_refund_adjustment_set': safe_json(order.get('total_cash_rounding_refund_adjustment_set')),
            'total_discounts_set': safe_json(order.get('total_discounts_set')),
            'total_line_items_price': safe_int(order.get('total_line_items_price')),
            'total_line_items_price_set': safe_json(order.get('total_line_items_price_set')),
            'total_price_set': safe_json(order.get('total_price_set')),
            'total_shipping_price_set': safe_json(order.get('total_shipping_price_set')),
            'total_tax_set': safe_json(order.get('total_tax_set')),
            'total_tip_received': safe_int(order.get('total_tip_received')),
            'user_id': order.get('user_id'),
            'order_status_url': safe_json(order.get('order_status_url'))
        }
    
    def _format_transaction_data(self, transaction: Dict, linked_store_id: str, order_id: int) -> Dict:
        """Format Shopify transaction data for database insertion"""
        def safe_int(value):
            if value is None or value == '':
                return None
            try:
                # Convert to float first, then multiply by 100 to convert to cents
                return int(float(value) * 100)
            except (ValueError, TypeError):
                return None
        
        def safe_timestamptz(value):
            """Convert a datetime string to datetime object (UTC)."""
            if not value:
                return None
            dt = parser.parse(value)
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            return dt.astimezone(timezone.utc)
        
        def safe_json(value):
            return Json(value) if value is not None else None
        
        def extract_reference_values(transaction: Dict) -> Dict:
            """Extract reference values from transaction data"""
            reference_values = {
                'reference_authorization_code': None,
                'reference_card_number': None,
                'reference_transaction_time': None,
                'reference_amount': None,
                'reference_currency': None,
                'reference_arn': None
            }
            
            # Extract authorization code from receipt.charges.data
            receipt = transaction.get('receipt', {})
            if receipt and isinstance(receipt, dict):
                charges = receipt.get('charges', {})
                if charges and isinstance(charges, dict):
                    charges_data = charges.get('data', [])
                    if isinstance(charges_data, list) and charges_data:
                        # Get the first charge's authorization code
                        first_charge = charges_data[0]
                        if isinstance(first_charge, dict):
                            payment_method_details = first_charge.get('payment_method_details', {})
                            if isinstance(payment_method_details, dict):
                                card = payment_method_details.get('card', {})
                                if isinstance(card, dict):
                                    reference_values['reference_authorization_code'] = card.get('authorization_code')
            
            # Extract card number from payment_details
            payment_details = transaction.get('payment_details', {})
            if payment_details and isinstance(payment_details, dict):
                reference_values['reference_card_number'] = payment_details.get('credit_card_number')
            
            # Use processed_at as reference transaction time
            reference_values['reference_transaction_time'] = safe_timestamptz(transaction.get('processed_at'))
            
            # Extract amount and currency
            reference_values['reference_amount'] = safe_int(transaction.get('amount'))
            reference_values['reference_currency'] = transaction.get('currency')
            
            # Extract ARN if available (could be in various places)
            reference_values['reference_arn'] = transaction.get('arn')
            
            return reference_values
        
        now = datetime.now(timezone.utc)
        
        # Extract reference values
        references = extract_reference_values(transaction)
        
        return {
            'id': transaction.get('id'),
            'linked_store_id': linked_store_id,
            'order_id': order_id,
            'fee': safe_int(transaction.get('fee')),
            'net': safe_int(transaction.get('net')),
            'test': transaction.get('test', False),
            'type': transaction.get('type'),
            'amount': safe_int(transaction.get('amount')),
            'currency': transaction.get('currency'),
            'source_id': transaction.get('source_id'),
            'source_type': transaction.get('source_type'),
            'processed_at': safe_timestamptz(transaction.get('processed_at')),
            'payout_status': transaction.get('payout_status'),
            'source_order_id': transaction.get('source_order_id'),
            'adjustment_reason': transaction.get('adjustment_reason'),
            'source_order_transaction_id': transaction.get('source_order_transaction_id'),
            'adjustment_order_transactions': safe_json(transaction.get('adjustment_order_transactions')),
            'amount_rounding': safe_int(transaction.get('amount_rounding')),
            'authorization': transaction.get('authorization'),
            'authorization_expires_at': safe_timestamptz(transaction.get('authorization_expires_at')),
            'currency_exchange_adjustment': safe_json(transaction.get('currency_exchange_adjustment')),
            'device_id': transaction.get('device_id'),
            'error_code': transaction.get('error_code'),
            'extended_authorization_attributes': safe_json(transaction.get('extended_authorization_attributes')),
            'gateway': transaction.get('gateway'),
            'kind': transaction.get('kind'),
            'location_id': safe_json(transaction.get('location_id')),
            'manual_payment_gateway': transaction.get('manual_payment_gateway'),
            'message': transaction.get('message'),
            'parent_id': transaction.get('parent_id'),
            'payment_details': safe_json(transaction.get('payment_details')),
            'payments_refund_attributes': safe_json(transaction.get('payments_refund_attributes')),
            'receipt': safe_json(transaction.get('receipt')),
            'source_name': transaction.get('source_name'),
            'status': transaction.get('status'),
            'total_unsettled_set': safe_json(transaction.get('total_unsettled_set')),
            'user_id': transaction.get('user_id'),
            'created_at': safe_timestamptz(transaction.get('created_at')),
            'updated_at': now,
            # Add reference values
            'reference_authorization_code': references['reference_authorization_code'],
            'reference_card_number': references['reference_card_number'],
            'reference_transaction_time': references['reference_transaction_time'],
            'reference_amount': references['reference_amount'],
            'reference_currency': references['reference_currency'],
            'reference_arn': references['reference_arn']
        }
    
    def _format_dispute_data(self, dispute: Dict, linked_store_id: str) -> Dict:
        """Format Shopify dispute data for database insertion"""
        def safe_int(value):
            if value is None or value == '':
                return None
            try:
                # Convert to float first, then multiply by 100 to convert to cents
                return int(float(value) * 100)
            except (ValueError, TypeError):
                return None
        
        def safe_timestamptz(value):
            """Convert a datetime string to datetime object (UTC)."""
            if not value:
                return None
            dt = parser.parse(value)
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            return dt.astimezone(timezone.utc)
        
        now = datetime.now(timezone.utc)
        
        return {
            'id': dispute.get('id'),
            'linked_store_id': linked_store_id,
            'transaction_id': dispute.get('transaction_id'),
            'type': dispute.get('type'),
            'amount': safe_int(dispute.get('amount')),
            'reason': dispute.get('reason'),
            'status': dispute.get('status'),
            'currency': dispute.get('currency'),
            'order_id': dispute.get('order_id'),
            'finalized_on': safe_timestamptz(dispute.get('finalized_on')),
            'initiated_at': safe_timestamptz(dispute.get('initiated_at')),
            'evidence_due_by': safe_timestamptz(dispute.get('evidence_due_by')),
            'evidence_sent_on': safe_timestamptz(dispute.get('evidence_sent_on')),
            'network_reason_code': dispute.get('network_reason_code'),
            'created_at': safe_timestamptz(dispute.get('created_at')) or safe_timestamptz(dispute.get('initiated_at')) or now,
            'updated_at': now
        }