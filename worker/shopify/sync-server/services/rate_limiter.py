"""
Rate Limiter module for controlling API request rates
"""
import time
import asyncio
from typing import Optional


class RateLimiter:
    """Token bucket rate limiter for precise rate limiting"""
    
    def __init__(self, rate: float, capacity: int):
        """
        Initialize rate limiter
        
        Args:
            rate: Number of tokens per second
            capacity: Maximum number of tokens in the bucket
        """
        self.rate = rate  # tokens per second
        self.capacity = capacity
        self.tokens = capacity
        self.last_update = time.time()
        self._lock = asyncio.Lock()
    
    async def acquire(self, tokens: int = 1) -> None:
        """
        Acquire tokens from the bucket, waiting if necessary
        
        Args:
            tokens: Number of tokens to acquire
        """
        async with self._lock:
            while True:
                # Update token count based on time elapsed
                now = time.time()
                elapsed = now - self.last_update
                self.tokens = min(self.capacity, self.tokens + elapsed * self.rate)
                self.last_update = now
                
                # Check if we have enough tokens
                if self.tokens >= tokens:
                    self.tokens -= tokens
                    return
                
                # Calculate wait time
                wait_time = (tokens - self.tokens) / self.rate
                await asyncio.sleep(wait_time)
    
    def available_tokens(self) -> float:
        """Get current number of available tokens"""
        now = time.time()
        elapsed = now - self.last_update
        return min(self.capacity, self.tokens + elapsed * self.rate)
    
    async def __aenter__(self):
        """Context manager entry"""
        await self.acquire()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        pass


class ShopifyRateLimiter(RateLimiter):
    """Shopify-specific rate limiter with default settings"""
    
    def __init__(self, variant: str = "standard"):
        """
        Initialize Shopify rate limiter
        
        Args:
            variant: Rate limit variant ("standard" or "plus")
        """
        if variant == "plus":
            # Shopify Plus: 4 requests/second
            super().__init__(rate=4.0, capacity=40)
        else:
            # Standard Shopify: 2 requests/second
            super().__init__(rate=2.0, capacity=40)