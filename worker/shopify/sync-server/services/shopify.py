#!/usr/bin/env python3
"""
Shopify Data Synchronization Script
Synchronizes orders and transactions from Shopify stores to PostgreSQL database
"""

import os
import sys
import json
import time
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple, Set, AsyncIterator
import requests
from dotenv import load_dotenv
import aiohttp
from asyncio import Semaphore, Queue
from collections import defaultdict
import gzip
import io
import urllib.request
from .db import DatabaseManager, ShopifyStore
from .rate_limiter import RateLimiter

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('shopify_sync.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ShopifyAPI:
    """Shopify API client with rate limiting"""
    
    def __init__(self, domain: str, access_token: str):
        self.domain = domain
        self.access_token = access_token
        self.api_version = os.getenv('SHOPIFY_API_VERSION', '2025-04')
        self.base_url = f"https://{domain}/admin/api/{self.api_version}"
        self.graphql_url = f"https://{domain}/admin/api/{self.api_version}/graphql.json"
        self.headers = {
            'X-Shopify-Access-Token': access_token,
            'Content-Type': 'application/json'
        }
        # Use token bucket for more precise rate limiting
        # 1.8 requests per second with burst capacity of 4
        # This is more conservative than Shopify's stated 2 req/sec limit
        self.rate_limiter = RateLimiter(rate=1.8, capacity=4)
        
        # Track current rate limit status (will be updated during API calls)
        self.current_rate_limit = 0
        self.max_rate_limit = 40
        self.rate_limit_percentage = 0
        
        self.session = None
        self.request_count = 0
        
        # Flag to track whether this store's API token supports bulk operations
        # Will be set to False if we encounter ACCESS_DENIED errors
        self.supports_bulk_operations = True
        
        # Store configuration to track capabilities
        self.has_shopify_payments = None  # Will be determined on first use
    
    async def __aenter__(self):
        """Async context manager entry"""
        timeout = aiohttp.ClientTimeout(total=30, connect=10, sock_read=20)
        connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
        self.session = aiohttp.ClientSession(
            timeout=timeout,
            connector=connector,
            headers=self.headers
        )
            
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def make_request(self, endpoint: str, params: Dict = None, method: str = "GET", data: Dict = None) -> Optional[Dict]:
        """Make API request with improved rate limiting and retry logic"""
        await self.rate_limiter.acquire()
        
        url = f"{self.base_url}/{endpoint}"
        max_retries = 5
        base_delay = 1
        
        for attempt in range(max_retries):
            try:
                self.request_count += 1
                if method.upper() == "GET":
                    async with self.session.get(url, params=params) as response:
                        # Check rate limit headers
                        if 'X-Shopify-Shop-Api-Call-Limit' in response.headers:
                            limit_header = response.headers['X-Shopify-Shop-Api-Call-Limit']
                            current, max_limit = map(int, limit_header.split('/'))
                            
                            # Update instance variables to track current rate limit status
                            self.current_rate_limit = current
                            self.max_rate_limit = max_limit
                            self.rate_limit_percentage = (current / max_limit) * 100
                            
                            # Adaptive delay based on current rate limit percentage
                            if self.rate_limit_percentage >= 90:
                                # Over 90% - significant delay
                                delay = 3.0
                                logger.warning(f"Critical rate limit ({limit_header}): waiting {delay}s")
                                await asyncio.sleep(delay)
                            elif self.rate_limit_percentage >= 80:
                                # 80-90% - moderate delay
                                delay = 2.0
                                logger.warning(f"Approaching rate limit: {limit_header} - waiting {delay}s")
                                await asyncio.sleep(delay)
                            elif self.rate_limit_percentage >= 70:
                                # 70-80% - small delay
                                delay = 1.0
                                logger.info(f"Moderate rate limit usage: {limit_header} - waiting {delay}s")
                                await asyncio.sleep(delay)
                        
                        if response.status == 429:
                            # Rate limited - use exponential backoff
                            retry_after = int(response.headers.get('Retry-After', base_delay * (2 ** attempt)))
                            logger.warning(f"Rate limited. Waiting {retry_after}s before retry")
                            await asyncio.sleep(retry_after)
                            continue
                        
                        if response.status >= 500:
                            # Server error - retry with backoff
                            if attempt < max_retries - 1:
                                wait_time = base_delay * (2 ** attempt)
                                logger.warning(f"Server error {response.status}. Retrying in {wait_time}s")
                                await asyncio.sleep(wait_time)
                                continue
                        
                        response.raise_for_status()
                        return await response.json()
                elif method.upper() == "POST":
                    async with self.session.post(url, json=data) as response:
                        # Similar rate limit checking
                        if 'X-Shopify-Shop-Api-Call-Limit' in response.headers:
                            limit_header = response.headers['X-Shopify-Shop-Api-Call-Limit']
                            current, max_limit = map(int, limit_header.split('/'))
                            
                            # Update instance variables to track current rate limit status
                            self.current_rate_limit = current
                            self.max_rate_limit = max_limit
                            self.rate_limit_percentage = (current / max_limit) * 100
                            
                            # Adaptive delay based on current rate limit percentage
                            if self.rate_limit_percentage >= 90:
                                # Over 90% - significant delay
                                delay = 3.0
                                logger.warning(f"Critical rate limit ({limit_header}): waiting {delay}s")
                                await asyncio.sleep(delay)
                            elif self.rate_limit_percentage >= 80:
                                # 80-90% - moderate delay
                                delay = 2.0
                                logger.warning(f"Approaching rate limit: {limit_header} - waiting {delay}s")
                                await asyncio.sleep(delay)
                            elif self.rate_limit_percentage >= 70:
                                # 70-80% - small delay
                                delay = 1.0
                                logger.info(f"Moderate rate limit usage: {limit_header} - waiting {delay}s")
                                await asyncio.sleep(delay)
                        
                        if response.status == 429:
                            retry_after = int(response.headers.get('Retry-After', base_delay * (2 ** attempt)))
                            logger.warning(f"Rate limited. Waiting {retry_after}s before retry")
                            await asyncio.sleep(retry_after)
                            continue
                        
                        if response.status >= 500:
                            if attempt < max_retries - 1:
                                wait_time = base_delay * (2 ** attempt)
                                logger.warning(f"Server error {response.status}. Retrying in {wait_time}s")
                                await asyncio.sleep(wait_time)
                                continue
                        
                        response.raise_for_status()
                        return await response.json()
                    
            except asyncio.TimeoutError:
                logger.error(f"Request timeout (attempt {attempt + 1})")
                if attempt < max_retries - 1:
                    await asyncio.sleep(base_delay * (2 ** attempt))
                    continue
                raise
            except aiohttp.ClientError as e:
                logger.error(f"API request failed (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(base_delay * (2 ** attempt))
                    continue
                raise
        
        return None
    
    async def get_orders_page(self, params: Dict) -> List[Dict]:
        """Get a single page of orders (REST API fallback)"""
        response = await self.make_request('orders.json', params)
        return response.get('orders', []) if response else []
    
    async def get_all_orders_chunked(self, chunk_size: int = 250) -> AsyncIterator[List[Dict]]:
        """Get ALL orders in chunks to reduce memory usage (REST API fallback)"""
        since_id = None
        
        while True:
            params = {
                'status': 'any',
                'limit': chunk_size
            }
            
            if since_id:
                params['since_id'] = since_id
            else:
                params['order'] = 'id asc'
            
            orders = await self.get_orders_page(params)
            
            if not orders:
                break
            
            yield orders
            since_id = orders[-1]['id']
            
            logger.info(f"Fetched chunk of {len(orders)} orders. Last ID: {since_id}")
    
    async def get_orders_bulk(self) -> List[Dict]:
        """Get ALL orders using GraphQL bulk operations"""
        # If this store doesn't support bulk operations, return early
        if not self.supports_bulk_operations:
            logger.info(f"Skipping bulk operations for {self.domain} (not supported)")
            return []
        
        # Skip preliminary tests and directly proceed with main query
        # This avoids unnecessary API calls and potential permission issues
        logger.info(f"Starting bulk operation for orders from {self.domain}")
            
        # Define the GraphQL query to get orders
        bulk_query = """
        {
            orders {
                edges {
                    node {
                        id
                        legacyResourceId
                        name
                        closedAt
                        createdAt
                        updatedAt
                        processedAt
                        phone
                        confirmed
                        cancelledAt
                        cancelReason
                        tags
                        test
                        displayFinancialStatus
                        displayFulfillmentStatus
                        fullyPaid
                        unpaid
                        paymentGatewayNames
                        totalPriceSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                            presentmentMoney {
                                amount
                                currencyCode
                            }
                        }
                        subtotalPriceSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                            presentmentMoney {
                                amount
                                currencyCode
                            }
                        }
                        totalTaxSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                            presentmentMoney {
                                amount
                                currencyCode
                            }
                        }
                        totalOutstandingSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                            presentmentMoney {
                                amount
                                currencyCode
                            }
                        }
                        totalReceivedSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                            presentmentMoney {
                                amount
                                currencyCode
                            }
                        }
                        totalRefundedSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                            presentmentMoney {
                                amount
                                currencyCode
                            }
                        }
                        totalShippingPriceSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                            presentmentMoney {
                                amount
                                currencyCode
                            }
                        }
                        shippingAddress {
                            address1
                            address2
                            city
                            province
                            country
                            zip
                            name
                            phone
                        }
                        billingAddress {
                            address1
                            address2
                            city
                            province
                            country
                            zip
                            name
                            phone
                        }
                        disputes {
                            id
                            status
                        }
                    }
                }
            }
        }
        """
        
        try:
            # Start the bulk operation
            operation_id = await self.start_bulk_operation(bulk_query)
            
            # Poll until the operation is complete
            operation = await self.poll_bulk_operation(operation_id)
            
            # Download and process the result data
            url = operation.get("url")
            partial_url = operation.get("partialDataUrl")
            
            if not url and not partial_url:
                logger.warning("No URL returned from bulk operation for orders, falling back to REST API")
                return []
                
            data = await self.download_bulk_operation_data(url, partial_url)
            
            # Process the results into the expected format
            # The data will be in JSONL format, with each line representing a node
            # We need to transform it to match our existing API's output format
            orders = []
            
            for item in data:
                if 'legacyResourceId' in item and item.get('__typename') == 'Order':
                    # Transform GraphQL format to match REST API format for compatibility
                    order = {
                        'id': int(item['legacyResourceId']),
                        'name': item.get('name'),
                        'closed_at': item.get('closedAt'),
                        'created_at': item.get('createdAt'),
                        'updated_at': item.get('updatedAt'),
                        'processed_at': item.get('processedAt'),
                        'confirmed': item.get('confirmed', False),
                        'cancelled_at': item.get('cancelledAt'),
                        'cancel_reason': item.get('cancelReason'),
                        'tags': item.get('tags', ''),
                        'test': item.get('test', False),
                        'financial_status': item.get('displayFinancialStatus'),
                        'fulfillment_status': item.get('displayFulfillmentStatus')
                    }
                    
                    # Add customer ID if available
                    if 'customer' in item and item['customer']:
                        customer = item['customer']
                        order['customer'] = {
                            'id': int(customer.get('legacyResourceId', 0))
                        }
                    
                    # Add price data
                    if 'totalPriceSet' in item and item['totalPriceSet'] and 'shopMoney' in item['totalPriceSet']:
                        shop_money = item['totalPriceSet']['shopMoney']
                        order['total_price'] = shop_money.get('amount', '0.00')
                        order['currency'] = shop_money.get('currencyCode')
                    
                    if 'subtotalPriceSet' in item and item['subtotalPriceSet'] and 'shopMoney' in item['subtotalPriceSet']:
                        shop_money = item['subtotalPriceSet']['shopMoney']
                        order['subtotal_price'] = shop_money.get('amount', '0.00')
                    
                    if 'totalTaxSet' in item and item['totalTaxSet'] and 'shopMoney' in item['totalTaxSet']:
                        shop_money = item['totalTaxSet']['shopMoney']
                        order['total_tax'] = shop_money.get('amount', '0.00')
                    
                    if 'totalShippingPriceSet' in item and item['totalShippingPriceSet'] and 'shopMoney' in item['totalShippingPriceSet']:
                        shop_money = item['totalShippingPriceSet']['shopMoney']
                        order['shipping_lines'] = [{"price": shop_money.get('amount', '0.00')}]
                    
                    # Add addresses
                    if 'shippingAddress' in item and item['shippingAddress']:
                        order['shipping_address'] = item['shippingAddress']
                    
                    if 'billingAddress' in item and item['billingAddress']:
                        order['billing_address'] = item['billingAddress']
                    
                    orders.append(order)
            
            logger.info(f"Retrieved {len(orders)} orders using bulk operations")
            return orders
            
        except Exception as e:
            logger.error(f"Failed to get orders using bulk operations: {e}")
            # Don't fall back to REST API, just return empty list
            logger.error("GraphQL bulk operation for orders failed, no fallback available")
            return []  # Return empty list so caller can handle gracefully
    
    async def execute_graphql(self, query: str, variables: Dict = None) -> Dict:
        """Execute a GraphQL query or mutation"""
        await self.rate_limiter.acquire()
        
        payload = {
            "query": query,
            "variables": variables or {}
        }
        
        max_retries = 5
        base_delay = 1
        
        for attempt in range(max_retries):
            try:
                self.request_count += 1
                async with self.session.post(self.graphql_url, json=payload) as response:
                    # Check rate limit headers
                    if 'X-Shopify-Shop-Api-Call-Limit' in response.headers:
                        limit_header = response.headers['X-Shopify-Shop-Api-Call-Limit']
                        current, max_limit = map(int, limit_header.split('/'))
                        
                        # If we're getting close to the limit, slow down
                        if current >= max_limit * 0.8:
                            logger.warning(f"Approaching GraphQL rate limit: {limit_header}")
                            await asyncio.sleep(2)
                    
                    if response.status == 429:
                        # Rate limited - use exponential backoff
                        retry_after = int(response.headers.get('Retry-After', base_delay * (2 ** attempt)))
                        logger.warning(f"GraphQL rate limited. Waiting {retry_after}s before retry")
                        await asyncio.sleep(retry_after)
                        continue
                    
                    if response.status >= 500:
                        # Server error - retry with backoff
                        if attempt < max_retries - 1:
                            wait_time = base_delay * (2 ** attempt)
                            logger.warning(f"GraphQL server error {response.status}. Retrying in {wait_time}s")
                            await asyncio.sleep(wait_time)
                            continue
                    
                    response.raise_for_status()
                    result = await response.json()
                    
                    # Check for GraphQL errors
                    if "errors" in result:
                        errors = result["errors"]
                        error_message = '; '.join([error.get("message", "Unknown error") for error in errors])
                        logger.error(f"GraphQL errors: {error_message}")
                        
                        # Check if we should retry based on error type
                        throttled_errors = [e for e in errors if "throttled" in e.get("message", "").lower()]
                        if throttled_errors and attempt < max_retries - 1:
                            wait_time = base_delay * (2 ** attempt)
                            logger.warning(f"GraphQL throttled. Retrying in {wait_time}s")
                            await asyncio.sleep(wait_time)
                            continue
                    
                    return result
                    
            except asyncio.TimeoutError:
                logger.error(f"GraphQL request timeout (attempt {attempt + 1})")
                if attempt < max_retries - 1:
                    await asyncio.sleep(base_delay * (2 ** attempt))
                    continue
                raise
            except aiohttp.ClientError as e:
                logger.error(f"GraphQL request failed (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(base_delay * (2 ** attempt))
                    continue
                raise
        
        raise Exception("GraphQL request failed after maximum retries")
    
    async def check_bulk_operations_status(self) -> Optional[Dict]:
        """Check if there are any bulk operations in progress and return details if found"""
        query = """
        {
            currentBulkOperation {
                id
                status
                errorCode
                createdAt
                completedAt
                objectCount
                fileSize
                url
                partialDataUrl
                type
                query
            }
        }
        """
        
        try:
            result = await self.execute_graphql(query)
            current_operation = result.get("data", {}).get("currentBulkOperation")
            
            if current_operation:
                status = current_operation.get("status")
                operation_id = current_operation.get("id")
                logger.info(f"Found existing bulk operation with ID: {operation_id}, status: {status}")
                return current_operation
                
            return None  # No operations in progress
            
        except Exception as e:
            logger.warning(f"Error checking bulk operations status: {e}")
            return None  # Continue as if no operations in progress
    
    async def wait_for_existing_bulk_operation(self, max_wait_time: int = 300) -> Optional[Dict]:
        """Wait for an existing bulk operation to complete and return its result
        
        Args:
            max_wait_time: Maximum time to wait in seconds (default 5 minutes)
            
        Returns:
            Dict containing operation details if successful, None otherwise
        """
        start_time = time.time()
        poll_interval = 10  # Check every 10 seconds
        
        while time.time() - start_time < max_wait_time:
            current_operation = await self.check_bulk_operations_status()
            
            if not current_operation:
                logger.info("No bulk operation in progress")
                return None
                
            status = current_operation.get("status")
            operation_id = current_operation.get("id")
            
            if status == "COMPLETED":
                logger.info(f"Existing bulk operation {operation_id} has completed, using its results")
                return current_operation
                
            elif status in ["FAILED", "CANCELED"]:
                error_code = current_operation.get("errorCode", "UNKNOWN")
                logger.warning(f"Existing bulk operation {operation_id} failed with error: {error_code}")
                return None
                
            logger.info(f"Waiting for existing bulk operation {operation_id} to complete (status: {status})")
            await asyncio.sleep(poll_interval)
            
        logger.warning(f"Timed out waiting for bulk operation to complete after {max_wait_time} seconds")
        return None
    
    async def start_bulk_operation(self, query: str) -> str:
        """Start a bulk operation with the given query and return the operation ID"""
        # First check if there's already a bulk operation in progress
        current_operation = await self.check_bulk_operations_status()
        
        if current_operation:
            status = current_operation.get("status")
            operation_id = current_operation.get("id")
            
            if status == "RUNNING" or status == "CREATED":
                logger.warning(f"A bulk operation is already in progress: {operation_id}")
                
                # Wait for the existing operation to complete and use its results if appropriate
                completed_operation = await self.wait_for_existing_bulk_operation()
                if completed_operation and completed_operation.get("status") == "COMPLETED":
                    return completed_operation.get("id")
                    
                # If waiting failed, we'll need to wait longer before starting a new operation
                # This is a defensive measure to avoid conflicts
                logger.info("Waiting 30 seconds before attempting a new bulk operation")
                await asyncio.sleep(30)
        
        # Proceed with starting a new bulk operation
        mutation = """
        mutation bulkOperationRunQuery($query: String!) {
            bulkOperationRunQuery(query: $query) {
                bulkOperation {
                    id
                    status
                    url
                    partialDataUrl
                    objectCount
                    fileSize
                    createdAt
                    completedAt
                    errorCode
                }
                userErrors {
                    field
                    message
                }
            }
        }
        """
        
        variables = {
            "query": query
        }
        
        # Validate the query before sending to avoid common issues
        if 'legacyResourceId' in query and any(entity in query for entity in ['OrderTransaction', 'Dispute']):
            logger.warning("Query contains potential schema issues - legacyResourceId may not exist on all objects")
        
        try:
            # Add retry logic specifically for the "operation already in progress" error
            max_retries = 3
            retry_delay = 30  # seconds
            
            for attempt in range(max_retries):
                try:
                    result = await self.execute_graphql(mutation, variables)
                    
                    # Check for user errors
                    user_errors = result.get("data", {}).get("bulkOperationRunQuery", {}).get("userErrors", [])
                    if user_errors:
                        error_messages = [f"{e.get('field', '')}: {e.get('message', '')}" for e in user_errors]
                        error_msg = '; '.join(error_messages)
                        
                        # If the error is about an operation already in progress, we'll retry after waiting
                        if "already in progress" in error_msg.lower():
                            if attempt < max_retries - 1:
                                logger.warning(f"Bulk operation already in progress, waiting {retry_delay} seconds before retry {attempt+1}/{max_retries}")
                                await asyncio.sleep(retry_delay)
                                continue
                        
                        raise Exception(f"Failed to start bulk operation: {error_msg}")
                    
                    # Get the operation ID
                    operation = result.get("data", {}).get("bulkOperationRunQuery", {}).get("bulkOperation", {})
                    operation_id = operation.get("id")
                    
                    if not operation_id:
                        raise Exception("Failed to get bulk operation ID")
                    
                    logger.info(f"Started bulk operation with ID: {operation_id}")
                    return operation_id
                    
                except Exception as e:
                    # Check if the error is about an operation already in progress
                    if "already in progress" in str(e).lower() and attempt < max_retries - 1:
                        logger.warning(f"Bulk operation already in progress, waiting {retry_delay} seconds before retry {attempt+1}/{max_retries}")
                        await asyncio.sleep(retry_delay)
                    else:
                        # Re-raise other exceptions
                        raise
            
            # If we got here, we've exhausted our retries
            raise Exception("Failed to start bulk operation after multiple retries")
            
        except Exception as e:
            # Handle specific error types with more information
            error_msg = str(e)
            if "ACCESS_DENIED" in error_msg:
                logger.error("Access denied for bulk operations. Check API permissions and scopes.")
                logger.error("Required scopes: read_orders, read_customers, read_products, read_content")
            elif "INVALID_QUERY" in error_msg or "Invalid bulk query" in error_msg:
                logger.error(f"Invalid GraphQL query: {error_msg}")
                logger.error("Check schema compatibility or field names that may have changed")
            elif "TIMEOUT" in error_msg:
                logger.error("Bulk operation timed out. Try reducing query complexity.")
            elif "already in progress" in error_msg.lower():
                logger.error("A bulk operation is already in progress and maximum retries were exhausted.")
                logger.error("Consider increasing the wait time between retries or adding more retry attempts.")
            
            # Re-raise with enhanced information
            raise Exception(f"Bulk operation failed: {e}") from e
    
    async def poll_bulk_operation(self, operation_id: str, poll_interval: int = 2, max_retries: int = 3, max_duration: int = 3600) -> Dict:
        """Poll for the status of a bulk operation until it's completed or failed
        
        Args:
            operation_id: The GraphQL global ID of the bulk operation
            poll_interval: Time in seconds between status checks
            max_retries: Maximum number of consecutive failures to tolerate
            max_duration: Maximum time to wait in seconds (default 1 hour)
        """
        query = """
        query {
            node(id: "$id") {
                ... on BulkOperation {
                    id
                    status
                    errorCode
                    createdAt
                    completedAt
                    objectCount
                    fileSize
                    url
                    partialDataUrl
                    rootObjectCount
                    type
                    partialDataUrl
                    query
                }
            }
        }
        """.replace("$id", operation_id)
        
        start_time = time.time()
        retry_count = 0
        first_status_check = True
        
        while True:
            # Check if we've exceeded the time limit
            elapsed = time.time() - start_time
            if elapsed > max_duration:
                logger.warning(f"Bulk operation polling timed out after {elapsed:.1f}s")
                # Instead of raising an exception, try to continue with any available data
                try:
                    result = await self.execute_graphql(query)
                    operation = result.get("data", {}).get("node", {})
                    if operation and (operation.get("url") or operation.get("partialDataUrl")):
                        logger.info(f"Operation timeout but data URL found - attempting to continue")
                        return operation
                except Exception:
                    pass
                raise Exception(f"Bulk operation polling timed out after {elapsed:.1f}s")
                
            try:
                result = await self.execute_graphql(query)
                operation = result.get("data", {}).get("node", {})
                
                # Reset retry counter on successful request
                retry_count = 0
                
                if not operation:
                    logger.warning(f"No operation data returned for ID: {operation_id}")
                    await asyncio.sleep(poll_interval)
                    continue
                
                status = operation.get("status")
                logger.info(f"Bulk operation status: {status}")
                
                if status == "COMPLETED":
                    object_count = operation.get("objectCount", 0)
                    logger.info(f"Bulk operation completed with {object_count} objects")
                    return operation
                elif status in ["FAILED", "CANCELED"]:
                    error_code = operation.get("errorCode", "UNKNOWN")
                    
                    # Fast failure for permission issues
                    if error_code == "ACCESS_DENIED":
                        store_domain = self.domain
                        logger.warning(f"Bulk operations not available for {store_domain} due to API permissions")
                        self.supports_bulk_operations = False  # Mark this API client as not supporting bulk ops
                        raise Exception(f"ACCESS_DENIED: Bulk operations not supported for this API token")
                    
                    # Provide more context based on error code
                    error_context = ""
                    if error_code == "TIMEOUT":
                        error_context = " - Try reducing query complexity"
                    elif error_code == "INTERNAL_SERVER_ERROR":
                        error_context = " - Shopify server error, retry later"
                    
                    raise Exception(f"Bulk operation failed with error code: {error_code}{error_context}")
                    
                # Fast fail if first status check is already FAILED with ACCESS_DENIED
                # This helps prevent repeated polling when we know the store doesn't support bulk ops
                if first_status_check and status == "FAILED":
                    error_code = operation.get("errorCode")
                    if error_code == "ACCESS_DENIED":
                        self.supports_bulk_operations = False  # Mark as not supporting bulk ops
                        raise Exception(f"ACCESS_DENIED: Bulk operations not supported for this API token")
                
                first_status_check = False
                
            except Exception as e:
                error_str = str(e)
                # Fast fail for ACCESS_DENIED to avoid unnecessary retries
                if "ACCESS_DENIED" in error_str:
                    self.supports_bulk_operations = False  # Mark as not supporting bulk ops
                    raise Exception(f"ACCESS_DENIED: Bulk operations not supported for this API token")
                
                retry_count += 1
                if retry_count >= max_retries:
                    logger.error(f"Maximum retries ({max_retries}) exceeded while polling bulk operation")
                    raise Exception(f"Failed to poll bulk operation after {max_retries} retries: {e}") from e
                
                logger.warning(f"Error polling bulk operation (attempt {retry_count}/{max_retries}): {e}")
            
            # Wait before polling again
            await asyncio.sleep(poll_interval)
    
    async def download_bulk_operation_data(self, url: str, partial_url: str = None) -> List[Dict]:
        """Download and parse the JSONL data from a completed bulk operation
        
        Args:
            url: The URL to download the complete bulk operation data
            partial_url: Optional URL to download partial data if the operation has partial results
        """
        if not url and not partial_url:
            raise Exception("No URL provided for bulk operation data")
        
        download_url = url or partial_url
        logger.info(f"Downloading bulk operation data from: {download_url}")
        
        # Download the file (which might be gzipped) using aiohttp
        async with aiohttp.ClientSession() as session:
            async with session.get(download_url) as response:
                content_bytes = await response.read()
                
        if download_url.endswith('.gz') or 'gzip' in response.headers.get('Content-Encoding', ''):
            with gzip.GzipFile(fileobj=io.BytesIO(content_bytes)) as f:
                content = f.read().decode('utf-8')
        else:
            content = content_bytes.decode('utf-8')
        
        # Parse the JSONL format (each line is a JSON object)
        data = []
        for line in content.splitlines():
            if line.strip():
                try:
                    data.append(json.loads(line))
                except json.JSONDecodeError as e:
                    logger.error(f"Error parsing JSON line: {e}. Line: {line[:100]}...")
        
        logger.info(f"Downloaded {len(data)} records from bulk operation")
        
        # If we have both URLs and used the full data URL, also check for partial data
        if url and partial_url and download_url == url:
            try:
                logger.info(f"Checking for additional partial data at: {partial_url}")
                partial_data = await self.download_bulk_operation_data(None, partial_url)
                if partial_data:
                    logger.info(f"Downloaded additional {len(partial_data)} records from partial data")
                    data.extend(partial_data)
            except Exception as e:
                logger.warning(f"Failed to download partial data: {e}")
        
        return data
    
    async def get_transactions_batch_bulk(self) -> Dict[int, List[Dict]]:
        """Get transactions for orders using GraphQL bulk operations"""
        # If this store doesn't support bulk operations, return early
        if not self.supports_bulk_operations:
            logger.info(f"Skipping bulk operations for transactions from {self.domain} (not supported)")
            return {}
            
        # Define the GraphQL query to get orders with their transactions
        bulk_query = """
        {
            orders {
                edges {
                    node {
                        id
                        legacyResourceId
                        name
                        transactions {
                            id
                            amountSet {
                                shopMoney {
                                    amount
                                    currencyCode
                                }
                                presentmentMoney {
                                    amount
                                    currencyCode
                                }
                            }
                            createdAt
                            gateway
                            status
                            kind
                            test
                            processedAt
                            paymentId
                            errorCode
                            authorizationCode
                            authorizationExpiresAt
                            formattedGateway
                            fees {
                                amount {
                                    amount
                                    currencyCode
                                }
                                taxAmount {
                                    amount
                                    currencyCode
                                }
                            }
                            manuallyCapturable
                            paymentDetails {
                                ... on CreditCardPaymentDetails {
                                    avsResultCode
                                    cvvResultCode
                                    creditCardBin
                                    creditCardCompany
                                    creditCardNumber
                                    creditCardName
                                }
                            }
                            receiptJson
                        }
                    }
                }
            }
        }
        """
        
        try:
            # Start the bulk operation
            operation_id = await self.start_bulk_operation(bulk_query)
            
            # Poll until the operation is complete
            operation = await self.poll_bulk_operation(operation_id)
            
            # Download and process the result data
            url = operation.get("url")
            partial_url = operation.get("partialDataUrl")
            
            if not url and not partial_url:
                logger.warning("No URL returned from bulk operation, falling back to REST API")
                return {}
                
            data = await self.download_bulk_operation_data(url, partial_url)
            
            # Process the results into the expected format
            # The data will be in JSONL format, with each line representing a node
            # We need to transform it to match our existing API's output format
            results = {}
            
            # First pass: collect orders
            for item in data:
                if item.get('__typename') == 'Order':
                    # Extract order ID from either legacyResourceId or parse from global ID
                    order_id = None
                    if 'legacyResourceId' in item:
                        order_id = int(item['legacyResourceId'])
                    elif 'id' in item:
                        # Extract ID from the GraphQL global ID (gid://shopify/Order/123456789)
                        id_parts = item['id'].split('/')
                        if len(id_parts) > 0:
                            try:
                                order_id = int(id_parts[-1])
                            except (ValueError, IndexError):
                                logger.warning(f"Could not parse order ID from {item['id']}")
                                continue
                    
                    if order_id:
                        results[order_id] = []
            
            # Second pass: process transactions
            for item in data:
                if item.get('__typename') == 'OrderTransaction':
                    # Extract transaction ID from id or parse from global ID
                    transaction_id = None
                    if 'id' in item:
                        # Extract ID from the GraphQL global ID
                        id_parts = item['id'].split('/')
                        if len(id_parts) > 0:
                            try:
                                transaction_id = int(id_parts[-1])
                            except (ValueError, IndexError):
                                logger.warning(f"Could not parse transaction ID from {item['id']}")
                                continue
                    
                    if not transaction_id:
                        logger.warning(f"Skipping transaction with no valid ID: {item}")
                        continue
                    
                    # Find parent order ID
                    parent_id = None
                    for key, value in item.items():
                        if '.id' in key and '.transactions.' in key:
                            # This is the parent order ID reference
                            # Parse global ID to get numeric ID
                            parent_id_parts = value.split('/')
                            if len(parent_id_parts) > 0:
                                try:
                                    parent_id = int(parent_id_parts[-1])
                                    break
                                except (ValueError, IndexError):
                                    continue
                    
                    if parent_id is None:
                        logger.warning(f"Could not find parent order for transaction {transaction_id}")
                        continue
                    
                    # Format the transaction to match our REST API format
                    transaction = {
                        'id': transaction_id,
                        'order_id': parent_id,
                        'created_at': item.get('createdAt'),
                        'gateway': item.get('gateway', item.get('formattedGateway')),
                        'status': item.get('status'),
                        'kind': item.get('kind'),
                        'test': item.get('test', False),
                        'processed_at': item.get('processedAt'),
                        'error_code': item.get('errorCode'),
                        'authorization_code': item.get('authorizationCode')
                    }
                    
                    # Add amount information from amountSet
                    if 'amountSet' in item and item['amountSet'] and 'shopMoney' in item['amountSet']:
                        shop_money = item['amountSet']['shopMoney']
                        transaction['amount'] = shop_money.get('amount', '0.00')
                        transaction['currency'] = shop_money.get('currencyCode')
                    
                    # Add payment ID if available
                    if 'paymentId' in item and item['paymentId']:
                        transaction['payment_id'] = item['paymentId']
                        
                    # Add additional financial fields if available
                    if 'fees' in item and item['fees']:
                        transaction['fees'] = item['fees']
                        
                    if 'formattedGateway' in item and item['formattedGateway']:
                        transaction['formatted_gateway'] = item['formattedGateway']
                    
                    # Initialize the array if it doesn't exist
                    if parent_id not in results:
                        results[parent_id] = []
                    
                    results[parent_id].append(transaction)
            
            logger.info(f"Retrieved transactions for {len(results)} orders using bulk operations")
            return results
            
        except Exception as e:
            logger.error(f"Failed to get transactions using bulk operations: {e}")
            # Don't fall back to REST API, just return empty dict
            logger.error("GraphQL bulk operation for transactions failed, no fallback available")
            return {}
    
    async def get_transactions_batch(self, order_ids: List[int]) -> Dict[int, List[Dict]]:
        """Get transactions for multiple orders concurrently with optimized batching"""
        # Process in smaller chunks to avoid overwhelming API
        chunk_size = 5  # Reduce chunk size to 5 orders at a time to minimize rate limit issues
        results = {}
        
        # Calculate adaptive delay based on total orders
        # More orders need more delay between chunks to avoid rate limits
        base_delay = 0.2  # Base delay of 200ms
        adaptive_delay = min(1.0, base_delay * (len(order_ids) / 50))  # Cap at 1 second
        
        for i in range(0, len(order_ids), chunk_size):
            chunk = order_ids[i:i + chunk_size]
            tasks = []
            
            # Create tasks for each order in this chunk
            for order_id in chunk:
                task = self.get_transactions(order_id)
                tasks.append((order_id, task))
            
            # Execute all tasks in this chunk concurrently
            chunk_results = await asyncio.gather(
                *[task for _, task in tasks], 
                return_exceptions=True
            )
            
            # Process results for this chunk
            failures = 0
            for idx, result in enumerate(chunk_results):
                order_id = chunk[idx]
                if isinstance(result, Exception):
                    failures += 1
                    logger.error(f"Failed to get transactions for order {order_id}: {result}")
                    results[order_id] = []
                else:
                    results[order_id] = result
            
            # Dynamic delay based on number of failures in the chunk
            # If we have failures, increase delay for the next chunk
            if failures > 0:
                adjusted_delay = adaptive_delay * (1 + failures / len(chunk))
                logger.info(f"Increasing delay to {adjusted_delay:.2f}s due to {failures} failures")
            else:
                adjusted_delay = adaptive_delay
            
            # Delay between chunks to avoid rate limiting
            if i + chunk_size < len(order_ids):
                await asyncio.sleep(adjusted_delay)
        
        return results
    
    async def get_transactions(self, order_id: int) -> List[Dict]:
        """Get transactions for a specific order"""
        try:
            response = await self.make_request(f'orders/{order_id}/transactions.json')
            transactions = response.get('transactions', []) if response else []
            
            # Fix for "invalid literal for int()" error
            # Some transaction fields might be returned as strings that look like floats
            for transaction in transactions:
                # Handle id as a special case
                if 'id' in transaction and isinstance(transaction['id'], str):
                    try:
                        # Remove decimal part if present
                        if '.' in transaction['id']:
                            transaction['id'] = transaction['id'].split('.')[0]
                        transaction['id'] = int(transaction['id'])
                    except (ValueError, TypeError):
                        # Keep as string if conversion fails
                        pass
                        
                # Handle other fields that might need integer conversion
                for field in ['order_id', 'user_id', 'device_id', 'payment_id']:
                    if field in transaction and isinstance(transaction[field], str) and '.' in transaction[field]:
                        try:
                            # Remove decimal part and convert to int
                            transaction[field] = int(transaction[field].split('.')[0])
                        except (ValueError, TypeError):
                            # Keep as is if conversion fails
                            pass
            
            return transactions
        except Exception as e:
            logger.error(f"Error retrieving transactions for order {order_id}: {e}")
            return []
    
    async def get_disputes_page(self, params: Dict) -> List[Dict]:
        """Get a single page of disputes (REST API fallback)"""
        response = await self.make_request('shopify_payments/disputes.json', params)
        return response.get('disputes', []) if response else []
    
    async def get_all_disputes_chunked(self, chunk_size: int = 250) -> AsyncIterator[List[Dict]]:
        """Get ALL disputes in chunks to reduce memory usage (REST API fallback)"""
        since_id = None
        
        while True:
            params = {
                'limit': chunk_size
            }
            
            if since_id:
                params['since_id'] = since_id
            else:
                params['order'] = 'id asc'
            
            disputes = await self.get_disputes_page(params)
            
            if not disputes:
                break
            
            yield disputes
            since_id = disputes[-1]['id']
            
            logger.info(f"Fetched chunk of {len(disputes)} disputes. Last ID: {since_id}")
    
    async def get_disputes_bulk(self) -> List[Dict]:
        """Get ALL disputes using GraphQL bulk operations"""
        # If this store doesn't support bulk operations, return early
        if not self.supports_bulk_operations:
            logger.info(f"Skipping bulk operations for disputes from {self.domain} (not supported)")
            return []
            
        # Define the GraphQL query to get disputes
        # Check if this store supports Shopify Payments disputes
        # This feature requires Shopify Payments to be enabled
        try:
            # Try direct GraphQL query first instead of bulk operations
            # Direct queries often have fewer permission requirements than bulk operations
            logger.info(f"Using direct GraphQL query for disputes (skipping bulk operation)")
            query = """
            {
                disputes {
                    edges {
                        node {
                            id
                            amount {
                                amount
                                currencyCode
                            }
                            status
                            initiatedAt
                            evidenceDueBy
                            evidenceSentOn
                            finalizedOn
                            reasonDetails {
                                reason
                                networkReasonCode
                            }
                            type
                            order {
                                id
                                name
                            }
                        }
                    }
                }
            }
            """
            
            try:
                # Execute direct query first
                result = await self.execute_graphql(query)
                if result and "data" in result and "disputes" in result["data"] and "edges" in result["data"]["disputes"]:
                    dispute_edges = result["data"]["disputes"]["edges"]
                    direct_disputes = []
                    
                    # Process disputes directly
                    for edge in dispute_edges:
                        if "node" in edge:
                            dispute_data = edge["node"]
                            dispute_id = None
                            
                            # Extract dispute ID
                            if "id" in dispute_data:
                                id_parts = dispute_data["id"].split("/")
                                if len(id_parts) > 0:
                                    try:
                                        dispute_id = int(id_parts[-1])
                                    except (ValueError, IndexError):
                                        pass
                            
                            # Extract order ID if available
                            order_id = None
                            if "order" in dispute_data and dispute_data["order"] and "id" in dispute_data["order"]:
                                order_id_parts = dispute_data["order"]["id"].split("/")
                                if len(order_id_parts) > 0:
                                    try:
                                        order_id = int(order_id_parts[-1])
                                    except (ValueError, IndexError):
                                        pass
                            
                            if dispute_id:
                                # Create dispute object
                                dispute = {
                                    "id": dispute_id,
                                    "order_id": order_id,
                                    "status": dispute_data.get("status"),
                                    "initiated_at": dispute_data.get("initiatedAt"),
                                    "evidence_due_by": dispute_data.get("evidenceDueBy"),
                                    "evidence_sent_on": dispute_data.get("evidenceSentOn"),
                                    "finalized_on": dispute_data.get("finalizedOn")
                                }
                                
                                direct_disputes.append(dispute)
                    
                    # If we found disputes with the direct query, return them
                    if direct_disputes:
                        logger.info(f"Retrieved {len(direct_disputes)} disputes using direct GraphQL query")
                        return direct_disputes
            except Exception as e:
                logger.warning(f"Direct GraphQL query for disputes failed: {e}, falling back to bulk operation")
            
            # If direct query failed, fall back to simplified bulk query
            logger.info(f"Falling back to bulk operations for disputes")
            bulk_query = """
            {
                disputes {
                    edges {
                        node {
                            id
                            amount {
                                amount
                                currencyCode
                            }
                            status
                            initiatedAt
                            evidenceDueBy
                            evidenceSentOn
                            finalizedOn
                            reasonDetails {
                                reason
                                networkReasonCode
                            }
                            type
                            order {
                                id
                                name
                            }
                        }
                    }
                }
            }
            """
        except Exception as e:
            logger.error(f"Error setting up dispute query: {e}")
            # Super simple fallback query using orders to get disputes
            bulk_query = """
            {
                orders(first: 50) {
                    edges {
                        node {
                            id
                            name
                            disputes {
                                id
                                status
                                type
                            }
                        }
                    }
                }
            }
            """
        
        try:
            # Start the bulk operation
            operation_id = await self.start_bulk_operation(bulk_query)
            
            # Poll until the operation is complete
            operation = await self.poll_bulk_operation(operation_id)
            
            # Download and process the result data
            url = operation.get("url")
            partial_url = operation.get("partialDataUrl")
            
            if not url and not partial_url:
                logger.warning("No URL returned from bulk operation for disputes, falling back to REST API")
                return []
                
            data = await self.download_bulk_operation_data(url, partial_url)
            
            # Process the results into the expected format
            disputes = []
            
            # Extract disputes from data
            # Count types for debugging
            type_counts = {}
            for item in data:
                # Infer the type from id pattern or fields present
                if 'id' in item and ('ShopifyPaymentsDispute' in item['id'] or 'Dispute' in item['id']):
                    inferred_type = 'ShopifyPaymentsDispute'
                elif 'status' in item and 'reasonDetails' in item and 'amount' in item and 'order' in item:
                    inferred_type = 'ShopifyPaymentsDispute'
                else:
                    inferred_type = item.get('__typename', 'Unknown')
                
                type_counts[inferred_type] = type_counts.get(inferred_type, 0) + 1
                
                # Check if this is a dispute object based on fields
                if (item.get('__typename') in ['ShopifyPaymentsDispute', 'Dispute'] or 
                    ('id' in item and ('ShopifyPaymentsDispute' in item['id'] or 'Dispute' in item['id'])) or
                    ('status' in item and 'reasonDetails' in item and 'amount' in item and 'order' in item)):
                    # Direct dispute objects from disputes query
                    dispute_id = None
                    if 'id' in item:
                        id_parts = item['id'].split('/')
                        if len(id_parts) > 0:
                            try:
                                dispute_id = int(id_parts[-1])
                            except (ValueError, IndexError):
                                logger.warning(f"Could not parse dispute ID from {item['id']}")
                                continue
                    
                    if not dispute_id:
                        logger.warning(f"Skipping dispute with no valid ID: {item}")
                        continue
                        
                    # Transform GraphQL format to match REST API format
                    dispute = {
                        'id': dispute_id,
                        'status': item.get('status').lower() if item.get('status') else None,
                        'initiated_at': item.get('initiatedAt'),
                        'evidence_due_by': item.get('evidenceDueBy'),
                        'evidence_sent_on': item.get('evidenceSentOn'),
                        'finalized_on': item.get('finalizedOn'),
                        'type': item.get('type').lower() if item.get('type') else None,
                        'network_reason_code': item.get('reasonDetails', {}).get('networkReasonCode'),
                        'reason': item.get('reasonDetails', {}).get('reason').lower() if item.get('reasonDetails') and item.get('reasonDetails', {}).get('reason') else None
                    }
                    
                    # Add amount information
                    if 'amount' in item and item['amount']:
                        dispute['amount'] = item['amount'].get('amount', '0.00')
                        dispute['currency'] = item['amount'].get('currencyCode')
                    
                    # Add order information if available
                    if 'order' in item and item['order'] and 'id' in item['order']:
                        # Extract order ID from global ID
                        order_id_parts = item['order']['id'].split('/')
                        if len(order_id_parts) > 0:
                            try:
                                dispute['order_id'] = int(order_id_parts[-1])
                            except (ValueError, IndexError):
                                logger.warning(f"Could not parse order ID from {item['order']['id']}")
                    
                    disputes.append(dispute)
                # Check if this is an order with disputes (legacy structure)
                elif item.get('__typename') == 'Order' and 'disputes' in item:
                    # Extract order ID
                    order_id = None
                    if 'legacyResourceId' in item:
                        try:
                            order_id = int(item['legacyResourceId'])
                        except (ValueError, TypeError):
                            if 'id' in item:
                                # Try to extract from the global ID
                                id_parts = item['id'].split('/')
                                if len(id_parts) > 0:
                                    try:
                                        order_id = int(id_parts[-1])
                                    except (ValueError, IndexError):
                                        logger.warning(f"Could not parse order ID from {item['id']}")
                    
                    # Process each dispute in the order
                    order_disputes = item.get('disputes', [])
                    for dispute_data in order_disputes:
                        # Get the dispute ID
                        dispute_id = None
                        if 'id' in dispute_data:
                            # Extract ID from the GraphQL global ID
                            id_parts = dispute_data['id'].split('/')
                            if len(id_parts) > 0:
                                try:
                                    dispute_id = int(id_parts[-1])
                                except (ValueError, IndexError):
                                    logger.warning(f"Could not parse dispute ID from {dispute_data['id']}")
                                    continue
                        
                        if not dispute_id:
                            logger.warning(f"Skipping dispute with no valid ID: {dispute_data}")
                            continue
                            
                        # Transform GraphQL format to match REST API format for compatibility
                        dispute = {
                            'id': dispute_id,
                            'status': dispute_data.get('status').lower() if dispute_data.get('status') else None,
                            'initiated_at': dispute_data.get('initiatedAt'),
                            'evidence_due_by': dispute_data.get('evidenceDueBy'),
                            'evidence_sent_on': dispute_data.get('evidenceSentOn'),
                            'finalized_on': dispute_data.get('finalizedOn'),
                            'type': dispute_data.get('type').lower() if dispute_data.get('type') else None,
                            'network_reason_code': dispute_data.get('reasonDetails', {}).get('networkReasonCode'),
                            'order_id': order_id
                        }
                        
                        # Add amount information
                        if 'amount' in dispute_data and dispute_data['amount']:
                            dispute['amount'] = dispute_data['amount'].get('amount', '0.00')
                            dispute['currency'] = dispute_data['amount'].get('currencyCode')
                        
                        # Add reason if available
                        if 'reason' in dispute_data:
                            dispute['reason'] = dispute_data.get('reason').lower() if dispute_data.get('reason') else None
                        
                        disputes.append(dispute)
                
                # Also check for standalone dispute objects (from fallback query)
                elif item.get('__typename') in ['ShopifyPaymentsDispute', 'Dispute']:
                    # Get the ID from the global ID
                    dispute_id = None
                    if 'id' in item:
                        id_parts = item['id'].split('/')
                        if len(id_parts) > 0:
                            try:
                                dispute_id = int(id_parts[-1])
                            except (ValueError, IndexError):
                                logger.warning(f"Could not parse dispute ID from {item['id']}")
                                continue
                    
                    if not dispute_id:
                        logger.warning(f"Skipping dispute with no valid ID: {item}")
                        continue
                        
                    # Transform GraphQL format to match REST API format
                    dispute = {
                        'id': dispute_id,
                        'status': item.get('status').lower() if item.get('status') else None,
                        'initiated_at': item.get('initiatedAt'),
                        'evidence_due_by': item.get('evidenceDueBy'),
                        'evidence_sent_on': item.get('evidenceSentOn'),
                        'finalized_on': item.get('finalizedOn'),
                        'type': item.get('type').lower() if item.get('type') else None,
                        'network_reason_code': item.get('networkReasonCode')
                    }
                    
                    # Add amount information
                    if 'amount' in item and item['amount']:
                        dispute['amount'] = item['amount'].get('amount', '0.00')
                        dispute['currency'] = item['amount'].get('currencyCode')
                    
                    # Add reason
                    if 'reason' in item:
                        dispute['reason'] = item.get('reason')
                    
                    # Add order information if available
                    if 'order' in item and item['order'] and 'id' in item['order']:
                        # Extract order ID from global ID
                        order_id_parts = item['order']['id'].split('/')
                        if len(order_id_parts) > 0:
                            try:
                                dispute['order_id'] = int(order_id_parts[-1])
                            except (ValueError, IndexError):
                                logger.warning(f"Could not parse order ID from {item['order']['id']}")
                    
                    disputes.append(dispute)
            
            logger.info(f"Object types in bulk operation data: {type_counts}")
            logger.info(f"Retrieved {len(disputes)} disputes using bulk operations")
            return disputes
            
        except Exception as e:
            logger.error(f"Failed to get disputes using bulk operations: {e}")
            # Fall back to the individual API calls if bulk operation fails
            logger.info("Falling back to REST API for disputes")
            return []  # Return empty list so caller can handle gracefully


class ShopifyDataSynchronizer:
    """Main synchronization class"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.order_batch_size = 100  # Process orders in batches
        self.transaction_batch_size = 100  # Fetch transactions for N orders at once
    
    async def sync_all_stores(self, sync_disputes: bool = None):
        """Synchronize data for all linked stores concurrently
        
        Args:
            sync_disputes: If True, only sync disputes. If False/None, sync all (orders + transactions + disputes).
        """
        stores = self.db_manager.get_linked_stores()
        logger.info(f"Found {len(stores)} Shopify stores to synchronize")
        
        # Process stores concurrently but limit to avoid overwhelming the system
        semaphore = Semaphore(3)  # Process up to 3 stores concurrently
        
        async def sync_with_semaphore(store):
            async with semaphore:
                try:
                    await self.sync_store(store, sync_disputes=sync_disputes)
                except Exception as e:
                    logger.error(f"Failed to sync store {store.store_name} ({store.domain}): {e}")
        
        tasks = [sync_with_semaphore(store) for store in stores]
        await asyncio.gather(*tasks)
    
    async def sync_store_by_id(self, store_id: str, sync_disputes: bool = None):
        """Synchronize data for a specific store by ID
        
        Args:
            store_id: The store ID to sync
            sync_disputes: If True, only sync disputes. If False/None, sync all (orders + transactions + disputes).
        """
        store = self.db_manager.get_store_by_id(store_id)
        if not store:
            raise ValueError(f"Store with ID {store_id} not found")
        
        logger.info(f"Synchronizing store: {store.store_name} ({store.domain})")
        await self.sync_store(store, sync_disputes=sync_disputes)
    
    async def sync_store(self, store: ShopifyStore, sync_disputes: bool = None):
        """Synchronize data for a single store with optimized processing
        
        Args:
            store: The ShopifyStore object to sync
            sync_disputes: If True, only sync disputes. If False/None, sync all (orders + transactions + disputes).
        """
        logger.info(f"Starting synchronization for store: {store.store_name} ({store.domain})")
        
        start_time = time.time()
        total_orders = 0
        total_transactions = 0
        dispute_count = 0
        
        # If sync_disputes is explicitly True, only sync disputes
        if sync_disputes is True:
            logger.info(f"Sync mode: DISPUTES ONLY for {store.store_name}")
            async with ShopifyAPI(store.domain, store.access_token) as api:
                try:
                    # Sync only disputes
                    dispute_count = await self.sync_store_disputes(store)
                except Exception as e:
                    logger.error(f"Failed to sync disputes for {store.store_name}: {e}")
                    dispute_count = 0
                    
                elapsed_time = time.time() - start_time
                logger.info(f"Completed dispute sync for {store.store_name}: "
                           f"{dispute_count} disputes in {elapsed_time:.2f}s "
                           f"({api.request_count} API calls)")
        else:
            # Default behavior: sync ALL (orders + transactions + disputes)
            logger.info(f"Sync mode: FULL SYNC (orders + transactions + disputes) for {store.store_name}")
            async with ShopifyAPI(store.domain, store.access_token) as api:
                # Sync orders (with transactions included) and disputes in that order
                # All transaction data is fetched along with orders to reduce API calls
                try:
                    # Try direct GraphQL query first instead of bulk operations for orders
                    # This approach is similar to how dispute synchronization works
                    logger.info(f"Attempting to use direct GraphQL query for orders")
                    
                    try:
                        # Use same query structure as direct_query for bulk operations
                        bulk_query = """
                        {
                            orders {
                                edges {
                                    node {
                                        id
                                        legacyResourceId
                                        name
                                        note
                                        tags
                                        test
                                        displayFinancialStatus
                                        displayFulfillmentStatus
                                        closedAt
                                        confirmed
                                        poNumber
                                        createdAt
                                        taxExempt
                                        updatedAt
                                        sourceName
                                        cancelledAt
                                        processedAt
                                        cancelReason
                                        taxesIncluded
                                        customerLocale
                                        currencyCode
                                        customer {
                                            legacyResourceId
                                        }
                                        shippingAddress {
                                            city
                                            province
                                            country
                                        }
                                        billingAddress {
                                            city
                                            province
                                            country
                                        }
                                        totalPriceSet {
                                            shopMoney {
                                                amount
                                                currencyCode
                                            }
                                            presentmentMoney {
                                                amount
                                                currencyCode
                                            }
                                        }
                                        subtotalPriceSet {
                                            shopMoney {
                                                amount
                                                currencyCode
                                            }
                                            presentmentMoney {
                                                amount
                                                currencyCode
                                            }
                                        }
                                        totalTaxSet {
                                            shopMoney {
                                                amount
                                                currencyCode
                                            }
                                            presentmentMoney {
                                                amount
                                                currencyCode
                                            }
                                        }
                                        totalDiscountsSet {
                                            shopMoney {
                                                amount
                                                currencyCode
                                            }
                                            presentmentMoney {
                                                amount
                                                currencyCode
                                            }
                                        }
                                        paymentGatewayNames
                                        transactions {
                                            id
                                            amountSet {
                                                shopMoney {
                                                    amount
                                                    currencyCode
                                                }
                                                presentmentMoney {
                                                    amount
                                                    currencyCode
                                                }
                                            }
                                            createdAt
                                            gateway
                                            status
                                            kind
                                            test
                                            processedAt
                                            paymentId
                                            errorCode
                                            authorizationCode
                                            authorizationExpiresAt
                                            formattedGateway
                                            fees {
                                                amount {
                                                    amount
                                                    currencyCode
                                                }
                                                taxAmount {
                                                    amount
                                                    currencyCode
                                                }
                                            }
                                            manuallyCapturable
                                            paymentDetails {
                                                ... on CardPaymentDetails {
                                                    avsResultCode
                                                    cvvResultCode
                                                    bin
                                                    company
                                                    number
                                                }
                                            }
                                            receiptJson
                                        }
                                    }
                                }
                            }
                        }
                        """
                        
                        # Start the bulk operation
                        operation_id = await api.start_bulk_operation(bulk_query)
                        
                        # Poll until the operation is complete
                        operation = await api.poll_bulk_operation(operation_id)
                        
                        # Download and process the result data
                        url = operation.get("url")
                        partial_url = operation.get("partialDataUrl")
                        
                        if not url and not partial_url:
                            logger.warning("No URL returned from bulk operation for orders, falling back to direct GraphQL query")
                            orders = []
                        else:
                            data = await api.download_bulk_operation_data(url, partial_url)
                            
                            # Process the results into the expected format
                            orders = []
                            
                            for item in data:
                                if 'legacyResourceId' in item and (item.get('__typename') == 'Order' or 'Order' in item.get('id', '')):
                                    # Transform GraphQL format to match REST API format for compatibility
                                    order = {
                                        'id': int(item['legacyResourceId']),
                                        'name': item.get('name'),
                                        'note': item.get('note'),
                                        'tags': item.get('tags', ''),
                                        'test': item.get('test', False),
                                        'closed_at': item.get('closedAt'),
                                        'created_at': item.get('createdAt'),
                                        'updated_at': item.get('updatedAt'),
                                        'processed_at': item.get('processedAt'),
                                        'confirmed': item.get('confirmed', False),
                                        'cancelled_at': item.get('cancelledAt'),
                                        'cancel_reason': item.get('cancelReason'),
                                        'po_number': item.get('poNumber'),
                                        'tax_exempt': item.get('taxExempt', False),
                                        'source_name': item.get('sourceName'),
                                        'taxes_included': item.get('taxesIncluded', False),
                                        'customer_locale': item.get('customerLocale'),
                                        'currency': item.get('currencyCode'),
                                        'financial_status': item.get('displayFinancialStatus'),
                                        'fulfillment_status': item.get('displayFulfillmentStatus')
                                    }
                                    
                                    # Add customer ID if available
                                    if 'customer' in item and item['customer']:
                                        customer = item['customer']
                                        order['customer'] = {
                                            'id': int(customer.get('legacyResourceId', 0))
                                        }
                                    
                                    # Add price data
                                    if 'totalPriceSet' in item and item['totalPriceSet'] and 'shopMoney' in item['totalPriceSet']:
                                        shop_money = item['totalPriceSet']['shopMoney']
                                        order['total_price'] = shop_money.get('amount', '0.00')
                                        order['currency'] = shop_money.get('currencyCode')
                                        order['total_price_set'] = item['totalPriceSet']
                                    
                                    if 'subtotalPriceSet' in item and item['subtotalPriceSet'] and 'shopMoney' in item['subtotalPriceSet']:
                                        shop_money = item['subtotalPriceSet']['shopMoney']
                                        order['subtotal_price'] = shop_money.get('amount', '0.00')
                                        order['subtotal_price_set'] = item['subtotalPriceSet']
                                    
                                    if 'totalTaxSet' in item and item['totalTaxSet'] and 'shopMoney' in item['totalTaxSet']:
                                        shop_money = item['totalTaxSet']['shopMoney']
                                        order['total_tax'] = shop_money.get('amount', '0.00')
                                        order['total_tax_set'] = item['totalTaxSet']
                                    
                                    if 'totalDiscountsSet' in item and item['totalDiscountsSet'] and 'shopMoney' in item['totalDiscountsSet']:
                                        shop_money = item['totalDiscountsSet']['shopMoney']
                                        order['total_discounts'] = shop_money.get('amount', '0.00')
                                        order['total_discounts_set'] = item['totalDiscountsSet']
                                    
                                    if 'totalShippingPriceSet' in item and item['totalShippingPriceSet'] and 'shopMoney' in item['totalShippingPriceSet']:
                                        shop_money = item['totalShippingPriceSet']['shopMoney']
                                        order['shipping_lines'] = [{"price": shop_money.get('amount', '0.00')}]
                                    
                                    # Add addresses
                                    if 'shippingAddress' in item and item['shippingAddress']:
                                        order['shipping_address'] = item['shippingAddress']
                                    
                                    if 'billingAddress' in item and item['billingAddress']:
                                        order['billing_address'] = item['billingAddress']
                                        
                                    # Add line items if available
                                    if 'lineItems' in item and item['lineItems'] and 'edges' in item['lineItems']:
                                        line_items = []
                                        for edge in item['lineItems']['edges']:
                                            if 'node' in edge:
                                                line_items.append(edge['node'])
                                        order['line_items'] = line_items
                                    
                                    # Add payment gateway names
                                    if 'paymentGatewayNames' in item:
                                        order['payment_gateway_names'] = item['paymentGatewayNames']
                                        
                                    # Add transactions if available
                                    if 'transactions' in item and item['transactions']:
                                        transactions = []
                                        for txn in item['transactions']:
                                            # Format the transaction to match our REST API format and database requirements
                                            transaction = {
                                                'id': int(txn['id'].split('/')[-1]) if 'id' in txn else None,
                                                'order_id': order['id'],
                                                'created_at': txn.get('createdAt'),
                                                'gateway': txn.get('gateway', txn.get('formattedGateway')),
                                                'status': txn.get('status'),
                                                'kind': txn.get('kind'),
                                                'test': txn.get('test', False),
                                                'processed_at': txn.get('processedAt'),
                                                'error_code': txn.get('errorCode'),
                                                'authorization': txn.get('authorizationCode'),
                                                'authorization_expires_at': txn.get('authorizationExpiresAt')
                                            }
                                            
                                            # Add amount information from amountSet
                                            if 'amountSet' in txn and txn['amountSet'] and 'shopMoney' in txn['amountSet']:
                                                shop_money = txn['amountSet']['shopMoney']
                                                transaction['amount'] = shop_money.get('amount', '0.00')
                                                transaction['currency'] = shop_money.get('currencyCode')
                                            
                                            # Add payment ID if available
                                            if 'paymentId' in txn and txn['paymentId']:
                                                transaction['payment_id'] = txn['paymentId']
                                                
                                            # Add additional financial fields if available
                                            if 'fees' in txn and txn['fees']:
                                                transaction['fees'] = txn['fees']
                                                
                                            if 'formattedGateway' in txn and txn['formattedGateway']:
                                                transaction['formatted_gateway'] = txn['formattedGateway']
                                                
                                            # Add payment details, excluding sensitive data
                                            if 'paymentDetails' in txn and txn['paymentDetails']:
                                                payment_details = txn['paymentDetails']
                                                transaction['payment_details'] = {
                                                    'avs_result_code': payment_details.get('avsResultCode'),
                                                    'cvv_result_code': payment_details.get('cvvResultCode'),
                                                    'credit_card_bin': payment_details.get('bin'),
                                                    'credit_card_company': payment_details.get('company'),
                                                    'credit_card_number': payment_details.get('number')
                                                }
                                            
                                            # Add receipt data if available
                                            if 'receiptJson' in txn and txn['receiptJson']:
                                                transaction['receipt'] = txn['receiptJson']
                                            
                                            transactions.append(transaction)
                                            
                                        order['transactions'] = transactions
                                    
                                    orders.append(order)
                            
                            logger.info(f"Retrieved {len(orders)} orders using bulk operations")
                        
                        if orders:
                            # Prepare batch for database insertion
                            order_batch = [(order, store.id) for order in orders]
                            
                            # Insert orders in batch
                            self.db_manager.upsert_orders_batch(order_batch)
                            total_orders = len(orders)
                            
                            # Extract transactions from order data
                            total_transactions = 0
                            transaction_batch = []
                            
                            # Process transactions that are now included in the order data
                            for order in orders:
                                if 'transactions' in order and order['transactions']:
                                    order_id = order['id']
                                    for transaction in order['transactions']:
                                        # Format the transaction properly for database storage
                                        transaction_batch.append((transaction, store.id, order_id))
                                        total_transactions += 1
                            
                            # Insert transactions in batch if any were found
                            if transaction_batch:
                                self.db_manager.upsert_transactions_batch(transaction_batch)
                                logger.info(f"Synchronized {total_transactions} transactions from order data")
                            else:
                                logger.warning(f"No transactions found in order data for {store.store_name}")
                                total_transactions = 0
                                
                            logger.info(f"Successfully processed {total_orders} orders with transactions using bulk operations")
                        else:
                            logger.info(f"No orders found for {store.store_name}")
                            total_orders = 0
                            total_transactions = 0
                            
                    except Exception as e:
                        logger.error(f"Bulk operations for orders failed: {e}, falling back to REST API")
                        # If orders exist from previous operation but something failed later, process them anyway
                        if 'orders' in locals() and orders:
                            # Process orders and any included transactions
                            order_batch = [(order, store.id) for order in orders]
                            
                            # Insert orders in batch
                            self.db_manager.upsert_orders_batch(order_batch)
                            total_orders = len(orders)
                            
                            # Process any transactions included in orders
                            transaction_batch = []
                            for order in orders:
                                if 'transactions' in order and order['transactions']:
                                    order_id = order['id']
                                    for transaction in order['transactions']:
                                        transaction_batch.append((transaction, store.id, order_id))
                                        total_transactions += 1
                            
                            # Insert transactions in batch if any were found
                            if transaction_batch:
                                self.db_manager.upsert_transactions_batch(transaction_batch)
                                logger.info(f"Synchronized {total_transactions} transactions from order data")
                            
                            logger.info(f"Successfully processed {total_orders} orders despite earlier failure")
                        else:
                            logger.warning(f"No orders found for {store.store_name} using any method")
                            total_orders = 0
                            total_transactions = 0
                    
                except Exception as e:
                    # Report error and mark sync as incomplete
                    logger.error(f"Failed to sync orders for {store.store_name}: {e}")
                    logger.error(f"Orders synchronization incomplete for {store.store_name}")
                    
                    # Reset counters - sync was unsuccessful
                    total_orders = 0
                    total_transactions = 0
                
                # Also sync disputes after orders and transactions are processed
                try:
                    dispute_count = await self.sync_store_disputes(store)
                except Exception as e:
                    logger.error(f"Failed to sync disputes for {store.store_name}: {e}")
                    dispute_count = 0
                    
                # Log the final sync results with timing information
                elapsed_time = time.time() - start_time
                logger.info(f"Completed full sync for {store.store_name}: {total_orders} orders, "
                           f"{total_transactions} transactions, {dispute_count} disputes in {elapsed_time:.2f}s "
                           f"({api.request_count} API calls)")
    
    async def sync_chunk_transactions(self, api: ShopifyAPI, store: ShopifyStore, orders: List[Dict]) -> int:
        """Sync transactions for a chunk of orders with optimized batching using bulk operations when possible
        
        This method follows a two-path approach:
        1. First attempts to use GraphQL bulk operations for efficient transaction retrieval
        2. Falls back to REST API if bulk operations fail or return no results
        
        The REST API path includes optimized batching and rate limit handling.
        """
        transaction_count = 0
        bulk_success = False
        
        # STEP 1: Try bulk operations first (preferred method)
        try:
            logger.info(f"Attempting to use bulk operations to fetch transactions for {store.store_name}")
            transactions_by_order = await api.get_transactions_batch_bulk()
            
            if transactions_by_order:
                # Bulk operation succeeded, prepare batch for database insertion
                transaction_batch = []
                for order_id, transactions in transactions_by_order.items():
                    for transaction in transactions:
                        transaction_batch.append((transaction, store.id, order_id))
                        transaction_count += 1
                
                # Insert transactions in batch
                if transaction_batch:
                    self.db_manager.upsert_transactions_batch(transaction_batch)
                    logger.info(f"Synchronized {transaction_count} transactions using bulk operations")
                    bulk_success = True
        except Exception as e:
            logger.error(f"Bulk operations failed, falling back to REST API: {e}")
            bulk_success = False
        
        # No fallback to REST API if bulk operations didn't work
        if not bulk_success:
            logger.error(f"Failed to sync transactions for {len(orders)} orders - no REST API fallback available")
            transaction_count = 0
        
        return transaction_count
    
    async def sync_store_disputes(self, store: ShopifyStore):
        """Synchronize disputes for a single store
        
        This method handles dispute synchronization with the following approaches:
        1. First checks if Shopify Payments is available for the store
        2. Attempts to use ShopifyPaymentsDisputes API if available
        3. Falls back to extracting disputes from orders if needed
        
        Dispute data is critical for chargeback management, so this method uses
        multiple strategies to ensure complete data retrieval.
        """
        logger.info(f"Starting dispute synchronization for store: {store.store_name} ({store.domain})")
        
        start_time = time.time()
        total_disputes = 0
        
        async with ShopifyAPI(store.domain, store.access_token) as api:
            # Even if store doesn't appear to have Shopify Payments enabled, still attempt to sync disputes
            # as they might be available through orders directly
            if api.has_shopify_payments is False:
                logger.warning(f"Store {store.store_name} does not appear to have Shopify Payments enabled")
                logger.warning(f"Full dispute information might be limited without Shopify Payments")
                logger.info(f"Will attempt to retrieve basic dispute information through orders")
                
            # Try to check if we have the required scope for disputes
            # but don't exit early if we don't - attempt to fetch disputes from orders
            disputes_api_available = True
            logger.info(f"Beginning comprehensive dispute retrieval for {store.store_name}")
            
            # Try multiple approaches to get disputes
            disputes = []
            
            # First, try ShopifyPaymentsDisputes API if available
            if disputes_api_available:
                try:
                    logger.info(f"Attempting to use GraphQL bulk operations for disputes from {store.store_name}")
                    disputes = await api.get_disputes_bulk()
                    
                    if disputes:
                        logger.info(f"Successfully retrieved {len(disputes)} disputes from Disputes API")
                    else:
                        logger.info(f"No disputes found via Disputes API for {store.store_name}")
                        
                except Exception as e:
                    logger.error(f"Failed to retrieve disputes from Disputes API: {e}")
                    disputes = []
            
            # We no longer need to extract disputes from orders as we get all disputes through the Disputes API
            
            # Process any disputes we found
            if disputes:
                # Prepare batch for database insertion
                dispute_batch = [(dispute, store.id) for dispute in disputes]
                
                # Insert disputes in batch
                self.db_manager.upsert_disputes_batch(dispute_batch)
                total_disputes = len(disputes)
                
                logger.info(f"Successfully synced {total_disputes} disputes for {store.store_name}")
            else:
                logger.info(f"No disputes found for {store.store_name}")
                total_disputes = 0
        
        elapsed_time = time.time() - start_time
        logger.info(f"Completed dispute sync for {store.store_name}: {total_disputes} disputes in {elapsed_time:.2f}s")
        
        return total_disputes
    
    def __del__(self):
        """Clean up database connections"""
        if hasattr(self, 'db_manager'):
            self.db_manager.close_all_connections()

async def main(store_id: Optional[str] = None, sync_disputes: bool = None):
    """Main function with performance monitoring"""
    if store_id:
        logger.info(f"Starting Shopify data synchronization for store ID: {store_id}")
        # Default to True for single store sync
        if sync_disputes is None:
            sync_disputes = True
    else:
        logger.info("Starting optimized Shopify data synchronization for all stores")
        # Default to False for all stores sync
        if sync_disputes is None:
            sync_disputes = False
    
    logger.info(f"Dispute sync: {'enabled' if sync_disputes else 'disabled'}")
    
    start_time = time.time()
    
    try:
        synchronizer = ShopifyDataSynchronizer()
        
        if store_id:
            await synchronizer.sync_store_by_id(store_id, sync_disputes=sync_disputes)
        else:
            await synchronizer.sync_all_stores(sync_disputes=sync_disputes)
        
        elapsed_time = time.time() - start_time
        logger.info(f"Shopify data synchronization completed successfully in {elapsed_time:.2f} seconds")
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'Synchronization completed successfully',
                'store_id': store_id,
                'sync_disputes': sync_disputes,
                'elapsed_time': elapsed_time
            })
        }
    except Exception as e:
        logger.error(f"Synchronization failed: {e}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': str(e),
                'store_id': store_id
            })
        }