import os
import json
import logging
import os
from typing import Dict, List, Optional, Any
import aiohttp
import asyncio

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
BACKEND_API_URL = os.getenv('BACKEND_API_URL', 'http://localhost:3000')
BACKEND_API_KEY = os.getenv('BACKEND_API_KEY', '')  # If authentication is needed

async def call_backend_api(method: str, endpoint: str, data: Dict = None) -> Dict:
    """Helper function to call backend API"""
    try:
        headers = {
            'Content-Type': 'application/json'
        }
        
        # Add authentication if token is provided
        if BACKEND_API_KEY:
            headers['Authorization'] = f'Bearer {BACKEND_API_KEY}'
        
        url = f"{BACKEND_API_URL}{endpoint}"
        
        async with aiohttp.ClientSession() as session:
            if method.upper() == 'GET':
                async with session.get(url, headers=headers) as response:
                    return await response.json()
            elif method.upper() == 'POST':
                async with session.post(url, headers=headers, json=data) as response:
                    return await response.json()
            elif method.upper() == 'PUT':
                async with session.put(url, headers=headers, json=data) as response:
                    return await response.json()
            elif method.upper() == 'DELETE':
                async with session.delete(url, headers=headers) as response:
                    return await response.json()
    except Exception as e:
        logger.error(f"Error calling backend API {method} {endpoint}: {str(e)}")
        return {
            'success': False,
            'status': 500,
            'message': f'API call failed: {str(e)}'
        }

def create_shopify_client(shop: str, access_token: str):
    """Create a Shopify REST client"""
    class ShopifyClient:
        def __init__(self, shop: str, access_token: str):
            self.shop = shop
            self.access_token = access_token
            self.base_url = f"https://{shop}/admin/api/{SHOPIFY_API_VERSION}"
            
        async def get(self, path: str) -> Dict:
            """Make GET request to Shopify API"""
            headers = {
                'X-Shopify-Access-Token': self.access_token,
                'Content-Type': 'application/json'
            }
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/{path}.json", headers=headers) as response:
                    return await response.json()
                    
        async def post(self, path: str, data: Dict) -> Dict:
            """Make POST request to Shopify API"""
            headers = {
                'X-Shopify-Access-Token': self.access_token,
                'Content-Type': 'application/json'
            }
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/{path}.json", headers=headers, json=data) as response:
                    return await response.json()
    
    return ShopifyClient(shop, access_token)

def sign_data(data: Dict, sign_key: str) -> str:
    """Create signature for API request"""
    # Sort keys and create string
    sorted_keys = sorted(data.keys())
    sign_string = '&'.join([f"{key}={data[key]}" for key in sorted_keys])
    
    # Create HMAC SHA256 signature
    signature = hmac.new(
        sign_key.encode('utf-8'),
        sign_string.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    return signature

async def get_store_by_block(block_id: str) -> Optional[str]:
    """
    Get store ID for a block by calling backend API
    """
    try:
        logger.info(f"Getting store for block {block_id}")
        
        # Call backend API to get store ID
        endpoint = f"/blocks/{block_id}/store"
        
        result = await call_backend_api('GET', endpoint)
        
        if result.get('success'):
            store_id = result.get('data', {}).get('storeId')
            logger.info(f"Found store ID {store_id} for block {block_id}")
            return store_id
        else:
            logger.warning(f"No store found for block {block_id}")
            return None
        
    except Exception as e:
        logger.error(f"Error getting store by block {block_id}: {str(e)}")
        return None


async def find_matched_order(block_id: str, store_id: str = None) -> Dict:
    """
    Find matched orders for a block by calling backend API
    """
    try:
        logger.info(f"Finding matched orders for block {block_id} and store {store_id}")
        
        # If store_id is not provided, we need to get it from the block
        if not store_id:
            # Get store ID from block using database manager
            store_id = await get_store_by_block(block_id)
            if not store_id:
                return {
                    'success': False,
                    'status': 404,
                    'message': 'Could not determine store for this block'
                }
        
        # Call backend API to find matched orders
        endpoint = f"/blocks/{block_id}/matched-orders/{store_id}"
        result = await call_backend_api('GET', endpoint)
        
        logger.info(f"Backend API response: {result.get('success', False)}")
        return result
        
    except Exception as error:
        logger.error(f"Error finding matched orders for block {block_id}: {str(error)}")
        return {
            'success': False,
            'status': 500,
            'message': f'Failed to find matched orders: {str(error)}',
            'data': None
        }
        




async def refund_order(block_id: str, store_id: str, order_id: str) -> Dict:
    """
    Create a full refund for an order by calling backend API
    """
    try:
        logger.info(f"Creating refund for order {order_id} in store {store_id}")
        
        # Call backend API to create refund
        endpoint = f"/shopify/stores/{store_id}/orders/{order_id}/refunds"
        refund_data = {
            'blockId': block_id,
            'reason': 'Automatic refund via chargeback system',
            'notify': True,
            'note': 'Automatic refund processed via chargeback system'
        }
        
        result = await call_backend_api('POST', endpoint, refund_data)
        
        logger.info(f"Backend refund API response: {result.get('success', False)}")
        return result
        
    except Exception as error:
        logger.error(f"Error creating refund for order {order_id}: {str(error)}")
        return {
            'success': False,
            'status': 500,
            'message': f'Failed to create refund: {str(error)}',
            'data': None
        }


async def send_block_feedback(block_id: str, feedback_data: Dict) -> Dict:
    """
    Send feedback for a block by calling backend API
    """
    try:
        logger.info(f"Sending feedback for block {block_id}")
        
        # Call backend API to send feedback
        endpoint = f"/blocks/feedback/{block_id}"
        
        result = await call_backend_api('POST', endpoint, feedback_data)
        
        logger.info(f"Backend feedback API response: {result.get('success', False)}")
        return result
        
    except Exception as error:
        logger.error(f"Error sending feedback for block {block_id}: {str(error)}")
        return {
            'success': False,
            'status': 500,
            'message': f'Failed to send feedback: {str(error)}',
            'data': None
        }
        


# Main function for testing
async def main():
    """Test the functions"""
    # Example usage
    block_id = "test-block-123"
    store_id = "test-store-456"
    order_id = "test-order-789"
    
    # Test find matched order
    result = await find_matched_order(block_id, store_id)
    print("Find matched order result:", result)
    
    # Test refund order
    refund_result = await refund_order(block_id, store_id, order_id)
    print("Refund order result:", refund_result)
    
    # Test send feedback
    feedback_data = {
        'outcome': 'REFUNDED',
        'refunded': 'true',
        'comments': 'Test feedback',
        'refundAmount': '100.00',
        'refundCurrency': 'USD'
    }
    feedback_result = await send_block_feedback(block_id, feedback_data)
    print("Send feedback result:", feedback_result)

async def auto_process_block(block_id: str, store_id: str = None) -> Dict:
    """
    Automatically process a block through the entire workflow by calling backend API
    """
    try:
        logger.info(f"Starting automatic processing for block {block_id}")
        
        # Call backend API for auto processing
        endpoint = f"/blocks/{block_id}/auto-process"
        process_data = {}
        if store_id:
            process_data['storeId'] = store_id
        
        result = await call_backend_api('POST', endpoint, process_data)
        
        logger.info(f"Backend auto-process API response: {result.get('success', False)}")
        return result
        
    except Exception as error:
        logger.error(f"Error auto-processing block {block_id}: {str(error)}")
        return {
            'success': False,
            'status': 500,
            'message': f'Failed to auto-process block: {str(error)}',
            'data': {
                'blockId': block_id,
                'storeId': store_id or 'auto-detect-failed',
                'error': str(error)
            }
        }




if __name__ == "__main__":
    asyncio.run(main())