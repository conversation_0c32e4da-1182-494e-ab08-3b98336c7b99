#!/usr/bin/env python3
"""
Stripe OAuth Token Management
Handles OAuth token refresh and validation for Stripe connected accounts
"""

import os
import json
import logging
import requests
from typing import Dict, Optional, Tuple
from datetime import datetime, timezone
from dotenv import load_dotenv
from .db import DatabaseManager, StripeStore

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)


class RefreshTokenError(Exception):
    """Base exception for token refresh errors"""
    pass


class RefreshTokenExpiredError(RefreshTokenError):
    """Exception raised when refresh token is expired or invalid"""
    pass


class StripeOAuthTokens:
    """Data class for Stripe OAuth tokens"""
    
    def __init__(self, access_token: str, refresh_token: str, stripe_user_id: str,
                 livemode: bool = False, scope: str = None, 
                 stripe_publishable_key: str = None, token_type: str = "bearer"):
        self.access_token = access_token
        self.refresh_token = refresh_token
        self.stripe_user_id = stripe_user_id
        self.livemode = livemode
        self.scope = scope
        self.stripe_publishable_key = stripe_publishable_key
        self.token_type = token_type

    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        return {
            'accessToken': self.access_token,
            'refreshToken': self.refresh_token,
            'stripeUserId': self.stripe_user_id,
            'liveMode': self.livemode,
            'scope': self.scope,
            'stripePublishableKey': self.stripe_publishable_key,
            'tokenType': self.token_type,
            'lastRefreshedAt': datetime.now(timezone.utc).isoformat()
        }


class StripeOAuthManager:
    """Stripe OAuth token management"""
    
    def __init__(self):
        self.stripe_secret_key = os.getenv('STRIPE_APP_SECRET_KEY')
        if not self.stripe_secret_key:
            raise ValueError("STRIPE_APP_SECRET_KEY environment variable is required")
        
        self.db_manager = DatabaseManager()
        self.oauth_token_url = "https://api.stripe.com/v1/oauth/token"
        
        # Configurable timeouts
        self.oauth_timeout = int(os.getenv('OAUTH_REQUEST_TIMEOUT', 30))
        self.validation_timeout = int(os.getenv('TOKEN_VALIDATION_TIMEOUT', 15))
    
    def refresh_access_token(self, refresh_token: str) -> StripeOAuthTokens:
        """
        Refresh expired access token using refresh token
        Based on the curl command: 
        curl -X POST https://api.stripe.com/v1/oauth/token \
          -u sk_test_...: \
          -d refresh_token=rt_... \
          -d grant_type=refresh_token
        """
        try:
            data = {
                'refresh_token': refresh_token,
                'grant_type': 'refresh_token'
            }
            
            response = requests.post(
                self.oauth_token_url,
                auth=(self.stripe_secret_key, ''),  # Basic auth with empty password
                data=data,
                headers={
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                timeout=self.oauth_timeout
            )
            
            if response.status_code != 200:
                error_data = {}
                try:
                    error_data = response.json()
                except:
                    error_data = {'error': 'unknown', 'error_description': response.text}
                
                error_type = error_data.get('error', 'unknown')
                error_desc = error_data.get('error_description', 'No description')
                
                if error_type == 'invalid_grant':
                    logger.error(f"Refresh token invalid or expired: {error_desc}")
                    raise RefreshTokenExpiredError(f"Refresh token is invalid or expired: {error_desc}")
                else:
                    logger.error(f"Stripe OAuth token refresh failed ({error_type}): {error_desc}")
                    raise RefreshTokenError(f"Token refresh failed: {error_type} - {error_desc}")
            
            token_data = response.json()
            
            # Validate required fields
            required_fields = ['access_token', 'refresh_token', 'stripe_user_id']
            for field in required_fields:
                if not token_data.get(field):
                    raise ValueError(f"Invalid token refresh response: missing {field}")
            
            logger.info(f"Successfully refreshed token for Stripe account: {token_data['stripe_user_id']}")
            
            return StripeOAuthTokens(
                access_token=token_data['access_token'],
                refresh_token=token_data['refresh_token'],
                stripe_user_id=token_data['stripe_user_id'],
                livemode=token_data.get('livemode', False),
                scope=token_data.get('scope'),
                stripe_publishable_key=token_data.get('stripe_publishable_key'),
                token_type=token_data.get('token_type', 'bearer')
            )
            
        except (RefreshTokenExpiredError, RefreshTokenError):
            # Re-raise our custom exceptions
            raise
        except requests.RequestException as e:
            logger.error(f"HTTP error during token refresh: {e}")
            raise RefreshTokenError(f"Network error during token refresh: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error refreshing access token: {e}")
            raise RefreshTokenError(f"Unexpected error during token refresh: {str(e)}")
    
    def validate_access_token(self, access_token: str, stripe_user_id: str) -> bool:
        """
        Validate if access token is still valid by making a simple API call
        """
        try:
            # Test token by retrieving the account information
            response = requests.get(
                f"https://api.stripe.com/v1/accounts/{stripe_user_id}",
                headers={
                    'Authorization': f'Bearer {access_token}',
                    'Content-Type': 'application/json'
                },
                timeout=self.validation_timeout
            )
            
            if response.status_code == 200:
                logger.debug(f"Access token valid for Stripe account: {stripe_user_id}")
                return True
            elif response.status_code == 401:
                logger.info(f"Access token expired for Stripe account: {stripe_user_id}")
                return False
            else:
                logger.warning(f"Unexpected response validating token: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error validating access token: {e}")
            return False
    
    def _refresh_linked_store_tokens(self, store_id: str) -> StripeStore:
        """
        Internal method to refresh tokens for a linked store and update store information
        """
        try:
            # Get the linked store from database
            conn = self.db_manager.get_connection()
            try:
                with conn.cursor() as cur:
                    cur.execute(f"""
                        SELECT id, user_id, store_name, data, provider_store_id
                        FROM {self.db_manager.schema}.linked_stores 
                        WHERE id = %s AND provider = 'stripe'
                    """, (store_id,))
                    
                    row = cur.fetchone()
                    if not row:
                        raise ValueError(f"Stripe linked store not found: {store_id}")
                    
                    store_data = row['data']
                    refresh_token = store_data.get('refreshToken')
                    
                    if not refresh_token:
                        raise ValueError(f"No refresh token available for store {store_id}")
                    
                    logger.info(f"Refreshing tokens for Stripe store: {store_id}")
                    
                    # Refresh the tokens
                    new_tokens = self.refresh_access_token(refresh_token)
                    
                    # Get updated account details with new access token
                    account_details = self._get_account_details(new_tokens.access_token, new_tokens.stripe_user_id)
                    
                    # Update store data with new tokens and account info
                    updated_store_data = {
                        **store_data,
                        'accessToken': new_tokens.access_token,
                        'refreshToken': new_tokens.refresh_token,
                        'lastRefreshedAt': datetime.now(timezone.utc).isoformat(),
                        'liveMode': new_tokens.livemode,
                        'scope': new_tokens.scope,
                        'stripePublishableKey': new_tokens.stripe_publishable_key,
                        'tokenType': new_tokens.token_type
                    }
                    
                    # Update account details if retrieved successfully
                    if account_details:
                        updated_store_data['accountDetails'] = account_details
                        # Update store name if account has business name
                        new_store_name = self._get_store_name(account_details)
                        if new_store_name:
                            updated_store_data['storeName'] = new_store_name
                    
                    # Update the linked store in database using the new method
                    self.db_manager.update_linked_store_data(
                        store_id,
                        updated_store_data,
                        updated_store_data.get('storeName', row['store_name'])
                    )
                    
                    logger.info(f"Successfully refreshed tokens and updated store: {store_id}")
                    
                    # Return updated StripeStore object
                    return StripeStore(
                        id=row['id'],
                        account_id=new_tokens.stripe_user_id,
                        access_token=new_tokens.access_token,
                        user_id=row['user_id'],
                        store_name=updated_store_data.get('storeName', row['store_name'])
                    )
                    
            finally:
                self.db_manager.put_connection(conn)
                
        except Exception as e:
            logger.error(f"Error refreshing tokens for store {store_id}: {e}")
            raise
    
    def ensure_valid_access_token_for_sync(self, store_id: str) -> str:
        """
        Internal method for sync operations: Check if access token is valid and refresh if needed.
        Returns a valid access token or raises an exception if token refresh fails.
        """
        try:
            # Get current store data
            store = self.db_manager.get_stripe_store_by_id(store_id)
            if not store:
                raise ValueError(f"Stripe store not found: {store_id}")
            
            current_token = store.access_token
            
            # Test if current token is valid
            if self.validate_access_token(current_token, store.account_id):
                logger.debug(f"Access token valid for store: {store_id}")
                return current_token
            
            # Token is invalid, refresh it
            logger.info(f"Access token expired for store {store_id}, refreshing...")
            updated_store = self._refresh_linked_store_tokens(store_id)
            
            return updated_store.access_token
            
        except RefreshTokenExpiredError as e:
            logger.error(f"Refresh token expired for store {store_id}: {e}")
            # Mark store as inactive due to invalid refresh token
            self._mark_store_inactive(store_id, str(e))
            raise
        except RefreshTokenError as e:
            logger.error(f"Token refresh failed for store {store_id}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error ensuring valid access token for store {store_id}: {e}")
            raise
    
    def _get_account_details(self, access_token: str, stripe_user_id: str) -> Optional[Dict]:
        """
        Get updated account details using the new access token
        """
        try:
            response = requests.get(
                f"https://api.stripe.com/v1/accounts/{stripe_user_id}",
                headers={
                    'Authorization': f'Bearer {access_token}',
                    'Content-Type': 'application/json'
                },
                timeout=self.validation_timeout
            )
            
            if response.status_code == 200:
                account_data = response.json()
                
                # Extract relevant account information
                return {
                    'id': account_data.get('id'),
                    'business_type': account_data.get('business_type'),
                    'country': account_data.get('country'),
                    'default_currency': account_data.get('default_currency'),
                    'email': account_data.get('email'),
                    'type': account_data.get('type'),
                    'business_profile': account_data.get('business_profile'),
                    'capabilities': account_data.get('capabilities'),
                    'charges_enabled': account_data.get('charges_enabled'),
                    'payouts_enabled': account_data.get('payouts_enabled'),
                }
            else:
                logger.warning(f"Failed to get account details: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting account details: {e}")
            return None
    
    def _mark_store_inactive(self, store_id: str, error_message: str):
        """
        Mark store as inactive when refresh token is expired
        """
        try:
            conn = self.db_manager.get_connection()
            try:
                with conn.cursor() as cur:
                    cur.execute(f"""
                        UPDATE {self.db_manager.schema}.linked_stores 
                        SET is_active = false, 
                            updated_at = NOW()
                        WHERE id = %s AND provider = 'stripe'
                    """, (store_id,))
                    
                    conn.commit()
                    logger.warning(f"Marked store {store_id} as inactive due to invalid refresh token")
                    
            finally:
                self.db_manager.put_connection(conn)
        except Exception as e:
            logger.error(f"Failed to mark store {store_id} as inactive: {e}")

    def _get_store_name(self, account_details: Dict) -> Optional[str]:
        """
        Generate a display name for the Stripe account
        """
        if not account_details:
            return None
        
        # Try business name first
        business_profile = account_details.get('business_profile', {})
        if business_profile and business_profile.get('name'):
            return business_profile['name']
        
        # Try email
        if account_details.get('email'):
            return account_details['email']
        
        # Fallback to account ID
        if account_details.get('id'):
            return f"Stripe Account {account_details['id']}"
        
        return None