"""
Background scheduler for automatic Stripe data synchronization
"""

import logging
import os
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from datetime import datetime
from .stripe_api import StripeDataSync

logger = logging.getLogger(__name__)

class SyncScheduler:
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
        
    async def sync_stripe_data(self):
        """Automated sync function that runs periodically"""
        try:
            start_time = datetime.now()
            logger.info(f"🔄 Starting scheduled Stripe sync at {start_time.strftime('%H:%M:%S')}")
            
            # Create sync service instance
            sync_service = StripeDataSync()
            
            try:
                # Get all stores info for logging
                stores = sync_service.db_manager.get_linked_stripe_stores()
                if not stores:
                    logger.warning("⚠️  No Stripe stores found to sync")
                    return
                
                # Run incremental sync for all stores
                await sync_service.sync_incremental(1)
                
                duration = (datetime.now() - start_time).total_seconds()
                logger.info(f"✅ Scheduled sync completed in {duration:.2f}s:")
                logger.info(f"   🏪 {len(stores)} stores synced")
                
            except Exception as e:
                logger.error(f"❌ Scheduled sync failed: {str(e)}")
                raise
            finally:
                sync_service.cleanup()
                
        except Exception as e:
            logger.error(f"💥 Error in scheduled sync: {str(e)}")
    
    def start_scheduler(self):
        """Start the background scheduler"""
        if not self.is_running:
            # Get sync interval from environment
            sync_interval_minutes = int(os.getenv("SYNC_INTERVAL_MINUTES", "1"))
            
            if sync_interval_minutes <= 0:
                logger.info("🔒 Scheduled sync disabled (SYNC_INTERVAL_MINUTES=0)")
                return
            
            # Add job to run at specified interval
            self.scheduler.add_job(
                self.sync_stripe_data,
                trigger=IntervalTrigger(minutes=sync_interval_minutes),
                id='stripe_sync_job',
                name='Stripe Data Synchronization',
                replace_existing=True
            )
            
            self.scheduler.start()
            self.is_running = True
            
            # Get next run time
            jobs = self.scheduler.get_jobs()
            next_run = jobs[0].next_run_time.strftime('%H:%M:%S') if jobs else "Unknown"
            logger.info(f"⏰ Scheduler started - running every {sync_interval_minutes} minute(s)")
            logger.info(f"🎯 Next sync at {next_run}")
    
    def stop_scheduler(self):
        """Stop the background scheduler"""
        if self.is_running:
            self.scheduler.shutdown()
            self.is_running = False
            logger.info("🛑 Scheduler stopped")
    
    def is_scheduler_running(self):
        """Check if scheduler is running"""
        return self.is_running