#!/usr/bin/env python3
"""
Database Manager for Stripe PostgreSQL operations
Handles all database connections and queries for the Stripe sync server
"""

import os
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timezone
from dateutil import parser
from psycopg2.extras import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, execute_batch
from psycopg2.pool import SimpleConnectionPool
from urllib.parse import urlparse, parse_qs
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class StripeStore:
    """Stripe store configuration"""
    id: str
    account_id: str
    access_token: str
    user_id: str
    store_name: str

class DatabaseManager:
    """PostgreSQL database manager for Stripe data"""
    
    def __init__(self):
        self.database_url = os.getenv('DATABASE_URL')
        if not self.database_url:
            raise ValueError("DATABASE_URL environment variable is required")
        
        # Parse the database URL and extract schema parameter
        parsed = urlparse(self.database_url)
        query_params = parse_qs(parsed.query)
        self.schema = query_params.get('schema', ['public'])[0]
        
        # Remove schema from query parameters for psycopg2
        clean_params = {k: v for k, v in query_params.items() if k != 'schema'}
        clean_query = '&'.join([f"{k}={v[0]}" for k, v in clean_params.items()])
        
        # Reconstruct URL without schema parameter
        self.clean_database_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
        if clean_query:
            self.clean_database_url += f"?{clean_query}"
        
        # Create connection pool
        self.pool = SimpleConnectionPool(
            2, 10,  # min 2, max 10 connections
            self.clean_database_url,
            cursor_factory=RealDictCursor
        )
        
        # Prepare batch size
        self.batch_size = 100
    
    def get_connection(self):
        """Get connection from pool"""
        return self.pool.getconn()
    
    def put_connection(self, conn):
        """Return connection to pool"""
        self.pool.putconn(conn)
    
    def close_all_connections(self):
        """Close all connections in the pool"""
        self.pool.closeall()
    
    def get_linked_stripe_stores(self) -> List[StripeStore]:
        """Get all active linked Stripe stores"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT id, user_id, store_name, data 
                    FROM {self.schema}.linked_stores 
                    WHERE provider = 'stripe' AND is_active = true
                """)
                
                stores = []
                for row in cur.fetchall():
                    data = row['data']
                    # Check for both possible field names in JSON data
                    stripe_account_id = data.get('stripeUserId') or data.get('stripe_account_id')
                    access_token = data.get('accessToken') or data.get('access_token')
                    
                    if stripe_account_id and access_token:
                        stores.append(StripeStore(
                            id=row['id'],
                            account_id=stripe_account_id,
                            access_token=access_token,
                            user_id=row['user_id'],
                            store_name=row['store_name']
                        ))
                
                logger.info(f"Found {len(stores)} active Stripe stores for sync")
                return stores
        finally:
            self.put_connection(conn)
    
    def get_stripe_store_by_id(self, store_id: str) -> Optional[StripeStore]:
        """Get a specific Stripe store by ID"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT id, user_id, store_name, data 
                    FROM {self.schema}.linked_stores 
                    WHERE provider = 'stripe' AND id = %s
                """, (store_id,))
                
                row = cur.fetchone()
                if row:
                    data = row['data']
                    # Check for both possible field names in JSON data
                    stripe_account_id = data.get('stripeUserId') or data.get('stripe_account_id')
                    access_token = data.get('accessToken') or data.get('access_token')
                    
                    if stripe_account_id and access_token:
                        return StripeStore(
                            id=row['id'],
                            account_id=stripe_account_id,
                            access_token=access_token,
                            user_id=row['user_id'],
                            store_name=row['store_name']
                        )
                
                return None
        finally:
            self.put_connection(conn)
    
    async def get_linked_store(self, store_id: str) -> Optional[Dict]:
        """Get linked store data from database"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                cur.execute(f"""
                    SELECT * FROM {self.schema}.linked_stores WHERE id = %s
                """, (store_id,))
                return cur.fetchone()
        finally:
            self.put_connection(conn)
    
    def update_linked_store_data(self, store_id: str, updated_data: Dict, store_name: str = None):
        """Update linked store data in database"""
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                if store_name:
                    cur.execute(f"""
                        UPDATE {self.schema}.linked_stores 
                        SET data = %s, store_name = %s, is_active = true, updated_at = NOW()
                        WHERE id = %s AND provider = 'stripe'
                    """, (Json(updated_data), store_name, store_id))
                else:
                    cur.execute(f"""
                        UPDATE {self.schema}.linked_stores 
                        SET data = %s, is_active = true, updated_at = NOW()
                        WHERE id = %s AND provider = 'stripe'
                    """, (Json(updated_data), store_id))
                
                conn.commit()
                affected_rows = cur.rowcount
                
                if affected_rows == 0:
                    raise ValueError(f"Stripe linked store not found: {store_id}")
                
                logger.info(f"Updated linked store data for store: {store_id}")
                
        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to update linked store {store_id}: {e}")
            raise
        finally:
            self.put_connection(conn)
    
    def upsert_payment_intents_batch(self, payment_intents: List[Tuple[Dict, str]]):
        """Batch upsert Stripe PaymentIntents for better performance"""
        if not payment_intents:
            return
        
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                # Prepare data for batch insert
                values = [self._format_payment_intent_data(pi, linked_store_id) 
                         for pi, linked_store_id in payment_intents]
                
                query = f"""
                    INSERT INTO {self.schema}.stripe_payment_intents (
                        id, linked_store_id, object, amount, amount_capturable, amount_received,
                        application, application_fee_amount, automatic_payment_methods,
                        canceled_at, cancellation_reason, capture_method, client_secret,
                        confirmation_method, created_at, currency, customer_id, description,
                        invoice_id, last_payment_error, latest_charge_id, livemode, metadata,
                        next_action, on_behalf_of, payment_method_id,
                        payment_method_configuration_details, payment_method_options,
                        payment_method_types, processing, receipt_email, review,
                        setup_future_usage, shipping, source, statement_descriptor,
                        statement_descriptor_suffix, status, transfer_data, transfer_group,
                        updated_at
                    ) VALUES (
                        %(id)s, %(linked_store_id)s, %(object)s, %(amount)s, %(amount_capturable)s,
                        %(amount_received)s, %(application)s, %(application_fee_amount)s,
                        %(automatic_payment_methods)s, %(canceled_at)s, %(cancellation_reason)s,
                        %(capture_method)s, %(client_secret)s, %(confirmation_method)s,
                        %(created_at)s, %(currency)s, %(customer_id)s, %(description)s,
                        %(invoice_id)s, %(last_payment_error)s, %(latest_charge_id)s,
                        %(livemode)s, %(metadata)s, %(next_action)s, %(on_behalf_of)s,
                        %(payment_method_id)s, %(payment_method_configuration_details)s,
                        %(payment_method_options)s, %(payment_method_types)s, %(processing)s,
                        %(receipt_email)s, %(review)s, %(setup_future_usage)s, %(shipping)s,
                        %(source)s, %(statement_descriptor)s, %(statement_descriptor_suffix)s,
                        %(status)s, %(transfer_data)s, %(transfer_group)s, %(updated_at)s
                    )
                    ON CONFLICT (id) DO UPDATE SET
                        updated_at = EXCLUDED.updated_at,
                        status = EXCLUDED.status,
                        amount_capturable = EXCLUDED.amount_capturable,
                        amount_received = EXCLUDED.amount_received,
                        canceled_at = EXCLUDED.canceled_at,
                        cancellation_reason = EXCLUDED.cancellation_reason,
                        latest_charge_id = EXCLUDED.latest_charge_id,
                        last_payment_error = EXCLUDED.last_payment_error,
                        next_action = EXCLUDED.next_action
                """
                
                execute_batch(cur, query, values, page_size=self.batch_size)
                conn.commit()
                logger.info(f"Batch upserted {len(values)} Stripe PaymentIntents")
                
        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to batch upsert PaymentIntents: {e}")
            raise
        finally:
            self.put_connection(conn)
    
    def upsert_charges_batch(self, charges: List[Tuple[Dict, str]]):
        """Batch upsert Stripe Charges for better performance"""
        if not charges:
            return
        
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                # Prepare data for batch insert
                values = [self._format_charge_data(charge, linked_store_id) 
                         for charge, linked_store_id in charges]
                
                query = f"""
                    INSERT INTO {self.schema}.stripe_charges (
                        id, linked_store_id, object, amount, amount_captured, amount_refunded,
                        application, application_fee, application_fee_amount, balance_transaction,
                        billing_details, calculated_statement_descriptor, captured, created_at,
                        currency, customer_id, description, destination, dispute_id, disputed,
                        failure_code, failure_message, fraud_details, invoice_id, livemode,
                        metadata, on_behalf_of, order_id, outcome, paid, payment_intent_id,
                        payment_method, payment_method_details, receipt_email, receipt_number,
                        receipt_url, refunded, refunds, review, shipping, source,
                        source_transfer, statement_descriptor, statement_descriptor_suffix,
                        status, transfer_data, transfer_group, reference_card_number,
                        reference_created_at, reference_amount, reference_currency, updated_at
                    ) VALUES (
                        %(id)s, %(linked_store_id)s, %(object)s, %(amount)s, %(amount_captured)s,
                        %(amount_refunded)s, %(application)s, %(application_fee)s,
                        %(application_fee_amount)s, %(balance_transaction)s, %(billing_details)s,
                        %(calculated_statement_descriptor)s, %(captured)s, %(created_at)s,
                        %(currency)s, %(customer_id)s, %(description)s, %(destination)s,
                        %(dispute_id)s, %(disputed)s, %(failure_code)s, %(failure_message)s,
                        %(fraud_details)s, %(invoice_id)s, %(livemode)s, %(metadata)s,
                        %(on_behalf_of)s, %(order_id)s, %(outcome)s, %(paid)s,
                        %(payment_intent_id)s, %(payment_method)s, %(payment_method_details)s,
                        %(receipt_email)s, %(receipt_number)s, %(receipt_url)s, %(refunded)s,
                        %(refunds)s, %(review)s, %(shipping)s, %(source)s, %(source_transfer)s,
                        %(statement_descriptor)s, %(statement_descriptor_suffix)s, %(status)s,
                        %(transfer_data)s, %(transfer_group)s, %(reference_card_number)s,
                        %(reference_created_at)s, %(reference_amount)s, %(reference_currency)s,
                        %(updated_at)s
                    )
                    ON CONFLICT (id) DO UPDATE SET
                        updated_at = EXCLUDED.updated_at,
                        status = EXCLUDED.status,
                        amount_captured = EXCLUDED.amount_captured,
                        amount_refunded = EXCLUDED.amount_refunded,
                        captured = EXCLUDED.captured,
                        disputed = EXCLUDED.disputed,
                        refunded = EXCLUDED.refunded,
                        dispute_id = EXCLUDED.dispute_id,
                        refunds = EXCLUDED.refunds,
                        reference_card_number = EXCLUDED.reference_card_number,
                        reference_created_at = EXCLUDED.reference_created_at,
                        reference_amount = EXCLUDED.reference_amount,
                        reference_currency = EXCLUDED.reference_currency
                """
                
                execute_batch(cur, query, values, page_size=self.batch_size)
                conn.commit()
                logger.info(f"Batch upserted {len(values)} Stripe Charges")
                
        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to batch upsert Charges: {e}")
            raise
        finally:
            self.put_connection(conn)
    
    def upsert_disputes_batch(self, disputes: List[Tuple[Dict, str]]):
        """Batch upsert Stripe Disputes for better performance"""
        if not disputes:
            return
        
        conn = self.get_connection()
        try:
            with conn.cursor() as cur:
                # Prepare data for batch insert
                values = [self._format_dispute_data(dispute, linked_store_id) 
                         for dispute, linked_store_id in disputes]
                
                query = f"""
                    INSERT INTO {self.schema}.stripe_disputes (
                        id, linked_store_id, object, amount, balance_transactions, charge_id,
                        created_at, currency, evidence, evidence_details,
                        is_charge_refundable, livemode, metadata, network_reason_code,
                        reason, status, updated_at
                    ) VALUES (
                        %(id)s, %(linked_store_id)s, %(object)s, %(amount)s,
                        %(balance_transactions)s, %(charge_id)s, %(created_at)s, %(currency)s,
                        %(evidence)s, %(evidence_details)s, %(is_charge_refundable)s,
                        %(livemode)s, %(metadata)s, %(network_reason_code)s, %(reason)s,
                        %(status)s, %(updated_at)s
                    )
                    ON CONFLICT (id) DO UPDATE SET
                        updated_at = EXCLUDED.updated_at,
                        status = EXCLUDED.status,
                        evidence = EXCLUDED.evidence,
                        evidence_details = EXCLUDED.evidence_details,
                        is_charge_refundable = EXCLUDED.is_charge_refundable,
                        balance_transactions = EXCLUDED.balance_transactions
                """
                
                execute_batch(cur, query, values, page_size=self.batch_size)
                conn.commit()
                logger.info(f"Batch upserted {len(values)} Stripe Disputes")
                
        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to batch upsert Disputes: {e}")
            raise
        finally:
            self.put_connection(conn)
    
    def _format_payment_intent_data(self, payment_intent: Dict, linked_store_id: str) -> Dict:
        """Format Stripe PaymentIntent data for database insertion"""
        def safe_timestamptz(value):
            """Convert a datetime timestamp to datetime object (UTC)."""
            if not value:
                return None
            if isinstance(value, int):
                return datetime.fromtimestamp(value, tz=timezone.utc)
            elif isinstance(value, str):
                dt = parser.parse(value)
                if dt.tzinfo is None:
                    dt = dt.replace(tzinfo=timezone.utc)
                return dt.astimezone(timezone.utc)
            return value
        
        def safe_json(value):
            return Json(value) if value is not None else None
        
        now = datetime.now(timezone.utc)
        
        return {
            'id': payment_intent.get('id'),
            'linked_store_id': linked_store_id,
            'object': payment_intent.get('object', 'payment_intent'),
            'amount': payment_intent.get('amount'),
            'amount_capturable': payment_intent.get('amount_capturable'),
            'amount_received': payment_intent.get('amount_received'),
            'application': payment_intent.get('application'),
            'application_fee_amount': payment_intent.get('application_fee_amount'),
            'automatic_payment_methods': safe_json(payment_intent.get('automatic_payment_methods')),
            'canceled_at': safe_timestamptz(payment_intent.get('canceled_at')),
            'cancellation_reason': payment_intent.get('cancellation_reason'),
            'capture_method': payment_intent.get('capture_method'),
            'client_secret': payment_intent.get('client_secret'),
            'confirmation_method': payment_intent.get('confirmation_method'),
            'created_at': safe_timestamptz(payment_intent.get('created')),
            'currency': payment_intent.get('currency'),
            'customer_id': payment_intent.get('customer'),
            'description': payment_intent.get('description'),
            'invoice_id': payment_intent.get('invoice'),
            'last_payment_error': safe_json(payment_intent.get('last_payment_error')),
            'latest_charge_id': payment_intent.get('latest_charge'),
            'livemode': payment_intent.get('livemode', False),
            'metadata': safe_json(payment_intent.get('metadata')),
            'next_action': safe_json(payment_intent.get('next_action')),
            'on_behalf_of': payment_intent.get('on_behalf_of'),
            'payment_method_id': payment_intent.get('payment_method'),
            'payment_method_configuration_details': safe_json(payment_intent.get('payment_method_configuration_details')),
            'payment_method_options': safe_json(payment_intent.get('payment_method_options')),
            'payment_method_types': payment_intent.get('payment_method_types', []),
            'processing': safe_json(payment_intent.get('processing')),
            'receipt_email': payment_intent.get('receipt_email'),
            'review': payment_intent.get('review'),
            'setup_future_usage': payment_intent.get('setup_future_usage'),
            'shipping': safe_json(payment_intent.get('shipping')),
            'source': payment_intent.get('source'),
            'statement_descriptor': payment_intent.get('statement_descriptor'),
            'statement_descriptor_suffix': payment_intent.get('statement_descriptor_suffix'),
            'status': payment_intent.get('status'),
            'transfer_data': safe_json(payment_intent.get('transfer_data')),
            'transfer_group': payment_intent.get('transfer_group'),
            'updated_at': now
        }
    
    def _format_charge_data(self, charge: Dict, linked_store_id: str) -> Dict:
        """Format Stripe Charge data for database insertion"""
        def safe_timestamptz(value):
            """Convert a datetime timestamp to datetime object (UTC)."""
            if not value:
                return None
            if isinstance(value, int):
                return datetime.fromtimestamp(value, tz=timezone.utc)
            elif isinstance(value, str):
                dt = parser.parse(value)
                if dt.tzinfo is None:
                    dt = dt.replace(tzinfo=timezone.utc)
                return dt.astimezone(timezone.utc)
            return value
        
        def safe_json(value):
            return Json(value) if value is not None else None
        
        now = datetime.now(timezone.utc)
        
        return {
            'id': charge.get('id'),
            'linked_store_id': linked_store_id,
            'object': charge.get('object', 'charge'),
            'amount': charge.get('amount'),
            'amount_captured': charge.get('amount_captured'),
            'amount_refunded': charge.get('amount_refunded'),
            'application': charge.get('application'),
            'application_fee': charge.get('application_fee'),
            'application_fee_amount': charge.get('application_fee_amount'),
            'balance_transaction': charge.get('balance_transaction'),
            'billing_details': safe_json(charge.get('billing_details')),
            'calculated_statement_descriptor': charge.get('calculated_statement_descriptor'),
            'captured': charge.get('captured', False),
            'created_at': safe_timestamptz(charge.get('created')),
            'currency': charge.get('currency'),
            'customer_id': charge.get('customer'),
            'description': charge.get('description'),
            'destination': charge.get('destination'),
            'dispute_id': charge.get('dispute'),
            'disputed': charge.get('disputed', False),
            'failure_code': charge.get('failure_code'),
            'failure_message': charge.get('failure_message'),
            'fraud_details': safe_json(charge.get('fraud_details')),
            'invoice_id': charge.get('invoice'),
            'livemode': charge.get('livemode', False),
            'metadata': safe_json(charge.get('metadata')),
            'on_behalf_of': charge.get('on_behalf_of'),
            'order_id': charge.get('order'),
            'outcome': safe_json(charge.get('outcome')),
            'paid': charge.get('paid', False),
            'payment_intent_id': charge.get('payment_intent'),
            'payment_method': charge.get('payment_method'),
            'payment_method_details': safe_json(charge.get('payment_method_details')),
            'receipt_email': charge.get('receipt_email'),
            'receipt_number': charge.get('receipt_number'),
            'receipt_url': charge.get('receipt_url'),
            'refunded': charge.get('refunded', False),
            'refunds': safe_json(charge.get('refunds')),
            'review': charge.get('review'),
            'shipping': safe_json(charge.get('shipping')),
            'source': safe_json(charge.get('source')),
            'source_transfer': charge.get('source_transfer'),
            'statement_descriptor': charge.get('statement_descriptor'),
            'statement_descriptor_suffix': charge.get('statement_descriptor_suffix'),
            'status': charge.get('status'),
            'transfer_data': safe_json(charge.get('transfer_data')),
            'transfer_group': charge.get('transfer_group'),
            'reference_card_number': f"•••• •••• •••• {charge.get('payment_method_details', {}).get('card', {}).get('last4')}" if charge.get('payment_method_details', {}).get('card', {}).get('last4') else None,
            'reference_created_at': safe_timestamptz(charge.get('created')),
            'reference_amount': charge.get('amount'),
            'reference_currency': charge.get('currency'),
            'updated_at': now
        }
    
    def _format_dispute_data(self, dispute: Dict, linked_store_id: str) -> Dict:
        """Format Stripe Dispute data for database insertion"""
        def safe_timestamptz(value):
            """Convert a datetime timestamp to datetime object (UTC)."""
            if not value:
                return None
            if isinstance(value, int):
                return datetime.fromtimestamp(value, tz=timezone.utc)
            elif isinstance(value, str):
                dt = parser.parse(value)
                if dt.tzinfo is None:
                    dt = dt.replace(tzinfo=timezone.utc)
                return dt.astimezone(timezone.utc)
            return value
        
        def safe_json(value):
            return Json(value) if value is not None else None
        
        now = datetime.now(timezone.utc)
        
        return {
            'id': dispute.get('id'),
            'linked_store_id': linked_store_id,
            'object': dispute.get('object', 'dispute'),
            'amount': dispute.get('amount'),
            'balance_transactions': safe_json(dispute.get('balance_transactions')),
            'charge_id': dispute.get('charge'),
            'created_at': safe_timestamptz(dispute.get('created')),
            'currency': dispute.get('currency'),
            'evidence': safe_json(dispute.get('evidence')),
            'evidence_details': safe_json(dispute.get('evidence_details')),
            'is_charge_refundable': dispute.get('is_charge_refundable', False),
            'livemode': dispute.get('livemode', False),
            'metadata': safe_json(dispute.get('metadata')),
            'network_reason_code': dispute.get('network_reason_code'),
            'reason': dispute.get('reason'),
            'status': dispute.get('status'),
            'updated_at': now
        }