#!/usr/bin/env python3
"""
Stripe Data Synchronization Script
Synchronizes PaymentIntents, Charges, and Disputes from Stripe to PostgreSQL database
"""

import os
import sys
import json
import time
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple, Set, AsyncIterator
import requests
from dotenv import load_dotenv
import aiohttp
from asyncio import Semaphore, Queue
from collections import defaultdict
import stripe
from .db import DatabaseManager, StripeStore
from .rate_limiter import RateLimiter
from .oauth import StripeOAuthManager, RefreshTokenExpiredError, RefreshTokenError

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stripe_sync.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class StripeAPI:
    """Stripe API client with rate limiting and token refresh"""
    
    def __init__(self, access_token: str, account_id: str = None, store_id: str = None):
        self.access_token = access_token
        self.account_id = account_id
        self.store_id = store_id
        # Set the Stripe API key
        stripe.api_key = access_token
        
        # Rate limiting: Stripe allows 100 requests per second for reading data
        # We'll be more conservative with 25 req/sec with burst capacity of 50
        self.rate_limiter = RateLimiter(rate=25, capacity=50)
        
        # OAuth manager for token refresh
        self.oauth_manager = StripeOAuthManager()
        
        self.session = None
        self.request_count = 0
    
    async def __aenter__(self):
        """Async context manager entry"""
        timeout = aiohttp.ClientTimeout(total=30, connect=10, sock_read=20)
        connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
        self.session = aiohttp.ClientSession(
            timeout=timeout,
            connector=connector,
            headers=headers
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    def _set_stripe_account(self):
        """Set the Stripe account if needed for Connect accounts"""
        if self.account_id:
            return {"stripe_account": self.account_id}
        return {}
    
    async def _ensure_valid_token(self):
        """Ensure we have a valid access token, refresh if needed"""
        if not self.store_id:
            # Can't refresh without store_id
            return
        
        try:
            # Check if current token is still valid
            if not self.oauth_manager.validate_access_token(self.access_token, self.account_id):
                logger.info(f"Token expired for store {self.store_id}, refreshing...")
                
                # Get a fresh token
                new_token = self.oauth_manager.ensure_valid_access_token_for_sync(self.store_id)
                
                # Update our token
                self.access_token = new_token
                stripe.api_key = new_token
                
                # Update session headers if session exists
                if self.session:
                    self.session.headers.update({
                        'Authorization': f'Bearer {new_token}'
                    })
                
                logger.info(f"Successfully refreshed token for store {self.store_id}")
                
        except RefreshTokenExpiredError as e:
            logger.error(f"Refresh token expired for store {self.store_id}: {e}")
            logger.warning(f"Store {self.store_id} marked inactive - sync will be skipped")
            raise  # Let the sync operation handle this
        except RefreshTokenError as e:
            logger.error(f"Token refresh failed for store {self.store_id}: {e}")
            raise  # Let the sync operation handle this
        except Exception as e:
            logger.error(f"Unexpected error during token refresh for store {self.store_id}: {e}")
            # Continue with existing token, let the API call fail naturally
            pass
    
    async def _handle_stripe_error(self, error, retry_count=0):
        """Handle Stripe API errors, including token refresh"""
        if hasattr(error, 'code') and error.code == 'invalid_api_key' and retry_count == 0:
            logger.info("Invalid API key error, attempting token refresh...")
            await self._ensure_valid_token()
            return True  # Indicate retry should be attempted
        return False  # No retry needed
    
    async def get_payment_intents(self, limit: int = 100, starting_after: str = None, 
                                 created_gte: int = None, created_lte: int = None, retry_count: int = 0) -> Dict:
        """Fetch PaymentIntents from Stripe API with token refresh support"""
        await self.rate_limiter.acquire()
        
        try:
            params = {
                'limit': min(limit, 100),  # Stripe max limit is 100
            }
            
            if starting_after:
                params['starting_after'] = starting_after
            
            if created_gte or created_lte:
                created_filter = {}
                if created_gte:
                    created_filter['gte'] = created_gte
                if created_lte:
                    created_filter['lte'] = created_lte
                params['created'] = created_filter
            
            kwargs = self._set_stripe_account()
            
            payment_intents = stripe.PaymentIntent.list(**params, **kwargs)
            self.request_count += 1
            
            logger.debug(f"Fetched {len(payment_intents.data)} PaymentIntents")
            return payment_intents
            
        except stripe.error.StripeError as e:
            # Try to handle token refresh for auth errors
            should_retry = await self._handle_stripe_error(e, retry_count)
            if should_retry and retry_count == 0:
                return await self.get_payment_intents(limit, starting_after, created_gte, created_lte, retry_count + 1)
            
            logger.error(f"Stripe API error fetching PaymentIntents: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error fetching PaymentIntents: {e}")
            raise
    
    async def get_charges(self, limit: int = 100, starting_after: str = None,
                         created_gte: int = None, created_lte: int = None, retry_count: int = 0) -> Dict:
        """Fetch Charges from Stripe API with token refresh support"""
        await self.rate_limiter.acquire()
        
        try:
            params = {
                'limit': min(limit, 100),  # Stripe max limit is 100
            }
            
            if starting_after:
                params['starting_after'] = starting_after
            
            if created_gte or created_lte:
                created_filter = {}
                if created_gte:
                    created_filter['gte'] = created_gte
                if created_lte:
                    created_filter['lte'] = created_lte
                params['created'] = created_filter
            
            kwargs = self._set_stripe_account()
            
            charges = stripe.Charge.list(**params, **kwargs)
            self.request_count += 1
            
            logger.debug(f"Fetched {len(charges.data)} Charges")
            return charges
            
        except stripe.error.StripeError as e:
            # Try to handle token refresh for auth errors
            should_retry = await self._handle_stripe_error(e, retry_count)
            if should_retry and retry_count == 0:
                return await self.get_charges(limit, starting_after, created_gte, created_lte, retry_count + 1)
            
            logger.error(f"Stripe API error fetching Charges: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error fetching Charges: {e}")
            raise
    
    async def get_disputes(self, limit: int = 100, starting_after: str = None,
                          created_gte: int = None, created_lte: int = None, retry_count: int = 0) -> Dict:
        """Fetch Disputes from Stripe API with token refresh support"""
        await self.rate_limiter.acquire()
        
        try:
            params = {
                'limit': min(limit, 100),  # Stripe max limit is 100
            }
            
            if starting_after:
                params['starting_after'] = starting_after
            
            if created_gte or created_lte:
                created_filter = {}
                if created_gte:
                    created_filter['gte'] = created_gte
                if created_lte:
                    created_filter['lte'] = created_lte
                params['created'] = created_filter
            
            kwargs = self._set_stripe_account()
            
            disputes = stripe.Dispute.list(**params, **kwargs)
            self.request_count += 1
            
            logger.debug(f"Fetched {len(disputes.data)} Disputes")
            return disputes
            
        except stripe.error.StripeError as e:
            # Try to handle token refresh for auth errors
            should_retry = await self._handle_stripe_error(e, retry_count)
            if should_retry and retry_count == 0:
                return await self.get_disputes(limit, starting_after, created_gte, created_lte, retry_count + 1)
            
            logger.error(f"Stripe API error fetching Disputes: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error fetching Disputes: {e}")
            raise
    
    async def get_all_payment_intents(self, since_timestamp: int = None) -> AsyncIterator[Dict]:
        """Fetch all PaymentIntents with pagination"""
        starting_after = None
        has_more = True
        
        while has_more:
            try:
                response = await self.get_payment_intents(
                    limit=100,
                    starting_after=starting_after,
                    created_gte=since_timestamp
                )
                
                for payment_intent in response.data:
                    yield payment_intent
                
                has_more = response.has_more
                if has_more and response.data:
                    starting_after = response.data[-1].id
                    
            except Exception as e:
                logger.error(f"Error in get_all_payment_intents: {e}")
                break
    
    async def get_all_charges(self, since_timestamp: int = None) -> AsyncIterator[Dict]:
        """Fetch all Charges with pagination"""
        starting_after = None
        has_more = True
        
        while has_more:
            try:
                response = await self.get_charges(
                    limit=100,
                    starting_after=starting_after,
                    created_gte=since_timestamp
                )
                
                for charge in response.data:
                    yield charge
                
                has_more = response.has_more
                if has_more and response.data:
                    starting_after = response.data[-1].id
                    
            except Exception as e:
                logger.error(f"Error in get_all_charges: {e}")
                break
    
    async def get_all_disputes(self, since_timestamp: int = None) -> AsyncIterator[Dict]:
        """Fetch all Disputes with pagination"""
        starting_after = None
        has_more = True
        
        while has_more:
            try:
                response = await self.get_disputes(
                    limit=100,
                    starting_after=starting_after,
                    created_gte=since_timestamp
                )
                
                for dispute in response.data:
                    yield dispute
                
                has_more = response.has_more
                if has_more and response.data:
                    starting_after = response.data[-1].id
                    
            except Exception as e:
                logger.error(f"Error in get_all_disputes: {e}")
                break


class StripeDataSync:
    """Main class for synchronizing Stripe data"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.sync_batch_size = int(os.getenv('SYNC_BATCH_SIZE', '1000'))
        self.max_concurrent_stores = int(os.getenv('MAX_CONCURRENT_STORES', '3'))
        self.semaphore = Semaphore(self.max_concurrent_stores)
    
    async def sync_store_data(self, store: StripeStore, since_timestamp: int = None):
        """Sync data for a single Stripe store"""
        async with self.semaphore:
            logger.info(f"Starting sync for store: {store.store_name} (ID: {store.id})")
            
            try:
                async with StripeAPI(store.access_token, store.account_id, store.id) as stripe_api:
                    try:
                        # Ensure we have a valid token before starting sync
                        await stripe_api._ensure_valid_token()
                        
                        # Sync PaymentIntents
                        await self._sync_payment_intents(stripe_api, store.id, since_timestamp)
                        
                        # Sync Charges
                        await self._sync_charges(stripe_api, store.id, since_timestamp)
                        
                        # Sync Disputes
                        await self._sync_disputes(stripe_api, store.id, since_timestamp)
                        
                        logger.info(f"Completed sync for store: {store.store_name}")
                        
                    except (RefreshTokenExpiredError, RefreshTokenError) as e:
                        logger.warning(f"Skipping store {store.store_name} due to token issues: {e}")
                        logger.info(f"Store {store.store_name} needs to reconnect their Stripe account")
                        return  # Skip this store, don't raise exception
                    except Exception as e:
                        logger.error(f"Error syncing store {store.store_name}: {e}")
                        raise
                        
            except (RefreshTokenExpiredError, RefreshTokenError):
                # Already handled above, just return to skip this store
                return
            except Exception as e:
                logger.error(f"Unexpected error setting up sync for store {store.store_name}: {e}")
                raise
    
    async def _sync_payment_intents(self, stripe_api: StripeAPI, store_id: str, since_timestamp: int = None):
        """Sync PaymentIntents for a store"""
        logger.info(f"Syncing PaymentIntents for store {store_id}")
        
        payment_intents_batch = []
        count = 0
        
        async for payment_intent in stripe_api.get_all_payment_intents(since_timestamp):
            payment_intents_batch.append((payment_intent, store_id))
            count += 1
            
            if len(payment_intents_batch) >= self.sync_batch_size:
                self.db_manager.upsert_payment_intents_batch(payment_intents_batch)
                payment_intents_batch = []
                logger.info(f"Synced {count} PaymentIntents for store {store_id}")
        
        # Process remaining items
        if payment_intents_batch:
            self.db_manager.upsert_payment_intents_batch(payment_intents_batch)
        
        logger.info(f"Completed PaymentIntents sync for store {store_id}. Total: {count}")
    
    async def _sync_charges(self, stripe_api: StripeAPI, store_id: str, since_timestamp: int = None):
        """Sync Charges for a store"""
        logger.info(f"Syncing Charges for store {store_id}")
        
        charges_batch = []
        count = 0
        
        async for charge in stripe_api.get_all_charges(since_timestamp):
            charges_batch.append((charge, store_id))
            count += 1
            
            if len(charges_batch) >= self.sync_batch_size:
                self.db_manager.upsert_charges_batch(charges_batch)
                charges_batch = []
                logger.info(f"Synced {count} Charges for store {store_id}")
        
        # Process remaining items
        if charges_batch:
            self.db_manager.upsert_charges_batch(charges_batch)
        
        logger.info(f"Completed Charges sync for store {store_id}. Total: {count}")
    
    async def _sync_disputes(self, stripe_api: StripeAPI, store_id: str, since_timestamp: int = None):
        """Sync Disputes for a store"""
        logger.info(f"Syncing Disputes for store {store_id}")
        
        disputes_batch = []
        count = 0
        
        async for dispute in stripe_api.get_all_disputes(since_timestamp):
            disputes_batch.append((dispute, store_id))
            count += 1
            
            if len(disputes_batch) >= self.sync_batch_size:
                self.db_manager.upsert_disputes_batch(disputes_batch)
                disputes_batch = []
                logger.info(f"Synced {count} Disputes for store {store_id}")
        
        # Process remaining items
        if disputes_batch:
            self.db_manager.upsert_disputes_batch(disputes_batch)
        
        logger.info(f"Completed Disputes sync for store {store_id}. Total: {count}")
    
    async def sync_all_stores(self, since_timestamp: int = None):
        """Sync data for all linked Stripe stores"""
        logger.info("Starting sync for all Stripe stores")
        
        stores = self.db_manager.get_linked_stripe_stores()
        if not stores:
            logger.info("No Stripe stores found to sync")
            return
        
        logger.info(f"Found {len(stores)} Stripe stores to sync")
        
        # Create tasks for concurrent store syncing
        tasks = []
        for store in stores:
            task = asyncio.create_task(self.sync_store_data(store, since_timestamp))
            tasks.append(task)
        
        # Wait for all stores to complete
        try:
            await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            logger.error(f"Error during store sync: {e}")
        
        logger.info("Completed sync for all Stripe stores")
    
    async def sync_incremental(self, hours_back: int = 24):
        """Perform incremental sync for the last N hours"""
        since_timestamp = int((datetime.now(timezone.utc).timestamp() - (hours_back * 3600)))
        logger.info(f"Starting incremental sync for last {hours_back} hours (since {datetime.fromtimestamp(since_timestamp, tz=timezone.utc)})")
        
        await self.sync_all_stores(since_timestamp)
    
    async def sync_full(self):
        """Perform full sync of all data"""
        logger.info("Starting full sync")
        await self.sync_all_stores()
    
    def cleanup(self):
        """Cleanup resources"""
        if self.db_manager:
            self.db_manager.close_all_connections()


async def main():
    """Main function for standalone execution"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Stripe Data Synchronization')
    parser.add_argument('--mode', choices=['full', 'incremental'], default='incremental',
                       help='Sync mode: full or incremental')
    parser.add_argument('--hours', type=int, default=24,
                       help='Hours to look back for incremental sync')
    parser.add_argument('--store-id', type=str,
                       help='Sync specific store only')
    
    args = parser.parse_args()
    
    sync = StripeDataSync()
    
    try:
        if args.store_id:
            store = sync.db_manager.get_stripe_store_by_id(args.store_id)
            if store:
                if args.mode == 'full':
                    await sync.sync_store_data(store)
                else:
                    since_timestamp = int((datetime.now(timezone.utc).timestamp() - (args.hours * 3600)))
                    await sync.sync_store_data(store, since_timestamp)
            else:
                logger.error(f"Store with ID {args.store_id} not found")
        else:
            if args.mode == 'full':
                await sync.sync_full()
            else:
                await sync.sync_incremental(args.hours)
    
    except KeyboardInterrupt:
        logger.info("Sync interrupted by user")
    except Exception as e:
        logger.error(f"Sync failed: {e}")
        raise
    finally:
        sync.cleanup()


if __name__ == "__main__":
    asyncio.run(main())