# Stripe Sync Server

Automatic Stripe data synchronization server with scheduled sync capabilities.

## Features

- 🔄 **Automatic Scheduled Sync**: Configurable interval-based sync
- 🎨 **Enhanced Logging**: Colorful logs with emojis for better monitoring
- 🏗 **Clean Architecture**: Structured codebase with dedicated services
- 🐳 **Docker Ready**: Full containerization support
- ⚡ **FastAPI**: High-performance async API framework
- **PaymentIntent Sync**: Synchronizes Stripe PaymentIntent data
- **Charge Sync**: Synchronizes Stripe Charge data  
- **Dispute Sync**: Synchronizes Stripe Dispute data
- **Rate Limiting**: Built-in rate limiting to respect Stripe API limits
- **Batch Processing**: Efficient batch database operations
- **Incremental Sync**: Support for both full and incremental synchronization
- **Multi-Store Support**: Can sync multiple Stripe accounts concurrently

## Quick Start

### Using Docker Compose

1. **Copy environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit environment variables:**
   ```bash
   nano .env
   ```

3. **Start the server:**
   ```bash
   docker compose up -d
   ```

4. **View logs:**
   ```bash
   docker compose logs -f stripe-sync-server
   ```

### Development

Use the development compose file for local development:

```bash
docker compose -f docker-compose.dev.yml up
```

### Manual Setup

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

3. **Run server:**
   ```bash
   python main.py
   ```

### Database Setup
Ensure your PostgreSQL database has the required Stripe tables:
- `stripe_payment_intents`
- `stripe_charges` 
- `stripe_disputes`
- `linked_stores` (with Stripe store data)

## API Endpoints

### Health Check
- `GET /` - Root endpoint
- `GET /health` - Health check

### Sync Operations
- `POST /sync/` - Start synchronization
  - `store_id` (optional): Sync specific store
  - `hours_back` (optional): Hours to look back for incremental sync (default: 24)
  - `full_sync` (optional): Perform full sync if true
- `GET /sync/status/{task_id}` - Check sync status


## Usage Examples

### Sync All Stores (Incremental - Last 24 Hours)
```bash
curl -X POST "http://localhost:8001/sync/" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### Sync Specific Store (Full Sync)
```bash
curl -X POST "http://localhost:8001/sync/" \
  -H "Content-Type: application/json" \
  -d '{"store_id": "store_123", "full_sync": true}'
```

### Sync Last 48 Hours
```bash
curl -X POST "http://localhost:8001/sync/" \
  -H "Content-Type: application/json" \
  -d '{"hours_back": 48}'
```

### Check Sync Status
```bash
curl "http://localhost:8001/sync/status/{task_id}"
```


## Database Schema

The sync server works with these Stripe tables:

### stripe_payment_intents
- `id` (TEXT, PRIMARY KEY)
- `linked_store_id` (TEXT, FOREIGN KEY)
- `amount`, `currency`, `status`
- `created_at`, `updated_at`
- Plus 30+ additional Stripe PaymentIntent fields

### stripe_charges  
- `id` (TEXT, PRIMARY KEY)
- `linked_store_id` (TEXT, FOREIGN KEY)
- `payment_intent_id` (TEXT, FOREIGN KEY)
- `amount`, `currency`, `status`
- `captured`, `disputed`, `refunded`
- Plus 30+ additional Stripe Charge fields

### stripe_disputes
- `id` (TEXT, PRIMARY KEY) 
- `linked_store_id` (TEXT, FOREIGN KEY)
- `charge_id` (TEXT, FOREIGN KEY)
- `amount`, `currency`, `reason`, `status`
- Plus additional Stripe Dispute fields

## Rate Limiting

The sync server implements rate limiting to respect Stripe API limits:
- **Standard**: 25 requests/second with burst capacity of 50
- **Read Heavy**: Configurable for heavy read operations

## Running the Server

### Development
```bash
python main.py
```

### Production
```bash
uvicorn main:app --host 0.0.0.0 --port 8001 --workers 1
```

## Configuration

| Environment Variable | Default | Description |
|---------------------|---------|-------------|
| `AUTO_SYNC_ENABLED` | `true` | Enable/disable automatic sync |
| `SYNC_INTERVAL_MINUTES` | `1` | Sync interval in minutes (0 to disable) |
| `DATABASE_URL` | - | PostgreSQL connection string |
| `STRIPE_APP_SECRET_KEY` | - | Stripe secret key |
| `LOG_LEVEL` | `INFO` | Logging level |
| `API_HOST` | `0.0.0.0` | Server host |
| `API_PORT` | `8765` | Server port |
| `SYNC_BATCH_SIZE` | `1000` | Batch size for database operations |
| `MAX_CONCURRENT_STORES` | `3` | Max concurrent store syncs |
| `OAUTH_REQUEST_TIMEOUT` | `30` | OAuth token refresh timeout (seconds) |
| `TOKEN_VALIDATION_TIMEOUT` | `15` | Token validation timeout (seconds) |

## Monitoring

The server provides comprehensive logging with:
- 🚀 Startup/shutdown events
- 🔄 Sync operations with timing
- ⚠️ Warnings and errors
- 📊 Statistics and metrics

## Docker Commands

```bash
# Build image
docker compose build

# Start services
docker compose up -d

# View logs
docker compose logs -f

# Stop services
docker compose down

# Restart
docker compose restart
```

## Health Check

The container includes automatic health checks:
- Endpoint: `http://localhost:8765/health`
- Interval: 30 seconds
- Timeout: 10 seconds
- Retries: 3

## Logging

Logs are written to both console and `stripe_sync.log` file with detailed information about:
- Sync progress and statistics
- API rate limiting status
- Error conditions and retries
- Database operation results

## Error Handling

The service includes comprehensive error handling for:
- Stripe API errors (rate limits, authentication, etc.)
- OAuth token expiration and automatic refresh
- Database connection issues
- Data validation problems
- Network timeouts and retries

## Automatic OAuth Token Refresh

The sync server includes built-in automatic OAuth token refresh functionality to handle token expiration seamlessly during sync operations:

### Features
- **Transparent Token Refresh**: Automatically detects and refreshes expired tokens during sync operations
- **Store Data Updates**: Updates linked store information with new tokens after successful refresh
- **Error Recovery**: Gracefully handles token refresh errors and retries operations
- **Zero Downtime**: Sync operations continue without interruption when tokens expire

### How It Works
1. **Token Validation**: Before Stripe API calls, the system validates the current access token
2. **Automatic Refresh**: If token is expired, it automatically refreshes using the stored refresh_token
3. **Database Update**: Updates the linked store with new access token and refresh token
4. **Retry Logic**: Retries the failed API call with the new valid token  
5. **Seamless Sync**: Sync operations continue without interruption

### Refresh Process
- Uses Stripe OAuth `/v1/oauth/token` endpoint with `grant_type=refresh_token`
- Stores new access_token and refresh_token in linked_stores table
- Updates store metadata like lastRefreshedAt timestamp
- Maintains sync operation state throughout the refresh process

This ensures uninterrupted data synchronization even when access tokens expire during long-running sync operations.