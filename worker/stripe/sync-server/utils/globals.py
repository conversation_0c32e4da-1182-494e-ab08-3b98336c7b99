"""
Global scheduler instance management
"""

from services.scheduler import SyncScheduler
from typing import Optional

_scheduler_instance: Optional[SyncScheduler] = None

def set_scheduler(scheduler: SyncScheduler) -> None:
    """Set the global scheduler instance"""
    global _scheduler_instance
    _scheduler_instance = scheduler

def get_scheduler() -> Optional[SyncScheduler]:
    """Get the global scheduler instance"""
    return _scheduler_instance

def has_scheduler() -> bool:
    """Check if scheduler instance exists"""
    return _scheduler_instance is not None