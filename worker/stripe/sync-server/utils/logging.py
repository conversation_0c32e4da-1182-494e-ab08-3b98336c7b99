"""
Enhanced logging utilities with colors and formatting
"""

import os
import sys
import logging
from datetime import datetime


def setup_logging():
    """Setup enhanced logging with colors and better formatting"""
    log_level = os.getenv("LOG_LEVEL", "INFO").upper()
    
    class ColoredFormatter(logging.Formatter):
        """Custom formatter with colors and better formatting"""
        
        # Color codes
        COLORS = {
            'DEBUG': '\033[36m',    # Cyan
            'INFO': '\033[92m',     # Green
            'WARNING': '\033[93m',  # Yellow
            'ERROR': '\033[91m',    # Red
            'CRITICAL': '\033[95m', # Magenta
        }
        RESET = '\033[0m'
        
        def format(self, record):
            # Add color to level name
            if record.levelname in self.COLORS:
                colored_level = f"{self.COLORS[record.levelname]}{record.levelname}{self.RESET}"
            else:
                colored_level = record.levelname
            
            # Format timestamp
            timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S')
            
            # Create the log message
            log_format = f"[{timestamp}] {colored_level} [{record.name}] {record.getMessage()}"
            
            return log_format
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level))
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(ColoredFormatter())
    root_logger.addHandler(console_handler)
    
    # Set specific logger levels
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.error").setLevel(logging.INFO)
    logging.getLogger("apscheduler").setLevel(logging.INFO)
    
    return logging.getLogger(__name__)