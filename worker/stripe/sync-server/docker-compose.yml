version: '3.8'

services:
  stripe-sync-server:
    build: .
    container_name: stripe-sync-server
    restart: unless-stopped
    ports:
      - "8765:8765"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - STRIPE_APP_SECRET_KEY=${STRIPE_APP_SECRET_KEY}
      - AUTO_SYNC_ENABLED=${AUTO_SYNC_ENABLED:-true}
      - SYNC_INTERVAL_MINUTES=${SYNC_INTERVAL_MINUTES:-1}
      - SYNC_BATCH_SIZE=${SYNC_BATCH_SIZE:-1000}
      - MAX_CONCURRENT_STORES=${MAX_CONCURRENT_STORES:-3}
      - OAUTH_REQUEST_TIMEOUT=${OAUTH_REQUEST_TIMEOUT:-30}
      - TOKEN_VALIDATION_TIMEOUT=${TOKEN_VALIDATION_TIMEOUT:-15}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - API_HOST=${API_HOST:-0.0.0.0}
      - API_PORT=${API_PORT:-8765}
    volumes:
      - ./logs:/app/logs
    networks:
      - stripe-sync
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8765/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  stripe-sync:
    driver: bridge

volumes:
  logs: