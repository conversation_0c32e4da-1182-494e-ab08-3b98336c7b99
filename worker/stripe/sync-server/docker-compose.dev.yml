version: '3.8'

services:
  stripe-sync-server-dev:
    build: .
    container_name: stripe-sync-server-dev
    restart: unless-stopped
    ports:
      - "8765:8765"
    environment:
      - DATABASE_URL=postgresql://postgres:<EMAIL>:5432/testdb?schema=chargeback
      - STRIPE_APP_SECRET_KEY=sk_test_your_test_key_here
      - AUTO_SYNC_ENABLED=true
      - SYNC_INTERVAL_MINUTES=5
      - SYNC_BATCH_SIZE=100
      - MAX_CONCURRENT_STORES=2
      - OAUTH_REQUEST_TIMEOUT=30
      - TOKEN_VALIDATION_TIMEOUT=15
      - LOG_LEVEL=DEBUG
      - API_HOST=0.0.0.0
      - API_PORT=8765
    volumes:
      - .:/app
      - ./logs:/app/logs
    networks:
      - stripe-sync-dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8765/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  stripe-sync-dev:
    driver: bridge

volumes:
  logs: