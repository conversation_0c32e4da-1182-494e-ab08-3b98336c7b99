"""
Stripe synchronization routes
"""

import uuid
from typing import Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from services.stripe_api import StripeDataSync
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/sync", tags=["sync"])

# Request/Response models
class SyncRequest(BaseModel):
    store_id: Optional[str] = None
    hours_back: Optional[int] = 24
    full_sync: Optional[bool] = False

class SyncResponse(BaseModel):
    message: str
    store_id: Optional[str] = None
    store_name: Optional[str] = None
    store_count: Optional[int] = None
    task_id: Optional[str] = None

class SyncStatus(BaseModel):
    task_id: str
    status: str
    message: str

# Simple in-memory task tracking
tasks = {}

@router.post("/", response_model=SyncResponse)
async def sync_stripe_data(
    request: SyncRequest,
    background_tasks: BackgroundTasks
):
    """
    Synchronize Stripe data
    
    - **store_id**: Optional store ID to sync a specific store
    - **hours_back**: Hours to look back for incremental sync (default: 24)
    - **full_sync**: If True, perform full sync ignoring hours_back
    - If no store_id is provided, all stores will be synced
    """
    try:
        # Generate a task ID
        task_id = str(uuid.uuid4())
        
        # Get store information first
        sync_service = StripeDataSync()
        
        if request.store_id:
            # Get specific store info
            store = sync_service.db_manager.get_stripe_store_by_id(request.store_id)
            if not store:
                raise HTTPException(status_code=404, detail=f"Store {request.store_id} not found")
            
            # Set task status
            tasks[task_id] = {
                "status": "running",
                "message": f"Syncing store {store.store_name}",
                "store_id": store.id
            }
            
            # Schedule background task for single store
            if request.full_sync:
                background_tasks.add_task(sync_single_store_full, sync_service, store, task_id)
            else:
                background_tasks.add_task(sync_single_store_incremental, sync_service, store, request.hours_back, task_id)
            
            return SyncResponse(
                message=f"Started sync for store: {store.store_name}",
                store_id=store.id,
                store_name=store.store_name,
                task_id=task_id
            )
        else:
            # Get all stores info
            stores = sync_service.db_manager.get_linked_stripe_stores()
            
            if not stores:
                raise HTTPException(status_code=404, detail="No Stripe stores found")
            
            # Set task status
            tasks[task_id] = {
                "status": "running", 
                "message": f"Syncing {len(stores)} stores",
                "store_count": len(stores)
            }
            
            # Schedule background task for all stores
            if request.full_sync:
                background_tasks.add_task(sync_all_stores_full, sync_service, task_id)
            else:
                background_tasks.add_task(sync_all_stores_incremental, sync_service, request.hours_back, task_id)
            
            return SyncResponse(
                message=f"Started sync for {len(stores)} stores",
                store_count=len(stores),
                task_id=task_id
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting sync: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status/{task_id}", response_model=SyncStatus)
async def get_sync_status(task_id: str):
    """Get the status of a sync task"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task = tasks[task_id]
    return SyncStatus(
        task_id=task_id,
        status=task["status"],
        message=task["message"]
    )

# Background task functions
async def sync_single_store_full(sync_service: StripeDataSync, store, task_id: str):
    """Background task to sync a single store (full sync)"""
    try:
        logger.info(f"Starting full sync for store: {store.store_name}")
        await sync_service.sync_store_data(store)
        
        tasks[task_id] = {
            "status": "completed",
            "message": f"Successfully synced store {store.store_name}",
            "store_id": store.id
        }
        logger.info(f"Completed full sync for store: {store.store_name}")
        
    except Exception as e:
        logger.error(f"Error syncing store {store.store_name}: {e}")
        tasks[task_id] = {
            "status": "failed",
            "message": f"Failed to sync store {store.store_name}: {str(e)}",
            "store_id": store.id
        }
    finally:
        sync_service.cleanup()

async def sync_single_store_incremental(sync_service: StripeDataSync, store, hours_back: int, task_id: str):
    """Background task to sync a single store (incremental sync)"""
    try:
        from datetime import datetime, timezone
        
        since_timestamp = int((datetime.now(timezone.utc).timestamp() - (hours_back * 3600)))
        logger.info(f"Starting incremental sync for store: {store.store_name} (last {hours_back} hours)")
        await sync_service.sync_store_data(store, since_timestamp)
        
        tasks[task_id] = {
            "status": "completed",
            "message": f"Successfully synced store {store.store_name} (last {hours_back} hours)",
            "store_id": store.id
        }
        logger.info(f"Completed incremental sync for store: {store.store_name}")
        
    except Exception as e:
        logger.error(f"Error syncing store {store.store_name}: {e}")
        tasks[task_id] = {
            "status": "failed",
            "message": f"Failed to sync store {store.store_name}: {str(e)}",
            "store_id": store.id
        }
    finally:
        sync_service.cleanup()

async def sync_all_stores_full(sync_service: StripeDataSync, task_id: str):
    """Background task to sync all stores (full sync)"""
    try:
        logger.info("Starting full sync for all stores")
        await sync_service.sync_full()
        
        tasks[task_id] = {
            "status": "completed",
            "message": "Successfully synced all stores"
        }
        logger.info("Completed full sync for all stores")
        
    except Exception as e:
        logger.error(f"Error syncing all stores: {e}")
        tasks[task_id] = {
            "status": "failed",
            "message": f"Failed to sync all stores: {str(e)}"
        }
    finally:
        sync_service.cleanup()

async def sync_all_stores_incremental(sync_service: StripeDataSync, hours_back: int, task_id: str):
    """Background task to sync all stores (incremental sync)"""
    try:
        logger.info(f"Starting incremental sync for all stores (last {hours_back} hours)")
        await sync_service.sync_incremental(hours_back)
        
        tasks[task_id] = {
            "status": "completed",
            "message": f"Successfully synced all stores (last {hours_back} hours)"
        }
        logger.info("Completed incremental sync for all stores")
        
    except Exception as e:
        logger.error(f"Error syncing all stores: {e}")
        tasks[task_id] = {
            "status": "failed",
            "message": f"Failed to sync all stores: {str(e)}"
        }
    finally:
        sync_service.cleanup()