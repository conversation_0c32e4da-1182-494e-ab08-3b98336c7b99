#!/usr/bin/env python3
"""
FastAPI application for Stripe data synchronization
"""

import os
import time
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, Request
import uvicorn
from dotenv import load_dotenv

# Import routers
from routes import health_router, sync_router
from services.scheduler import SyncScheduler
from utils.globals import set_scheduler, get_scheduler
from utils.logging import setup_logging

# Load environment variables first
load_dotenv()

# Setup enhanced logging
logger = setup_logging()

# Request logging middleware
async def log_requests(request: Request, call_next):
    start_time = time.time()
    
    # Log request
    logger.info(f"📥 {request.method} {request.url.path} - Client: {request.client.host}")
    
    response = await call_next(request)
    
    # Calculate processing time
    process_time = time.time() - start_time
    
    # Log response
    status_emoji = "✅" if 200 <= response.status_code < 300 else "❌" if response.status_code >= 400 else "⚠️"
    logger.info(f"📤 {status_emoji} {response.status_code} - {request.method} {request.url.path} - {process_time:.3f}s")
    
    return response

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle"""
    # Startup
    logger.info("🚀 Starting Stripe Sync Server...")
    
    # Log Stripe configuration
    stripe_key = os.getenv("STRIPE_APP_SECRET_KEY", "")
    if stripe_key:
        key_type = "TEST" if stripe_key.startswith("sk_test_") else "LIVE"
        logger.info(f"🔑 Stripe API Key: {key_type} (***{stripe_key[-6:]})")
    else:
        logger.warning("⚠️  No Stripe API key configured")
    
    # Check if auto-sync is enabled
    auto_sync_enabled = os.getenv("AUTO_SYNC_ENABLED", "true").lower() == "true"
    
    if auto_sync_enabled:
        try:
            logger.info("⏰ Initializing auto-sync scheduler...")
            scheduler = SyncScheduler()
            set_scheduler(scheduler)
            scheduler.start_scheduler()
            
            if scheduler.is_scheduler_running():
                interval = int(os.getenv("SYNC_INTERVAL_MINUTES", "1"))
                logger.info(f"✅ Auto-sync scheduler started - running every {interval} minute(s)")
            else:
                logger.info("🔒 Auto-sync disabled by configuration")
        except Exception as e:
            logger.error(f"❌ Failed to start scheduler: {str(e)}")
    else:
        logger.info("🔒 Auto-sync disabled")
    
    logger.info("🎯 Sync server ready!")
    
    yield
    
    # Shutdown
    logger.info("🛑 Shutting down Stripe Sync Server...")
    scheduler = get_scheduler()
    if scheduler:
        scheduler.stop_scheduler()
        logger.info("⏹️  Scheduler stopped")
    logger.info("👋 Goodbye!")

# Create FastAPI app
app = FastAPI(
    title="Stripe Data Sync API",
    description="API for synchronizing Stripe store data to PostgreSQL with auto-scheduling",
    version="1.0.0",
    lifespan=lifespan
)

# Add request logging middleware
app.middleware("http")(log_requests)

# Include routers
app.include_router(health_router)
app.include_router(sync_router)

if __name__ == "__main__":
    # Run with uvicorn when executed directly
    port = int(os.getenv("API_PORT", 8765))  # Updated port as requested
    host = os.getenv("API_HOST", "0.0.0.0")
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )