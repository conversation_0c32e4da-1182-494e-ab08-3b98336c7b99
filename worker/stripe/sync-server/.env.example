# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/database?schema=chargeback

# API Configuration  
API_HOST=0.0.0.0
API_PORT=8765

# Stripe Configuration
# Your Stripe App secret key for OAuth token refresh
STRIPE_APP_SECRET_KEY=sk_test_your_stripe_secret_key_here

# Sync Configuration
SYNC_BATCH_SIZE=1000
MAX_CONCURRENT_STORES=3

# Scheduled Sync Configuration
# Enable/disable automatic sync (true/false)
AUTO_SYNC_ENABLED=true
# Sync interval in minutes (set to 0 to disable scheduled sync)
SYNC_INTERVAL_MINUTES=10

# Token Refresh Configuration
# Timeout for OAuth token refresh requests (in seconds)
OAUTH_REQUEST_TIMEOUT=30

# Timeout for token validation requests (in seconds)  
TOKEN_VALIDATION_TIMEOUT=15

# Logging
LOG_LEVEL=INFO