#!/usr/bin/env python3
"""
FastAPI application for Stripe sandbox data seeding
"""

import os
import logging
import sys
from datetime import datetime
from fastapi import FastAPI, Request
import uvicorn
from dotenv import load_dotenv
from contextlib import asynccontextmanager
import time

# Import routers
from routes import health_router, seed_router
from services.scheduler import SeedScheduler
from utils.globals import set_scheduler, get_scheduler

# Load environment variables first
load_dotenv()

# Configure enhanced logging
def setup_logging():
    log_level = os.getenv("LOG_LEVEL", "INFO").upper()
    
    # Create custom formatter
    class ColoredFormatter(logging.Formatter):
        """Custom formatter with colors and better formatting"""
        
        # Color codes
        COLORS = {
            'DEBUG': '\033[36m',    # Cyan
            'INFO': '\033[92m',     # Green
            'WARNING': '\033[93m',  # Yellow
            'ERROR': '\033[91m',    # Red
            'CRITICAL': '\033[95m', # Magenta
        }
        RESET = '\033[0m'
        
        def format(self, record):
            # Add color to level name
            if record.levelname in self.COLORS:
                record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.RESET}"
            
            # Format timestamp
            timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S')
            
            # Create the log message
            log_format = f"[{timestamp}] {record.levelname} [{record.name}] {record.getMessage()}"
            
            return log_format
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level))
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(ColoredFormatter())
    root_logger.addHandler(console_handler)
    
    # Set specific logger levels
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.error").setLevel(logging.INFO)
    logging.getLogger("apscheduler").setLevel(logging.INFO)
    
    return logging.getLogger(__name__)

logger = setup_logging()

# Request logging middleware
async def log_requests(request: Request, call_next):
    start_time = time.time()
    
    # Log request
    logger.info(f"📥 {request.method} {request.url.path} - Client: {request.client.host}")
    
    response = await call_next(request)
    
    # Calculate processing time
    process_time = time.time() - start_time
    
    # Log response
    status_emoji = "✅" if 200 <= response.status_code < 300 else "❌" if response.status_code >= 400 else "⚠️"
    logger.info(f"📤 {status_emoji} {response.status_code} - {request.method} {request.url.path} - {process_time:.3f}s")
    
    return response

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("🚀 Starting Stripe Seed Server...")
    
    # Log configuration
    stripe_key = os.getenv("STRIPE_SECRET_KEY", "")
    if stripe_key:
        key_type = "TEST" if stripe_key.startswith("sk_test_") else "LIVE"
        logger.info(f"🔑 Stripe API Key: {key_type} (***{stripe_key[-6:]})")
    else:
        logger.warning("⚠️  No Stripe API key configured")
    
    # Check if auto-seeding is enabled
    auto_seed_enabled = os.getenv("AUTO_SEED_ENABLED", "true").lower() == "true"
    
    if auto_seed_enabled:
        try:
            logger.info("⏰ Initializing auto-seeding scheduler...")
            scheduler = SeedScheduler()
            set_scheduler(scheduler)
            scheduler.start_scheduler()
            logger.info("✅ Auto-seeding scheduler started - will run every 10 minutes")
        except Exception as e:
            logger.error(f"❌ Failed to start scheduler: {str(e)}")
    else:
        logger.info("🔒 Auto-seeding disabled")
    
    logger.info("🎯 Seed server ready!")
    
    yield
    
    # Shutdown
    logger.info("🛑 Shutting down Stripe Seed Server...")
    scheduler = get_scheduler()
    if scheduler:
        scheduler.stop_scheduler()
        logger.info("⏹️  Scheduler stopped")
    logger.info("👋 Goodbye!")

# Create FastAPI app
app = FastAPI(
    title="Stripe Seed API",
    description="API for seeding Stripe sandbox data with auto-scheduling",
    version="1.0.0",
    lifespan=lifespan
)

# Add request logging middleware
app.middleware("http")(log_requests)

# Include routers
app.include_router(health_router)
app.include_router(seed_router)

if __name__ == "__main__":
    # Run with uvicorn when executed directly
    port = int(os.getenv("API_PORT", 8001))
    host = os.getenv("API_HOST", "0.0.0.0")
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )