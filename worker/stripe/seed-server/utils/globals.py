"""
Global variables to avoid circular imports
"""

from typing import Optional
from services.scheduler import SeedScheduler

# Global scheduler instance
scheduler: Optional[SeedScheduler] = None

def get_scheduler() -> Optional[SeedScheduler]:
    """Get the global scheduler instance"""
    return scheduler

def set_scheduler(scheduler_instance: SeedScheduler) -> None:
    """Set the global scheduler instance"""
    global scheduler
    scheduler = scheduler_instance