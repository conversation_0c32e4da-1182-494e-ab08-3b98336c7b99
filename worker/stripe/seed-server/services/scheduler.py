"""
Background scheduler for automatic seeding
"""

import asyncio
import logging
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from datetime import datetime
from .stripe_seeder import StripeSeeder

logger = logging.getLogger(__name__)

class SeedScheduler:
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.seeder = StripeSeeder()
        self.is_running = False
        
    async def seed_realtime_data(self):
        """Seed new realtime data with current timestamps"""
        try:
            start_time = datetime.now()
            logger.info(f"🌱 Starting scheduled seed at {start_time.strftime('%H:%M:%S')}")
            
            # Generate new realtime data
            result = await self.seeder.seed_realtime_data()
            
            if result["success"]:
                data = result.get("data", {})
                customers_count = len(data.get("customers", []))
                payments_count = len(data.get("payment_intents", []))
                invoices_count = len(data.get("invoices", []))
                events_count = len(data.get("events", []))
                
                duration = (datetime.now() - start_time).total_seconds()
                
                logger.info(f"✅ Scheduled seed completed in {duration:.2f}s:")
                logger.info(f"   👥 {customers_count} customers")
                logger.info(f"   💳 {payments_count} payment intents") 
                logger.info(f"   📄 {invoices_count} invoices")
                logger.info(f"   📊 {events_count} events")
            else:
                logger.error(f"❌ Scheduled seed failed: {result['message']}")
                
        except Exception as e:
            logger.error(f"💥 Error in scheduled seeding: {str(e)}")
    
    def start_scheduler(self):
        """Start the background scheduler"""
        if not self.is_running:
            # Add job to run every 10 minutes
            self.scheduler.add_job(
                self.seed_realtime_data,
                trigger=IntervalTrigger(minutes=10),
                id='realtime_seed_job',
                name='Realtime Stripe Data Seeding',
                replace_existing=True
            )
            
            self.scheduler.start()
            self.is_running = True
            
            # Get next run time
            jobs = self.scheduler.get_jobs()
            next_run = jobs[0].next_run_time.strftime('%H:%M:%S') if jobs else "Unknown"
            logger.info(f"⏰ Scheduler started - next run at {next_run}")
    
    def stop_scheduler(self):
        """Stop the background scheduler"""
        if self.is_running:
            self.scheduler.shutdown()
            self.is_running = False
            logger.info("🛑 Scheduler stopped")
    
    def get_scheduler_info(self):
        """Get scheduler status information"""
        jobs = self.scheduler.get_jobs()
        return {
            "running": self.is_running,
            "jobs_count": len(jobs),
            "next_run": jobs[0].next_run_time.isoformat() if jobs else None,
            "jobs": [
                {
                    "id": job.id,
                    "name": job.name,
                    "next_run": job.next_run_time.isoformat() if job.next_run_time else None
                }
                for job in jobs
            ]
        }