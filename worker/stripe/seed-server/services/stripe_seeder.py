"""
Stripe seeding service for sandbox data
"""

import os
import logging
import stripe
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import random
import uuid

logger = logging.getLogger(__name__)

class StripeSeeder:
    def __init__(self):
        # Use test key for sandbox
        stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
        if not stripe.api_key:
            raise ValueError("STRIPE_SECRET_KEY environment variable is required")
        
        if not stripe.api_key.startswith("sk_test_"):
            raise ValueError("Only test keys are allowed for seeding")

    async def seed_all(self) -> Dict[str, Any]:
        """Seed all Stripe data for sandbox"""
        logger.info("🚀 Starting complete Stripe data seeding...")
        
        results = {
            "customers": [],
            "products": [],
            "prices": [],
            "payment_intents": [],
            "subscriptions": [],
            "invoices": [],
            "coupons": [],
            "payment_links": [],
            "disputes": []
        }
        
        try:
            # Seed customers first
            logger.info("👥 Seeding customers...")
            results["customers"] = await self.seed_customers()
            logger.info(f"✅ Created {len(results['customers'])} customers")
            
            # Seed products and prices
            logger.info("📦 Seeding products...")
            results["products"] = await self.seed_products()
            results["prices"] = await self.seed_prices(results["products"])
            logger.info(f"✅ Created {len(results['products'])} products and {len(results['prices'])} prices")
            
            # Seed coupons
            logger.info("🎫 Seeding coupons...")
            results["coupons"] = await self.seed_coupons()
            logger.info(f"✅ Created {len(results['coupons'])} coupons")
            
            # Seed payment intents
            logger.info("💳 Seeding payment intents...")
            results["payment_intents"] = await self.seed_payment_intents(results["customers"])
            logger.info(f"✅ Created {len(results['payment_intents'])} payment intents")
            
            # Seed subscriptions
            logger.info("🔄 Seeding subscriptions...")
            results["subscriptions"] = await self.seed_subscriptions(results["customers"], results["prices"])
            logger.info(f"✅ Created {len(results['subscriptions'])} subscriptions")
            
            # Seed invoices
            logger.info("📄 Seeding invoices...")
            results["invoices"] = await self.seed_invoices(results["customers"], results["prices"])
            logger.info(f"✅ Created {len(results['invoices'])} invoices")
            
            # Seed payment links
            logger.info("🔗 Seeding payment links...")
            results["payment_links"] = await self.seed_payment_links(results["prices"])
            logger.info(f"✅ Created {len(results['payment_links'])} payment links")
            
            total_items = sum(len(v) for v in results.values() if isinstance(v, list))
            logger.info(f"🎉 All Stripe data seeded successfully! Total items: {total_items}")
            
            return {
                "success": True,
                "message": "All Stripe data seeded successfully",
                "data": results
            }
            
        except Exception as e:
            logger.error(f"❌ Error seeding Stripe data: {str(e)}")
            return {
                "success": False,
                "message": f"Error seeding data: {str(e)}",
                "data": results
            }

    async def seed_customers(self) -> List[Dict[str, Any]]:
        """Create test customers"""
        customers = []
        test_customers = [
            {"name": "John Doe", "email": "<EMAIL>"},
            {"name": "Jane Smith", "email": "<EMAIL>"},
            {"name": "Bob Johnson", "email": "<EMAIL>"},
            {"name": "Alice Wilson", "email": "<EMAIL>"},
            {"name": "Charlie Brown", "email": "<EMAIL>"},
        ]
        
        for customer_data in test_customers:
            try:
                customer = stripe.Customer.create(**customer_data)
                customers.append({
                    "id": customer.id,
                    "name": customer.name,
                    "email": customer.email
                })
            except Exception as e:
                logger.error(f"Error creating customer {customer_data['name']}: {str(e)}")
        
        return customers

    async def seed_products(self) -> List[Dict[str, Any]]:
        """Create test products"""
        products = []
        test_products = [
            {"name": "Basic Plan", "description": "Basic subscription plan"},
            {"name": "Premium Plan", "description": "Premium subscription plan"},
            {"name": "Enterprise Plan", "description": "Enterprise subscription plan"},
            {"name": "One-time Service", "description": "One-time service fee"},
            {"name": "Add-on Feature", "description": "Additional feature add-on"},
        ]
        
        for product_data in test_products:
            try:
                product = stripe.Product.create(**product_data)
                products.append({
                    "id": product.id,
                    "name": product.name,
                    "description": product.description
                })
            except Exception as e:
                logger.error(f"Error creating product {product_data['name']}: {str(e)}")
        
        return products

    async def seed_prices(self, products: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create test prices for products"""
        prices = []
        price_configs = [
            {"unit_amount": 999, "currency": "usd", "recurring": {"interval": "month"}},
            {"unit_amount": 2999, "currency": "usd", "recurring": {"interval": "month"}},
            {"unit_amount": 9999, "currency": "usd", "recurring": {"interval": "month"}},
            {"unit_amount": 4999, "currency": "usd"},
            {"unit_amount": 1999, "currency": "usd"},
        ]
        
        for i, product in enumerate(products):
            if i < len(price_configs):
                try:
                    price_data = {
                        "product": product["id"],
                        **price_configs[i]
                    }
                    price = stripe.Price.create(**price_data)
                    prices.append({
                        "id": price.id,
                        "product": product["id"],
                        "unit_amount": price.unit_amount,
                        "currency": price.currency
                    })
                except Exception as e:
                    logger.error(f"Error creating price for product {product['name']}: {str(e)}")
        
        return prices

    async def seed_payment_intents(self, customers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create test payment intents with auto-payment"""
        payment_intents = []
        amounts = [1000, 2500, 5000, 7500, 10000]
        
        for i, customer in enumerate(customers[:3]):  # Create for first 3 customers
            try:
                amount = amounts[i % len(amounts)]
                pi = stripe.PaymentIntent.create(
                    amount=amount,
                    currency="usd",
                    customer=customer["id"],
                    automatic_payment_methods={
                        "enabled": True,
                        "allow_redirects": "never"
                    },
                    metadata={"test_data": "true"}
                )
                
                # Auto-confirm payment intent with test card
                confirmed_pi = await self.auto_confirm_payment_intent(pi.id, customer["id"])
                
                payment_intents.append({
                    "id": confirmed_pi.id,
                    "customer": customer["id"],
                    "amount": confirmed_pi.amount,
                    "status": confirmed_pi.status
                })
            except Exception as e:
                logger.error(f"Error creating payment intent for customer {customer['name']}: {str(e)}")
        
        return payment_intents

    async def seed_subscriptions(self, customers: List[Dict[str, Any]], prices: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create test subscriptions"""
        subscriptions = []
        recurring_prices = [p for p in prices if "recurring" in str(p)]
        
        for i, customer in enumerate(customers[:3]):  # Create for first 3 customers
            if i < len(recurring_prices):
                try:
                    subscription = stripe.Subscription.create(
                        customer=customer["id"],
                        items=[{"price": recurring_prices[i]["id"]}],
                        metadata={"test_data": "true"}
                    )
                    subscriptions.append({
                        "id": subscription.id,
                        "customer": customer["id"],
                        "status": subscription.status
                    })
                except Exception as e:
                    logger.error(f"Error creating subscription for customer {customer['name']}: {str(e)}")
        
        return subscriptions

    async def seed_invoices(self, customers: List[Dict[str, Any]], prices: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create test invoices"""
        invoices = []
        
        for i, customer in enumerate(customers[:2]):  # Create for first 2 customers
            try:
                # Create invoice
                invoice = stripe.Invoice.create(
                    customer=customer["id"],
                    metadata={"test_data": "true"}
                )
                
                # Add invoice item if we have prices
                if prices:
                    price = prices[i % len(prices)]
                    stripe.InvoiceItem.create(
                        customer=customer["id"],
                        amount=price["unit_amount"],
                        currency="usd",
                        invoice=invoice.id,
                        description=f"Service item"
                    )
                
                # Finalize invoice
                finalized_invoice = stripe.Invoice.finalize_invoice(invoice.id)
                
                # Auto-pay the invoice with test payment method
                paid_invoice = await self.auto_pay_invoice(finalized_invoice.id, customer["id"])
                
                invoices.append({
                    "id": paid_invoice.id,
                    "customer": customer["id"],
                    "status": paid_invoice.status,
                    "total": paid_invoice.total,
                    "auto_paid": paid_invoice.status == "paid"
                })
            except Exception as e:
                logger.error(f"Error creating invoice for customer {customer['name']}: {str(e)}")
        
        return invoices

    async def seed_coupons(self) -> List[Dict[str, Any]]:
        """Create test coupons"""
        coupons = []
        coupon_configs = [
            {"name": "10% Off", "percent_off": 10, "duration": "once"},
            {"name": "20% Off Monthly", "percent_off": 20, "duration": "repeating", "duration_in_months": 3},
            {"name": "$5 Off", "amount_off": 500, "currency": "usd", "duration": "once"},
            {"name": "Forever 15%", "percent_off": 15, "duration": "forever"},
            {"name": "25% Black Friday", "percent_off": 25, "duration": "once"},
            {"name": "$10 Welcome Bonus", "amount_off": 1000, "currency": "usd", "duration": "once"},
            {"name": "30% Student Discount", "percent_off": 30, "duration": "forever"},
            {"name": "First Month Free", "percent_off": 100, "duration": "repeating", "duration_in_months": 1},
            {"name": "$20 Referral Credit", "amount_off": 2000, "currency": "usd", "duration": "once"},
            {"name": "50% Early Bird", "percent_off": 50, "duration": "once"},
        ]
        
        for coupon_data in coupon_configs:
            try:
                coupon = stripe.Coupon.create(**coupon_data)
                coupons.append({
                    "id": coupon.id,
                    "name": coupon_data["name"],
                    "percent_off": coupon.percent_off,
                    "amount_off": coupon.amount_off
                })
            except Exception as e:
                logger.error(f"Error creating coupon {coupon_data['name']}: {str(e)}")
        
        return coupons

    async def create_test_payment_method(self, customer_id: str) -> str:
        """Create a test payment method using Stripe test tokens"""
        try:
            # Use Stripe's test payment method tokens instead of raw card numbers
            test_payment_methods = [
                "pm_card_visa",           # Visa test card
                "pm_card_mastercard",     # Mastercard test card
                "pm_card_amex",          # American Express test card
                "pm_card_discover"       # Discover test card
            ]
            
            # Choose a random test payment method
            test_pm_id = random.choice(test_payment_methods)
            
            # Retrieve the test payment method
            payment_method = stripe.PaymentMethod.retrieve(test_pm_id)
            
            # Attach to customer
            payment_method.attach(customer=customer_id)
            
            logger.info(f"✅ Created test payment method {payment_method.id} for customer {customer_id}")
            return payment_method.id
            
        except Exception as e:
            logger.error(f"❌ Error creating test payment method: {str(e)}")
            raise e

    async def auto_confirm_payment_intent(self, payment_intent_id: str, customer_id: str) -> Any:
        """Auto-confirm payment intent with test payment method"""
        try:
            # Create test payment method for customer
            payment_method_id = await self.create_test_payment_method(customer_id)
            
            # Confirm payment intent with the payment method
            confirmed_pi = stripe.PaymentIntent.confirm(
                payment_intent_id,
                payment_method=payment_method_id
            )
            
            logger.info(f"💳 Auto-confirmed payment intent {payment_intent_id} with status: {confirmed_pi.status}")
            return confirmed_pi
            
        except Exception as e:
            logger.error(f"❌ Error auto-confirming payment intent {payment_intent_id}: {str(e)}")
            # Return original payment intent if confirmation fails
            return stripe.PaymentIntent.retrieve(payment_intent_id)

    async def auto_pay_invoice(self, invoice_id: str, customer_id: str) -> Any:
        """Auto-pay invoice using test payment method"""
        try:
            # Get the invoice
            invoice = stripe.Invoice.retrieve(invoice_id)
            
            if invoice.status != "open":
                logger.warning(f"Invoice {invoice_id} is not in 'open' status, cannot pay")
                return invoice
            
            # Create test payment method for customer if not exists
            payment_method_id = await self.create_test_payment_method(customer_id)
            
            # Pay the invoice
            paid_invoice = stripe.Invoice.pay(
                invoice_id,
                payment_method=payment_method_id
            )
            
            logger.info(f"💰 Auto-paid invoice {invoice_id} with status: {paid_invoice.status}")
            return paid_invoice
            
        except Exception as e:
            logger.error(f"❌ Error auto-paying invoice {invoice_id}: {str(e)}")
            # Return original invoice if payment fails
            return stripe.Invoice.retrieve(invoice_id)

    async def seed_payment_intents_with_auto_payment(self, customers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create and auto-confirm payment intents"""
        payment_intents = []
        amounts = [1500, 2500, 3500, 5000, 7500, 10000, 15000]
        
        for i, customer in enumerate(customers):
            try:
                amount = random.choice(amounts)
                
                # Create payment intent
                pi = stripe.PaymentIntent.create(
                    amount=amount,
                    currency="usd",
                    customer=customer["id"],
                    automatic_payment_methods={
                        "enabled": True,
                        "allow_redirects": "never"
                    },
                    metadata={
                        "test_data": "true",
                        "auto_payment": "true"
                    }
                )
                
                # Auto-confirm payment
                confirmed_pi = await self.auto_confirm_payment_intent(pi.id, customer["id"])
                
                payment_intents.append({
                    "id": confirmed_pi.id,
                    "customer": customer["id"],
                    "customer_name": customer.get("name", "Unknown"),
                    "amount": confirmed_pi.amount,
                    "status": confirmed_pi.status,
                    "auto_paid": confirmed_pi.status == "succeeded"
                })
                
            except Exception as e:
                logger.error(f"Error creating auto-payment intent for customer {customer.get('name', 'Unknown')}: {str(e)}")
        
        return payment_intents

    async def seed_payment_links(self, prices: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create test payment links"""
        payment_links = []
        
        if not prices:
            logger.warning("No prices available for payment link creation")
            return payment_links
        
        link_configs = [
            {"name": "Quick Purchase Link", "description": "Fast checkout for premium products"},
            {"name": "Donation Link", "description": "Support our cause"},
            {"name": "Subscription Link", "description": "Start your subscription today"},
            {"name": "Product Sale Link", "description": "Limited time offer"},
            {"name": "Service Payment Link", "description": "Pay for professional services"},
        ]
        
        for i, link_config in enumerate(link_configs):
            if i < len(prices):
                try:
                    price = prices[i]
                    payment_link = stripe.PaymentLink.create(
                        line_items=[{
                            "price": price["id"],
                            "quantity": 1,
                        }],
                        metadata={
                            "test_data": "true",
                            "link_type": link_config["name"]
                        }
                    )
                    payment_links.append({
                        "id": payment_link.id,
                        "url": payment_link.url,
                        "price": price["id"],
                        "name": link_config["name"],
                        "active": payment_link.active
                    })
                except Exception as e:
                    logger.error(f"Error creating payment link {link_config['name']}: {str(e)}")
        
        return payment_links

    async def seed_invoices_for_different_customers(self, customers: List[Dict[str, Any]], prices: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create invoices for different customers with various scenarios"""
        invoices = []
        
        if not customers or not prices:
            logger.warning("No customers or prices available for invoice creation")
            return invoices
        
        # Create invoices for all customers, not just the first 2
        for i, customer in enumerate(customers):
            try:
                # Create invoice
                invoice = stripe.Invoice.create(
                    customer=customer["id"],
                    metadata={
                        "test_data": "true",
                        "customer_type": "diverse_customer",
                        "invoice_scenario": f"scenario_{i+1}"
                    }
                )
                
                # Add multiple invoice items for variety
                num_items = random.randint(1, 3)
                for item_num in range(num_items):
                    price = random.choice(prices)
                    quantity = random.randint(1, 2)
                    stripe.InvoiceItem.create(
                        customer=customer["id"],
                        amount=price["unit_amount"] * quantity,
                        currency="usd",
                        invoice=invoice.id,
                        description=f"Service item {item_num+1} for {customer['name']}"
                    )
                
                # Add random discount for some invoices
                if random.choice([True, False]):
                    # Get existing coupons
                    existing_coupons = stripe.Coupon.list(limit=3).data
                    if existing_coupons:
                        discount_coupon = random.choice(existing_coupons)
                        try:
                            invoice.modify(discount={
                                "coupon": discount_coupon.id
                            })
                        except Exception as e:
                            logger.warning(f"Could not apply discount to invoice: {str(e)}")
                
                # Finalize invoice
                finalized_invoice = stripe.Invoice.finalize_invoice(invoice.id)
                
                # Auto-pay the invoice with test payment method
                paid_invoice = await self.auto_pay_invoice(finalized_invoice.id, customer["id"])
                
                invoices.append({
                    "id": paid_invoice.id,
                    "customer": customer["id"],
                    "customer_name": customer["name"],
                    "status": paid_invoice.status,
                    "total": paid_invoice.total,
                    "items_count": num_items,
                    "has_discount": hasattr(paid_invoice, 'discount') and paid_invoice.discount is not None,
                    "auto_paid": paid_invoice.status == "paid"
                })
            except Exception as e:
                logger.error(f"Error creating invoice for customer {customer['name']}: {str(e)}")
        
        return invoices

    async def cleanup_all(self) -> Dict[str, Any]:
        """Clean up all test data"""
        logger.info("🧹 Starting cleanup of all test data...")
        
        try:
            cleanup_results = {
                "customers_deleted": 0,
                "products_deleted": 0,
                "coupons_deleted": 0,
                "subscriptions_cancelled": 0,
                "payment_links_deactivated": 0
            }
            
            # Cancel subscriptions
            logger.info("🔄 Cancelling subscriptions...")
            subscriptions = stripe.Subscription.list(limit=100)
            for sub in subscriptions.data:
                if sub.metadata.get("test_data") == "true":
                    stripe.Subscription.delete(sub.id)
                    cleanup_results["subscriptions_cancelled"] += 1
            
            # Delete customers
            logger.info("👥 Deleting customers...")
            customers = stripe.Customer.list(limit=100)
            for customer in customers.data:
                stripe.Customer.delete(customer.id)
                cleanup_results["customers_deleted"] += 1
            
            # Delete products
            logger.info("📦 Deleting products...")
            products = stripe.Product.list(limit=100)
            for product in products.data:
                product.delete()
                cleanup_results["products_deleted"] += 1
            
            # Delete coupons
            logger.info("🎫 Deleting coupons...")
            coupons = stripe.Coupon.list(limit=100)
            for coupon in coupons.data:
                coupon.delete()
                cleanup_results["coupons_deleted"] += 1
            
            # Deactivate payment links (cannot delete them)
            logger.info("🔗 Deactivating payment links...")
            payment_links = stripe.PaymentLink.list(limit=100)
            for link in payment_links.data:
                if link.metadata.get("test_data") == "true":
                    stripe.PaymentLink.modify(link.id, active=False)
                    cleanup_results["payment_links_deactivated"] += 1
            
            total_deleted = sum(cleanup_results.values())
            logger.info(f"✅ Cleanup completed successfully! Processed {total_deleted} items")
            logger.info(f"   🔄 {cleanup_results['subscriptions_cancelled']} subscriptions cancelled")
            logger.info(f"   👥 {cleanup_results['customers_deleted']} customers deleted")
            logger.info(f"   📦 {cleanup_results['products_deleted']} products deleted")
            logger.info(f"   🎫 {cleanup_results['coupons_deleted']} coupons deleted")
            logger.info(f"   🔗 {cleanup_results['payment_links_deactivated']} payment links deactivated")
            
            return {
                "success": True,
                "message": "Cleanup completed successfully",
                "data": cleanup_results
            }
            
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {str(e)}")
            return {
                "success": False,
                "message": f"Error during cleanup: {str(e)}"
            }

    async def seed_realtime_data(self) -> Dict[str, Any]:
        """Seed new realtime data with current timestamps"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results = {
                "timestamp": timestamp,
                "customers": [],
                "payment_intents": [],
                "invoices": [],
                "payment_links": [],
                "coupons": [],
                "events": []
            }
            
            # Create new customers with timestamp
            new_customers = await self.seed_realtime_customers(timestamp)
            results["customers"] = new_customers
            
            # Create payment intents for existing customers
            existing_customers = stripe.Customer.list(limit=10).data
            if existing_customers:
                new_payment_intents = await self.seed_realtime_payment_intents(existing_customers[:3], timestamp)
                results["payment_intents"] = new_payment_intents
            
            # Create invoices for existing customers
            if existing_customers:
                new_invoices = await self.seed_realtime_invoices(existing_customers[:2], timestamp)
                results["invoices"] = new_invoices
            
            # Create realtime payment links
            existing_prices = stripe.Price.list(limit=5).data
            if existing_prices:
                price_data = [{"id": p.id, "unit_amount": p.unit_amount} for p in existing_prices]
                new_payment_links = await self.seed_realtime_payment_links(price_data[:2], timestamp)
                results["payment_links"] = new_payment_links
            
            # Create realtime coupons
            new_coupons = await self.seed_realtime_coupons(timestamp)
            results["coupons"] = new_coupons
            
            # Simulate some activity events
            results["events"] = await self.create_activity_events(timestamp)
            
            return {
                "success": True,
                "message": f"Realtime data seeded at {timestamp}",
                "data": results
            }
            
        except Exception as e:
            logger.error(f"Error seeding realtime data: {str(e)}")
            return {
                "success": False,
                "message": f"Error seeding realtime data: {str(e)}"
            }

    async def seed_realtime_customers(self, timestamp: str) -> List[Dict[str, Any]]:
        """Create new customers with timestamp"""
        customers = []
        
        # Generate random customer names
        first_names = ["Alex", "Jordan", "Casey", "Taylor", "Morgan", "Riley", "Avery", "Quinn"]
        last_names = ["Johnson", "Williams", "Brown", "Davis", "Miller", "Wilson", "Moore", "Jackson"]
        
        # Create 2-3 new customers
        num_customers = random.randint(2, 3)
        
        for i in range(num_customers):
            first_name = random.choice(first_names)
            last_name = random.choice(last_names)
            name = f"{first_name} {last_name}"
            email = f"{first_name.lower()}.{last_name.lower()}.{timestamp}@example.com"
            
            try:
                customer = stripe.Customer.create(
                    name=name,
                    email=email,
                    metadata={
                        "test_data": "true",
                        "seed_timestamp": timestamp,
                        "realtime_seed": "true"
                    }
                )
                customers.append({
                    "id": customer.id,
                    "name": customer.name,
                    "email": customer.email,
                    "created_at": timestamp
                })
            except Exception as e:
                logger.error(f"Error creating realtime customer {name}: {str(e)}")
        
        return customers

    async def seed_realtime_payment_intents(self, customers: List[Any], timestamp: str) -> List[Dict[str, Any]]:
        """Create new payment intents with current timestamp"""
        payment_intents = []
        amounts = [1500, 2500, 3500, 4500, 5500, 7500, 10000, 15000]
        
        for customer in customers:
            try:
                amount = random.choice(amounts)
                pi = stripe.PaymentIntent.create(
                    amount=amount,
                    currency="usd",
                    customer=customer.id,
                    automatic_payment_methods={
                        "enabled": True,
                        "allow_redirects": "never"
                    },
                    metadata={
                        "test_data": "true",
                        "seed_timestamp": timestamp,
                        "realtime_seed": "true"
                    }
                )
                payment_intents.append({
                    "id": pi.id,
                    "customer": customer.id,
                    "amount": pi.amount,
                    "status": pi.status,
                    "created_at": timestamp
                })
            except Exception as e:
                logger.error(f"Error creating realtime payment intent: {str(e)}")
        
        return payment_intents

    async def seed_realtime_invoices(self, customers: List[Any], timestamp: str) -> List[Dict[str, Any]]:
        """Create new invoices with current timestamp"""
        invoices = []
        
        # Get existing prices for invoice items
        prices = stripe.Price.list(limit=10).data
        if not prices:
            logger.warning("No prices available for invoice creation")
            return invoices
        
        for customer in customers:
            try:
                # Create invoice
                invoice = stripe.Invoice.create(
                    customer=customer.id,
                    metadata={
                        "test_data": "true",
                        "seed_timestamp": timestamp,
                        "realtime_seed": "true"
                    }
                )
                
                # Add random invoice item using amount instead of price
                price = random.choice(prices)
                stripe.InvoiceItem.create(
                    customer=customer.id,
                    amount=price["unit_amount"],
                    currency="usd",
                    invoice=invoice.id,
                    description=f"Service charge"
                )
                
                # Finalize invoice
                finalized_invoice = stripe.Invoice.finalize_invoice(invoice.id)
                
                # Auto-pay the invoice with test payment method
                paid_invoice = await self.auto_pay_invoice(finalized_invoice.id, customer.id)
                
                invoices.append({
                    "id": paid_invoice.id,
                    "customer": customer.id,
                    "status": paid_invoice.status,
                    "total": paid_invoice.total,
                    "created_at": timestamp,
                    "auto_paid": paid_invoice.status == "paid"
                })
            except Exception as e:
                logger.error(f"Error creating realtime invoice: {str(e)}")
        
        return invoices

    async def create_activity_events(self, timestamp: str) -> List[Dict[str, Any]]:
        """Create simulated activity events"""
        events = []
        
        event_types = [
            "customer.created",
            "payment_intent.created", 
            "invoice.created",
            "invoice.finalized",
            "charge.succeeded"
        ]
        
        # Create 3-5 simulated events
        num_events = random.randint(3, 5)
        
        for i in range(num_events):
            event = {
                "id": f"evt_{uuid.uuid4().hex[:24]}",
                "type": random.choice(event_types),
                "created": int(datetime.now().timestamp()),
                "timestamp": timestamp,
                "simulated": True
            }
            events.append(event)
        
        return events

    async def seed_realtime_payment_links(self, prices: List[Dict[str, Any]], timestamp: str) -> List[Dict[str, Any]]:
        """Create new payment links with current timestamp"""
        payment_links = []
        
        link_types = ["Flash Sale", "Quick Buy", "Limited Offer", "Express Checkout"]
        
        for i, price in enumerate(prices):
            if i < len(link_types):
                try:
                    payment_link = stripe.PaymentLink.create(
                        line_items=[{
                            "price": price["id"],
                            "quantity": 1,
                        }],
                        metadata={
                            "test_data": "true",
                            "seed_timestamp": timestamp,
                            "realtime_seed": "true",
                            "link_type": link_types[i]
                        }
                    )
                    payment_links.append({
                        "id": payment_link.id,
                        "url": payment_link.url,
                        "price": price["id"],
                        "type": link_types[i],
                        "created_at": timestamp
                    })
                except Exception as e:
                    logger.error(f"Error creating realtime payment link: {str(e)}")
        
        return payment_links

    async def seed_realtime_coupons(self, timestamp: str) -> List[Dict[str, Any]]:
        """Create new coupons with current timestamp"""
        coupons = []
        
        coupon_types = [
            {"name": f"Flash {timestamp}", "percent_off": random.randint(10, 30), "duration": "once"},
            {"name": f"Limited {timestamp}", "amount_off": random.choice([500, 1000, 1500]), "currency": "usd", "duration": "once"},
        ]
        
        for coupon_data in coupon_types:
            try:
                coupon = stripe.Coupon.create(
                    **coupon_data,
                    metadata={
                        "test_data": "true",
                        "seed_timestamp": timestamp,
                        "realtime_seed": "true"
                    }
                )
                coupons.append({
                    "id": coupon.id,
                    "name": coupon_data["name"],
                    "percent_off": coupon.percent_off,
                    "amount_off": coupon.amount_off,
                    "created_at": timestamp
                })
            except Exception as e:
                logger.error(f"Error creating realtime coupon: {str(e)}")
        
        return coupons