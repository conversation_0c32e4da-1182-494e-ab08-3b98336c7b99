"""
Stripe seeding routes
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any
import logging
from services.stripe_seeder import StripeSeeder
from utils.globals import get_scheduler, set_scheduler

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/seed", tags=["seeding"])

class SeedResponse(BaseModel):
    success: bool
    message: str
    data: Dict[str, Any] = {}

@router.post("/all", response_model=SeedResponse)
async def seed_all_data():
    """Seed all Stripe sandbox data"""
    try:
        seeder = StripeSeeder()
        result = await seeder.seed_all()
        
        if result["success"]:
            return SeedResponse(**result)
        else:
            raise HTTPException(status_code=500, detail=result["message"])
            
    except ValueError as e:
        logger.error(f"Configuration error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error seeding data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/customers", response_model=SeedResponse)
async def seed_customers():
    """Seed test customers only"""
    try:
        seeder = StripeSeeder()
        customers = await seeder.seed_customers()
        
        return SeedResponse(
            success=True,
            message=f"Successfully created {len(customers)} customers",
            data={"customers": customers}
        )
        
    except Exception as e:
        logger.error(f"Error seeding customers: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/products", response_model=SeedResponse)
async def seed_products():
    """Seed test products and prices"""
    try:
        seeder = StripeSeeder()
        products = await seeder.seed_products()
        prices = await seeder.seed_prices(products)
        
        return SeedResponse(
            success=True,
            message=f"Successfully created {len(products)} products and {len(prices)} prices",
            data={"products": products, "prices": prices}
        )
        
    except Exception as e:
        logger.error(f"Error seeding products: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/coupons", response_model=SeedResponse)
async def seed_coupons():
    """Seed test coupons"""
    try:
        seeder = StripeSeeder()
        coupons = await seeder.seed_coupons()
        
        return SeedResponse(
            success=True,
            message=f"Successfully created {len(coupons)} coupons",
            data={"coupons": coupons}
        )
        
    except Exception as e:
        logger.error(f"Error seeding coupons: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/payment-links", response_model=SeedResponse)
async def seed_payment_links():
    """Seed test payment links"""
    try:
        seeder = StripeSeeder()
        
        # Get existing prices for payment links
        import stripe
        prices = stripe.Price.list(limit=10).data
        if not prices:
            raise HTTPException(status_code=400, detail="No prices available. Please seed products and prices first.")
        
        price_data = [{"id": p.id, "unit_amount": p.unit_amount} for p in prices]
        payment_links = await seeder.seed_payment_links(price_data)
        
        return SeedResponse(
            success=True,
            message=f"Successfully created {len(payment_links)} payment links",
            data={"payment_links": payment_links}
        )
        
    except Exception as e:
        logger.error(f"Error seeding payment links: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/invoices-diverse", response_model=SeedResponse)
async def seed_diverse_invoices():
    """Seed invoices for all customers with various scenarios"""
    try:
        seeder = StripeSeeder()
        
        # Get existing customers and prices
        import stripe
        customers = stripe.Customer.list(limit=10).data
        prices = stripe.Price.list(limit=10).data
        
        if not customers:
            raise HTTPException(status_code=400, detail="No customers available. Please seed customers first.")
        if not prices:
            raise HTTPException(status_code=400, detail="No prices available. Please seed products and prices first.")
        
        customer_data = [{"id": c.id, "name": c.name} for c in customers]
        price_data = [{"id": p.id, "unit_amount": p.unit_amount} for p in prices]
        
        invoices = await seeder.seed_invoices_for_different_customers(customer_data, price_data)
        
        return SeedResponse(
            success=True,
            message=f"Successfully created {len(invoices)} diverse invoices",
            data={"invoices": invoices}
        )
        
    except Exception as e:
        logger.error(f"Error seeding diverse invoices: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/cleanup", response_model=SeedResponse)
async def cleanup_test_data():
    """Clean up all test data from Stripe"""
    try:
        seeder = StripeSeeder()
        result = await seeder.cleanup_all()
        
        if result["success"]:
            return SeedResponse(**result)
        else:
            raise HTTPException(status_code=500, detail=result["message"])
            
    except Exception as e:
        logger.error(f"Error during cleanup: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/realtime", response_model=SeedResponse)
async def seed_realtime():
    """Manually trigger realtime data seeding"""
    try:
        seeder = StripeSeeder()
        result = await seeder.seed_realtime_data()
        
        if result["success"]:
            return SeedResponse(**result)
        else:
            raise HTTPException(status_code=500, detail=result["message"])
            
    except Exception as e:
        logger.error(f"Error seeding realtime data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/scheduler")
async def get_scheduler_status():
    """Get scheduler status and information"""
    try:
        scheduler = get_scheduler()
        if scheduler:
            return scheduler.get_scheduler_info()
        else:
            return {
                "running": False,
                "message": "Scheduler not initialized"
            }
    except Exception as e:
        logger.error(f"Error getting scheduler status: {str(e)}")
        return {
            "running": False,
            "error": str(e)
        }

@router.post("/scheduler/start")
async def start_scheduler():
    """Start the auto-seeding scheduler"""
    try:
        scheduler = get_scheduler()
        if not scheduler:
            from services.scheduler import SeedScheduler
            scheduler = SeedScheduler()
            set_scheduler(scheduler)
        
        if not scheduler.is_running:
            scheduler.start_scheduler()
            return {"success": True, "message": "Scheduler started"}
        else:
            return {"success": False, "message": "Scheduler already running"}
            
    except Exception as e:
        logger.error(f"Error starting scheduler: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/scheduler/stop")
async def stop_scheduler():
    """Stop the auto-seeding scheduler"""
    try:
        scheduler = get_scheduler()
        if scheduler and scheduler.is_running:
            scheduler.stop_scheduler()
            return {"success": True, "message": "Scheduler stopped"}
        else:
            return {"success": False, "message": "Scheduler not running"}
            
    except Exception as e:
        logger.error(f"Error stopping scheduler: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/auto-payments", response_model=SeedResponse)
async def seed_auto_payments():
    """Create payment intents with auto-payment using test cards"""
    try:
        seeder = StripeSeeder()
        
        # Get existing customers
        import stripe
        customers = stripe.Customer.list(limit=10).data
        
        if not customers:
            raise HTTPException(status_code=400, detail="No customers available. Please seed customers first.")
        
        customer_data = [{"id": c.id, "name": c.name} for c in customers]
        payment_intents = await seeder.seed_payment_intents_with_auto_payment(customer_data)
        
        return SeedResponse(
            success=True,
            message=f"Successfully created and auto-paid {len(payment_intents)} payment intents",
            data={"payment_intents": payment_intents}
        )
        
    except Exception as e:
        logger.error(f"Error seeding auto-payments: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/balance-summary")
async def get_balance_summary():
    """Get summary of payments and balance tracking"""
    try:
        import stripe
        
        # Get successful payment intents
        payment_intents = stripe.PaymentIntent.list(limit=100).data
        successful_payments = [pi for pi in payment_intents if pi.status == "succeeded"]
        
        # Get paid invoices
        invoices = stripe.Invoice.list(limit=100).data
        paid_invoices = [inv for inv in invoices if inv.status == "paid"]
        
        # Calculate totals
        total_payment_intents_amount = sum(pi.amount for pi in successful_payments)
        total_invoices_amount = sum(inv.total for inv in paid_invoices)
        total_balance = total_payment_intents_amount + total_invoices_amount
        
        return {
            "success": True,
            "balance_summary": {
                "successful_payment_intents": len(successful_payments),
                "paid_invoices": len(paid_invoices),
                "total_payment_intents_amount": total_payment_intents_amount,
                "total_invoices_amount": total_invoices_amount,
                "total_balance_cents": total_balance,
                "total_balance_usd": f"${total_balance / 100:.2f}",
                "currency": "usd"
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting balance summary: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/status")
async def get_seed_status():
    """Get current seeding status and Stripe connection"""
    try:
        seeder = StripeSeeder()
        # Test Stripe connection by trying to list customers
        import stripe
        customers = stripe.Customer.list(limit=1)
        
        # Get scheduler info
        scheduler_info = {}
        scheduler = get_scheduler()
        if scheduler:
            scheduler_info = scheduler.get_scheduler_info()
        
        return {
            "stripe_connected": True,
            "api_key_type": "test" if stripe.api_key.startswith("sk_test_") else "live",
            "customers_count": len(stripe.Customer.list(limit=100).data),
            "products_count": len(stripe.Product.list(limit=100).data),
            "scheduler": scheduler_info
        }
        
    except Exception as e:
        logger.error(f"Error checking status: {str(e)}")
        return {
            "stripe_connected": False,
            "error": str(e),
            "scheduler": {"running": False}
        }