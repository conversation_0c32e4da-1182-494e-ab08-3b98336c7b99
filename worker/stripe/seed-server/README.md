# Stripe Seed Server

FastAPI application for seeding Stripe sandbox data for testing and development.

## Features

- **Auto-scheduling**: Automatically seeds new realtime data every 10 minutes
- **Realtime data generation**: Creates new customers, payment intents, invoices with timestamps
- **Auto-payment**: Automatically completes payments using secure Stripe test tokens
- **Balance tracking**: Track successful payments and balance increases
- Seed comprehensive Stripe test data including:
  - Customers
  - Products and Prices
  - Payment Intents (with auto-payment)
  - Subscriptions
  - Invoices (with auto-payment)
  - Coupons
  - Payment Links
- Individual seeding endpoints for specific data types
- Cleanup functionality to remove test data
- Scheduler management endpoints
- Health check endpoints
- Docker support

## Setup

1. Copy environment variables:
```bash
cp .env.example .env
```

2. Set your Stripe test key in `.env`:
```
STRIPE_SECRET_KEY=sk_test_your_stripe_test_key_here
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

## Running

### Local Development
```bash
python main.py
```

### Docker
```bash
docker compose up -d
```

## API Endpoints

### Health Check
- `GET /` - Root endpoint
- `GET /health` - Health check
- `GET /seed/status` - Stripe connection status

### Seeding Operations
- `POST /seed/all` - Seed all test data (with auto-payment)
- `POST /seed/customers` - Seed customers only
- `POST /seed/products` - Seed products and prices only
- `POST /seed/coupons` - Seed coupons only (10 different types)
- `POST /seed/payment-links` - Seed payment links
- `POST /seed/invoices-diverse` - Seed invoices for all customers with various scenarios (with auto-payment)
- `POST /seed/auto-payments` - Create payment intents with auto-payment using test cards
- `POST /seed/realtime` - Manually trigger realtime data seeding (with auto-payment)
- `DELETE /seed/cleanup` - Clean up all test data

### Balance & Payment Tracking
- `GET /seed/balance-summary` - Get summary of successful payments and balance tracking

### Scheduler Management
- `GET /seed/scheduler` - Get scheduler status
- `POST /seed/scheduler/start` - Start auto-seeding scheduler
- `POST /seed/scheduler/stop` - Stop auto-seeding scheduler

## Usage Examples

### Seed all data:
```bash
curl -X POST http://localhost:8001/seed/all
```

### Check status:
```bash
curl http://localhost:8001/seed/status
```

### Trigger realtime seeding:
```bash
curl -X POST http://localhost:8001/seed/realtime
```

### Seed payment links:
```bash
curl -X POST http://localhost:8001/seed/payment-links
```

### Seed diverse invoices:
```bash
curl -X POST http://localhost:8001/seed/invoices-diverse
```

### Seed more coupons:
```bash
curl -X POST http://localhost:8001/seed/coupons
```

### Check scheduler status:
```bash
curl http://localhost:8001/seed/scheduler
```

### Start/stop scheduler:
```bash
# Start scheduler
curl -X POST http://localhost:8001/seed/scheduler/start

# Stop scheduler  
curl -X POST http://localhost:8001/seed/scheduler/stop
```

### Create auto-payments:
```bash
curl -X POST http://localhost:8001/seed/auto-payments
```

### Check balance summary:
```bash
curl http://localhost:8001/seed/balance-summary
```

### Clean up test data:
```bash
curl -X DELETE http://localhost:8001/seed/cleanup
```

## Safety Features

- Only works with Stripe test keys (`sk_test_*`)
- All created data is tagged with metadata for easy identification
- Auto-payment uses secure Stripe test payment method tokens
- Test payment methods are automatically created and attached to customers
- Comprehensive error handling and logging
- Cleanup functionality to remove test data

## Auto-Payment Features

The seed server automatically completes payments using Stripe's test payment method tokens:

- **Test Payment Methods**: Uses secure test tokens (pm_card_visa, pm_card_mastercard, etc.)
- **Safe Testing**: No raw card numbers - uses Stripe's recommended test tokens
- **Payment Methods**: Automatically created and attached to customers
- **Payment Intents**: Auto-confirmed using test payment methods
- **Invoices**: Auto-paid after finalization
- **Balance Tracking**: Track successful payments and total balance increases

## Port

Default port: 8001 (different from sync-server which uses 8000)