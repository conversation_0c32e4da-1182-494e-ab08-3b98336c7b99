"""
Configuration settings for block-bot
"""

import os
from typing import Optional

class Config:
    """Application configuration"""
    
    @classmethod
    def _get_database_url(cls) -> str:
        return os.getenv("DATABASE_URL", "")
    
    @classmethod
    def _get_rdr_block_fee(cls) -> int:
        return int(os.getenv("RDR_BLOCK_FEE", "2500"))
    
    @classmethod
    def _get_ethoca_block_fee(cls) -> int:
        return int(os.getenv("ETHOCA_BLOCK_FEE", "2500"))
    
    @classmethod
    def _get_sync_interval_minutes(cls) -> int:
        return int(os.getenv("SYNC_INTERVAL_MINUTES", "10"))
    
    @classmethod
    def _get_shopify_api_version(cls) -> str:
        return os.getenv("SHOPIFY_API_VERSION", "2024-01")
    
    @classmethod
    def _get_stripe_api_key(cls) -> Optional[str]:
        return os.getenv("STRIPE_API_KEY")
    
    @classmethod
    def _get_process_interval_minutes(cls) -> int:
        return int(os.getenv("PROCESS_INTERVAL_MINUTES", "5"))
    
    @classmethod
    def _get_max_retries(cls) -> int:
        return int(os.getenv("MAX_RETRIES", "3"))
    
    @classmethod
    def _get_retry_delay_minutes(cls) -> int:
        return int(os.getenv("RETRY_DELAY_MINUTES", "5"))
    
    @classmethod
    def _get_log_level(cls) -> str:
        return os.getenv("LOG_LEVEL", "INFO")
    
    @classmethod
    def _get_oauth_base_url(cls) -> str:
        return os.getenv("OAUTH_BASE_URL", "")
    
    @classmethod
    def _get_oauth_client_id(cls) -> Optional[str]:
        return os.getenv("OAUTH_CLIENT_ID")
    
    @classmethod
    def _get_oauth_client_secret(cls) -> Optional[str]:
        return os.getenv("OAUTH_CLIENT_SECRET")
    
    # Properties that read from environment at runtime
    @property
    def DATABASE_URL(self) -> str:
        return self._get_database_url()
    
    @property
    def RDR_BLOCK_FEE(self) -> int:
        return self._get_rdr_block_fee()
    
    @property
    def ETHOCA_BLOCK_FEE(self) -> int:
        return self._get_ethoca_block_fee()
    
    @property
    def SYNC_INTERVAL_MINUTES(self) -> int:
        return self._get_sync_interval_minutes()
    
    @property
    def SHOPIFY_API_VERSION(self) -> str:
        return self._get_shopify_api_version()
    
    @property
    def STRIPE_API_KEY(self) -> Optional[str]:
        return self._get_stripe_api_key()
    
    @property
    def PROCESS_INTERVAL_MINUTES(self) -> int:
        return self._get_process_interval_minutes()
    
    @property
    def MAX_RETRIES(self) -> int:
        return self._get_max_retries()
    
    @property
    def RETRY_DELAY_MINUTES(self) -> int:
        return self._get_retry_delay_minutes()
    
    @property
    def LOG_LEVEL(self) -> str:
        return self._get_log_level()
    
    @property
    def OAUTH_BASE_URL(self) -> str:
        return self._get_oauth_base_url()
    
    @property
    def OAUTH_CLIENT_ID(self) -> Optional[str]:
        return self._get_oauth_client_id()
    
    @property
    def OAUTH_CLIENT_SECRET(self) -> Optional[str]:
        return self._get_oauth_client_secret()
    
    def validate(self) -> None:
        """Validate required configuration"""
        if not self.DATABASE_URL:
            raise ValueError("DATABASE_URL environment variable is required")
        
        if self.PROCESS_INTERVAL_MINUTES <= 0:
            raise ValueError("PROCESS_INTERVAL_MINUTES must be greater than 0")
        
        if self.MAX_RETRIES < 0:
            raise ValueError("MAX_RETRIES must be non-negative")


# Create singleton instance
config = Config()