"""
Block Matcher Service
Handles matching blocks to linked stores based on alert_infos with filtering logic
"""
from typing import List, Dict, Any, Optional, Set, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from datetime import datetime
import logging

from models import Block, MatchedBlock, AlertInfo, LinkedStore, User
from config import db, settings


logger = logging.getLogger(__name__)

class BlockMatcherService:
    """Service for matching blocks to stores based on alert info filters"""
    
    def __init__(self, session: Session = None):
        self.session = session or db.get_session()
        self._should_close_session = session is None
    
    def _build_alert_info_conditions(self, block: Block) -> List:
        """Build conditions for matching alert_infos based on block data"""
        conditions = []
        
        # For ETHOCA alerts: match by descriptor
        if block.descriptor:
            ethoca_condition = and_(
                AlertInfo.alert_type == 'ETHOCA',
                AlertInfo.registration_status == 'EFFECTED',
                AlertInfo.descriptor == block.descriptor
            )
            conditions.append(ethoca_condition)
        
        # For RDR alerts: match by bin or caid
        rdr_conditions = []
        if block.card_bin:
            rdr_conditions.append(AlertInfo.bin == block.card_bin)
        
        if block.caid:
            rdr_conditions.append(AlertInfo.caid == block.caid)
        
        if rdr_conditions:
            rdr_condition = and_(
                AlertInfo.alert_type == 'RDR',
                AlertInfo.registration_status == 'EFFECTED',
                or_(*rdr_conditions)
            )
            conditions.append(rdr_condition)
        
        return conditions
    
    def _build_block_filter_conditions(self, filters: Dict[str, Any]) -> List:
        """Build conditions for filtering blocks based on filters dictionary"""
        conditions = []
        
        # Handle descriptor filter (ETHOCA alerts)
        if 'descriptor' in filters:
            descriptors = filters['descriptor']
            if isinstance(descriptors, list):
                conditions.append(Block.descriptor.in_(descriptors))
            else:
                conditions.append(Block.descriptor == descriptors)
        
        # Handle cardBin filter (RDR alerts)
        if 'cardBin' in filters:
            bins = filters['cardBin']
            if isinstance(bins, list):
                conditions.append(Block.card_bin.in_(bins))
            else:
                conditions.append(Block.card_bin == bins)
        
        # Handle caid filter (RDR alerts)
        if 'caid' in filters:
            caids = filters['caid']
            if isinstance(caids, list):
                conditions.append(Block.caid.in_(caids))
            else:
                conditions.append(Block.caid == caids)
        
        # Handle type filter
        if 'type' in filters:
            conditions.append(Block.type == filters['type'])
        
        return conditions
    
    def _execute_query_with_error_handling(self, query_func, error_message: str, *args, **kwargs):
        """Execute a database query with consistent error handling"""
        try:
            return query_func(*args, **kwargs)
        except Exception as e:
            logger.error(f"{error_message}: {str(e)}")
            raise
    
    
    def _bulk_upsert_matched_blocks(self, matched_blocks_data: List[Dict[str, Any]]) -> int:
        """
        Bulk upsert matched blocks using PostgreSQL ON CONFLICT with memory limits and retry
        
        Args:
            matched_blocks_data: List of dictionaries with matched block data
            
        Returns:
            Number of upserted records
        """
        if not matched_blocks_data:
            return 0
        
        # Memory safety check using settings
        if len(matched_blocks_data) > settings.MAX_UPSERT_BATCH_SIZE:
            raise ValueError(f"Batch too large ({len(matched_blocks_data)}), maximum allowed: {settings.MAX_UPSERT_BATCH_SIZE}")
        
        import time
        MAX_RETRIES = settings.MAX_RETRIES
        
        for attempt in range(MAX_RETRIES):
            try:
                return self._execute_upsert_with_monitoring(matched_blocks_data)
                
            except Exception as e:
                if attempt == MAX_RETRIES - 1:
                    logger.error(f"All {MAX_RETRIES} upsert attempts failed: {str(e)}")
                    raise
                
                wait_time = 2 ** attempt
                logger.warning(f"Upsert attempt {attempt + 1} failed, retrying in {wait_time}s: {str(e)}")
                time.sleep(wait_time)
        
        return 0

    def _execute_upsert_with_monitoring(self, matched_blocks_data: List[Dict[str, Any]]) -> int:
        """Execute upsert with performance monitoring"""
        import time
        start_time = time.time()
        
        try:
            from sqlalchemy.dialects.postgresql import insert
            
            # Use PostgreSQL's INSERT ... ON CONFLICT ... DO UPDATE
            stmt = insert(MatchedBlock.__table__)
            
            # Define the ON CONFLICT clause using column names
            # Based on the unique constraint: (id, linked_store_id, provider)
            upsert_stmt = stmt.on_conflict_do_update(
                index_elements=['id', 'linked_store_id', 'provider'],
                set_={
                    # Update all fields with latest data when conflict occurs
                    'user_id': stmt.excluded.user_id,
                    'linked_store_id': stmt.excluded.linked_store_id,
                    'provider': stmt.excluded.provider,
                    'matched_data': stmt.excluded.matched_data,
                    'alert_id': stmt.excluded.alert_id,
                    'alert_time': stmt.excluded.alert_time,
                    'alert_type': stmt.excluded.alert_type,
                    'amount': stmt.excluded.amount,
                    'currency': stmt.excluded.currency,
                    'descriptor': stmt.excluded.descriptor,
                    'auth_code': stmt.excluded.auth_code,
                    'card_bin': stmt.excluded.card_bin,
                    'card_number': stmt.excluded.card_number,
                    'chargeback_code': stmt.excluded.chargeback_code,
                    'dispute_amount': stmt.excluded.dispute_amount,
                    'dispute_currency': stmt.excluded.dispute_currency,
                    'transaction_time': stmt.excluded.transaction_time,
                    'age': stmt.excluded.age,
                    'alert_source': stmt.excluded.alert_source,
                    'arn': stmt.excluded.arn,
                    'issuer': stmt.excluded.issuer,
                    'initiated_by': stmt.excluded.initiated_by,
                    'liability': stmt.excluded.liability,
                    'merchant_category_code': stmt.excluded.merchant_category_code,
                    'transaction_id': stmt.excluded.transaction_id,
                    'transaction_type': stmt.excluded.transaction_type,
                    'acquirer_bin': stmt.excluded.acquirer_bin,
                    'acquirer_reference_number': stmt.excluded.acquirer_reference_number,
                    'alert_status': stmt.excluded.alert_status,
                    'caid': stmt.excluded.caid,
                    'descriptor_contact': stmt.excluded.descriptor_contact,
                    'rule_name': stmt.excluded.rule_name,
                    'rule_type': stmt.excluded.rule_type,
                    'type': stmt.excluded.type,
                    'feedback_data': stmt.excluded.feedback_data,
                    'feedback_status': stmt.excluded.feedback_status,
                    'feedback_time': stmt.excluded.feedback_time,
                    'created_at': stmt.excluded.created_at,
                    'updated_at': stmt.excluded.updated_at
                }
            )
            
            # Remove internal tracking keys before database insertion
            clean_data = []
            for item in matched_blocks_data:
                clean_item = {k: v for k, v in item.items() if k != '_composite_key'}
                clean_data.append(clean_item)
            
            # Execute the upsert with cleaned data
            self.session.execute(upsert_stmt, clean_data)
            self.session.flush()
            
            duration = time.time() - start_time
            
            # Performance monitoring and alerting using settings
            if settings.PERFORMANCE_MONITORING_ENABLED and duration > settings.SLOW_OPERATION_THRESHOLD_SECONDS:
                logger.warning(f"Slow upsert operation: {duration:.2f}s for {len(matched_blocks_data)} records")
            
            logger.info(f"Bulk upserted {len(matched_blocks_data)} matched blocks in {duration:.2f}s (INSERT or UPDATE)")
            return len(matched_blocks_data)
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Error in bulk upsert after {duration:.2f}s: {str(e)}")
            raise
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self._should_close_session:
            self.session.close()
    
    def get_blocks_batch(self, offset: int = 0, batch_size: int = 500) -> List[Block]:
        """
        Get blocks from database in batches to prevent memory overflow
        
        Args:
            offset: Number of records to skip
            batch_size: Maximum number of blocks to retrieve per batch
            
        Returns:
            List of Block objects
        """
        try:
            query = self.session.query(Block).order_by(Block.created_at.desc())
            query = query.offset(offset).limit(batch_size)
            
            blocks = query.all()
            logger.debug(f"Retrieved batch: {len(blocks)} blocks (offset: {offset})")
            
            return blocks
            
        except Exception as e:
            logger.error(f"Error getting blocks batch: {str(e)}")
            raise

    def get_total_blocks_count(self) -> int:
        """Get total count of blocks for monitoring"""
        try:
            count = self.session.query(Block).count()
            return count
        except Exception as e:
            logger.error(f"Error getting blocks count: {str(e)}")
            return 0
    
    def find_matching_alert_infos_for_block(
        self, 
        block: Block, 
        selected_type: Optional[str] = None
    ) -> List[Tuple[AlertInfo, LinkedStore]]:
        """
        Find matching alert_infos for a specific block based on the filtering logic
        
        Args:
            block: Block object to find matches for
            selected_type: Optional specific type filter
            
        Returns:
            List of tuples (AlertInfo, LinkedStore) that match the block
        """
        try:
            # Build conditions for matching
            conditions = self._build_alert_info_conditions(block)
            
            if not conditions:
                logger.debug(f"No matching conditions for block {block.id}")
                return []
            
            # Query alert_infos with linked stores
            query = self.session.query(AlertInfo, LinkedStore).join(
                LinkedStore, AlertInfo.store_id == LinkedStore.id
            ).filter(
                LinkedStore.is_active == True,
                or_(*conditions)
            )
            
            # Apply type filter if specified
            if selected_type:
                # This filters on block type, but we need to ensure the block matches
                if block.type != selected_type:
                    return []
            
            matches = query.all()
            
            logger.debug(f"Found {len(matches)} alert_info matches for block {block.id}")
            return matches
            
        except Exception as e:
            logger.error(f"Error finding matching alert_infos for block {block.id}: {str(e)}")
            raise
    
    def get_alert_infos_for_store(self, store_id: str) -> List[AlertInfo]:
        """Get all alert infos for a specific store"""
        try:
            alert_infos = self.session.query(AlertInfo).filter(
                AlertInfo.store_id == store_id
            ).all()
            
            logger.info(f"Found {len(alert_infos)} alert infos for store {store_id}")
            return alert_infos
        except Exception as e:
            logger.error(f"Error getting alert infos for store {store_id}: {str(e)}")
            raise
    
    def find_matching_blocks(
        self, 
        filters: Dict[str, Any], 
        limit: Optional[int] = None
    ) -> List[Block]:
        """
        Find blocks that match the given filters
        
        Args:
            filters: Dictionary of filters to apply
            limit: Optional limit on number of results
            
        Returns:
            List of matching Block objects
        """
        try:
            query = self.session.query(Block)
            
            # Build filter conditions using helper method
            conditions = self._build_block_filter_conditions(filters)
            
            # Apply all conditions with AND logic
            if conditions:
                query = query.filter(and_(*conditions))
            
            # Apply limit if specified
            if limit:
                query = query.limit(limit)
            
            # Order by most recent first
            query = query.order_by(Block.alert_time.desc())
            
            blocks = query.all()
            logger.info(f"Found {len(blocks)} matching blocks with filters: {filters}")
            
            return blocks
            
        except Exception as e:
            logger.error(f"Error finding matching blocks: {str(e)}")
            raise
    
    
    
    def process_all_blocks_bulk(
        self, 
        selected_type: Optional[str] = None,
        limit: Optional[int] = None,
        batch_size: int = None
    ) -> List[MatchedBlock]:
        """
        PRODUCTION-READY: Process blocks using batching, retry, monitoring and robust error handling
        
        Args:
            selected_type: Optional type filter
            limit: Optional limit on number of blocks to process
            batch_size: Number of records to process in each batch (max 500 for memory safety)
            
        Returns:
            List of created MatchedBlock objects
        """
        import time
        start_time = time.time()
        
        # Use settings for batch size with safety limits
        if batch_size is None:
            batch_size = settings.get_safe_batch_size()
        else:
            batch_size = min(batch_size, settings.get_safe_batch_size())  # Enforce safety limits
        
        try:
            # Get total count for monitoring
            total_blocks = self.get_total_blocks_count()
            if limit:
                total_blocks = min(total_blocks, limit)
                
            logger.info(f"Starting batch processing of {total_blocks} blocks (batch_size: {batch_size})")
            
            total_processed = 0
            total_upserted = 0
            offset = 0
            
            while total_processed < total_blocks:
                # Step 1: Get batch of blocks
                batch_limit = batch_size
                if limit and (total_processed + batch_size) > limit:
                    batch_limit = limit - total_processed
                    
                blocks = self.get_blocks_batch(offset, batch_limit)
                
                if not blocks:
                    logger.info(f"No more blocks to process at offset {offset}")
                    break
                    
                # Step 2: Process this batch with intelligent grouping
                batch_result = self._process_batch_with_grouping(blocks, selected_type)
                
                total_processed += len(blocks)
                total_upserted += batch_result
                offset += len(blocks)
                
                # Progress monitoring using settings
                if settings.PROGRESS_LOG_ENABLED:
                    progress = (total_processed / total_blocks) * 100 if total_blocks > 0 else 100
                    logger.info(f"Batch {offset//batch_size}: {batch_result} upserted. Progress: {progress:.1f}% ({total_processed}/{total_blocks})")
                
                # Memory cleanup between batches using settings
                if settings.MEMORY_CLEANUP_ENABLED:
                    import gc
                    gc.collect()
            
            duration = time.time() - start_time
            avg_speed = total_processed / duration if duration > 0 else 0
            
            logger.info(f"BATCH PROCESSING COMPLETED: {total_processed} blocks processed, {total_upserted} upserted in {duration:.2f}s (avg: {avg_speed:.1f} blocks/s)")
            
            return []
            
        except Exception as e:
            duration = time.time() - start_time
            self.session.rollback()
            logger.error(f"Error in batch processing after {duration:.2f}s: {str(e)}")
            raise
    
    def _process_batch_with_grouping(self, blocks: List[Block], selected_type: Optional[str] = None) -> int:
        """Process a batch of blocks with intelligent grouping and robust NULL handling"""
        try:
            # Step 2: Group blocks by matching criteria with robust NULL handling
            descriptor_groups = {}  # descriptor -> [blocks]
            bin_groups = {}        # card_bin -> [blocks]
            caid_groups = {}       # caid -> [blocks]
            
            for block in blocks:
                # Group by descriptor for ETHOCA alerts - robust NULL handling
                if block.descriptor and block.descriptor.strip():
                    descriptor = block.descriptor.strip()
                    if descriptor not in descriptor_groups:
                        descriptor_groups[descriptor] = []
                    descriptor_groups[descriptor].append(block)
                
                # Group by card_bin for RDR alerts - robust NULL handling
                if block.card_bin and block.card_bin.strip():
                    card_bin = block.card_bin.strip()
                    if card_bin not in bin_groups:
                        bin_groups[card_bin] = []
                    bin_groups[card_bin].append(block)
                
                # Group by caid for RDR alerts - robust NULL handling
                if block.caid and block.caid.strip():
                    caid = block.caid.strip()
                    if caid not in caid_groups:
                        caid_groups[caid] = []
                    caid_groups[caid].append(block)
            
            logger.debug(f"Grouped batch: {len(descriptor_groups)} descriptor groups, {len(bin_groups)} bin groups, {len(caid_groups)} caid groups")
            
            # Step 3: Bulk query alert_infos for each group
            blocks_to_upsert = []  # dict data for bulk upsert
            processed_count = 0
            
            # Process ETHOCA alerts by descriptor groups
            if descriptor_groups:
                ethoca_alerts = self.session.query(AlertInfo, LinkedStore).join(
                    LinkedStore, AlertInfo.store_id == LinkedStore.id
                ).filter(
                    AlertInfo.alert_type == 'ETHOCA',
                    AlertInfo.registration_status == 'EFFECTED',
                    AlertInfo.descriptor.in_(descriptor_groups.keys()),
                    LinkedStore.is_active == True
                ).all()
                
                # Create mapping for quick lookup
                ethoca_map = {}  # descriptor -> [(alert_info, linked_store)]
                for alert_info, linked_store in ethoca_alerts:
                    if alert_info.descriptor not in ethoca_map:
                        ethoca_map[alert_info.descriptor] = []
                    ethoca_map[alert_info.descriptor].append((alert_info, linked_store))
                
                # Process each descriptor group
                for descriptor, blocks_group in descriptor_groups.items():
                    if descriptor in ethoca_map:
                        for block in blocks_group:
                            processed_count += 1
                            if selected_type and block.type != selected_type:
                                continue
                                
                            for alert_info, linked_store in ethoca_map[descriptor]:
                                self._prepare_upsert_data(
                                    block, alert_info, linked_store, 
                                    blocks_to_upsert
                                )
            
            # Process RDR alerts by bin groups
            if bin_groups:
                bin_alerts = self.session.query(AlertInfo, LinkedStore).join(
                    LinkedStore, AlertInfo.store_id == LinkedStore.id
                ).filter(
                    AlertInfo.alert_type == 'RDR',
                    AlertInfo.registration_status == 'EFFECTED',
                    AlertInfo.bin.in_(bin_groups.keys()),
                    LinkedStore.is_active == True
                ).all()
                
                # Create mapping
                bin_map = {}  # bin -> [(alert_info, linked_store)]
                for alert_info, linked_store in bin_alerts:
                    if alert_info.bin not in bin_map:
                        bin_map[alert_info.bin] = []
                    bin_map[alert_info.bin].append((alert_info, linked_store))
                
                # Process each bin group
                for card_bin, blocks_group in bin_groups.items():
                    if card_bin in bin_map:
                        for block in blocks_group:
                            processed_count += 1
                            if selected_type and block.type != selected_type:
                                continue
                                
                            for alert_info, linked_store in bin_map[card_bin]:
                                self._prepare_upsert_data(
                                    block, alert_info, linked_store,
                                    blocks_to_upsert
                                )
            
            # Process RDR alerts by caid groups
            if caid_groups:
                caid_alerts = self.session.query(AlertInfo, LinkedStore).join(
                    LinkedStore, AlertInfo.store_id == LinkedStore.id
                ).filter(
                    AlertInfo.alert_type == 'RDR',
                    AlertInfo.registration_status == 'EFFECTED',
                    AlertInfo.caid.in_(caid_groups.keys()),
                    LinkedStore.is_active == True
                ).all()
                
                # Create mapping
                caid_map = {}  # caid -> [(alert_info, linked_store)]
                for alert_info, linked_store in caid_alerts:
                    if alert_info.caid not in caid_map:
                        caid_map[alert_info.caid] = []
                    caid_map[alert_info.caid].append((alert_info, linked_store))
                
                # Process each caid group
                for caid, blocks_group in caid_groups.items():
                    if caid in caid_map:
                        for block in blocks_group:
                            processed_count += 1
                            if selected_type and block.type != selected_type:
                                continue
                                
                            for alert_info, linked_store in caid_map[caid]:
                                self._prepare_upsert_data(
                                    block, alert_info, linked_store,
                                    blocks_to_upsert
                                )
            
            # Step 4: Execute bulk upsert operations with connection management
            upserted_count = 0
            
            if blocks_to_upsert:
                try:
                    upserted_count = self._bulk_upsert_matched_blocks(blocks_to_upsert)
                    self.session.commit()
                    logger.debug(f"Batch upsert committed: {upserted_count} records")
                except Exception as e:
                    self.session.rollback()
                    logger.error(f"Batch upsert failed, rolled back: {str(e)}")
                    raise
                finally:
                    # Connection pool management
                    self.session.flush()
            
            return upserted_count
            
        except Exception as e:
            logger.error(f"Error in batch processing: {str(e)}")
            self.session.rollback()
            raise
    
    def _prepare_upsert_data(
        self, 
        block: Block, 
        alert_info: AlertInfo, 
        linked_store: LinkedStore,
        blocks_to_upsert: List[Dict[str, Any]]
    ):
        """Helper method to prepare data for bulk upsert operations"""
        # Create composite key to avoid duplicates within the same batch
        composite_key = f"{block.id}_{linked_store.id}_{linked_store.provider}"
        
        # Check if this combination already exists in current batch
        existing_keys = {item.get('_composite_key') for item in blocks_to_upsert if '_composite_key' in item}
        if composite_key in existing_keys:
            logger.debug(f"Skipping duplicate composite key: {composite_key}")
            return
        
        # Prepare matched_data
        matched_data = {
            'alert_info_id': alert_info.id,
            'alert_info_type': alert_info.alert_type,
            'alert_info_descriptor': alert_info.descriptor,
            'alert_info_bin': alert_info.bin,
            'alert_info_caid': alert_info.caid,
            'alert_info_arn': alert_info.arn,
            'registration_status': alert_info.registration_status,
            'matched_at': datetime.utcnow().isoformat(),
            'match_method': 'intelligent_upsert_matcher',
            'confidence_score': 1.0
        }
        
        # Prepare data for bulk upsert
        upsert_data = {
            '_composite_key': composite_key,  # Internal tracking only, not inserted to DB
            'id': block.id,
            'user_id': linked_store.user_id,
            'linked_store_id': linked_store.id,
            'provider': linked_store.provider,
            'matched_data': matched_data,
            'alert_id': block.alert_id,
            'alert_time': block.alert_time,
            'alert_type': block.alert_type,
            'amount': block.amount,
            'currency': block.currency,
            'descriptor': block.descriptor,
            'auth_code': block.auth_code,
            'card_bin': block.card_bin,
            'card_number': block.card_number,
            'chargeback_code': block.chargeback_code,
            'dispute_amount': block.dispute_amount,
            'dispute_currency': block.dispute_currency,
            'transaction_time': block.transaction_time,
            'age': block.age,
            'alert_source': block.alert_source,
            'arn': block.arn,
            'issuer': block.issuer,
            'initiated_by': block.initiated_by,
            'liability': block.liability,
            'merchant_category_code': block.merchant_category_code,
            'transaction_id': block.transaction_id,
            'transaction_type': block.transaction_type,
            'acquirer_bin': block.acquirer_bin,
            'acquirer_reference_number': block.acquirer_reference_number,
            'alert_status': block.alert_status,
            'caid': block.caid,
            'descriptor_contact': block.descriptor_contact,
            'rule_name': block.rule_name,
            'rule_type': block.rule_type,
            'type': block.type,
            'feedback_data': block.feedback_data,
            'feedback_status': block.feedback_status,
            'feedback_time': block.feedback_time,
            'created_at': block.created_at,
            'updated_at': block.updated_at
        }
        
        blocks_to_upsert.append(upsert_data)
    
    def get_matched_blocks_for_store(
        self, 
        store_id: str,
        provider: Optional[str] = None
    ) -> List[MatchedBlock]:
        """Get all matched blocks for a store"""
        try:
            query = self.session.query(MatchedBlock).filter(
                MatchedBlock.linked_store_id == store_id
            )
            
            if provider:
                query = query.filter(MatchedBlock.provider == provider)
            
            matched_blocks = query.order_by(MatchedBlock.alert_time.desc()).all()
            
            logger.info(f"Found {len(matched_blocks)} matched blocks for store {store_id}")
            return matched_blocks
            
        except Exception as e:
            logger.error(f"Error getting matched blocks for store {store_id}: {str(e)}")
            raise

# Convenience functions for one-off operations

def process_all_blocks_bulk(
    selected_type: Optional[str] = None,
    limit: Optional[int] = None,
    batch_size: int = 1000
) -> List[MatchedBlock]:
    """
    OPTIMIZED: Convenience function to process all blocks using bulk operations
    
    Args:
        selected_type: Optional type filter (ETHOCA, RDR, etc.)
        limit: Optional limit on number of blocks to process
        batch_size: Number of records to process in each batch
        
    Returns:
        List of created MatchedBlock objects (empty for bulk operations)
    """
    with BlockMatcherService() as matcher:
        return matcher.process_all_blocks_bulk(selected_type, limit, batch_size)

def process_single_block(
    block_id: str,
    selected_type: Optional[str] = None
) -> List[MatchedBlock]:
    """
    Process a single block for matching using bulk operations
    
    Args:
        block_id: Block ID to process
        selected_type: Optional type filter
        
    Returns:
        List of created MatchedBlock objects
    """
    with BlockMatcherService() as matcher:
        # Get the specific block
        block = matcher.session.query(Block).filter(Block.id == block_id).first()
        
        if not block:
            raise ValueError(f"Block not found: {block_id}")
        
        # Use bulk processing with limit=1 on this specific block
        # This is more efficient than recreating the individual logic
        logger.info(f"Processing single block {block_id} using bulk operations")
        
        # Process just this block using bulk method (with a filter to match only this ID)
        blocks_to_upsert = []
        matching_alert_infos = matcher.find_matching_alert_infos_for_block(block, selected_type)
        
        if not matching_alert_infos:
            logger.info(f"No matching alert_infos found for block {block_id}")
            return []
        
        # Prepare data for upsert
        for alert_info, linked_store in matching_alert_infos:
            matcher._prepare_upsert_data(block, alert_info, linked_store, blocks_to_upsert)
        
        # Execute upsert
        if blocks_to_upsert:
            matcher._bulk_upsert_matched_blocks(blocks_to_upsert)
            matcher.session.commit()
            logger.info(f"Processed single block {block_id}, upserted {len(blocks_to_upsert)} matched_blocks")
        
        return []  # Bulk operations don't return objects