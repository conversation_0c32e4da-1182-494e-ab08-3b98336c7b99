"""
Stats Calculator Service
Handles calculation and storage of chargeback statistics by country and reason
"""
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, text, func
from datetime import datetime, timedelta
import logging
import uuid

# Optional pycountry import with fallback
try:
    import pycountry
    HAS_PYCOUNTRY = True
except ImportError:
    HAS_PYCOUNTRY = False
    pycountry = None

from models import (
    StatCountry, StatReason, User, LinkedStore, UnifiedDispute,
    ShopifyDispute, StripeDispute, ShopifyTransaction, StripeCharge
)
from config import db, settings

logger = logging.getLogger(__name__)

class StatsCalculatorService:
    """Service for calculating and storing chargeback statistics"""
    
    def __init__(self, session: Session = None):
        self.session = session or db.get_session()
        self._should_close_session = session is None
        
        # Log warning if pycountry is not available
        if not HAS_PYCOUNTRY:
            logger.warning("⚠️  pycountry package not available. Using fallback country name mapping. Install with: pip install pycountry")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self._should_close_session:
            self.session.close()
    
    # ========================================
    # COUNTRY STATISTICS
    # ========================================
    
    def calculate_country_stats_for_store(self, linked_store_id: str, provider: str = None) -> Dict[str, int]:
        """
        Calculate chargeback statistics by country for a specific store
        
        Args:
            linked_store_id: The store ID to calculate stats for
            provider: Optional provider filter ('shopify', 'stripe')
            
        Returns:
            Dictionary with country_code as key and count as value
        """
        try:
            # Get store info
            store = self.session.query(LinkedStore).filter_by(id=linked_store_id).first()
            if not store:
                logger.warning(f"Store not found: {linked_store_id}")
                return {}
            
            country_stats = {}
            
            # Calculate from Shopify disputes if provider allows
            if not provider or provider == 'shopify':
                shopify_stats = self._calculate_shopify_country_stats(linked_store_id)
                self._merge_country_stats(country_stats, shopify_stats)
            
            # Calculate from Stripe disputes if provider allows
            if not provider or provider == 'stripe':
                stripe_stats = self._calculate_stripe_country_stats(linked_store_id)
                self._merge_country_stats(country_stats, stripe_stats)
            
            return country_stats
            
        except Exception as e:
            logger.error(f"Error calculating country stats for store {linked_store_id}: {str(e)}")
            return {}
    
    def _calculate_shopify_country_stats(self, linked_store_id: str) -> Dict[str, int]:
        """Calculate country stats from Shopify transactions/disputes"""
        country_stats = {}
        
        try:
            # Query Shopify disputes directly linked to orders (not through transactions)
            query = text("""
                SELECT 
                    -- Try to get country code from customer_locale first (format: en-US, en-NZ)
                    COALESCE(
                        CASE 
                            WHEN so.customer_locale LIKE '%-%' 
                            THEN UPPER(SPLIT_PART(so.customer_locale, '-', 2))
                            ELSE NULL
                        END,
                        -- Fallback to mapping full country names to codes
                        CASE 
                            WHEN so.billing_address->>'country' = 'United States' THEN 'US'
                            WHEN so.billing_address->>'country' = 'New Zealand' THEN 'NZ'
                            WHEN so.billing_address->>'country' = 'United Kingdom' THEN 'GB'
                            WHEN so.billing_address->>'country' = 'Canada' THEN 'CA'
                            WHEN so.billing_address->>'country' = 'Australia' THEN 'AU'
                            WHEN so.billing_address->>'country' = 'Germany' THEN 'DE'
                            WHEN so.billing_address->>'country' = 'France' THEN 'FR'
                            WHEN so.billing_address->>'country' = 'Italy' THEN 'IT'
                            WHEN so.billing_address->>'country' = 'Spain' THEN 'ES'
                            WHEN so.billing_address->>'country' = 'Netherlands' THEN 'NL'
                            WHEN so.billing_address->>'country' = 'Belgium' THEN 'BE'
                            WHEN so.billing_address->>'country' = 'Switzerland' THEN 'CH'
                            WHEN so.billing_address->>'country' = 'Austria' THEN 'AT'
                            WHEN so.billing_address->>'country' = 'Sweden' THEN 'SE'
                            WHEN so.billing_address->>'country' = 'Norway' THEN 'NO'
                            WHEN so.billing_address->>'country' = 'Denmark' THEN 'DK'
                            WHEN so.billing_address->>'country' = 'Finland' THEN 'FI'
                            WHEN so.billing_address->>'country' = 'Japan' THEN 'JP'
                            WHEN so.billing_address->>'country' = 'China' THEN 'CN'
                            WHEN so.billing_address->>'country' = 'India' THEN 'IN'
                            WHEN so.billing_address->>'country' = 'Brazil' THEN 'BR'
                            WHEN so.billing_address->>'country' = 'Mexico' THEN 'MX'
                            WHEN so.billing_address->>'country' = 'South Korea' THEN 'KR'
                            WHEN so.billing_address->>'country' = 'Singapore' THEN 'SG'
                            WHEN so.billing_address->>'country' = 'Hong Kong' THEN 'HK'
                            WHEN so.billing_address->>'country' = 'Greece' THEN 'GR'
                            ELSE UPPER(LEFT(so.billing_address->>'country', 2))
                        END,
                        -- Final fallback to shipping address
                        CASE 
                            WHEN so.shipping_address->>'country' = 'United States' THEN 'US'
                            WHEN so.shipping_address->>'country' = 'New Zealand' THEN 'NZ'
                            WHEN so.shipping_address->>'country' = 'United Kingdom' THEN 'GB'
                            WHEN so.shipping_address->>'country' = 'Canada' THEN 'CA'
                            ELSE UPPER(LEFT(so.shipping_address->>'country', 2))
                        END,
                        'XX'
                    ) as country_code,
                    COUNT(*) as dispute_count
                FROM shopify_disputes sd
                LEFT JOIN shopify_orders so ON sd.order_id = so.id
                WHERE sd.linked_store_id = :store_id
                  AND sd.status IN ('open', 'under_review', 'charge_refunded', 'lost')
                GROUP BY country_code
                ORDER BY dispute_count DESC
            """)
            
            result = self.session.execute(query, {"store_id": linked_store_id})
            
            for row in result:
                country_code = row.country_code
                if country_code and country_code != 'XX' and len(country_code) == 2:
                    country_stats[country_code.upper()] = row.dispute_count
                    
        except Exception as e:
            logger.error(f"Error calculating Shopify country stats: {str(e)}")
        
        return country_stats
    
    def _calculate_stripe_country_stats(self, linked_store_id: str) -> Dict[str, int]:
        """Calculate country stats from Stripe charges/disputes"""
        country_stats = {}
        
        try:
            # Query Stripe charges with billing details
            query = text("""
                SELECT 
                    COALESCE(
                        (sc.billing_details->'address'->>'country'),
                        'Unknown'
                    ) as country_code,
                    COUNT(*) as dispute_count
                FROM stripe_disputes sd
                LEFT JOIN stripe_charges sc ON sd.charge_id = sc.id
                WHERE sd.linked_store_id = :store_id
                  AND sd.status IN ('warning_needs_response', 'warning_under_review', 'warning_closed', 'needs_response', 'under_review', 'charge_refunded', 'lost')
                GROUP BY country_code
                ORDER BY dispute_count DESC
            """)
            
            result = self.session.execute(query, {"store_id": linked_store_id})
            
            for row in result:
                country_code = row.country_code
                if country_code and country_code != 'Unknown':
                    country_stats[country_code.upper()] = row.dispute_count
                    
        except Exception as e:
            logger.error(f"Error calculating Stripe country stats: {str(e)}")
        
        return country_stats
    
    def _merge_country_stats(self, target: Dict[str, int], source: Dict[str, int]):
        """Merge country statistics from source into target"""
        for country_code, count in source.items():
            target[country_code] = target.get(country_code, 0) + count
    
    def save_country_stats(self, user_id: str, linked_store_id: str, provider: str, country_stats: Dict[str, int]) -> int:
        """
        Save country statistics to database
        
        Returns:
            Number of records saved
        """
        saved_count = 0
        
        try:
            for country_code, count in country_stats.items():
                if count <= 0:
                    continue
                
                country_name = self._get_country_name(country_code)
                
                # Upsert country stats
                existing = self.session.query(StatCountry).filter_by(
                    user_id=user_id,
                    linked_store_id=linked_store_id,
                    provider=provider,
                    country_code=country_code
                ).first()
                
                if existing:
                    existing.count = count
                    existing.country_name = country_name
                    existing.updated_at = datetime.utcnow()
                else:
                    new_stat = StatCountry(
                        id=str(uuid.uuid4()),
                        user_id=user_id,
                        linked_store_id=linked_store_id,
                        provider=provider,
                        country_code=country_code,
                        country_name=country_name,
                        count=count,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    self.session.add(new_stat)
                
                saved_count += 1
            
            self.session.commit()
            logger.info(f"Saved {saved_count} country stats for store {linked_store_id}")
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"Error saving country stats: {str(e)}")
            saved_count = 0
        
        return saved_count
    
    # ========================================
    # REASON STATISTICS
    # ========================================
    
    def calculate_reason_stats_for_store(self, linked_store_id: str, provider: str = None) -> Dict[str, Tuple[str, int]]:
        """
        Calculate chargeback statistics by reason for a specific store
        
        Args:
            linked_store_id: The store ID to calculate stats for
            provider: Optional provider filter ('shopify', 'stripe')
            
        Returns:
            Dictionary with reason_code as key and (reason_name, count) as value
        """
        try:
            reason_stats = {}
            
            # Calculate from Shopify disputes if provider allows
            if not provider or provider == 'shopify':
                shopify_stats = self._calculate_shopify_reason_stats(linked_store_id)
                self._merge_reason_stats(reason_stats, shopify_stats)
            
            # Calculate from Stripe disputes if provider allows
            if not provider or provider == 'stripe':
                stripe_stats = self._calculate_stripe_reason_stats(linked_store_id)
                self._merge_reason_stats(reason_stats, stripe_stats)
            
            return reason_stats
            
        except Exception as e:
            logger.error(f"Error calculating reason stats for store {linked_store_id}: {str(e)}")
            return {}
    
    def _calculate_shopify_reason_stats(self, linked_store_id: str) -> Dict[str, Tuple[str, int]]:
        """Calculate reason stats from Shopify disputes"""
        reason_stats = {}
        
        try:
            query = text("""
                SELECT 
                    COALESCE(reason, 'unknown') as reason_code,
                    reason as reason_name,
                    COUNT(*) as dispute_count
                FROM shopify_disputes
                WHERE linked_store_id = :store_id
                  AND status IN ('open', 'under_review', 'charge_refunded', 'lost')
                GROUP BY reason_code, reason_name
                ORDER BY dispute_count DESC
            """)
            
            result = self.session.execute(query, {"store_id": linked_store_id})
            
            for row in result:
                reason_code = row.reason_code or 'unknown'
                reason_name = self._normalize_shopify_reason_name(row.reason_name)
                reason_stats[reason_code] = (reason_name, row.dispute_count)
                    
        except Exception as e:
            logger.error(f"Error calculating Shopify reason stats: {str(e)}")
        
        return reason_stats
    
    def _calculate_stripe_reason_stats(self, linked_store_id: str) -> Dict[str, Tuple[str, int]]:
        """Calculate reason stats from Stripe disputes"""
        reason_stats = {}
        
        try:
            query = text("""
                SELECT 
                    COALESCE(reason, 'general') as reason_code,
                    reason as reason_name,
                    COUNT(*) as dispute_count
                FROM stripe_disputes
                WHERE linked_store_id = :store_id
                  AND status IN ('warning_needs_response', 'warning_under_review', 'warning_closed', 'needs_response', 'under_review', 'charge_refunded', 'lost')
                GROUP BY reason_code, reason_name
                ORDER BY dispute_count DESC
            """)
            
            result = self.session.execute(query, {"store_id": linked_store_id})
            
            for row in result:
                reason_code = row.reason_code or 'general'
                reason_name = self._normalize_stripe_reason_name(row.reason_name)
                reason_stats[reason_code] = (reason_name, row.dispute_count)
                    
        except Exception as e:
            logger.error(f"Error calculating Stripe reason stats: {str(e)}")
        
        return reason_stats
    
    def _merge_reason_stats(self, target: Dict[str, Tuple[str, int]], source: Dict[str, Tuple[str, int]]):
        """Merge reason statistics from source into target"""
        for reason_code, (reason_name, count) in source.items():
            if reason_code in target:
                existing_name, existing_count = target[reason_code]
                target[reason_code] = (reason_name, existing_count + count)
            else:
                target[reason_code] = (reason_name, count)
    
    def save_reason_stats(self, user_id: str, linked_store_id: str, provider: str, reason_stats: Dict[str, Tuple[str, int]]) -> int:
        """
        Save reason statistics to database
        
        Returns:
            Number of records saved
        """
        saved_count = 0
        
        try:
            for reason_code, (reason_name, count) in reason_stats.items():
                if count <= 0:
                    continue
                
                # Upsert reason stats
                existing = self.session.query(StatReason).filter_by(
                    user_id=user_id,
                    linked_store_id=linked_store_id,
                    provider=provider,
                    reason_code=reason_code
                ).first()
                
                if existing:
                    existing.count = count
                    existing.reason_name = reason_name
                    existing.updated_at = datetime.utcnow()
                else:
                    new_stat = StatReason(
                        id=str(uuid.uuid4()),
                        user_id=user_id,
                        linked_store_id=linked_store_id,
                        provider=provider,
                        reason_code=reason_code,
                        reason_name=reason_name,
                        count=count,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    self.session.add(new_stat)
                
                saved_count += 1
            
            self.session.commit()
            logger.info(f"Saved {saved_count} reason stats for store {linked_store_id}")
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"Error saving reason stats: {str(e)}")
            saved_count = 0
        
        return saved_count
    
    # ========================================
    # PUBLIC INTERFACE
    # ========================================
    
    def calculate_and_save_stats_for_store(self, linked_store_id: str, provider: str = None) -> Dict[str, int]:
        """
        Calculate and save both country and reason stats for a store
        
        Returns:
            Dictionary with 'country_stats' and 'reason_stats' counts
        """
        try:
            # Get store info
            store = self.session.query(LinkedStore).filter_by(id=linked_store_id).first()
            if not store:
                logger.warning(f"Store not found: {linked_store_id}")
                return {"country_stats": 0, "reason_stats": 0}
            
            user_id = store.user_id
            
            # Calculate and save country stats
            country_stats = self.calculate_country_stats_for_store(linked_store_id, provider)
            country_saved = self.save_country_stats(user_id, linked_store_id, provider or store.provider, country_stats)
            
            # Calculate and save reason stats
            reason_stats = self.calculate_reason_stats_for_store(linked_store_id, provider)
            reason_saved = self.save_reason_stats(user_id, linked_store_id, provider or store.provider, reason_stats)
            
            logger.info(f"Stats calculation completed for store {linked_store_id}: {country_saved} countries, {reason_saved} reasons")
            
            return {
                "country_stats": country_saved,
                "reason_stats": reason_saved,
                "total_countries": len(country_stats),
                "total_reasons": len(reason_stats)
            }
            
        except Exception as e:
            logger.error(f"Error calculating stats for store {linked_store_id}: {str(e)}")
            return {"country_stats": 0, "reason_stats": 0}
    
    def calculate_and_save_stats_for_all_stores(self) -> Dict[str, Any]:
        """
        Calculate and save stats for all active stores
        
        Returns:
            Summary of processing results
        """
        try:
            stores = self.session.query(LinkedStore).filter_by(is_active=True).all()
            
            results = {
                "total_stores": len(stores),
                "processed_stores": 0,
                "total_country_stats": 0,
                "total_reason_stats": 0,
                "errors": []
            }
            
            for store in stores:
                try:
                    store_results = self.calculate_and_save_stats_for_store(store.id)
                    results["processed_stores"] += 1
                    results["total_country_stats"] += store_results.get("country_stats", 0)
                    results["total_reason_stats"] += store_results.get("reason_stats", 0)
                    
                except Exception as e:
                    error_msg = f"Error processing store {store.id}: {str(e)}"
                    logger.error(error_msg)
                    results["errors"].append(error_msg)
            
            return results
            
        except Exception as e:
            logger.error(f"Error calculating stats for all stores: {str(e)}")
            return {"error": str(e)}
    
    # ========================================
    # HELPER METHODS
    # ========================================
    
    def _get_country_name(self, country_code: str) -> str:
        """Get full country name from country code"""
        if not HAS_PYCOUNTRY:
            # Fallback to basic country mapping without pycountry
            return self._get_country_name_fallback(country_code)
        
        try:
            country = pycountry.countries.get(alpha_2=country_code.upper())
            return country.name if country else country_code
        except Exception:
            return self._get_country_name_fallback(country_code)
    
    def _get_country_name_fallback(self, country_code: str) -> str:
        """Fallback country name mapping when pycountry is not available"""
        # Basic mapping for common countries
        country_mapping = {
            'US': 'United States',
            'CA': 'Canada',
            'GB': 'United Kingdom',
            'AU': 'Australia',
            'DE': 'Germany',
            'FR': 'France',
            'IT': 'Italy',
            'ES': 'Spain',
            'NL': 'Netherlands',
            'JP': 'Japan',
            'CN': 'China',
            'IN': 'India',
            'BR': 'Brazil',
            'MX': 'Mexico',
            'RU': 'Russia',
            'KR': 'South Korea',
            'SG': 'Singapore',
            'HK': 'Hong Kong',
            'TW': 'Taiwan',
            'MY': 'Malaysia',
            'TH': 'Thailand',
            'VN': 'Vietnam',
            'PH': 'Philippines',
            'ID': 'Indonesia',
            'ZA': 'South Africa',
            'EG': 'Egypt',
            'NG': 'Nigeria',
            'AR': 'Argentina',
            'CL': 'Chile',
            'CO': 'Colombia',
            'PE': 'Peru',
            'VE': 'Venezuela',
            'SE': 'Sweden',
            'NO': 'Norway',
            'DK': 'Denmark',
            'FI': 'Finland',
            'CH': 'Switzerland',
            'AT': 'Austria',
            'BE': 'Belgium',
            'IE': 'Ireland',
            'PT': 'Portugal',
            'GR': 'Greece',
            'CZ': 'Czech Republic',
            'PL': 'Poland',
            'HU': 'Hungary',
            'RO': 'Romania',
            'BG': 'Bulgaria',
            'HR': 'Croatia',
            'SK': 'Slovakia',
            'SI': 'Slovenia',
            'EE': 'Estonia',
            'LV': 'Latvia',
            'LT': 'Lithuania',
            'IL': 'Israel',
            'TR': 'Turkey',
            'SA': 'Saudi Arabia',
            'AE': 'United Arab Emirates',
            'QA': 'Qatar',
            'KW': 'Kuwait',
            'BH': 'Bahrain',
            'OM': 'Oman',
            'JO': 'Jordan',
            'LB': 'Lebanon',
            'NZ': 'New Zealand'
        }
        
        country_code_upper = country_code.upper()
        return country_mapping.get(country_code_upper, country_code_upper)
    
    def _normalize_shopify_reason_name(self, reason: str) -> str:
        """Normalize Shopify dispute reason names"""
        if not reason:
            return "Unknown"
        
        reason_mapping = {
            "chargeback": "Chargeback",
            "inquiry": "Inquiry", 
            "credit_not_processed": "Credit Not Processed",
            "duplicate": "Duplicate Charge",
            "fraudulent": "Fraudulent",
            "general": "General",
            "product_not_received": "Product Not Received",
            "product_unacceptable": "Product Unacceptable",
            "subscription_canceled": "Subscription Canceled",
            "unrecognized": "Unrecognized"
        }
        
        return reason_mapping.get(reason.lower(), reason.title())
    
    def _normalize_stripe_reason_name(self, reason: str) -> str:
        """Normalize Stripe dispute reason names"""
        if not reason:
            return "General"
        
        reason_mapping = {
            "credit_not_processed": "Credit Not Processed",
            "duplicate": "Duplicate Charge", 
            "fraudulent": "Fraudulent",
            "general": "General",
            "product_not_received": "Product Not Received",
            "product_unacceptable": "Product Unacceptable",
            "subscription_canceled": "Subscription Canceled",
            "unrecognized": "Unrecognized"
        }
        
        return reason_mapping.get(reason.lower(), reason.title())