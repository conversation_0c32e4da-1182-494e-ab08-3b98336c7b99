"""
Shopify provider implementation for block processing
"""

from typing import Dict, Any, Optional
import psycopg2.extras
from .base import BaseProvider
from utils.logging import get_logger

logger = get_logger("providers.shopify")

class ShopifyProvider(BaseProvider):
    """Shopify-specific provider for processing blocks"""
    
    def __init__(self, db_service):
        super().__init__(db_service)
    
    # ========================================
    # PUBLIC METHODS
    # ========================================
        
    def find_matched_entry(self, store_id: str, block: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Find a matching Shopify order based on block data
        
        Args:
            store_id: The store ID to search in
            block: Block data containing descriptor and amount
        
        Returns:
            Matched order with refund status, or None if no match
        """
        try:
            descriptor = block.get("descriptor")
            amount = block.get("amount")  # Amount should be in cents
            
            if not descriptor or amount is None:
                logger.warning(f"Missing descriptor or amount in block: {block.get('id')}")
                return None
            
            # Validate block data first
            if not self.validate_block_data(block):
                logger.warning(f"Invalid block data for Shopify matching: {block.get('id')}")
                return None
            
            # Find the transaction
            transaction = self._find_transaction(descriptor, amount, store_id)
            if not transaction:
                logger.info(f"No Shopify transaction found for descriptor: {descriptor}, amount: {amount}")
                return None
            
            # Match to order
            order = self._match_order(store_id, transaction)
            if order:
                # Check refund status
                refund_status = "none"
                refunded_amount = order.get("refundedAmount", 0)
                total_amount = order.get("totalPrice", 0)
                
                if refunded_amount > 0:
                    if refunded_amount >= total_amount:
                        refund_status = "full"
                    else:
                        refund_status = "partial"
                
                match_data = {
                    "id": order.get("id"),
                    "name": order.get("name"),
                    "refundStatus": refund_status,
                    "refundedAmount": refunded_amount,
                    "totalAmount": total_amount,
                    "transaction": transaction,
                    "order_data": order,
                    "match_quality": self._calculate_match_quality(descriptor, amount, transaction, order)
                }
                
                return self.format_match_result(match_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Error finding matched Shopify entry: {str(e)}")
            return None
    
    # ========================================
    # PRIVATE METHODS
    # ========================================
    
    def _find_transaction(self, descriptor: str, amount: int, store_id: str) -> Optional[Dict[str, Any]]:
        """Find a Shopify transaction matching the descriptor and amount"""
        try:
            query = """
                SELECT st.*, o."name" as order_name, o."totalPrice" as order_total
                FROM "ShopifyTransaction" st
                JOIN "ShopifyOrder" o ON o.id = st."orderId"
                WHERE o."linkedStoreId" = %s
                AND (
                    UPPER(st.receipt->>'descriptor') = UPPER(%s)
                    OR UPPER(st.receipt->>'merchant_descriptor') = UPPER(%s)
                )
                AND st.amount = %s
                AND st.kind = 'sale'
                AND st.status = 'success'
                ORDER BY st."createdAt" DESC
                LIMIT 1
            """
            
            conn = None
            try:
                conn = self.db.pool.getconn()
                with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                    cursor.execute(query, (store_id, descriptor, descriptor, amount))
                    row = cursor.fetchone()
                    return dict(row) if row else None
            finally:
                if conn:
                    self.db.pool.putconn(conn)
                
        except Exception as e:
            logger.error(f"Error finding Shopify transaction: {str(e)}")
            return None
    
    def _match_order(self, store_id: str, transaction_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Match a Shopify transaction to an order"""
        try:
            order_id = transaction_data.get("orderId")
            if not order_id:
                return None
            
            query = """
                SELECT * FROM "ShopifyOrder"
                WHERE id = %s AND "linkedStoreId" = %s
            """
            
            conn = None
            try:
                conn = self.db.pool.getconn()
                with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                    cursor.execute(query, (order_id, store_id))
                    row = cursor.fetchone()
                    return dict(row) if row else None
            finally:
                if conn:
                    self.db.pool.putconn(conn)
                
        except Exception as e:
            logger.error(f"Error matching Shopify order: {str(e)}")
            return None
    
    def _calculate_match_quality(self, descriptor: str, amount: int, transaction: Dict[str, Any], order: Dict[str, Any]) -> float:
        """Calculate the quality of the match (0.0 to 1.0)"""
        quality = 0.0
        
        # Exact descriptor match
        tx_descriptor = transaction.get("receipt", {}).get("descriptor", "")
        if tx_descriptor.upper() == descriptor.upper():
            quality += 0.5
        
        # Exact amount match
        if transaction.get("amount") == amount:
            quality += 0.3
        
        # Transaction success status
        if transaction.get("status") == "success":
            quality += 0.1
        
        # Order exists and matches
        if order:
            quality += 0.1
        
        return min(quality, 1.0)