"""
Stripe provider implementation for block processing
"""

from typing import Dict, Any, Optional
import psycopg2.extras
import stripe
import os
import requests
from datetime import datetime, timezone
from dataclasses import dataclass
from .base import BaseProvider
from utils.logging import get_logger
from utils.formatters import parse_descriptor

logger = get_logger("providers.stripe")

@dataclass
class StripeOAuthTokens:
    """Data class for Stripe OAuth tokens"""
    access_token: str
    refresh_token: str
    stripe_user_id: str
    livemode: bool = False
    scope: Optional[str] = None
    stripe_publishable_key: Optional[str] = None
    token_type: str = "bearer"

class RefreshTokenError(Exception):
    """Base exception for token refresh errors"""
    pass

class RefreshTokenExpiredError(RefreshTokenError):
    """Exception raised when refresh token is expired or invalid"""
    pass

class StripeProvider(BaseProvider):
    """Stripe-specific provider for processing blocks"""

    def __init__(self, db_service):
        super().__init__(db_service)
        # Initialize Stripe API key
        self._setup_stripe_api()
        # OAuth configuration
        self.stripe_secret_key = os.getenv("STRIPE_API_KEY")
        self.oauth_token_url = "https://api.stripe.com/v1/oauth/token"
        self.oauth_timeout = 30
        self.validation_timeout = 10
    
    # ========================================
    # PUBLIC METHODS
    # ========================================
    
    def find_matched_entry(self, store_id: str, block: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Find a matching Stripe charge based on block data
        
        Args:
            store_id: The store ID to search in
            block: Block data containing descriptor and amount
        
        Returns:
            Matched charge with refund status, or None if no match
        """
        try:
            descriptor = block.get("descriptor")
            amount = block.get("amount")  # Amount should be in cents
            
            # Validate block data first
            if not self.validate_block_data(block):
                logger.warning(f"Invalid block data for Stripe matching: {block.get('id')}")
                return None
            
            if not descriptor or amount is None:
                logger.warning(f"Missing descriptor or amount in block: {block.get('id')}")
                return None
            
            # Find the charge/transaction
            charge = self._find_transaction(descriptor, amount, store_id)
            if not charge:
                logger.info(f"No Stripe charge found for descriptor: {descriptor}, amount: {amount}")
                return None
            
            # Check refund status
            refund_status = "none"
            refunded_amount = charge.get("amountRefunded", 0)
            total_amount = charge.get("amount", 0)
            
            if refunded_amount > 0:
                if refunded_amount >= total_amount:
                    refund_status = "full"
                else:
                    refund_status = "partial"
            
            # Get additional order metadata
            order_info = self._match_order(charge)
            
            match_data = {
                "id": charge.get("id"),
                "name": charge.get("description") or charge.get("id"),
                "refundStatus": refund_status,
                "refundedAmount": refunded_amount,
                "totalAmount": total_amount,
                "paymentIntentId": charge.get("paymentIntentId"),
                "metadata": order_info,
                "charge_data": charge,
                "match_quality": self._calculate_match_quality(descriptor, amount, charge)
            }
            
            return self.format_match_result(match_data)

        except Exception as e:
            logger.error(f"Error finding matched Stripe entry: {str(e)}")
            return None

    def create_refund(self, store_id: str, charge_id: str, block_id: str, amount: Optional[int] = None, reason: str = "requested_by_customer", stripe_account: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create a refund for a Stripe charge or payment intent

        Args:
            store_id: The store ID
            charge_id: The Stripe charge ID or payment intent ID
            block_id: The block ID for tracking
            amount: Optional amount to refund in cents (if None, creates full refund)
            reason: Reason for the refund
            stripe_account: Optional pre-fetched Stripe account data to avoid duplicate queries

        Returns:
            Dict containing success status, refund data, and any error messages
        """
        try:
            logger.info(f"[Stripe Refund] Starting refund process for charge {charge_id} in store {store_id}")

            # Use provided stripe_account or fetch it
            if not stripe_account:
                stripe_account = self._get_stripe_account_with_valid_token(store_id)
            else:
                # Ensure the provided account has a valid token
                stripe_account = self._ensure_valid_token_for_account(stripe_account, store_id)

            if not stripe_account:
                logger.error(f"[Stripe Refund] No valid Stripe account found for store {store_id}")
                return {
                    "success": False,
                    "message": "No valid Stripe account found for store",
                    "blockRefund": None
                }

            # Determine if this is a charge ID or payment intent ID
            is_payment_intent = charge_id.startswith('pi_')
            is_charge = charge_id.startswith('ch_')

            if not (is_payment_intent or is_charge):
                logger.error(f"[Stripe Refund] Invalid charge/payment intent ID format: {charge_id}")
                return {
                    "success": False,
                    "message": "Invalid charge or payment intent ID format",
                    "blockRefund": None
                }

            # Create refund parameters
            refund_params = {
                "reason": reason,
                "metadata": {
                    "block_id": block_id,
                    "store_id": store_id,
                    "refund_source": "chargeback_system"
                }
            }

            # Add amount if specified (for partial refunds)
            if amount is not None:
                refund_params["amount"] = amount

            # Add charge or payment_intent parameter
            if is_payment_intent:
                refund_params["payment_intent"] = charge_id
            else:
                refund_params["charge"] = charge_id

            # Create the refund using Stripe API with auto token refresh
            logger.info(f"[Stripe Refund] Creating refund with params: {refund_params}")

            refund = self._make_stripe_api_call(
                store_id,
                stripe.Refund.create,
                stripe_account,
                **refund_params
            )

            logger.info(f"[Stripe Refund] Refund created successfully. Refund ID: {refund.id}, Status: {refund.status}")

            # Create BlockRefund record for tracking
            block_refund = self._create_block_refund(
                block_id=block_id,
                store_id=store_id,
                charge_id=charge_id,
                stripe_refund=refund
            )

            return {
                "success": True,
                "data": {
                    "stripeRefund": refund,
                    "blockRefund": block_refund
                },
                "message": f"Refund created successfully: {refund.id}"
            }

        except stripe.error.InvalidRequestError as e:
            logger.error(f"[Stripe Refund] Invalid request error: {str(e)}")
            error_message = f"Invalid request: {str(e)}"

            # Create failed BlockRefund record
            block_refund = self._create_block_refund(
                block_id=block_id,
                store_id=store_id,
                charge_id=charge_id,
                stripe_refund=None,
                error_message=error_message
            )

            return {
                "success": False,
                "message": error_message,
                "blockRefund": block_refund
            }

        except stripe.error.PermissionError as e:
            logger.error(f"[Stripe Refund] Permission error: {str(e)}")
            error_message = f"Permission denied: {str(e)}"

            block_refund = self._create_block_refund(
                block_id=block_id,
                store_id=store_id,
                charge_id=charge_id,
                stripe_refund=None,
                error_message=error_message
            )

            return {
                "success": False,
                "message": error_message,
                "blockRefund": block_refund
            }

        except stripe.error.StripeError as e:
            logger.error(f"[Stripe Refund] Stripe API error: {str(e)}")
            error_message = f"Stripe API error: {str(e)}"

            block_refund = self._create_block_refund(
                block_id=block_id,
                store_id=store_id,
                charge_id=charge_id,
                stripe_refund=None,
                error_message=error_message
            )

            return {
                "success": False,
                "message": error_message,
                "blockRefund": block_refund
            }

        except Exception as e:
            logger.error(f"[Stripe Refund] Unexpected error: {str(e)}")
            error_message = f"Unexpected error: {str(e)}"

            block_refund = self._create_block_refund(
                block_id=block_id,
                store_id=store_id,
                charge_id=charge_id,
                stripe_refund=None,
                error_message=error_message
            )

            return {
                "success": False,
                "message": error_message,
                "blockRefund": block_refund
            }

    def process_refund_for_matched_charge(self, store_id: str, block: Dict[str, Any], matched_charge: Dict[str, Any], stripe_account: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a refund for a matched Stripe charge

        Args:
            store_id: The store ID
            block: The block data
            matched_charge: The matched charge data from find_matched_entry
            stripe_account: Optional pre-fetched Stripe account data to avoid duplicate queries

        Returns:
            Dict containing refund result
        """
        try:
            block_id = block.get("id")
            charge_data = matched_charge.get("match_data", {}).get("charge_data", {})
            charge_id = charge_data.get("id")
            payment_intent_id = charge_data.get("paymentIntentId")

            if not charge_id and not payment_intent_id:
                logger.error(f"No charge ID or payment intent ID found for block {block_id}")
                return {
                    "success": False,
                    "message": "No charge ID or payment intent ID found"
                }

            # Use payment intent ID if available, otherwise use charge ID
            refund_target = payment_intent_id if payment_intent_id else charge_id

            # Check if already refunded
            refund_status = matched_charge.get("match_data", {}).get("refundStatus", "none")
            if refund_status == "full":
                logger.info(f"Charge {refund_target} is already fully refunded")
                return {
                    "success": True,
                    "message": "Charge is already fully refunded",
                    "alreadyRefunded": True
                }

            # Create the refund (pass stripe_account to avoid duplicate query)
            logger.info(f"Creating refund for charge {refund_target} (block {block_id})")
            refund_result = self.create_refund(
                store_id=store_id,
                charge_id=refund_target,
                block_id=block_id,
                reason="fraudulent",  # Use fraudulent reason for chargeback-related refunds
                stripe_account=stripe_account
            )

            return refund_result

        except Exception as e:
            logger.error(f"Error processing refund for matched charge: {str(e)}")
            return {
                "success": False,
                "message": f"Error processing refund: {str(e)}"
            }
    
    # ========================================
    # PRIVATE METHODS
    # ========================================

    def _setup_stripe_api(self) -> None:
        """Initialize Stripe API with the API key from environment"""
        api_key = os.getenv("STRIPE_API_KEY")
        if not api_key:
            logger.warning("STRIPE_API_KEY not found in environment variables")
            return

        stripe.api_key = api_key
        logger.info("Stripe API initialized successfully")

    def _get_stripe_account_with_valid_token(self, store_id: str) -> Optional[Dict[str, Any]]:
        """Get Stripe account information for a store with valid access token"""
        try:
            query = """
                SELECT sa.*, ls.store_name
                FROM stripe_accounts sa
                JOIN linked_stores ls ON ls.id = sa.store_id
                WHERE sa.store_id = %s
                LIMIT 1
            """

            conn = None
            try:
                conn = self.db.pool.getconn()
                with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                    cursor.execute(query, (store_id,))
                    row = cursor.fetchone()

                    if not row:
                        return None

                    account_data = dict(row)
                    return self._ensure_valid_token_for_account(account_data, store_id)

            finally:
                if conn:
                    self.db.pool.putconn(conn)

        except Exception as e:
            logger.error(f"Error getting Stripe account for store {store_id}: {str(e)}")
            return None

    def _ensure_valid_token_for_account(self, account_data: Dict[str, Any], store_id: str) -> Optional[Dict[str, Any]]:
        """Ensure the account has a valid access token, refresh if needed"""
        try:
            # TODO: OAuth fields not yet in StripeAccount schema - using snake_case field names
            current_token = account_data.get('access_token')  # TODO: Add to schema
            stripe_user_id = account_data.get('stripe_account_id')  # Correct field name
            refresh_token = account_data.get('refresh_token')  # TODO: Add to schema

            if not current_token or not stripe_user_id:
                logger.warning(f"Missing OAuth token data for store {store_id} - OAuth not fully implemented")
                return account_data  # Return as-is if no token data

            # Test if current token is valid
            if self._validate_access_token(current_token, stripe_user_id):
                logger.debug(f"Access token valid for store: {store_id}")
                return account_data

            # Token is invalid, try to refresh it
            if not refresh_token:
                logger.error(f"No refresh token available for store {store_id}")
                self._mark_store_inactive(store_id, "No refresh token available")
                return None

            logger.info(f"Access token expired for store {store_id}, refreshing...")
            new_tokens = self._refresh_access_token(refresh_token)

            if new_tokens:
                # Update account data with new tokens
                account_data['access_token'] = new_tokens.access_token
                account_data['refresh_token'] = new_tokens.refresh_token

                # Update database with new tokens
                self._update_store_tokens(store_id, new_tokens)

                logger.info(f"Successfully refreshed tokens for store {store_id}")
                return account_data
            else:
                logger.error(f"Failed to refresh tokens for store {store_id}")
                self._mark_store_inactive(store_id, "Token refresh failed")
                return None

        except Exception as e:
            logger.error(f"Error ensuring valid token for store {store_id}: {str(e)}")
            self._mark_store_inactive(store_id, f"Token validation error: {str(e)}")
            return None

    def _create_block_refund(self, block_id: str, store_id: str, charge_id: str,
                           stripe_refund: Optional[Any] = None, error_message: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Create a BlockRefund record for tracking"""
        try:
            # Prepare refund data
            refund_data = {
                "block_id": block_id,
                "store_id": store_id,
                "charge_id": charge_id,
                "amount": 0,
                "currency": "USD",
                "reason": "customer",
                "note": error_message or "Stripe refund processed via chargeback system",
                "notify": True,
                "provider": "stripe",
                "status": "failed" if error_message else "succeeded"
            }

            if stripe_refund:
                refund_data.update({
                    "refund_id": stripe_refund.id,
                    "amount": stripe_refund.amount / 100.0,  # Convert from cents to dollars
                    "currency": stripe_refund.currency.upper(),
                    "status": stripe_refund.status,
                    "stripe_refund_data": {
                        "id": stripe_refund.id,
                        "amount": stripe_refund.amount,
                        "currency": stripe_refund.currency,
                        "status": stripe_refund.status,
                        "reason": stripe_refund.reason,
                        "created": stripe_refund.created,
                        "metadata": stripe_refund.metadata
                    }
                })

            # Insert into database
            query = """
                INSERT INTO block_refunds (
                    block_id, store_id, order_id, refund_id, amount, currency,
                    reason, note, notify, processed_at, restock, created_at, updated_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, NOW(), NOW()
                ) RETURNING *
            """

            conn = None
            try:
                conn = self.db.pool.getconn()
                with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                    cursor.execute(query, (
                        block_id,
                        store_id,
                        charge_id,  # Using charge_id as orderId for Stripe
                        refund_data.get("refund_id"),
                        refund_data["amount"],
                        refund_data["currency"],
                        refund_data["reason"],
                        refund_data["note"],
                        refund_data["notify"],
                        True  # restock
                    ))

                    conn.commit()
                    row = cursor.fetchone()

                    if row:
                        logger.info(f"BlockRefund record created for block {block_id}")
                        return dict(row)

            finally:
                if conn:
                    self.db.pool.putconn(conn)

        except Exception as e:
            logger.error(f"Error creating BlockRefund record: {str(e)}")

        return None
    
    def _find_transaction(self, descriptor: str, amount: int, store_id: str) -> Optional[Dict[str, Any]]:
        """Find a Stripe charge matching the descriptor and amount"""
        try:
            # Parse descriptor to get potential charge ID or merchant info
            parsed = parse_descriptor(descriptor)
            
            query = """
                SELECT sc.*, sa.stripe_account_id
                FROM stripe_charges sc
                JOIN stripe_accounts sa ON sa.store_id = sc.linked_store_id
                WHERE sc.linked_store_id = %s
                AND sc.amount = %s
                AND (
                    sc.statement_descriptor LIKE %s
                    OR sc.calculated_statement_descriptor LIKE %s
                )
                AND sc.status = 'succeeded'
                ORDER BY sc.created_at DESC
                LIMIT 1
            """
            
            conn = None
            try:
                conn = self.db.pool.getconn()
                with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                    cursor.execute(
                        query, 
                        (store_id, amount, f"%{parsed['merchant']}%", f"%{parsed['merchant']}%")
                    )
                    row = cursor.fetchone()
                    return dict(row) if row else None
            finally:
                if conn:
                    self.db.pool.putconn(conn)
                
        except Exception as e:
            logger.error(f"Error finding Stripe transaction: {str(e)}")
            return None
    
    def _match_order(self, transaction_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Match a Stripe charge to order metadata"""
        try:
            charge_id = transaction_data.get("id")
            if not charge_id:
                return None
            
            # Get charge metadata which might contain order info
            query = """
                SELECT metadata, payment_intent_id, description
                FROM stripe_charges
                WHERE id = %s
            """
            
            conn = None
            try:
                conn = self.db.pool.getconn()
                with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                    cursor.execute(query, (charge_id,))
                    row = cursor.fetchone()
                    if row:
                        metadata = row.get("metadata", {})
                        return {
                            "order_id": metadata.get("order_id"),
                            "order_number": metadata.get("order_number"),
                            "payment_intent": row.get("payment_intent_id"),
                            "description": row.get("description")
                        }
                    return None
            finally:
                if conn:
                    self.db.pool.putconn(conn)
                
        except Exception as e:
            logger.error(f"Error matching Stripe order: {str(e)}")
            return None
    
    def _calculate_match_quality(self, descriptor: str, amount: int, charge: Dict[str, Any]) -> float:
        """Calculate the quality of the match (0.0 to 1.0)"""
        quality = 0.0
        
        # Parse descriptors for comparison
        parsed_descriptor = parse_descriptor(descriptor)
        charge_descriptor = charge.get("statementDescriptor", "") or charge.get("calculatedStatementDescriptor", "")
        
        # Descriptor similarity (fuzzy match)
        if parsed_descriptor['merchant'].upper() in charge_descriptor.upper():
            quality += 0.4
        elif charge_descriptor.upper() in descriptor.upper():
            quality += 0.3
        
        # Exact amount match
        if charge.get("amount") == amount:
            quality += 0.4
        
        # Charge success status
        if charge.get("status") == "succeeded":
            quality += 0.1
        
        # Has payment intent (more structured)
        if charge.get("paymentIntentId"):
            quality += 0.1
        
        return min(quality, 1.0)

    def _make_stripe_api_call(self, store_id: str, api_call_func, stripe_account: Optional[Dict[str, Any]] = None, *args, **kwargs):
        """
        Make Stripe API call with automatic token refresh on failure

        Args:
            store_id: The store ID
            api_call_func: The Stripe API function to call
            stripe_account: Optional pre-fetched Stripe account data
            *args, **kwargs: Arguments to pass to the API function

        Returns:
            API call result or raises exception
        """
        try:
            # Use provided stripe_account or fetch it
            if not stripe_account:
                stripe_account = self._get_stripe_account_with_valid_token(store_id)

            if not stripe_account:
                raise Exception(f"No valid Stripe account found for store {store_id}")

            # Prepare API call parameters
            access_token = stripe_account.get("access_token")  # TODO: Add to schema
            stripe_account_id = stripe_account.get("stripe_account_id")  # Correct field name

            # Add authentication to kwargs
            if access_token and stripe_account_id:
                kwargs["api_key"] = access_token
            elif stripe_account_id:
                kwargs["stripe_account"] = stripe_account_id

            # Make the API call
            return api_call_func(*args, **kwargs)

        except stripe.error.AuthenticationError as e:
            logger.warning(f"Authentication error for store {store_id}, attempting token refresh: {str(e)}")

            # Try to refresh token and retry once
            refreshed_account = self._get_stripe_account_with_valid_token(store_id)
            if refreshed_account and refreshed_account.get("access_token"):
                logger.info(f"Token refreshed for store {store_id}, retrying API call")

                # Update kwargs with new token
                if "api_key" in kwargs:
                    kwargs["api_key"] = refreshed_account["access_token"]

                # Retry the API call
                return api_call_func(*args, **kwargs)
            else:
                logger.error(f"Could not refresh token for store {store_id}")
                raise

        except Exception as e:
            logger.error(f"Stripe API call failed for store {store_id}: {str(e)}")
            raise

    # ========================================
    # OAUTH TOKEN MANAGEMENT METHODS
    # ========================================

    def _refresh_access_token(self, refresh_token: str) -> Optional[StripeOAuthTokens]:
        """
        Refresh expired access token using refresh token
        """
        try:
            data = {
                'refresh_token': refresh_token,
                'grant_type': 'refresh_token'
            }

            response = requests.post(
                self.oauth_token_url,
                auth=(self.stripe_secret_key, ''),  # Basic auth with empty password
                data=data,
                headers={
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                timeout=self.oauth_timeout
            )

            if response.status_code != 200:
                error_data = {}
                try:
                    error_data = response.json()
                except:
                    error_data = {'error': 'unknown', 'error_description': response.text}

                error_type = error_data.get('error', 'unknown')
                error_desc = error_data.get('error_description', 'No description')

                if error_type == 'invalid_grant':
                    logger.error(f"Refresh token invalid or expired: {error_desc}")
                    raise RefreshTokenExpiredError(f"Refresh token is invalid or expired: {error_desc}")
                else:
                    logger.error(f"Stripe OAuth token refresh failed ({error_type}): {error_desc}")
                    raise RefreshTokenError(f"Token refresh failed: {error_type} - {error_desc}")

            token_data = response.json()

            # Validate required fields
            required_fields = ['access_token', 'refresh_token', 'stripe_user_id']
            for field in required_fields:
                if not token_data.get(field):
                    raise ValueError(f"Invalid token refresh response: missing {field}")

            logger.info(f"Successfully refreshed token for Stripe account: {token_data['stripe_user_id']}")

            return StripeOAuthTokens(
                access_token=token_data['access_token'],
                refresh_token=token_data['refresh_token'],
                stripe_user_id=token_data['stripe_user_id'],
                livemode=token_data.get('livemode', False),
                scope=token_data.get('scope'),
                stripe_publishable_key=token_data.get('stripe_publishable_key'),
                token_type=token_data.get('token_type', 'bearer')
            )

        except (RefreshTokenError, RefreshTokenExpiredError):
            return None
        except Exception as e:
            logger.error(f"Unexpected error refreshing token: {str(e)}")
            return None

    def _validate_access_token(self, access_token: str, stripe_user_id: str) -> bool:
        """
        Validate if access token is still valid by making a simple API call
        """
        try:
            # Test token by retrieving the account information
            response = requests.get(
                f"https://api.stripe.com/v1/accounts/{stripe_user_id}",
                headers={
                    'Authorization': f'Bearer {access_token}',
                    'Content-Type': 'application/json'
                },
                timeout=self.validation_timeout
            )

            if response.status_code == 200:
                logger.debug(f"Access token valid for Stripe account: {stripe_user_id}")
                return True
            elif response.status_code == 401:
                logger.info(f"Access token expired for Stripe account: {stripe_user_id}")
                return False
            else:
                logger.warning(f"Unexpected response validating token: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"Error validating access token: {str(e)}")
            return False

    def _update_store_tokens(self, store_id: str, tokens: StripeOAuthTokens) -> bool:
        """Update store with new tokens"""
        try:
            # TODO: StripeAccount schema needs to be extended to support OAuth tokens
            # Need to add: access_token, refresh_token, last_refreshed_at columns
            logger.warning(f"OAuth token update not implemented - StripeAccount schema needs OAuth fields")
            logger.info(f"Would update tokens for store {store_id} (access_token: {tokens.access_token[:12]}...)")

            # Temporarily return True to not break the flow
            return True

            # TODO: Uncomment when schema is updated
            # query = """
            #     UPDATE stripe_accounts
            #     SET access_token = %s,
            #         refresh_token = %s,
            #         last_refreshed_at = %s,
            #         updated_at = %s
            #     WHERE store_id = %s
            # """
            #
            # conn = None
            # try:
            #     conn = self.db.pool.getconn()
            #     with conn.cursor() as cursor:
            #         cursor.execute(query, (
            #             tokens.access_token,
            #             tokens.refresh_token,
            #             datetime.now(timezone.utc),
            #             datetime.now(timezone.utc),
            #             store_id
            #         ))
            #         conn.commit()
            #
            #     logger.info(f"Updated tokens for store {store_id}")
            #     return True
            # finally:
            #     if conn:
            #         self.db.pool.putconn(conn)

        except Exception as e:
            logger.error(f"Error updating store tokens for {store_id}: {str(e)}")
            return False

    def _mark_store_inactive(self, store_id: str, reason: str) -> None:
        """Mark LinkedStore as inactive due to token issues"""
        try:
            # Update the LinkedStore status, not StripeAccount
            # TODO: LinkedStore schema needs last_error field to track error reasons
            query = """
                UPDATE linked_stores
                SET is_active = false,
                    updated_at = %s
                WHERE id = %s
            """

            conn = None
            try:
                conn = self.db.pool.getconn()
                with conn.cursor() as cursor:
                    cursor.execute(query, (
                        datetime.now(timezone.utc),
                        store_id
                    ))
                    conn.commit()

                logger.warning(f"Marked LinkedStore {store_id} as inactive: {reason}")
                logger.info(f"Error reason (not stored in DB): {reason}")
            finally:
                if conn:
                    self.db.pool.putconn(conn)

        except Exception as e:
            logger.error(f"Error marking LinkedStore inactive for {store_id}: {str(e)}")