"""
Base provider class for payment system integrations
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

class BaseProvider(ABC):
    """Abstract base class for payment providers"""
    
    def __init__(self, db_service):
        self.db = db_service
    
    # ========================================
    # PUBLIC METHODS (ABSTRACT)
    # ========================================
        
    @abstractmethod
    def find_matched_entry(self, store_id: str, block: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Find the matching entry for the given store and block data.
        
        Args:
            store_id (str): The store ID to search in.
            block (dict): The block data to match against.
        
        Returns:
            dict | None: The matched entry, or None if no match is found.
        """
        pass
    
    # ========================================
    # PUBLIC METHODS (UTILITY)
    # ========================================
    
    def get_provider_name(self) -> str:
        """Get the provider name"""
        return self.__class__.__name__.replace("Provider", "").upper()
    
    def validate_block_data(self, block: Dict[str, Any]) -> bool:
        """Validate required block data for this provider"""
        required_fields = ["descriptor", "amount"]
        return all(field in block and block[field] is not None for field in required_fields)
    
    def format_match_result(self, match_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format the match result with common fields"""
        return {
            "id": match_data.get("id"),
            "provider": self.get_provider_name().lower(),
            "match_data": match_data
        }