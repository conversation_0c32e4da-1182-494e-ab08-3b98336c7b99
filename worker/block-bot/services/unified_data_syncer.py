"""
Unified Data Syncer Service
Handles syncing transaction and dispute data from Shopify and Stripe to unified tables
"""
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, text
from datetime import datetime, timedelta
import logging
import uuid

from models import (
    UnifiedTransaction, UnifiedDispute, User, LinkedStore,
    ShopifyTransaction, ShopifyDispute, StripePaymentIntent, StripeCharge, StripeDispute
)
from config import db, settings

logger = logging.getLogger(__name__)

class UnifiedDataSyncerService:
    """Service for syncing provider-specific data to unified tables"""
    
    def __init__(self, session: Session = None):
        self.session = session or db.get_session()
        self._should_close_session = session is None
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self._should_close_session:
            self.session.close()
    
    def _bulk_upsert_unified_transactions(self, transactions_data: List[Dict[str, Any]]) -> int:
        """
        Bulk upsert unified transactions using PostgreSQL ON CONFLICT
        
        Args:
            transactions_data: List of dictionaries with transaction data
            
        Returns:
            Number of upserted records
        """
        if not transactions_data:
            return 0
        
        # Memory safety check
        if len(transactions_data) > settings.MAX_UPSERT_BATCH_SIZE:
            raise ValueError(f"Batch too large ({len(transactions_data)}), maximum allowed: {settings.MAX_UPSERT_BATCH_SIZE}")
        
        try:
            from sqlalchemy.dialects.postgresql import insert
            
            stmt = insert(UnifiedTransaction.__table__)
            
            # ON CONFLICT clause using provider + provider_transaction_id unique constraint
            upsert_stmt = stmt.on_conflict_do_update(
                index_elements=['provider', 'provider_transaction_id'],
                set_={
                    'user_id': stmt.excluded.user_id,
                    'linked_store_id': stmt.excluded.linked_store_id,
                    'amount': stmt.excluded.amount,
                    'currency': stmt.excluded.currency,
                    'status': stmt.excluded.status,
                    'processed_at': stmt.excluded.processed_at,
                    'updated_at': stmt.excluded.updated_at
                }
            )
            
            self.session.execute(upsert_stmt, transactions_data)
            self.session.flush()
            
            logger.info(f"Bulk upserted {len(transactions_data)} unified transactions")
            return len(transactions_data)
            
        except Exception as e:
            logger.error(f"Error in bulk upsert unified transactions: {str(e)}")
            raise
    
    def _bulk_upsert_unified_disputes(self, disputes_data: List[Dict[str, Any]]) -> int:
        """
        Bulk upsert unified disputes using PostgreSQL ON CONFLICT
        
        Args:
            disputes_data: List of dictionaries with dispute data
            
        Returns:
            Number of upserted records
        """
        if not disputes_data:
            return 0
        
        # Memory safety check
        if len(disputes_data) > settings.MAX_UPSERT_BATCH_SIZE:
            raise ValueError(f"Batch too large ({len(disputes_data)}), maximum allowed: {settings.MAX_UPSERT_BATCH_SIZE}")
        
        try:
            from sqlalchemy.dialects.postgresql import insert
            
            stmt = insert(UnifiedDispute.__table__)
            
            # ON CONFLICT clause using provider + provider_dispute_id unique constraint
            upsert_stmt = stmt.on_conflict_do_update(
                index_elements=['provider', 'provider_dispute_id'],
                set_={
                    'user_id': stmt.excluded.user_id,
                    'linked_store_id': stmt.excluded.linked_store_id,
                    'unified_transaction_id': stmt.excluded.unified_transaction_id,
                    'amount': stmt.excluded.amount,
                    'currency': stmt.excluded.currency,
                    'type': stmt.excluded.type,
                    'status': stmt.excluded.status,
                    'reason': stmt.excluded.reason,
                    'initiated_at': stmt.excluded.initiated_at,
                    'updated_at': stmt.excluded.updated_at
                }
            )
            
            self.session.execute(upsert_stmt, disputes_data)
            self.session.flush()
            
            logger.info(f"Bulk upserted {len(disputes_data)} unified disputes")
            return len(disputes_data)
            
        except Exception as e:
            logger.error(f"Error in bulk upsert unified disputes: {str(e)}")
            raise
    
    def sync_shopify_transactions(
        self, 
        user_id: Optional[str] = None, 
        store_id: Optional[str] = None,
        batch_size: int = 500,
        days_back: int = 30
    ) -> int:
        """
        Sync ALL Shopify transactions to unified_transactions table using SQLAlchemy models
        Processes in batches until all data is synced
        
        Args:
            user_id: Optional filter by user_id
            store_id: Optional filter by store_id  
            batch_size: Number of records to process in each batch
            days_back: How many days back to sync data
            
        Returns:
            Total number of transactions synced across all batches
        """
        total_synced = 0
        offset = 0
        
        try:
            # Calculate date range
            date_from = datetime.utcnow() - timedelta(days=days_back)
            
            while True:
                # Build query using SQLAlchemy model
                query = self.session.query(ShopifyTransaction).filter(
                    ShopifyTransaction.processed_at >= date_from
                )
                
                if user_id:
                    query = query.filter(ShopifyTransaction.user_id == int(user_id))
                
                if store_id:
                    query = query.filter(ShopifyTransaction.linked_store_id == store_id)
                
                # Order by most recent and process in batches
                shopify_transactions = query.order_by(
                    ShopifyTransaction.processed_at.desc()
                ).offset(offset).limit(batch_size).all()
                
                # Break if no more transactions to process
                if not shopify_transactions:
                    if total_synced == 0:
                        logger.info("No Shopify transactions found to sync")
                    break
                
                logger.info(f"Processing Shopify batch {offset//batch_size + 1}: {len(shopify_transactions)} transactions")
                
                # Group by linked_store_id to minimize queries
                store_ids = list(set(st.linked_store_id for st in shopify_transactions))
                
                # Batch fetch all linked_stores
                linked_stores_map = {}
                if store_ids:
                    linked_stores = self.session.query(LinkedStore).filter(
                        LinkedStore.id.in_(store_ids)
                    ).all()
                    linked_stores_map = {store.id: store for store in linked_stores}
                
                # Prepare data for bulk upsert
                transactions_data = []
                for st in shopify_transactions:
                    linked_store = linked_stores_map.get(st.linked_store_id)
                    
                    if not linked_store:
                        logger.warning(f"LinkedStore not found for shopify transaction {st.id}")
                        continue
                    
                    transaction_data = {
                        'id': str(uuid.uuid4()),
                        'user_id': linked_store.user_id,  # Get from cached linked_store
                        'linked_store_id': st.linked_store_id,
                        'provider': 'shopify',
                        'provider_transaction_id': str(st.id),
                        'amount': st.amount if st.amount else 0,  # Already in cents for Shopify
                        'currency': st.currency or 'USD',
                        'status': st.status or 'unknown',
                        'processed_at': st.processed_at or st.created_at,
                        'created_at': st.created_at,
                        'updated_at': st.updated_at
                    }
                    transactions_data.append(transaction_data)
                
                if transactions_data:
                    # Execute bulk upsert for this batch
                    batch_synced = self._bulk_upsert_unified_transactions(transactions_data)
                    self.session.commit()
                    total_synced += batch_synced
                    logger.info(f"Batch {offset//batch_size + 1}: Synced {batch_synced} Shopify transactions")
                
                # Move to next batch
                offset += batch_size
            
            if total_synced > 0:
                logger.info(f"✅ Completed syncing {total_synced} total Shopify transactions to unified table")
            
            return total_synced
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"Error syncing Shopify transactions: {str(e)}")
            raise
    
    def sync_stripe_transactions(
        self, 
        user_id: Optional[str] = None, 
        store_id: Optional[str] = None,
        batch_size: int = 500,
        days_back: int = 30
    ) -> int:
        """
        Sync ALL Stripe payment intents to unified_transactions table using SQLAlchemy models
        Processes in batches until all data is synced
        
        Args:
            user_id: Optional filter by user_id
            store_id: Optional filter by store_id
            batch_size: Number of records to process in each batch  
            days_back: How many days back to sync data
            
        Returns:
            Total number of transactions synced across all batches
        """
        total_synced = 0
        offset = 0
        
        try:
            # Calculate date range
            date_from = datetime.utcnow() - timedelta(days=days_back)
            
            while True:
                # Build query using SQLAlchemy model
                query = self.session.query(StripePaymentIntent).filter(
                    StripePaymentIntent.created_at >= date_from
                )
                
                if store_id:
                    query = query.filter(StripePaymentIntent.linked_store_id == store_id)
                
                # Order by most recent and process in batches
                stripe_payment_intents = query.order_by(
                    StripePaymentIntent.created_at.desc()
                ).offset(offset).limit(batch_size).all()
                
                # Break if no more transactions to process
                if not stripe_payment_intents:
                    if total_synced == 0:
                        logger.info("No Stripe payment intents found to sync")
                    break
                
                logger.info(f"Processing Stripe batch {offset//batch_size + 1}: {len(stripe_payment_intents)} payment intents")
                
                # Group by linked_store_id to minimize queries
                store_ids = list(set(pi.linked_store_id for pi in stripe_payment_intents))
                
                # Batch fetch all linked_stores
                linked_stores_map = {}
                if store_ids:
                    linked_stores = self.session.query(LinkedStore).filter(
                        LinkedStore.id.in_(store_ids)
                    ).all()
                    linked_stores_map = {store.id: store for store in linked_stores}
                
                # Prepare data for bulk upsert
                transactions_data = []
                for pi in stripe_payment_intents:
                    linked_store = linked_stores_map.get(pi.linked_store_id)
                    
                    if not linked_store:
                        logger.warning(f"LinkedStore not found for stripe payment intent {pi.id}")
                        continue
                    
                    # Check user_id filter if provided
                    if user_id and str(linked_store.user_id) != user_id:
                        continue

                    transaction_data = {
                        'id': str(uuid.uuid4()),
                        'user_id': linked_store.user_id,  # Get from cached linked_store
                        'linked_store_id': pi.linked_store_id,
                        'provider': 'stripe',
                        'provider_transaction_id': pi.id,
                        'amount': int(pi.amount) if pi.amount else 0,  # Already in cents for Stripe
                        'currency': pi.currency or 'USD',
                        'status': pi.status or 'unknown',
                        'processed_at': pi.created_at,
                        'created_at': pi.created_at,
                        'updated_at': pi.updated_at
                    }
                    transactions_data.append(transaction_data)
                
                if transactions_data:
                    # Execute bulk upsert for this batch
                    batch_synced = self._bulk_upsert_unified_transactions(transactions_data)
                    self.session.commit()
                    total_synced += batch_synced
                    logger.info(f"Batch {offset//batch_size + 1}: Synced {batch_synced} Stripe payment intents")
                
                # Move to next batch
                offset += batch_size
            
            if total_synced > 0:
                logger.info(f"✅ Completed syncing {total_synced} total Stripe payment intents to unified table")
            
            return total_synced
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"Error syncing Stripe payment intents: {str(e)}")
            raise
    
    def sync_shopify_disputes(
        self, 
        user_id: Optional[str] = None, 
        store_id: Optional[str] = None,
        batch_size: int = 500,
        days_back: int = 90
    ) -> int:
        """
        Sync ALL Shopify disputes to unified_disputes table using SQLAlchemy models
        Processes in batches until all data is synced
        
        Args:
            user_id: Optional filter by user_id
            store_id: Optional filter by store_id
            batch_size: Number of records to process in each batch
            days_back: How many days back to sync data
            
        Returns:
            Total number of disputes synced across all batches
        """
        total_synced = 0
        offset = 0
        
        try:
            # Calculate date range
            date_from = datetime.utcnow() - timedelta(days=days_back)
            
            while True:
                # Build query using SQLAlchemy model
                query = self.session.query(ShopifyDispute).filter(
                    ShopifyDispute.initiated_at >= date_from
                )
                
                if store_id:
                    query = query.filter(ShopifyDispute.linked_store_id == store_id)
                
                # Order by most recent and process in batches
                shopify_disputes = query.order_by(
                    ShopifyDispute.initiated_at.desc()
                ).offset(offset).limit(batch_size).all()
                
                # Break if no more disputes to process
                if not shopify_disputes:
                    if total_synced == 0:
                        logger.info("No Shopify disputes found to sync")
                    break
            
            # Group by linked_store_id to minimize queries
            store_ids = list(set(sd.linked_store_id for sd in shopify_disputes))
            
            # Batch fetch all linked_stores
            linked_stores_map = {}
            if store_ids:
                linked_stores = self.session.query(LinkedStore).filter(
                    LinkedStore.id.in_(store_ids)
                ).all()
                linked_stores_map = {store.id: store for store in linked_stores}
            
            # Batch fetch unified transactions for disputes that have transaction_id
            unified_transactions_map = {}
            transaction_ids = [str(sd.transaction_id) for sd in shopify_disputes if sd.transaction_id]
            
            # Optimize: Skip unified transaction lookup if no transaction_ids or too many
            if transaction_ids and len(transaction_ids) <= 1000:  # Limit to avoid huge IN clauses
                try:
                    unified_transactions = self.session.query(UnifiedTransaction).filter(
                        UnifiedTransaction.provider == 'shopify',
                        UnifiedTransaction.provider_transaction_id.in_(transaction_ids)
                    ).all()
                    # Key by (provider_transaction_id, linked_store_id) for uniqueness
                    unified_transactions_map = {
                        (ut.provider_transaction_id, ut.linked_store_id): ut.id 
                        for ut in unified_transactions
                    }
                    logger.info(f"Loaded {len(unified_transactions_map)} unified transaction references for Shopify disputes")
                except Exception as e:
                    logger.warning(f"Skipping unified transaction lookup due to error: {e}")
                    unified_transactions_map = {}
            elif len(transaction_ids) > 1000:
                logger.info(f"Skipping unified transaction lookup for {len(transaction_ids)} transactions (performance optimization)")
            else:
                logger.info("No transaction_ids to lookup")
            
            # Prepare data for bulk upsert
            disputes_data = []
            for sd in shopify_disputes:
                linked_store = linked_stores_map.get(sd.linked_store_id)
                
                if not linked_store:
                    logger.warning(f"LinkedStore not found for shopify dispute {sd.id}")
                    continue
                
                # Check user_id filter if provided
                if user_id and str(linked_store.user_id) != user_id:
                    continue
                
                # Find matching unified transaction from cache
                unified_transaction_id = None
                if sd.transaction_id:
                    unified_transaction_id = unified_transactions_map.get(
                        (str(sd.transaction_id), sd.linked_store_id)
                    )

                dispute_data = {
                    'id': str(uuid.uuid4()),
                    'user_id': linked_store.user_id,  # Get from cached linked_store
                    'linked_store_id': sd.linked_store_id,
                    'provider': 'shopify',
                    'provider_dispute_id': str(sd.id),
                    'unified_transaction_id': unified_transaction_id,
                    'amount': sd.amount if sd.amount else 0,  # Already in cents for Shopify
                    'currency': sd.currency or 'USD',
                    'type': sd.type or 'chargeback',
                    'status': sd.status or 'unknown',
                    'reason': sd.reason,
                    'initiated_at': sd.initiated_at,
                    'created_at': sd.created_at,
                    'updated_at': sd.updated_at
                }
                disputes_data.append(dispute_data)
            
            if not disputes_data:
                logger.info("No valid Shopify disputes found after processing")
                return 0
            
            # Execute bulk upsert
            synced_count = self._bulk_upsert_unified_disputes(disputes_data)
            self.session.commit()
            
            logger.info(f"Synced {synced_count} Shopify disputes to unified table")
            return synced_count
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"Error syncing Shopify disputes: {str(e)}")
            raise
    
    def sync_stripe_disputes(
        self, 
        user_id: Optional[str] = None, 
        store_id: Optional[str] = None,
        batch_size: int = 500,
        days_back: int = 90
    ) -> int:
        """
        Sync ALL Stripe disputes to unified_disputes table using SQLAlchemy models
        Processes in batches until all data is synced
        
        Args:
            user_id: Optional filter by user_id
            store_id: Optional filter by store_id
            batch_size: Number of records to process in each batch
            days_back: How many days back to sync data
            
        Returns:
            Total number of disputes synced across all batches
        """
        total_synced = 0
        offset = 0
        
        try:
            # Calculate date range
            date_from = datetime.utcnow() - timedelta(days=days_back)
            
            while True:
                # Build query using SQLAlchemy model
                query = self.session.query(StripeDispute).filter(
                    StripeDispute.created_at >= date_from
                )
                
                if store_id:
                    query = query.filter(StripeDispute.linked_store_id == store_id)
                
                # Order by most recent and process in batches
                stripe_disputes = query.order_by(
                    StripeDispute.created_at.desc()
                ).offset(offset).limit(batch_size).all()
                
                # Break if no more disputes to process
                if not stripe_disputes:
                    if total_synced == 0:
                        logger.info("No Stripe disputes found to sync")
                    break
                    
                logger.info(f"Processing Stripe disputes batch {offset//batch_size + 1}: {len(stripe_disputes)} disputes")
                
                # Group by linked_store_id to minimize queries
                store_ids = list(set(sd.linked_store_id for sd in stripe_disputes))
                
                # Batch fetch all linked_stores
                linked_stores_map = {}
                if store_ids:
                    linked_stores = self.session.query(LinkedStore).filter(
                        LinkedStore.id.in_(store_ids)
                    ).all()
                    linked_stores_map = {store.id: store for store in linked_stores}
                
                # Batch fetch stripe charges and unified transactions for complex lookup
                charge_ids = [sd.charge_id for sd in stripe_disputes if sd.charge_id]
                stripe_charges_map = {}
                unified_transactions_map = {}
                
                # Optimize: Limit charge_ids to avoid performance issues
                if charge_ids and len(charge_ids) <= 500:  # Smaller limit for Stripe (more complex lookup)
                    try:
                        # Get all stripe charges
                        stripe_charges = self.session.query(StripeCharge).filter(
                            StripeCharge.id.in_(charge_ids)
                        ).all()
                        stripe_charges_map = {charge.id: charge for charge in stripe_charges}
                        
                        # Get payment intent IDs from charges
                        payment_intent_ids = [charge.payment_intent_id for charge in stripe_charges if charge.payment_intent_id]
                        
                        if payment_intent_ids and len(payment_intent_ids) <= 500:
                            # Get unified transactions
                            unified_transactions = self.session.query(UnifiedTransaction).filter(
                                UnifiedTransaction.provider == 'stripe',
                                UnifiedTransaction.provider_transaction_id.in_(payment_intent_ids)
                            ).all()
                            # Key by (provider_transaction_id, linked_store_id) for uniqueness
                            unified_transactions_map = {
                                (ut.provider_transaction_id, ut.linked_store_id): ut.id 
                                for ut in unified_transactions
                            }
                            logger.info(f"Loaded {len(unified_transactions_map)} unified transaction references for Stripe disputes")
                        else:
                            logger.info(f"Skipping unified transaction lookup for {len(payment_intent_ids)} payment intents (performance optimization)")
                    except Exception as e:
                        logger.warning(f"Skipping Stripe unified transaction lookup due to error: {e}")
                        stripe_charges_map = {}
                        unified_transactions_map = {}
                elif len(charge_ids) > 500:
                    logger.info(f"Skipping Stripe charges lookup for {len(charge_ids)} charges (performance optimization)")
                else:
                    logger.info("No charge_ids to lookup for Stripe disputes")
                
                # Prepare data for bulk upsert
                disputes_data = []
                for sd in stripe_disputes:
                    linked_store = linked_stores_map.get(sd.linked_store_id)
                    
                    if not linked_store:
                        logger.warning(f"LinkedStore not found for stripe dispute {sd.id}")
                        continue
                    
                    # Check user_id filter if provided
                    if user_id and str(linked_store.user_id) != user_id:
                        continue
                    
                    # Find matching unified transaction from cache
                    unified_transaction_id = None
                    if sd.charge_id:
                        stripe_charge = stripe_charges_map.get(sd.charge_id)
                        if stripe_charge and stripe_charge.payment_intent_id:
                            unified_transaction_id = unified_transactions_map.get(
                                (stripe_charge.payment_intent_id, sd.linked_store_id)
                            )

                    
                    dispute_data = {
                        'id': str(uuid.uuid4()),
                        'user_id': linked_store.user_id,  # Get from cached linked_store
                        'linked_store_id': sd.linked_store_id,
                        'provider': 'stripe',
                        'provider_dispute_id': sd.id,
                        'unified_transaction_id': unified_transaction_id,
                        'amount': int(sd.amount) if sd.amount else 0,  # Already in cents for Stripe
                        'currency': sd.currency or 'USD',
                        'reason': sd.reason,
                        'status': sd.status or 'unknown',
                        'initiated_at': sd.created_at,  # Use created_at as initiated_at
                        'created_at': sd.created_at,
                        'updated_at': sd.updated_at
                    }
                    disputes_data.append(dispute_data)
                
                if disputes_data:
                    # Execute bulk upsert for this batch
                    batch_synced = self._bulk_upsert_unified_disputes(disputes_data)
                    self.session.commit()
                    total_synced += batch_synced
                    logger.info(f"Batch {offset//batch_size + 1}: Synced {batch_synced} Stripe disputes")
                
                # Move to next batch
                offset += batch_size
            
            if total_synced > 0:
                logger.info(f"✅ Completed syncing {total_synced} total Stripe disputes to unified table")
            
            return total_synced
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"Error syncing Stripe disputes: {str(e)}")
            raise
    
    def sync_all_data(
        self, 
        user_id: Optional[str] = None, 
        store_id: Optional[str] = None,
        batch_size: int = 500,
        transaction_days_back: int = 30,
        dispute_days_back: int = 90,
        skip_transaction_refs: bool = False
    ) -> Dict[str, int]:
        """
        Sync all provider data to unified tables
        
        Args:
            user_id: Optional filter by user_id
            store_id: Optional filter by store_id
            batch_size: Number of records to process in each batch
            transaction_days_back: How many days back to sync transactions
            dispute_days_back: How many days back to sync disputes
            
        Returns:
            Dictionary with sync counts
        """
        try:
            results = {
                'shopify_transactions': 0,
                'stripe_transactions': 0,
                'shopify_disputes': 0,
                'stripe_disputes': 0
            }
            
            # Sync transactions first (disputes depend on them)
            logger.info("Starting unified data sync - transactions first")
            
            results['shopify_transactions'] = self.sync_shopify_transactions(
                user_id, store_id, batch_size, transaction_days_back
            )
            
            results['stripe_transactions'] = self.sync_stripe_transactions(
                user_id, store_id, batch_size, transaction_days_back
            )
            
            # Sync disputes after transactions are available
            logger.info("Syncing disputes with transaction references")
            
            results['shopify_disputes'] = self.sync_shopify_disputes(
                user_id, store_id, batch_size, dispute_days_back
            )
            
            results['stripe_disputes'] = self.sync_stripe_disputes(
                user_id, store_id, batch_size, dispute_days_back
            )
            
            total_synced = sum(results.values())
            logger.info(f"Unified data sync completed: {total_synced} total records synced")
            logger.info(f"Sync breakdown: {results}")
            
            return results
            
        except Exception as e:
            self.session.rollback()
            logger.error(f"Error in unified data sync: {str(e)}")
            raise

# Convenience functions for one-off operations

def sync_all_unified_data(
    user_id: Optional[str] = None,
    store_id: Optional[str] = None, 
    batch_size: int = 500,
    transaction_days_back: int = 30,
    dispute_days_back: int = 90
) -> Dict[str, int]:
    """
    Convenience function to sync all unified data
    
    Args:
        user_id: Optional filter by user_id
        store_id: Optional filter by store_id
        batch_size: Number of records to process in each batch
        transaction_days_back: How many days back to sync transactions
        dispute_days_back: How many days back to sync disputes
        
    Returns:
        Dictionary with sync counts
    """
    with UnifiedDataSyncerService() as syncer:
        return syncer.sync_all_data(
            user_id, store_id, batch_size, 
            transaction_days_back, dispute_days_back
        )

def sync_unified_transactions_only(
    user_id: Optional[str] = None,
    store_id: Optional[str] = None,
    batch_size: int = 500, 
    days_back: int = 30
) -> Dict[str, int]:
    """
    Convenience function to sync only transactions
    
    Returns:
        Dictionary with sync counts for transactions only
    """
    with UnifiedDataSyncerService() as syncer:
        results = {
            'shopify_transactions': syncer.sync_shopify_transactions(user_id, store_id, batch_size, days_back),
            'stripe_transactions': syncer.sync_stripe_transactions(user_id, store_id, batch_size, days_back)
        }
        return results

def sync_unified_disputes_only(
    user_id: Optional[str] = None,
    store_id: Optional[str] = None,
    batch_size: int = 500,
    days_back: int = 90
) -> Dict[str, int]:
    """
    Convenience function to sync only disputes
    
    Returns:
        Dictionary with sync counts for disputes only  
    """
    with UnifiedDataSyncerService() as syncer:
        results = {
            'shopify_disputes': syncer.sync_shopify_disputes(user_id, store_id, batch_size, days_back),
            'stripe_disputes': syncer.sync_stripe_disputes(user_id, store_id, batch_size, days_back)
        }
        return results