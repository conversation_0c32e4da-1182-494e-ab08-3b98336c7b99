"""
Service for creating bills for block processing
"""

from typing import Optional, Dict, Any
from utils.logging import get_logger
import psycopg2.pool
import psycopg2.extras
import os
import json
from datetime import datetime

logger = get_logger("services.billing")

class BillingService:
    """Service for creating bills for block processing"""
    
    def __init__(self, db_pool: psycopg2.pool.ThreadedConnectionPool):
        self.pool = db_pool
        self.default_fees = {
            "ETHOCA": int(os.getenv("ETHOCA_BLOCK_FEE", "2500")),  # $25.00 in cents
            "RDR": int(os.getenv("RDR_BLOCK_FEE", "2500"))  # $25.00 in cents
        }
    
    # ========================================
    # PUBLIC METHODS
    # ========================================
    
    def create_bill_for_block(self, 
                              store_id: str, 
                              block_id: str, 
                              block_type: str,
                              custom_fee: Optional[int] = None) -> Optional[str]:
        """
        Create a bill for block processing
        
        Args:
            store_id: ID of the store to bill
            block_id: ID of the block being processed
            block_type: Type of block (ETHOCA or RDR)
            custom_fee: Custom fee amount in cents (optional)
            
        Returns:
            Bill ID if successful, None otherwise
        """
        conn = None
        try:
            # Determine fee amount
            fee_amount = custom_fee if custom_fee is not None else self.default_fees.get(block_type, 2500)
            
            logger.info(f"💰 Creating bill for block {block_id} (type: {block_type}), fee: ${fee_amount/100:.2f}")
            
            query = """
                INSERT INTO bills (
                    linked_store_id,
                    amount,
                    currency,
                    description,
                    status,
                    due_date,
                    created_at,
                    updated_at
                )
                VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
                RETURNING id
            """
            
            conn = self.pool.getconn()
            with conn.cursor() as cursor:
                # Calculate due date (30 days from now)
                from datetime import datetime, timedelta
                due_date = datetime.utcnow() + timedelta(days=30)

                cursor.execute(
                    query,
                    (
                        store_id,           # linked_store_id
                        fee_amount,         # amount (positive for charges)
                        "USD",              # currency
                        f"{block_type} Block Processing Fee - Block ID: {block_id}",  # description
                        "PENDING",          # status
                        due_date            # due_date
                    )
                )
                
                bill_id = cursor.fetchone()[0] if cursor.rowcount > 0 else None
                conn.commit()
                
                if bill_id:
                    logger.info(f"✅ Bill created: {bill_id} for block {block_id}")
                    
                    # Update store balance
                    self._update_store_balance(store_id, -fee_amount)
                    
                    return bill_id
                else:
                    logger.error(f"❌ Failed to create bill for block {block_id}")
                    return None
                    
        except Exception as e:
            logger.error(f"❌ Error creating bill for block {block_id}: {str(e)}")
            if conn:
                conn.rollback()
            return None
        finally:
            if conn:
                self.pool.putconn(conn)
    
    # ========================================
    # PRIVATE METHODS
    # ========================================
    
    def _update_store_balance(self, store_id: str, amount: int) -> bool:
        """Update store balance after billing"""
        # TODO: Store balance tracking not implemented in schema yet
        # Need to add balance field to LinkedStore or create separate StoreBalance table
        logger.info(f"💰 Would update balance for store {store_id}: ${amount/100:.2f} (not implemented)")
        return True

        # TODO: Uncomment when balance tracking is implemented
        # conn = None
        # try:
        #     query = """
        #         UPDATE linked_stores
        #         SET
        #             balance = COALESCE(balance, 0) + %s,
        #             updated_at = NOW()
        #         WHERE id = %s
        #     """
        #
        #     conn = self.pool.getconn()
        #     with conn.cursor() as cursor:
        #         cursor.execute(query, (amount, store_id))
        #         conn.commit()
        #         logger.info(f"✅ Updated balance for store {store_id}: ${amount/100:.2f}")
        #         return True
        #
        # except Exception as e:
        #     logger.error(f"❌ Failed to update store balance: {str(e)}")
        #     if conn:
        #         conn.rollback()
        #     return False
        # finally:
        #     if conn:
        #         self.pool.putconn(conn)