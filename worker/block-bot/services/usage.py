"""
Service for managing block usage records
"""

from typing import Dict, Any
from utils.logging import get_logger
import psycopg2.pool
import psycopg2.extras

logger = get_logger("services.block_usage")

class BlockUsageService:
    """Service for creating and managing block usage records"""
    
    def __init__(self, db_pool: psycopg2.pool.ThreadedConnectionPool):
        self.pool = db_pool
    
    # ========================================
    # PUBLIC METHODS
    # ========================================
        
    def create_usage_record(self, store_id: str, block_id: str, block_type: str) -> bool:
        """
        Create a block usage record
        """
        conn = None
        try:
            logger.info(f"Creating usage record for block {block_id} (type: {block_type}) for store {store_id}")
            
            query = """
                INSERT INTO block_usages (
                    store_id,
                    block_id,
                    type,
                    created_at
                )
                VALUES (%s, %s, %s, NOW())
                ON CONFLICT (store_id, block_id) DO NOTHING
                RETURNING id
            """
            
            conn = self.pool.getconn()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            cursor.execute(query, (store_id, block_id, block_type))
            result = cursor.fetchone()
            conn.commit()
            
            if result and result['id']:
                logger.info(f"✅ Block usage record created with ID: {result['id']}")
                return True
            else:
                logger.warning(f"⚠️ Block usage record already exists for block {block_id}")
                return False
                
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"❌ Error creating block usage record: {str(e)}")
            return False
        finally:
            if conn:
                self.pool.putconn(conn)