"""
Service for finding and managing linked stores
"""

from typing import Optional, Dict, Any
from utils.logging import get_logger
import psycopg2.pool
import psycopg2.extras

logger = get_logger("services.linked_store")

class LinkedStoreService:
    """Service for finding linked stores based on block data"""
    
    def __init__(self, db_pool: psycopg2.pool.ThreadedConnectionPool):
        self.pool = db_pool
    
    # ========================================
    # PUBLIC METHODS
    # ========================================
        
    def find_for_ethoca_block(self, descriptor: str) -> Optional[str]:
        """
        Find linked store for ETHOCA block based on descriptor
        """
        conn = None
        try:
            logger.info(f"[ETHOCA] Searching for linked store with descriptor: '{descriptor}'")
            
            query = """
                SELECT store_id
                FROM alert_infos
                WHERE alert_type = 'ETHOCA'
                AND descriptor = %s
                AND registration_status = 'EFFECTED'
                LIMIT 1
            """
            
            conn = self.pool.getconn()
            with conn.cursor() as cursor:
                cursor.execute(query, (descriptor,))
                result = cursor.fetchone()
                store_id = result[0] if result else None
                
                if store_id:
                    logger.info(f"[ETHOCA] ✅ Found linked store: {store_id} for descriptor: {descriptor}")
                else:
                    logger.warning(f"[ETHOCA] ⚠️ No linked store found for descriptor: {descriptor}")
                    
                return store_id
                
        except Exception as e:
            logger.error(f"[ETHOCA] Error finding linked store: {str(e)}")
            return None
        finally:
            if conn:
                self.pool.putconn(conn)
    
    def find_for_rdr_block(self, card_bin: str, caid: str) -> Optional[str]:
        """
        Find linked store for RDR block based on BIN and CAID
        """
        conn = None
        try:
            logger.info(f"[RDR] Searching for linked store with BIN: {card_bin}, CAID: {caid}")
            
            query = """
                SELECT store_id
                FROM alert_infos
                WHERE alert_type = 'RDR'
                AND bin = %s
                AND caid = %s
                AND registration_status = 'EFFECTED'
                LIMIT 1
            """
            
            conn = self.pool.getconn()
            with conn.cursor() as cursor:
                cursor.execute(query, (card_bin, caid))
                result = cursor.fetchone()
                store_id = result[0] if result else None
                
                if store_id:
                    logger.info(f"[RDR] ✅ Found linked store: {store_id} for BIN: {card_bin}, CAID: {caid}")
                else:
                    logger.warning(f"[RDR] ⚠️ No linked store found for BIN: {card_bin}, CAID: {caid}")
                    
                return store_id
                
        except Exception as e:
            logger.error(f"[RDR] Error finding linked store: {str(e)}")
            return None
        finally:
            if conn:
                self.pool.putconn(conn)
    
    def get_store(self, store_id: str) -> Optional[Dict[str, Any]]:
        """Get linked store with provider information for processing"""
        conn = None
        try:
            query = """
                SELECT ls.*,
                       ls.provider,
                       ls.store_name,
                       ls.is_active as store_status
                FROM linked_stores ls
                WHERE ls.id = %s
            """
            conn = self.pool.getconn()
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                cursor.execute(query, (store_id,))
                row = cursor.fetchone()
                if row:
                    store_data = dict(row)
                    logger.info(f"Retrieved store {store_id}: {store_data.get('store_name')} (provider: {store_data.get('provider')})")
                    return store_data
                else:
                    logger.warning(f"Store {store_id} not found")
                    return None
        except Exception as e:
            logger.error(f"Error getting store details {store_id}: {str(e)}")
            return None
        finally:
            if conn:
                self.pool.putconn(conn)