"""
Database service for block operations
"""

import os
import psycopg2
import psycopg2.pool
from typing import Optional
from urllib.parse import urlparse, parse_qs
from utils.logging import get_logger

logger = get_logger("database")

class DatabaseService:
    """Handles database connection pool for block processing"""
    
    def __init__(self):
        self.pool: Optional[psycopg2.pool.ThreadedConnectionPool] = None
        self.database_url = os.getenv("DATABASE_URL")
        
        # Parse the database URL and extract schema parameter
        if self.database_url:
            parsed = urlparse(self.database_url)
            query_params = parse_qs(parsed.query)
            self.schema = query_params.get('schema', ['chargeback'])[0]
            
            # psycopg2 uses the URL as-is, including the schema parameter
            self.clean_database_url = self.database_url
        else:
            self.schema = "chargeback"
            self.clean_database_url = None
    
    # ========================================
    # PUBLIC METHODS (CONNECTION MANAGEMENT)
    # ========================================
        
    def connect(self):
        """Initialize database connection pool"""
        if not self.clean_database_url:
            raise ValueError("DATABASE_URL environment variable not set")
            
        try:
            self.pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=2,
                maxconn=10,
                dsn=self.clean_database_url
            )
            logger.info("✅ Database connection pool established")
        except Exception as e:
            logger.error(f"❌ Failed to connect to database: {str(e)}")
            raise
    
    def disconnect(self):
        """Close database connection pool"""
        if self.pool:
            self.pool.closeall()
            logger.info("Database connection pool closed")
