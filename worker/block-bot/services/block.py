"""
Service for managing block database operations
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from utils.logging import get_logger
import psycopg2.pool
import psycopg2.extras
import json

logger = get_logger("services.block")


class BlockService:
    """Service for managing block records in database and processing logic"""
    
    def __init__(self, db_pool: psycopg2.pool.ThreadedConnectionPool):
        self.pool = db_pool
        self.schema = "chargeback"  # Database schema name
    
    # ========================================
    # PUBLIC METHODS
    # ========================================
    
    def get_pending_blocks(self, limit: int = None) -> List[Dict[str, Any]]:
        """
        Fetch all pending blocks from database
        """
        conn = None
        try:
            if limit:
                query = """
                    SELECT * FROM blocks
                    WHERE feedback_status = 'PENDING'
                    ORDER BY created_at ASC
                    LIMIT %s
                """
                params = (limit,)
            else:
                query = """
                    SELECT * FROM blocks
                    WHERE feedback_status = 'PENDING'
                    ORDER BY created_at ASC
                """
                params = ()
            
            conn = self.pool.getconn()
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                cursor.execute(query, params)
                rows = cursor.fetchall()
                blocks = [dict(row) for row in rows]
                
                logger.info(f"Fetched {len(blocks)} pending blocks")
                return blocks
                
        except Exception as e:
            logger.error(f"Error fetching pending blocks: {str(e)}")
            return []
        finally:
            if conn:
                self.pool.putconn(conn)
    
    def update_block_status(
        self, 
        block_id: str, 
        status: str,
        error_message: Optional[str] = None,
        linked_store_id: Optional[str] = None,
        matched_entry_id: Optional[str] = None
    ) -> bool:
        """
        Update block status and related fields
        """
        conn = None
        try:
            updates = {
                "feedback_status": status,
                "updated_at": datetime.utcnow()
            }
            
            if error_message:
                updates["feedback_data"] = json.dumps({"error": error_message})
            
            if status == 'SENT':
                updates["feedback_time"] = datetime.utcnow()
            
            # Build dynamic update query
            set_clauses = []
            values = []
            for key, value in updates.items():
                set_clauses.append(f'{key} = %s')
                values.append(value)
            
            values.append(block_id)  # Add block_id at the end for WHERE clause
            
            query = f"""
                UPDATE blocks
                SET {', '.join(set_clauses)}
                WHERE id = %s
                RETURNING id
            """
            
            conn = self.pool.getconn()
            with conn.cursor() as cursor:
                cursor.execute(query, values)
                result = cursor.fetchone()
                conn.commit()
                
                if result:
                    logger.info(f"Updated block {block_id} feedback_status to {status}")
                    return True
                else:
                    logger.warning(f"Block {block_id} not found for update")
                    return False
                    
        except Exception as e:
            logger.error(f"Error updating block {block_id}: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                self.pool.putconn(conn)
    
    def mark_block_processed(
        self,
        block_id: str,
        store_id: str,
        matched_entry_id: Optional[str] = None
    ) -> bool:
        """
        Mark a block as successfully processed
        """
        return self.update_block_status(
            block_id=block_id,
            status="SENT",
            linked_store_id=store_id,
            matched_entry_id=matched_entry_id
        )
    
    def mark_block_failed(
        self,
        block_id: str,
        error_message: str
    ) -> bool:
        """
        Mark a block as failed with error message
        """
        return self.update_block_status(
            block_id=block_id,
            status="FAILED",
            error_message=error_message
        )
    
    def mark_block_no_store(self, block_id: str) -> bool:
        """
        Mark a block as having no linked store
        """
        return self.update_block_status(
            block_id=block_id,
            status="FAILED",
            error_message="No linked store found"
        )
    
    def mark_block_no_match(
        self,
        block_id: str,
        store_id: str
    ) -> bool:
        """
        Mark a block as having no matched entry
        """
        return self.update_block_status(
            block_id=block_id,
            status="FAILED",
            error_message="No matching order/transaction found",
            linked_store_id=store_id
        )
    
    def process_rdr_block(self, block: Dict[str, Any], linked_store_service, usage_service, billing_service, config) -> Dict[str, Any]:
        """Process RDR block - business logic"""
        block_id = block.get("id")
        card_bin = block.get("card_bin")
        caid = block.get("caid")
        
        # Validate required fields
        if not card_bin or not caid:
            logger.warning(f"⚠️ Missing BIN or CAID for RDR block {block_id}")
            self.update_block_status(block_id, "INVALID_DATA")
            return {
                "success": False,
                "block_id": block_id,
                "error": "Missing required fields: cardBin or caid"
            }
        
        # Find linked store
        store_id = linked_store_service.find_for_rdr_block(card_bin, caid)
        
        if store_id:
            logger.info(f"✅ Found linked store: {store_id}")
            
            # Create block usage record
            usage_service.create_usage_record(store_id, block_id, "RDR")
            
            # Create bill
            billing_service.create_bill_for_block(
                store_id, block_id, "RDR", custom_fee=config.RDR_BLOCK_FEE
            )
            
            # Mark as processed
            self.mark_block_processed(block_id, store_id)
            
            return {
                "success": True,
                "block_id": block_id,
                "store_id": store_id,
                "message": "RDR block processed successfully"
            }
        else:
            logger.warning(f"⚠️ No linked store found for BIN: {card_bin}, CAID: {caid}")
            self.mark_block_no_store(block_id)
            return {
                "success": True,
                "block_id": block_id,
                "message": "No linked store found"
            }
    
    def process_ethoca_block(self, block: Dict[str, Any], linked_store_service, usage_service, billing_service, providers, config) -> Dict[str, Any]:
        """Process ETHOCA block - business logic"""
        block_id = block.get("id")
        descriptor = block.get("descriptor")
        
        # Validate required fields
        if not descriptor:
            logger.warning(f"⚠️ No descriptor for Ethoca block {block_id}")
            self.update_block_status(block_id, "NO_DESCRIPTOR")
            return {
                "success": False,
                "block_id": block_id,
                "error": "Missing descriptor"
            }
        
        # Find linked store
        store_id = linked_store_service.find_for_ethoca_block(descriptor)
        if not store_id:
            logger.warning(f"⚠️ No linked store found for descriptor: {descriptor}")
            self.mark_block_no_store(block_id)
            return {
                "success": True,
                "block_id": block_id,
                "message": "No linked store found"
            }
        
        logger.info(f"✅ Found linked store: {store_id}")
        
        # Get store details to determine provider
        store = linked_store_service.get_store(store_id)
        provider_type = store.get("provider", "").upper() if store else ""
        
        # Find matched entry
        matched_entry = None
        stripe_account = None  # Store account info to reuse for refund

        if provider_type in providers:
            provider = providers[provider_type]
            matched_entry = provider.find_matched_entry(store_id, block)

            # For Stripe provider, get account info once for potential refund use
            if provider_type == "stripe" and matched_entry and hasattr(provider, '_get_stripe_account_with_valid_token'):
                try:
                    stripe_account = provider._get_stripe_account_with_valid_token(store_id)
                    logger.debug(f"Retrieved Stripe account info for potential refund use")
                except Exception as e:
                    logger.warning(f"Could not retrieve Stripe account info: {str(e)}")

        # Process based on match result
        if matched_entry:
            logger.info(f"✅ Found matched entry: {matched_entry.get('id')}")

            # Attempt automatic refund for matched entries
            refund_result = None
            if provider_type in providers:
                provider = providers[provider_type]
                # Check if provider supports refunds
                if hasattr(provider, 'process_refund_for_matched_charge'):
                    logger.info(f"🔄 Attempting automatic refund for {provider_type} entry")
                    try:
                        # Pass stripe_account to avoid duplicate database query
                        if provider_type == "stripe" and stripe_account:
                            refund_result = provider.process_refund_for_matched_charge(store_id, block, matched_entry, stripe_account)
                        else:
                            refund_result = provider.process_refund_for_matched_charge(store_id, block, matched_entry)

                        if refund_result.get("success"):
                            logger.info(f"✅ Automatic refund successful for block {block_id}")
                        else:
                            logger.warning(f"⚠️ Automatic refund failed for block {block_id}: {refund_result.get('message')}")
                    except Exception as e:
                        logger.error(f"❌ Error during automatic refund for block {block_id}: {str(e)}")
                        refund_result = {"success": False, "message": str(e)}

            self.mark_block_processed(
                block_id,
                store_id,
                matched_entry.get("id")
            )
        else:
            logger.info(f"⚠️ No matched entry found")
            self.mark_block_no_match(block_id, store_id)
        
        # Create block usage record
        usage_service.create_usage_record(store_id, block_id, "ETHOCA")
        
        # Create bill
        billing_service.create_bill_for_block(
            store_id, block_id, "ETHOCA", custom_fee=config.ETHOCA_BLOCK_FEE
        )
        
        return {
            "success": True,
            "block_id": block_id,
            "store_id": store_id,
            "matched": matched_entry is not None,
            "refund_attempted": refund_result is not None if matched_entry else False,
            "refund_success": refund_result.get("success", False) if refund_result else False,
            "refund_message": refund_result.get("message") if refund_result else None,
            "message": "Ethoca block processed successfully"
        }
    
    def process_single_block(self, block: Dict[str, Any], linked_store_service, usage_service, billing_service, providers, config) -> Dict[str, Any]:
        """Process a single block based on its type - business logic"""
        block_type = block.get("type", "").upper()
        block_id = block.get("id")
        
        logger.info(f"🔄 Processing {block_type} block: {block_id}")
        
        if block_type == "RDR":
            return self.process_rdr_block(block, linked_store_service, usage_service, billing_service, config)
        elif block_type == "ETHOCA":
            return self.process_ethoca_block(block, linked_store_service, usage_service, billing_service, providers, config)
        else:
            logger.error(f"Unknown block type: {block_type}")
            self.update_block_status(block_id, "INVALID_TYPE")
            return {
                "success": False,
                "block_id": block_id,
                "error": f"Unknown block type: {block_type}"
            }
    
    def process_batch(self, blocks: List[Dict[str, Any]], linked_store_service, usage_service, billing_service, providers, config) -> Dict[str, Any]:
        """Process multiple blocks - business logic"""
        results = {
            "total": len(blocks),
            "success": 0,
            "failed": 0,
            "results": []
        }
        
        for block in blocks:
            try:
                result = self.process_single_block(block, linked_store_service, usage_service, billing_service, providers, config)
            except Exception as e:
                block_id = block.get("id")
                logger.error(f"❌ Failed to process block {block_id}: {str(e)}")
                self.mark_block_failed(block_id, str(e))
                result = {
                    "success": False,
                    "block_id": block_id,
                    "error": str(e)
                }
            
            if result.get("success"):
                results["success"] += 1
            else:
                results["failed"] += 1
            results["results"].append(result)
        
        logger.info(f"✅ Batch complete: {results['success']}/{results['total']} succeeded")
        return results
    
    def process_pending_blocks(self, linked_store_service, usage_service, billing_service, providers, config) -> Dict[str, Any]:
        """Process all pending blocks - orchestration logic"""
        try:
            logger.info("⏰ Starting block processing")
            start_time = datetime.now(timezone.utc)
            
            # Get all pending blocks without limit
            blocks = self.get_pending_blocks()
            
            if not blocks:
                logger.info("No pending blocks to process")
                return {"success": True, "message": "No pending blocks"}
            
            # Process batch
            result = self.process_batch(blocks, linked_store_service, usage_service, billing_service, providers, config)
            
            # Log results
            elapsed = (datetime.now(timezone.utc) - start_time).total_seconds()
            logger.info(f"✅ Processing complete in {elapsed:.2f}s")
            
            if result.get("success", 0) > 0:
                logger.info(f"📊 Processed {result['success']} blocks, {result.get('failed', 0)} failed")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Block processing failed: {str(e)}")
            return {"success": False, "error": str(e)}