from sqlalchemy import Column, String, Integer, ForeignKey, Index
from sqlalchemy.dialects.postgresql import BIGINT, TIMESTAMP
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

class ShopifyDispute(Base):
    __tablename__ = 'shopify_disputes'

    id = Column(BIGINT, primary_key=True)
    linked_store_id = Column('linked_store_id', String, ForeignKey('linked_stores.id'), nullable=False)
    transaction_id = Column('transaction_id', BIGINT, nullable=True)
    type = Column(String, nullable=True)
    amount = Column(Integer, nullable=True)
    reason = Column(String, nullable=True)
    status = Column(String, nullable=True)
    currency = Column(String, nullable=True)
    order_id = Column('order_id', BIGINT, nullable=True)
    finalized_on = Column('finalized_on', TIMESTAMP(timezone=True), nullable=True)
    initiated_at = Column('initiated_at', TIMESTAMP(timezone=True), nullable=True)
    evidence_due_by = Column('evidence_due_by', TIMESTAMP(timezone=True), nullable=True)
    evidence_sent_on = Column('evidence_sent_on', TIMESTAMP(timezone=True), nullable=True)
    network_reason_code = Column('network_reason_code', String, nullable=True)
    
    created_at = Column('created_at', TIMESTAMP(timezone=True), default=func.now(), nullable=False)
    updated_at = Column('updated_at', TIMESTAMP(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    linked_store = relationship("LinkedStore", back_populates="shopify_disputes")

    # PostgreSQL specific indexes
    __table_args__ = (
        Index('ix_shopify_disputes_linked_store_id', 'linked_store_id'),
        Index('ix_shopify_disputes_transaction_id', 'transaction_id'),
        Index('ix_shopify_disputes_order_id', 'order_id'),
        Index('ix_shopify_disputes_initiated_at', 'initiated_at'),
        Index('ix_shopify_disputes_status', 'status'),
        Index('ix_shopify_disputes_type', 'type'),
        Index('ix_shopify_disputes_reason', 'reason'),
        Index('ix_shopify_disputes_amount', 'amount'),
        Index('ix_shopify_disputes_currency', 'currency'),
        Index('ix_shopify_disputes_evidence_due_by', 'evidence_due_by'),
    )

    def __repr__(self):
        return f"<ShopifyDispute(id='{self.id}', linked_store_id='{self.linked_store_id}', amount={self.amount})>"