from sqlalchemy import Column, <PERSON>, Integer, ForeignKey, Index
from sqlalchemy.dialects.postgresql import BIGINT, TIMESTAMP
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

class UnifiedTransaction(Base):
    __tablename__ = 'unified_transactions'

    id = Column(String, primary_key=True)
    user_id = Column('user_id', String, ForeignKey('users.id'), nullable=False)
    linked_store_id = Column('linked_store_id', String, ForeignKey('linked_stores.id'), nullable=False)
    provider = Column(String, nullable=False)  # 'shopify' or 'stripe'
    
    # Core transaction data
    provider_transaction_id = Column('provider_transaction_id', String, nullable=False)
    amount = Column(BIGINT, nullable=False)  # Amount in cents
    currency = Column(String, nullable=False)
    status = Column(String, nullable=False)
    
    # Time tracking
    processed_at = Column('processed_at', TIMESTAMP(timezone=True), nullable=False)
    created_at = Column('created_at', TIMESTAMP(timezone=True), default=func.now(), nullable=False)
    updated_at = Column('updated_at', TIMESTAMP(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    user = relationship("User", back_populates="unified_transactions")
    linked_store = relationship("LinkedStore", back_populates="unified_transactions")
    unified_disputes = relationship("UnifiedDispute", back_populates="unified_transaction")

    # PostgreSQL specific indexes for chargeback rate calculations
    __table_args__ = (
        Index('ix_unified_transactions_user_id', 'user_id'),
        Index('ix_unified_transactions_linked_store_id', 'linked_store_id'),
        Index('ix_unified_transactions_provider', 'provider'),
        Index('ix_unified_transactions_processed_at', 'processed_at'),
        Index('ix_unified_transactions_user_processed_at', 'user_id', 'processed_at'),
        Index('ix_unified_transactions_store_processed_at', 'linked_store_id', 'processed_at'),
        Index('ix_unified_transactions_provider_processed_at', 'provider', 'processed_at'),
        Index('ix_unified_transactions_provider_transaction_id', 'provider_transaction_id'),
    )

    def __repr__(self):
        return f"<UnifiedTransaction(id='{self.id}', provider='{self.provider}', amount={self.amount})>"