from sqlalchemy import Column, <PERSON>, Integer, ForeignKey, Index
from sqlalchemy.dialects.postgresql import TIMESTAMP
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

class StatCountry(Base):
    __tablename__ = 'stat_country'

    id = Column(String, primary_key=True)
    user_id = Column('user_id', String, ForeignKey('users.id'), nullable=False)
    linked_store_id = Column('linked_store_id', String, ForeignKey('linked_stores.id'), nullable=False)
    provider = Column(String, nullable=False)  # 'shopify', 'stripe', etc.
    country_code = Column('country_code', String, nullable=False)  # ISO 2-letter country code (US, CA, GB, etc.)
    country_name = Column('country_name', String, nullable=False)  # Full country name
    count = Column(Integer, nullable=False, default=0)  # Number of chargebacks from this country
    created_at = Column('created_at', TIMESTAMP(timezone=True), default=func.now(), nullable=False)
    updated_at = Column('updated_at', TIMESTAMP(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    user = relationship("User", back_populates="stat_countries")
    linked_store = relationship("LinkedStore", back_populates="stat_countries")

    # PostgreSQL specific indexes for performance
    __table_args__ = (
        Index('ix_stat_country_user_id', 'user_id'),
        Index('ix_stat_country_linked_store_id', 'linked_store_id'),
        Index('ix_stat_country_provider', 'provider'),
        Index('ix_stat_country_country_code', 'country_code'),
        Index('ix_stat_country_count', 'count'),
        Index('stat_country_user_id_linked_store_id_provider_country_code_key', 
              'user_id', 'linked_store_id', 'provider', 'country_code', unique=True),
    )

    def __repr__(self):
        return f"<StatCountry(id='{self.id}', country_code='{self.country_code}', count={self.count})>"

    def to_dict(self):
        """Convert model instance to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'linked_store_id': self.linked_store_id,
            'provider': self.provider,
            'country_code': self.country_code,
            'country_name': self.country_name,
            'count': self.count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }