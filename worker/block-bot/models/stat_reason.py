from sqlalchemy import Column, <PERSON>, Integer, ForeignKey, Index
from sqlalchemy.dialects.postgresql import TIMESTAMP
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

class StatReason(Base):
    __tablename__ = 'stat_reason'

    id = Column(String, primary_key=True)
    user_id = Column('user_id', String, ForeignKey('users.id'), nullable=False)
    linked_store_id = Column('linked_store_id', String, ForeignKey('linked_stores.id'), nullable=False)
    provider = Column(String, nullable=False)  # 'shopify', 'stripe', etc.
    reason_code = Column('reason_code', String, nullable=False)  # Chargeback reason code
    reason_name = Column('reason_name', String, nullable=False)  # Human-readable reason name
    count = Column(Integer, nullable=False, default=0)  # Number of chargebacks with this reason
    created_at = Column('created_at', TIMESTAMP(timezone=True), default=func.now(), nullable=False)
    updated_at = Column('updated_at', TIMESTAMP(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    user = relationship("User", back_populates="stat_reasons")
    linked_store = relationship("LinkedStore", back_populates="stat_reasons")

    # PostgreSQL specific indexes for performance
    __table_args__ = (
        Index('ix_stat_reason_user_id', 'user_id'),
        Index('ix_stat_reason_linked_store_id', 'linked_store_id'),
        Index('ix_stat_reason_provider', 'provider'),
        Index('ix_stat_reason_reason_code', 'reason_code'),
        Index('ix_stat_reason_count', 'count'),
        Index('stat_reason_user_id_linked_store_id_provider_reason_code_key', 
              'user_id', 'linked_store_id', 'provider', 'reason_code', unique=True),
    )

    def __repr__(self):
        return f"<StatReason(id='{self.id}', reason_code='{self.reason_code}', count={self.count})>"

    def to_dict(self):
        """Convert model instance to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'linked_store_id': self.linked_store_id,
            'provider': self.provider,
            'reason_code': self.reason_code,
            'reason_name': self.reason_name,
            'count': self.count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }