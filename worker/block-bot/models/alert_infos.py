from sqlalchemy import Column, String, DateTime, Text, ForeignKey
from sqlalchemy.dialects.postgresql import TIMESTAMP
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

class AlertInfo(Base):
    __tablename__ = 'alert_infos'

    id = Column(String, primary_key=True)
    alert_type = Column('alert_type', String, nullable=False)  # "RDR" or "ETHOCA"
    descriptor = Column(String, nullable=False)
    bin = Column(String, nullable=True)
    caid = Column(String, nullable=True)
    arn = Column(String, nullable=True)  # Acquirer Reference Number
    registration_status = Column('registration_status', String, default="WAITING", nullable=False)  # "WAITING", "EFFECTED", "CLOSING", "CLOSED"
    registration_message = Column('registration_message', Text, nullable=True)
    registered_at = Column('registered_at', TIMESTAMP(timezone=True), nullable=True)
    closed_at = Column('closed_at', TIMESTAMP(timezone=True), nullable=True)
    store_id = Column('store_id', String, ForeignKey('linked_stores.id'), nullable=False)
    created_at = Column('created_at', TIMESTAMP(timezone=True), default=func.now(), nullable=False)
    updated_at = Column('updated_at', TIMESTAMP(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    store = relationship("LinkedStore", back_populates="alert_infos")

    def __repr__(self):
        return f"<AlertInfo(id='{self.id}', alert_type='{self.alert_type}', descriptor='{self.descriptor}')>"