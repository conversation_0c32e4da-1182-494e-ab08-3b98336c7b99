from sqlalchemy import Column, String, <PERSON>olean, ForeignKey, Index, Text
from sqlalchemy.dialects.postgresql import BIGINT, JSON, TIMESTAMP, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

class StripePaymentIntent(Base):
    __tablename__ = 'stripe_payment_intents'

    # Fields matching exactly with Prisma schema
    id = Column(String, primary_key=True)
    linked_store_id = Column('linked_store_id', String, ForeignKey('linked_stores.id'), nullable=False)
    object = Column(String, default="payment_intent", nullable=False)
    amount = Column(BIGINT, nullable=False)
    amount_capturable = Column('amount_capturable', BIGINT, nullable=True)
    amount_received = Column('amount_received', BIGINT, nullable=True)
    application = Column(String, nullable=True)
    application_fee_amount = Column('application_fee_amount', BIGINT, nullable=True)
    automatic_payment_methods = Column('automatic_payment_methods', JSON, nullable=True)
    canceled_at = Column('canceled_at', TIMESTAMP(timezone=True), nullable=True)
    cancellation_reason = Column('cancellation_reason', String, nullable=True)
    capture_method = Column('capture_method', String, nullable=True)
    client_secret = Column('client_secret', String, nullable=True)
    confirmation_method = Column('confirmation_method', String, nullable=True)
    created_at = Column('created_at', TIMESTAMP(timezone=True), nullable=False)
    currency = Column(String, nullable=False)
    customer_id = Column('customer_id', String, nullable=True)
    description = Column(String, nullable=True)
    invoice_id = Column('invoice_id', String, nullable=True)
    last_payment_error = Column('last_payment_error', JSON, nullable=True)
    latest_charge_id = Column('latest_charge_id', String, nullable=True)
    livemode = Column(Boolean, nullable=False)
    stripe_metadata = Column('metadata', JSON, nullable=True)
    next_action = Column('next_action', JSON, nullable=True)
    on_behalf_of = Column('on_behalf_of', String, nullable=True)
    payment_method_id = Column('payment_method_id', String, nullable=True)
    payment_method_configuration_details = Column('payment_method_configuration_details', JSON, nullable=True)
    payment_method_options = Column('payment_method_options', JSON, nullable=True)
    payment_method_types = Column('payment_method_types', ARRAY(String), nullable=True)
    processing = Column(JSON, nullable=True)
    receipt_email = Column('receipt_email', String, nullable=True)
    review = Column(String, nullable=True)
    setup_future_usage = Column('setup_future_usage', String, nullable=True)
    shipping = Column(JSON, nullable=True)
    source = Column(String, nullable=True)
    statement_descriptor = Column('statement_descriptor', String, nullable=True)
    statement_descriptor_suffix = Column('statement_descriptor_suffix', String, nullable=True)
    status = Column(String, nullable=False)
    transfer_data = Column('transfer_data', JSON, nullable=True)
    transfer_group = Column('transfer_group', String, nullable=True)
    updated_at = Column('updated_at', TIMESTAMP(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    linked_store = relationship("LinkedStore", back_populates="stripe_payment_intents")
    charges = relationship("StripeCharge", back_populates="payment_intent")

    # PostgreSQL specific indexes
    __table_args__ = (
        Index('ix_stripe_payment_intents_linked_store_id', 'linked_store_id'),
        Index('ix_stripe_payment_intents_status', 'status'),
        Index('ix_stripe_payment_intents_created_at', 'created_at'),
        Index('ix_stripe_payment_intents_customer_id', 'customer_id'),
        Index('ix_stripe_payment_intents_latest_charge_id', 'latest_charge_id'),
        Index('ix_stripe_payment_intents_amount', 'amount'),
        Index('ix_stripe_payment_intents_currency', 'currency'),
        Index('ix_stripe_payment_intents_livemode', 'livemode'),
    )

    def __repr__(self):
        return f"<StripePaymentIntent(id='{self.id}', linked_store_id='{self.linked_store_id}', amount={self.amount})>"