from sqlalchemy import Column, String, Integer, ForeignKey, Index
from sqlalchemy.dialects.postgresql import BIGINT, TIMESTAMP
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

class UnifiedDispute(Base):
    __tablename__ = 'unified_disputes'

    id = Column(String, primary_key=True)
    user_id = Column('user_id', String, ForeignKey('users.id'), nullable=False)
    linked_store_id = Column('linked_store_id', String, ForeignKey('linked_stores.id'), nullable=False)
    provider = Column(String, nullable=False)  # 'shopify' or 'stripe'
    
    # Core dispute data
    provider_dispute_id = Column('provider_dispute_id', String, nullable=False)
    unified_transaction_id = Column('unified_transaction_id', String, ForeignKey('unified_transactions.id'), nullable=True)
    amount = Column(BIGINT, nullable=False)  # Dispute amount in cents
    currency = Column(String, nullable=False)
    type = Column(String, nullable=False)
    status = Column(String, nullable=False)
    reason = Column(String, nullable=True)
    
    # Time tracking
    initiated_at = Column('initiated_at', TIMESTAMP(timezone=True), nullable=True)  # Key for chargeback rate calculations
    created_at = Column('created_at', TIMESTAMP(timezone=True), default=func.now(), nullable=False)
    updated_at = Column('updated_at', TIMESTAMP(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    user = relationship("User", back_populates="unified_disputes")
    linked_store = relationship("LinkedStore", back_populates="unified_disputes")
    unified_transaction = relationship("UnifiedTransaction", back_populates="unified_disputes")

    # PostgreSQL specific indexes for chargeback rate calculations
    __table_args__ = (
        Index('ix_unified_disputes_user_id', 'user_id'),
        Index('ix_unified_disputes_linked_store_id', 'linked_store_id'),
        Index('ix_unified_disputes_provider', 'provider'),
        Index('ix_unified_disputes_initiated_at', 'initiated_at'),
        Index('ix_unified_disputes_user_initiated_at', 'user_id', 'initiated_at'),
        Index('ix_unified_disputes_store_initiated_at', 'linked_store_id', 'initiated_at'),
        Index('ix_unified_disputes_provider_initiated_at', 'provider', 'initiated_at'),
        Index('ix_unified_disputes_provider_dispute_id', 'provider_dispute_id'),
        Index('ix_unified_disputes_unified_transaction_id', 'unified_transaction_id'),
    )

    def __repr__(self):
        return f"<UnifiedDispute(id='{self.id}', provider='{self.provider}', amount={self.amount})>"