from sqlalchemy import Column, <PERSON>, <PERSON>olean, ForeignKey, Index
from sqlalchemy.dialects.postgresql import BIGINT, JSON, TIMESTAMP
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

class StripeDispute(Base):
    __tablename__ = 'stripe_disputes'

    id = Column(String, primary_key=True)
    linked_store_id = Column('linked_store_id', String, ForeignKey('linked_stores.id'), nullable=False)
    object = Column(String, default="dispute", nullable=False)
    amount = Column(BIGINT, nullable=False)
    balance_transactions = Column('balance_transactions', JSON, nullable=True)
    charge_id = Column('charge_id', String, ForeignKey('stripe_charges.id'), nullable=False)
    currency = Column(String, nullable=False)
    evidence = Column(JSON, nullable=True)
    evidence_details = Column('evidence_details', JSON, nullable=True)
    is_charge_refundable = Column('is_charge_refundable', <PERSON>olean, nullable=False)
    livemode = Column(Boolean, nullable=False)
    stripe_metadata = Column('metadata', JSON, nullable=True)
    network_reason_code = Column('network_reason_code', String, nullable=True)
    reason = Column(String, nullable=False)
    status = Column(String, nullable=False)
    
    created_at = Column('created_at', TIMESTAMP(timezone=True), default=func.now(), nullable=False)
    updated_at = Column('updated_at', TIMESTAMP(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    linked_store = relationship("LinkedStore", back_populates="stripe_disputes")
    charge = relationship("StripeCharge", back_populates="disputes")

    # PostgreSQL specific indexes
    __table_args__ = (
        Index('ix_stripe_disputes_linked_store_id', 'linked_store_id'),
        Index('ix_stripe_disputes_charge_id', 'charge_id'),
        Index('ix_stripe_disputes_status', 'status'),
        Index('ix_stripe_disputes_created_at', 'created_at'),
        Index('ix_stripe_disputes_reason', 'reason'),
        Index('ix_stripe_disputes_amount', 'amount'),
        Index('ix_stripe_disputes_currency', 'currency'),
        Index('ix_stripe_disputes_network_reason_code', 'network_reason_code'),
        Index('ix_stripe_disputes_livemode', 'livemode'),
    )

    def __repr__(self):
        return f"<StripeDispute(id='{self.id}', linked_store_id='{self.linked_store_id}', amount={self.amount})>"