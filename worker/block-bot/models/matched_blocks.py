from sqlalchemy import Column, String, DateTime, Integer, Text, ForeignKey, Index, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSON, UUID, TIMESTAMP
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

class MatchedBlock(Base):
    __tablename__ = 'matched_blocks'

    id = Column(String, ForeignKey('blocks.id'), primary_key=True)  # Same as Block ID
    user_id = Column('user_id', String, ForeignKey('users.id'), nullable=False)
    linked_store_id = Column('linked_store_id', String, ForeignKey('linked_stores.id'), nullable=False)
    provider = Column(String, nullable=False)  # "shopify", "stripe", etc.
    
    # Matched transaction data - flexible JSON structure to store matched orders/transactions
    matched_data = Column('matched_data', JSON, nullable=True)
    
    # Block data fields (copied from blocks table)
    alert_id = Column('alert_id', String, nullable=False)
    alert_time = Column('alert_time', TIMESTAMP(timezone=True), nullable=False)
    alert_type = Column('alert_type', String, nullable=False)
    amount = Column(Integer, nullable=False)
    currency = Column(String, nullable=False)
    descriptor = Column(String, nullable=False)
    auth_code = Column('auth_code', String, nullable=True)
    card_bin = Column('card_bin', String, nullable=True)
    card_number = Column('card_number', String, nullable=True)
    chargeback_code = Column('chargeback_code', String, nullable=True)
    dispute_amount = Column('dispute_amount', Integer, nullable=True)
    dispute_currency = Column('dispute_currency', String, nullable=True)
    transaction_time = Column('transaction_time', TIMESTAMP(timezone=True), nullable=True)
    age = Column(String, nullable=True)
    alert_source = Column('alert_source', String, nullable=True)
    arn = Column(String, nullable=True)
    issuer = Column(String, nullable=True)
    initiated_by = Column('initiated_by', String, nullable=True)
    liability = Column(String, nullable=True)
    merchant_category_code = Column('merchant_category_code', String, nullable=True)
    transaction_id_block = Column('transaction_id', String, nullable=True)
    transaction_type = Column('transaction_type', String, nullable=True)
    acquirer_bin = Column('acquirer_bin', String, nullable=True)
    acquirer_reference_number = Column('acquirer_reference_number', String, nullable=True)
    alert_status = Column('alert_status', String, nullable=True)
    caid = Column(String, nullable=True)
    descriptor_contact = Column('descriptor_contact', String, nullable=True)
    rule_name = Column('rule_name', String, nullable=True)
    rule_type = Column('rule_type', String, nullable=True)
    type = Column(String, default="ETHOCA", nullable=False)
    feedback_data = Column('feedback_data', JSON, nullable=True)
    feedback_status = Column('feedback_status', String, default="PENDING", nullable=False)
    feedback_time = Column('feedback_time', TIMESTAMP(timezone=True), nullable=True)
    
    created_at = Column('created_at', TIMESTAMP(timezone=True), default=func.now(), nullable=False)
    updated_at = Column('updated_at', TIMESTAMP(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="matched_blocks")
    block = relationship("Block", back_populates="matched_blocks")
    linked_store = relationship("LinkedStore", back_populates="matched_blocks")

    # PostgreSQL specific constraints and indexes
    __table_args__ = (
        UniqueConstraint('id', 'linked_store_id', 'provider', name='uq_matched_blocks_id_store_provider'),
        Index('ix_matched_blocks_user_id', 'user_id'),
        Index('ix_matched_blocks_linked_store_id', 'linked_store_id'),
        Index('ix_matched_blocks_provider', 'provider'),
        Index('ix_matched_blocks_descriptor', 'descriptor'),
        Index('ix_matched_blocks_type', 'type'),
        Index('ix_matched_blocks_alert_time', 'alert_time'),
        Index('ix_matched_blocks_feedback_status', 'feedback_status'),
        Index('ix_matched_blocks_card_bin', 'card_bin'),
        Index('ix_matched_blocks_caid', 'caid'),
        Index('ix_matched_blocks_amount', 'amount'),
    )

    def __repr__(self):
        return f"<MatchedBlock(id='{self.id}', provider='{self.provider}', descriptor='{self.descriptor}')>"