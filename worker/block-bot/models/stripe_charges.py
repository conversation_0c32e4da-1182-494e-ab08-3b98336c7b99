from sqlalchemy import Column, <PERSON>, <PERSON>olean, ForeignKey, Index, Text
from sqlalchemy.dialects.postgresql import BIGINT, JSON, TIMESTAMP
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

class StripeCharge(Base):
    __tablename__ = 'stripe_charges'

    # Fields matching exactly with Prisma schema
    id = Column(String, primary_key=True)
    linked_store_id = Column('linked_store_id', String, ForeignKey('linked_stores.id'), nullable=False)
    object = Column(String, default="charge", nullable=False)
    amount = Column(BIGINT, nullable=False)
    amount_captured = Column('amount_captured', BIGINT, nullable=True)
    amount_refunded = Column('amount_refunded', BIGINT, nullable=True)
    application = Column(String, nullable=True)
    application_fee = Column('application_fee', String, nullable=True)
    application_fee_amount = Column('application_fee_amount', BIGINT, nullable=True)
    balance_transaction = Column('balance_transaction', String, nullable=True)
    billing_details = Column('billing_details', JSON, nullable=True)
    calculated_statement_descriptor = Column('calculated_statement_descriptor', String, nullable=True)
    captured = Column(Boolean, nullable=False)
    created_at = Column('created_at', TIMESTAMP(timezone=True), nullable=False)
    currency = Column(String, nullable=False)
    customer_id = Column('customer_id', String, nullable=True)
    description = Column(String, nullable=True)
    destination = Column(String, nullable=True)
    dispute_id = Column('dispute_id', String, nullable=True)
    disputed = Column(Boolean, default=False, nullable=False)
    failure_code = Column('failure_code', String, nullable=True)
    failure_message = Column('failure_message', String, nullable=True)
    fraud_details = Column('fraud_details', JSON, nullable=True)
    invoice_id = Column('invoice_id', String, nullable=True)
    livemode = Column(Boolean, nullable=False)
    stripe_metadata = Column('metadata', JSON, nullable=True)
    on_behalf_of = Column('on_behalf_of', String, nullable=True)
    order_id = Column('order_id', String, nullable=True)
    outcome = Column(JSON, nullable=True)
    paid = Column(Boolean, nullable=False)
    payment_intent_id = Column('payment_intent_id', String, ForeignKey('stripe_payment_intents.id'), nullable=True)
    payment_method = Column('payment_method', String, nullable=True)
    payment_method_details = Column('payment_method_details', JSON, nullable=True)
    receipt_email = Column('receipt_email', String, nullable=True)
    receipt_number = Column('receipt_number', String, nullable=True)
    receipt_url = Column('receipt_url', String, nullable=True)
    refunded = Column(Boolean, nullable=False)
    refunds = Column(JSON, nullable=True)
    review = Column(String, nullable=True)
    shipping = Column(JSON, nullable=True)
    source = Column(JSON, nullable=True)
    source_transfer = Column('source_transfer', String, nullable=True)
    statement_descriptor = Column('statement_descriptor', String, nullable=True)
    statement_descriptor_suffix = Column('statement_descriptor_suffix', String, nullable=True)
    status = Column(String, nullable=False)
    transfer_data = Column('transfer_data', JSON, nullable=True)
    transfer_group = Column('transfer_group', String, nullable=True)
    reference_card_number = Column('reference_card_number', String, nullable=True)
    reference_created_at = Column('reference_created_at', TIMESTAMP(timezone=True), nullable=True)
    reference_amount = Column('reference_amount', BIGINT, nullable=True)
    reference_currency = Column('reference_currency', String, nullable=True)
    updated_at = Column('updated_at', TIMESTAMP(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    linked_store = relationship("LinkedStore", back_populates="stripe_charges")
    payment_intent = relationship("StripePaymentIntent", back_populates="charges")
    disputes = relationship("StripeDispute", back_populates="charge")

    # PostgreSQL specific indexes
    __table_args__ = (
        Index('ix_stripe_charges_linked_store_id', 'linked_store_id'),
        Index('ix_stripe_charges_payment_intent_id', 'payment_intent_id'),
        Index('ix_stripe_charges_status', 'status'),
        Index('ix_stripe_charges_created_at', 'created_at'),
        Index('ix_stripe_charges_customer_id', 'customer_id'),
        Index('ix_stripe_charges_dispute_id', 'dispute_id'),
        Index('ix_stripe_charges_disputed', 'disputed'),
        Index('ix_stripe_charges_amount', 'amount'),
        Index('ix_stripe_charges_currency', 'currency'),
        Index('ix_stripe_charges_captured', 'captured'),
        Index('ix_stripe_charges_paid', 'paid'),
        Index('ix_stripe_charges_refunded', 'refunded'),
        Index('ix_stripe_charges_livemode', 'livemode'),
    )

    def __repr__(self):
        return f"<StripeCharge(id='{self.id}', linked_store_id='{self.linked_store_id}', amount={self.amount})>"