from sqlalchemy import Column, String, DateTime, Integer, Text
from sqlalchemy.dialects.postgresql import <PERSON>SO<PERSON>, TIMESTAMP
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

class Block(Base):
    __tablename__ = 'blocks'

    id = Column(String, primary_key=True)
    alert_id = Column('alert_id', String, nullable=False)
    alert_time = Column('alert_time', TIMESTAMP(timezone=True), nullable=False)
    alert_type = Column('alert_type', String, nullable=False)
    amount = Column(Integer, nullable=False)
    currency = Column(String, nullable=False)
    descriptor = Column(String, nullable=False)
    auth_code = Column('auth_code', String, nullable=True)
    card_bin = Column('card_bin', String, nullable=True)
    card_number = Column('card_number', String, nullable=True)
    chargeback_code = Column('chargeback_code', String, nullable=True)
    dispute_amount = Column('dispute_amount', Integer, nullable=True)
    dispute_currency = Column('dispute_currency', String, nullable=True)
    transaction_time = Column('transaction_time', TIMESTAMP(timezone=True), nullable=True)
    age = Column(String, nullable=True)
    alert_source = Column('alert_source', String, nullable=True)
    arn = Column(String, nullable=True)
    issuer = Column(String, nullable=True)
    initiated_by = Column('initiated_by', String, nullable=True)
    liability = Column(String, nullable=True)
    merchant_category_code = Column('merchant_category_code', String, nullable=True)
    transaction_id = Column('transaction_id', String, nullable=True)
    transaction_type = Column('transaction_type', String, nullable=True)
    acquirer_bin = Column('acquirer_bin', String, nullable=True)
    acquirer_reference_number = Column('acquirer_reference_number', String, nullable=True)
    alert_status = Column('alert_status', String, nullable=True)
    caid = Column(String, nullable=True)
    descriptor_contact = Column('descriptor_contact', String, nullable=True)
    rule_name = Column('rule_name', String, nullable=True)
    rule_type = Column('rule_type', String, nullable=True)
    type = Column(String, default="ETHOCA", nullable=False)
    created_at = Column('created_at', TIMESTAMP(timezone=True), default=func.now(), nullable=False)
    updated_at = Column('updated_at', TIMESTAMP(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)
    feedback_data = Column('feedback_data', JSON, nullable=True)
    feedback_status = Column('feedback_status', String, default="PENDING", nullable=False)
    feedback_time = Column('feedback_time', TIMESTAMP(timezone=True), nullable=True)

    # Relationships
    matched_blocks = relationship("MatchedBlock", back_populates="block")

    def __repr__(self):
        return f"<Block(id='{self.id}', alert_type='{self.alert_type}', amount={self.amount}, descriptor='{self.descriptor}')>"