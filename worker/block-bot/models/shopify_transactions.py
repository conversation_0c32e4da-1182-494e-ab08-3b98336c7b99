from sqlalchemy import Column, <PERSON>, Integer, <PERSON>olean, Foreign<PERSON>ey, Index, BigInteger
from sqlalchemy.dialects.postgresql import BIGINT, JSON, TIMESTAMP
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

class ShopifyTransaction(Base):
    __tablename__ = 'shopify_transactions'

    id = Column(BIGINT, primary_key=True)
    linked_store_id = Column('linked_store_id', String, ForeignKey('linked_stores.id'), nullable=False)
    order_id = Column('order_id', BIGINT, nullable=True)
    payout_id = Column('payout_id', BIGINT, nullable=True)
    fee = Column(Integer, nullable=True)
    net = Column(Integer, nullable=True)
    test = Column(Boolean, nullable=True)
    type = Column(String, nullable=True)
    amount = Column(Integer, nullable=True)
    amount_rounding = Column('amount_rounding', Integer, nullable=True)
    authorization = Column(String, nullable=True)
    authorization_expires_at = Column('authorization_expires_at', TIMESTAMP(timezone=True), nullable=True)
    currency = Column(String, nullable=True)
    device_id = Column('device_id', BIGINT, nullable=True)
    error_code = Column('error_code', String, nullable=True)
    extended_authorization_attributes = Column('extended_authorization_attributes', JSON, nullable=True)
    gateway = Column(String, nullable=True)
    kind = Column(String, nullable=True)
    location_id = Column('location_id', JSON, nullable=True)
    message = Column(String, nullable=True)
    parent_id = Column('parent_id', BIGINT, nullable=True)
    payment_details = Column('payment_details', JSON, nullable=True)
    payments_refund_attributes = Column('payments_refund_attributes', JSON, nullable=True)
    processed_at = Column('processed_at', TIMESTAMP(timezone=True), nullable=True)
    receipt = Column(JSON, nullable=True)
    source_name = Column('source_name', String, nullable=True)
    status = Column(String, nullable=True)
    total_unsettled_set = Column('total_unsettled_set', JSON, nullable=True)
    user_id = Column('user_id', BIGINT, nullable=True)
    currency_exchange_adjustment = Column('currency_exchange_adjustment', JSON, nullable=True)
    manual_payment_gateway = Column('manual_payment_gateway', Boolean, nullable=True)
    source_id = Column('source_id', BIGINT, nullable=True)
    source_type = Column('source_type', String, nullable=True)
    payout_status = Column('payout_status', String, nullable=True)
    source_order_id = Column('source_order_id', BIGINT, nullable=True)
    adjustment_reason = Column('adjustment_reason', String, nullable=True)
    source_order_transaction_id = Column('source_order_transaction_id', BIGINT, nullable=True)
    adjustment_order_transactions = Column('adjustment_order_transactions', JSON, nullable=True)
    reference_card_number = Column('reference_card_number', String, nullable=True)
    reference_amount = Column('reference_amount', Integer, nullable=True)
    reference_transaction_time = Column('reference_transaction_time', TIMESTAMP(timezone=True), nullable=True)
    reference_currency = Column('reference_currency', String, nullable=True)
    reference_arn = Column('reference_arn', String, nullable=True)
    reference_authorization_code = Column('reference_authorization_code', String, nullable=True)
    
    created_at = Column('created_at', TIMESTAMP(timezone=True), default=func.now(), nullable=False)
    updated_at = Column('updated_at', TIMESTAMP(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    linked_store = relationship("LinkedStore", back_populates="shopify_transactions")

    # PostgreSQL specific indexes
    __table_args__ = (
        Index('ix_shopify_transactions_linked_store_id', 'linked_store_id'),
        Index('ix_shopify_transactions_order_id', 'order_id'),
        Index('ix_shopify_transactions_payout_id', 'payout_id'),
        Index('ix_shopify_transactions_processed_at', 'processed_at'),
        Index('ix_shopify_transactions_status', 'status'),
        Index('ix_shopify_transactions_type', 'type'),
        Index('ix_shopify_transactions_user_id', 'user_id'),
        Index('ix_shopify_transactions_amount', 'amount'),
        Index('ix_shopify_transactions_currency', 'currency'),
    )

    def __repr__(self):
        return f"<ShopifyTransaction(id='{self.id}', linked_store_id='{self.linked_store_id}', amount={self.amount})>"