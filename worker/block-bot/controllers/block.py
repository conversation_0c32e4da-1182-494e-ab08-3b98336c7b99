"""
Block controller - Automated block processor that runs continuously
Processes chargeback blocks from various providers (Ethoca, RDR, etc.) on a scheduled basis
"""

import asyncio
import signal
from utils.logging import get_logger
from services.db import DatabaseService
from services.block import BlockService
from services.linked_store import LinkedStoreService
from services.usage import BlockUsageService
from services.billing import BillingService
from services.providers.shopify import ShopifyProvider
from services.providers.stripe import StripeProvider
from config import config

logger = get_logger("controllers.block")


class BlockController:
    """Automated block processing controller that runs continuously in the background"""
    
    def __init__(self):
        # Initialize database connection
        self.db = DatabaseService()
        
        # Initialize services
        self.block_service = None
        self.linked_store_service = None
        self.usage_service = None
        self.billing_service = None
        
        # Initialize providers
        self.providers = {}
        
        # Runtime state
        self.running = False
        
    def initialize(self):
        """Initialize controller with database connection"""
        self.db.connect()
        
        # Initialize services with db pool
        self.block_service = BlockService(self.db.pool)
        self.linked_store_service = LinkedStoreService(self.db.pool)
        self.usage_service = BlockUsageService(self.db.pool)
        self.billing_service = BillingService(self.db.pool)
        
        # Initialize providers
        self.providers = {
            "SHOPIFY": ShopifyProvider(self.db),
            "STRIPE": StripeProvider(self.db)
        }
        
        logger.info("✅ Block controller initialized")
    
    def shutdown(self):
        """Cleanup resources"""
        self.db.disconnect()
        logger.info("Block controller shutdown")
    
    async def process_pending_blocks(self):
        """Process all pending blocks - delegate to BlockService"""
        return self.block_service.process_pending_blocks(
            self.linked_store_service,
            self.usage_service, 
            self.billing_service,
            self.providers,
            config
        )
    
    async def start_processing(self):
        """Start the block processing service"""
        logger.info("🚀 Starting Block Processing Controller...")
        
        try:
            # Initialize controller
            self.initialize()
            logger.info("✅ Block controller initialized")
            
            logger.info(f"⏰ Auto-processing running every {config.PROCESS_INTERVAL_MINUTES} minute(s)")
            self.running = True
                
        except Exception as e:
            logger.error(f"❌ Failed to initialize controller: {str(e)}")
            raise
        
        logger.info("🎯 Block controller ready!")
    
    async def stop_processing(self):
        """Stop the block processing service"""
        logger.info("🛑 Shutting down Block Processing Controller...")
        
        self.shutdown()
        self.running = False
        
        logger.info("👋 Controller stopped!")
    
    async def run_processing_loop(self):
        """Main processing loop - orchestration function"""
        await self.start_processing()
        
        # Setup signal handlers for graceful shutdown
        def signal_handler(signum, _):
            logger.info(f"📡 Received signal {signum}")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            # Run first processing immediately
            if self.running:
                await self.process_pending_blocks()
                
            # Run continuous processing loop
            while self.running:
                await asyncio.sleep(config.PROCESS_INTERVAL_MINUTES * 60)
                await self.process_pending_blocks()
                    
        except KeyboardInterrupt:
            logger.info("⚠️  Received interrupt signal")
        except Exception as e:
            logger.error(f"❌ Processing loop failed: {str(e)}")
            raise
        finally:
            await self.stop_processing()
    
    async def main(self):
        """Main entry point for the block controller - orchestration function"""
        try:
            # Validate configuration
            config.validate()
            logger.info("✅ Configuration validated")
            
            await self.run_processing_loop()
            
        except Exception as e:
            logger.error(f"❌ Controller failed: {str(e)}")
            raise