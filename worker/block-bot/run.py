#!/usr/bin/env python3
"""
Service Controller - Centralized service management
Easy to add new services with simple registration
"""
import os
import sys
import time
import signal
from datetime import datetime
from typing import Dict, Callable, Any

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

from config.settings import settings
import logging

# Minimal logging setup
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('service_controller')

# Global shutdown flag
shutdown_requested = False

def signal_handler(signum, frame):
    global shutdown_requested
    shutdown_requested = True

def setup_signal_handlers():
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

class ServiceController:
    """Centralized service management"""

    def __init__(self):
        self.services = {}
        self.frequencies = {}

    def register(self, name: str, func: Callable, frequency: int = 1):
        """Register a service"""
        self.services[name] = func
        self.frequencies[name] = frequency

    def should_run(self, name: str, cycle: int) -> bool:
        """Check if service should run this cycle"""
        return cycle % self.frequencies[name] == 0

    def execute(self, name: str) -> Dict[str, Any]:
        """Execute a service"""
        try:
            result = self.services[name]()
            return {"service": name, "success": True, "result": result}
        except Exception as e:
            return {"service": name, "success": False, "error": str(e)}

# ============================================================================
# SERVICE REGISTRATION AREA - Add new services here
# ============================================================================

controller = ServiceController()

# Core Services
def data_sync_service():
    from services.unified_data_syncer import sync_all_unified_data
    return sync_all_unified_data(
        batch_size=settings.get_safe_batch_size(),
        transaction_days_back=7,
        dispute_days_back=14
    )

def block_matcher_service():
    from services.block_matcher import process_all_blocks_bulk
    return process_all_blocks_bulk(
        limit=None,
        batch_size=settings.get_safe_batch_size(),
    )

def stats_calculator_service():
    from services.stats_calculator import StatsCalculatorService
    with StatsCalculatorService() as stats_service:
        return stats_service.calculate_and_save_stats_for_all_stores()

# Register core services
controller.register("data_sync", data_sync_service, frequency=1)
controller.register("block_matcher", block_matcher_service, frequency=1)
controller.register("stats_calculator", stats_calculator_service, frequency=4)

# ============================================================================
# ADD NEW SERVICES HERE - Example:
#
# def my_new_service():
#     # Your service logic
#     return {"status": "completed"}
#
# controller.register("my_service", my_new_service, frequency=2)
# ============================================================================

def run_cycle(cycle_number: int):
    """Execute services for this cycle"""
    results = {}

    for service_name in controller.services:
        if controller.should_run(service_name, cycle_number):
            result = controller.execute(service_name)
            results[service_name] = result

            if result["success"]:
                logger.info(f"✅ {service_name} completed")
            else:
                logger.error(f"❌ {service_name} failed: {result.get('error', 'Unknown error')}")

    return results

def start_scheduler():
    """Start the service scheduler"""
    setup_signal_handlers()

    logger.info(f"Service Controller started - {settings.SCHEDULE_INTERVAL_MINUTES}min intervals")
    logger.info(f"Registered: {list(controller.services.keys())}")

    cycle = 1

    # Run first cycle
    run_cycle(cycle)

    # Main scheduler loop
    while not shutdown_requested:
        try:
            sleep_seconds = settings.SCHEDULE_INTERVAL_MINUTES * 60

            # Sleep in chunks for responsive shutdown
            for _ in range(sleep_seconds):
                if shutdown_requested:
                    break
                time.sleep(1)

            if not shutdown_requested:
                cycle += 1
                run_cycle(cycle)

        except KeyboardInterrupt:
            break

    logger.info("Service Controller stopped")

def main():
    """Main entry point"""
    start_scheduler()

if __name__ == "__main__":
    main()