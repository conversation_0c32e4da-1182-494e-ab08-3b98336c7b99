#!/usr/bin/env python3
"""
Service Controller - Centralized service management
Easy to add new services with simple registration
"""
import os
import sys
import time
import signal
from datetime import datetime
from typing import Callable

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

from config.settings import settings
import logging

# Minimal logging setup
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('service_controller')

# Global shutdown flag
shutdown_requested = False

def signal_handler(signum, frame):
    global shutdown_requested
    shutdown_requested = True

def setup_signal_handlers():
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

class ServiceController:
    """Simple service controller - all services run independently on schedule"""

    def __init__(self):
        self.services = []

    def add_service(self, name: str, func: Callable, frequency: int = 1):
        """Add a service to run every N cycles"""
        self.services.append({
            "name": name,
            "func": func,
            "frequency": frequency
        })

    def run_cycle(self, cycle: int):
        """Run all services that should execute this cycle"""
        for service in self.services:
            if cycle % service["frequency"] == 0:
                try:
                    service["func"]()
                    logger.info(f"✅ {service['name']} completed")
                except Exception as e:
                    logger.error(f"❌ {service['name']} failed: {str(e)}")

# ============================================================================
# SERVICE DEFINITIONS - All services run automatically on schedule
# ============================================================================

controller = ServiceController()

def unified_data_syncer_service():
    from services.unified_data_syncer import sync_all_unified_data
    sync_all_unified_data(
        batch_size=settings.get_safe_batch_size(),
        transaction_days_back=7,
        dispute_days_back=14
    )

def block_matcher_service():
    from services.block_matcher import process_all_blocks_bulk
    process_all_blocks_bulk(
        limit=None,
        batch_size=settings.get_safe_batch_size(),
    )

def stats_calculator_service():
    from services.stats_calculator import StatsCalculatorService
    with StatsCalculatorService() as stats_service:
        stats_service.calculate_and_save_stats_for_all_stores()

# Auto-register all services (tên service = tên file)
controller.add_service("unified_data_syncer_service", unified_data_syncer_service, frequency=1)
controller.add_service("block_matcher_service", block_matcher_service, frequency=1)
controller.add_service("stats_calculator_service", stats_calculator_service, frequency=4)

# ============================================================================
# ADD NEW SERVICES HERE:
# Naming convention: [filename]_service
# Example: services/backup_manager.py => backup_manager_service
#
# def backup_manager_service():
#     from services.backup_manager import BackupManager
#     BackupManager().run_backup()
#
# controller.add_service("backup_manager_service", backup_manager_service, frequency=6)
# ============================================================================

def start_scheduler():
    """Start all services on automatic schedule"""
    setup_signal_handlers()

    service_names = [s["name"] for s in controller.services]
    logger.info(f"Starting services: {service_names}")
    logger.info(f"Schedule: Every {settings.SCHEDULE_INTERVAL_MINUTES} minutes")

    cycle = 1

    # Run services immediately
    controller.run_cycle(cycle)

    # Continue running on schedule
    while not shutdown_requested:
        # Wait for next cycle
        sleep_seconds = settings.SCHEDULE_INTERVAL_MINUTES * 60
        for _ in range(sleep_seconds):
            if shutdown_requested:
                break
            time.sleep(1)

        if not shutdown_requested:
            cycle += 1
            controller.run_cycle(cycle)

    logger.info("Services stopped")

def main():
    """Main entry point"""
    start_scheduler()

if __name__ == "__main__":
    main()