#!/usr/bin/env python3
"""
Simple Service Runner - Easy to extend with new services
Core functionality: Run services on schedule with graceful shutdown
"""
import os
import sys
import time
import signal
from datetime import datetime
from typing import Dict, List, Callable, Any

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Import settings and configure logging
from config.settings import settings
import logging

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger('service_runner')

# Global shutdown flag
shutdown_requested = False

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    global shutdown_requested
    logger.info(f"Received signal {signum}, requesting shutdown...")
    shutdown_requested = True

def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown"""
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

class ServiceRegistry:
    """Simple service registry for easy extension"""

    def __init__(self):
        self.services = {}
        self.service_frequencies = {}
        self.counters = {}

    def register_service(self, name: str, func: Callable, frequency: int = 1):
        """Register a service to run every N cycles"""
        self.services[name] = func
        self.service_frequencies[name] = frequency
        self.counters[name] = 0
        logger.info(f"📝 Registered service '{name}' (runs every {frequency} cycle(s))")

    def should_run_service(self, name: str, cycle: int) -> bool:
        """Check if service should run this cycle"""
        return cycle % self.service_frequencies[name] == 0

    def run_service(self, name: str) -> Dict[str, Any]:
        """Run a single service"""
        try:
            logger.info(f"🔄 Starting {name} at {datetime.now()}")
            result = self.services[name]()
            logger.info(f"✅ {name} completed successfully")
            return {"service": name, "success": True, "result": result}
        except Exception as e:
            logger.error(f"❌ {name} failed: {str(e)}")
            logger.exception("Full traceback:")
            return {"service": name, "success": False, "error": str(e)}

# Initialize service registry
registry = ServiceRegistry()

def run_unified_data_sync():
    """Execute the unified data sync process"""
    from services.unified_data_syncer import sync_all_unified_data

    results = sync_all_unified_data(
        batch_size=settings.get_safe_batch_size(),
        transaction_days_back=7,
        dispute_days_back=14
    )

    total_synced = sum(results.values())
    logger.info(f"Synced: {total_synced} records. Breakdown: {results}")
    return results

def run_block_matcher():
    """Execute the block matcher process"""
    from services.block_matcher import process_all_blocks_bulk

    matches = process_all_blocks_bulk(
        limit=None,
        batch_size=settings.get_safe_batch_size(),
    )

    logger.info(f"Processed: {matches} matches")
    return matches

def run_stats_calculator():
    """Execute the stats calculator process"""
    from services.stats_calculator import StatsCalculatorService

    with StatsCalculatorService() as stats_service:
        results = stats_service.calculate_and_save_stats_for_all_stores()

        total_stores = results.get("total_stores", 0)
        processed_stores = results.get("processed_stores", 0)
        total_country_stats = results.get("total_country_stats", 0)
        total_reason_stats = results.get("total_reason_stats", 0)
        errors = results.get("errors", [])

        logger.info(f"Processed {processed_stores}/{total_stores} stores")
        logger.info(f"Generated {total_country_stats} country stats, {total_reason_stats} reason stats")

        if errors:
            logger.warning(f"{len(errors)} errors occurred during processing")

    return results

# Register all services
registry.register_service("data_sync", run_unified_data_sync, frequency=1)
registry.register_service("block_matcher", run_block_matcher, frequency=1)
registry.register_service("stats_calculator", run_stats_calculator, frequency=4)  # Every 4 cycles

def run_cycle(cycle_number: int):
    """Run one cycle of services"""
    start_time = datetime.now()
    results = {}

    logger.info(f"🚀 Starting cycle {cycle_number} at {start_time}")

    # Run services that should run this cycle
    for service_name in registry.services:
        if registry.should_run_service(service_name, cycle_number):
            result = registry.run_service(service_name)
            results[service_name] = result

    # Summary
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()

    successful = [name for name, result in results.items() if result.get("success")]
    failed = [name for name, result in results.items() if not result.get("success")]

    logger.info(f"🎯 Cycle {cycle_number} completed in {duration:.2f}s")
    if successful:
        logger.info(f"✅ Successful: {', '.join(successful)}")
    if failed:
        logger.error(f"❌ Failed: {', '.join(failed)}")

    return results

def run_scheduled():
    """Run services on a schedule with configurable intervals"""
    setup_signal_handlers()

    logger.info(f"🚀 Starting Service Runner - {settings.SCHEDULE_INTERVAL_MINUTES}-minute intervals")
    logger.info(f"📝 Registered services: {list(registry.services.keys())}")

    # Log current settings for transparency
    if settings.PERFORMANCE_MONITORING_ENABLED:
        settings.log_settings()

    cycle_number = 1

    # Run first cycle immediately
    try:
        run_cycle(cycle_number)
    except Exception as e:
        logger.error(f"❌ Error in initial cycle: {str(e)}")

    # Schedule subsequent runs
    while not shutdown_requested:
        try:
            # Sleep for the configured interval (convert minutes to seconds)
            sleep_seconds = settings.SCHEDULE_INTERVAL_MINUTES * 60

            logger.info(f"⏰ Next run in {settings.SCHEDULE_INTERVAL_MINUTES} minutes...")

            # Sleep in small chunks to allow for responsive shutdown
            for _ in range(sleep_seconds):
                if shutdown_requested:
                    break
                time.sleep(1)

            if not shutdown_requested:
                cycle_number += 1
                try:
                    run_cycle(cycle_number)
                except Exception as e:
                    logger.error(f"❌ Error in cycle {cycle_number}: {str(e)}")
                    # Continue running even if one cycle fails

        except KeyboardInterrupt:
            logger.info("Scheduler interrupted by user")
            break

    logger.info("✅ Scheduler stopped gracefully")

def add_new_service_example():
    """
    Example of how to add a new service:

    def my_new_service():
        # Your service logic here
        logger.info("Running my new service")
        return {"processed": 100}

    # Register the service to run every 2 cycles
    registry.register_service("my_service", my_new_service, frequency=2)
    """
    pass

def main():
    """Main entry point"""
    logger.info("🚀 Starting Simple Service Runner...")
    run_scheduled()

if __name__ == "__main__":
    main()