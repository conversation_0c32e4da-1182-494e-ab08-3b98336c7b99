#!/usr/bin/env python3
"""
Production Block Matcher with configurable scheduling and unified logging
Multi-threaded support for parallel execution
"""
import os
import sys
import time
import signal
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Import settings and configure logging
from config.settings import settings
import logging

# Configure unified logging system based on settings
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Create logger for main application
logger = logging.getLogger('block_matcher_runner')

# Global flag for graceful shutdown
shutdown_requested = False

# Thread-safe logging queue
log_queue = queue.Queue()
log_lock = threading.Lock()

# Stats calculation tracking
stats_calculation_counter = 0
STATS_CALCULATION_FREQUENCY = int(os.getenv('STATS_CALCULATION_FREQUENCY', '4'))  # Every 4 cycles by default

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    global shutdown_requested
    with log_lock:
        logger.info(f"Received signal {signum}, requesting shutdown...")
    shutdown_requested = True

def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown"""
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def run_unified_data_sync():
    """Execute the unified data sync process"""
    thread_id = threading.get_ident()
    try:
        with log_lock:
            logger.info(f"🔄 [Thread-{thread_id}] Starting unified data sync at {datetime.now()}")
        
        # Import and run unified data sync
        from services.unified_data_syncer import sync_all_unified_data
        
        with log_lock:
            logger.info(f"[Thread-{thread_id}] Syncing unified data with batch_size={settings.BATCH_SIZE}")
        
        results = sync_all_unified_data(
            batch_size=settings.get_safe_batch_size(),# Use direct env value
            transaction_days_back=7,   # Reduced from 30 days for better performance
            dispute_days_back=14       # Reduced from 90 days for better performance
        )
        
        total_synced = sum(results.values())
        with log_lock:
            logger.info(f"✅ [Thread-{thread_id}] Unified data sync completed. Synced: {total_synced} records")
            logger.info(f"[Thread-{thread_id}] Sync breakdown: {results}")
        
        return {"task": "unified_data_sync", "success": True, "results": results}
        
    except Exception as e:
        with log_lock:
            logger.error(f"❌ [Thread-{thread_id}] Error in unified data sync: {str(e)}")
            logger.exception("Full traceback:")
        return {"task": "unified_data_sync", "success": False, "error": str(e)}

def run_block_matcher():
    """Execute the block matcher process"""
    thread_id = threading.get_ident()
    try:
        with log_lock:
            logger.info(f"🔄 [Thread-{thread_id}] Starting block matcher run at {datetime.now()}")
        
        # Import and run bulk processing with settings
        from services.block_matcher import process_all_blocks_bulk
        
        with log_lock:
            logger.info(f"[Thread-{thread_id}] Initializing BATCH operations with batch_size={settings.BATCH_SIZE}")
        
        matches = process_all_blocks_bulk(
            limit=None,  # Process all blocks
            batch_size=settings.get_safe_batch_size(),  # Use direct env value
        )
        
        with log_lock:
            logger.info(f"✅ [Thread-{thread_id}] Block matcher run completed. Processed: {matches} matches")
        
        return {"task": "block_matcher", "success": True, "matches": matches}
        
    except Exception as e:
        with log_lock:
            logger.error(f"❌ [Thread-{thread_id}] Error in block matcher run: {str(e)}")
            logger.exception("Full traceback:")
        return {"task": "block_matcher", "success": False, "error": str(e)}

def run_stats_calculator():
    """Execute the stats calculator process"""
    thread_id = threading.get_ident()
    try:
        with log_lock:
            logger.info(f"📊 [Thread-{thread_id}] Starting stats calculation at {datetime.now()}")
        
        # Import and run stats calculation
        from services.stats_calculator import StatsCalculatorService
        
        with StatsCalculatorService() as stats_service:
            with log_lock:
                logger.info(f"[Thread-{thread_id}] Calculating stats for all active stores")
            
            results = stats_service.calculate_and_save_stats_for_all_stores()
            
            total_stores = results.get("total_stores", 0)
            processed_stores = results.get("processed_stores", 0)
            total_country_stats = results.get("total_country_stats", 0)
            total_reason_stats = results.get("total_reason_stats", 0)
            errors = results.get("errors", [])
            
            with log_lock:
                logger.info(f"✅ [Thread-{thread_id}] Stats calculation completed.")
                logger.info(f"[Thread-{thread_id}] Processed {processed_stores}/{total_stores} stores")
                logger.info(f"[Thread-{thread_id}] Generated {total_country_stats} country stats, {total_reason_stats} reason stats")
                
                if errors:
                    logger.warning(f"[Thread-{thread_id}] {len(errors)} errors occurred during processing")
        
        return {"task": "stats_calculator", "success": True, "results": results}
        
    except Exception as e:
        with log_lock:
            logger.error(f"❌ [Thread-{thread_id}] Error in stats calculation: {str(e)}")
            logger.exception("Full traceback:")
        return {"task": "stats_calculator", "success": False, "error": str(e)}

def run_tasks_parallel(include_stats=False):
    """Execute unified data sync and block matcher in parallel, optionally including stats calculation"""
    start_time = datetime.now()
    max_workers = 3 if include_stats else 2
    
    with log_lock:
        tasks = "data sync, block matcher" + (", stats calculator" if include_stats else "")
        logger.info(f"🚀 Starting parallel execution at {start_time} - Running: {tasks}")
    
    # Use ThreadPoolExecutor for parallel execution
    with ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="Worker") as executor:
        try:
            # Submit main tasks concurrently
            future_data_sync = executor.submit(run_unified_data_sync)
            future_block_matcher = executor.submit(run_block_matcher)
            futures = [future_data_sync, future_block_matcher]
            
            # Conditionally submit stats calculation
            future_stats = None
            if include_stats:
                future_stats = executor.submit(run_stats_calculator)
                futures.append(future_stats)
            
            # Wait for all tasks to complete
            results = {}
            for future in as_completed(futures):
                try:
                    result = future.result()
                    results[result["task"]] = result
                    
                    with log_lock:
                        if result["success"]:
                            logger.info(f"✅ {result['task']} completed successfully")
                        else:
                            logger.error(f"❌ {result['task']} failed: {result.get('error', 'Unknown error')}")
                            
                except Exception as e:
                    with log_lock:
                        logger.error(f"❌ Error getting result from future: {str(e)}")
            
            # Summary logging
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            successful_tasks = [task for task, result in results.items() if result.get("success")]
            failed_tasks = [task for task, result in results.items() if not result.get("success")]
            
            with log_lock:
                logger.info(f"🎯 Parallel execution completed in {duration:.2f}s")
                if successful_tasks:
                    logger.info(f"✅ Successful: {', '.join(successful_tasks)}")
                if failed_tasks:
                    logger.error(f"❌ Failed: {', '.join(failed_tasks)}")
            
            return results
            
        except Exception as e:
            with log_lock:
                logger.error(f"❌ Error in parallel execution: {str(e)}")
                logger.exception("Full traceback:")
            return {"error": str(e)}

def run_tasks_sequential(include_stats=False):
    """Execute tasks sequentially (fallback mode)"""
    with log_lock:
        tasks = "data sync, block matcher" + (", stats calculator" if include_stats else "")
        logger.info(f"🔄 Running tasks sequentially (fallback mode) - Running: {tasks}")
    
    # Run unified data sync first, then block matcher, then optionally stats
    sync_result = run_unified_data_sync()
    matcher_result = run_block_matcher()
    
    results = {
        "unified_data_sync": sync_result,
        "block_matcher": matcher_result
    }
    
    if include_stats:
        stats_result = run_stats_calculator()
        results["stats_calculator"] = stats_result
    
    return results

def run_scheduled():
    """Run block matcher on a schedule with configurable intervals using multithreading"""
    global stats_calculation_counter
    setup_signal_handlers()
    
    with log_lock:
        logger.info(f"🚀 Starting Scheduled Block Matcher with Unified Data Sync - {settings.SCHEDULE_INTERVAL_MINUTES}-minute intervals")
        logger.info(f"📊 Stats calculation will run every {STATS_CALCULATION_FREQUENCY} cycles")
        logger.info("⚡ Multithreaded execution enabled for improved performance")
    
    # Log current settings for transparency
    if settings.PERFORMANCE_MONITORING_ENABLED:
        settings.log_settings()
    
    # Determine execution mode - try parallel first, fallback to sequential if needed
    use_parallel = True
    
    # Run first execution immediately
    stats_calculation_counter += 1
    should_run_stats = (stats_calculation_counter % STATS_CALCULATION_FREQUENCY) == 0
    
    try:
        if use_parallel:
            results = run_tasks_parallel(include_stats=should_run_stats)
        else:
            results = run_tasks_sequential(include_stats=should_run_stats)
            
        # Check if parallel execution had issues
        if use_parallel and "error" in results:
            with log_lock:
                logger.warning("⚠️  Parallel execution encountered issues, switching to sequential mode")
            use_parallel = False
            results = run_tasks_sequential(include_stats=should_run_stats)
            
    except Exception as e:
        with log_lock:
            logger.error(f"❌ Error in initial execution: {str(e)}")
        use_parallel = False
    
    # Schedule subsequent runs
    while not shutdown_requested:
        try:
            # Sleep for the configured interval (convert minutes to seconds)
            sleep_seconds = settings.SCHEDULE_INTERVAL_MINUTES * 60
            
            with log_lock:
                execution_mode = "parallel" if use_parallel else "sequential"
                logger.info(f"⏰ Next run in {settings.SCHEDULE_INTERVAL_MINUTES} minutes... (Mode: {execution_mode})")
            
            # Sleep in small chunks to allow for responsive shutdown
            for _ in range(sleep_seconds):
                if shutdown_requested:
                    break
                time.sleep(1)
            
            if not shutdown_requested:
                # Execute tasks based on determined mode
                stats_calculation_counter += 1
                should_run_stats = (stats_calculation_counter % STATS_CALCULATION_FREQUENCY) == 0
                
                if should_run_stats:
                    with log_lock:
                        logger.info(f"📊 Cycle {stats_calculation_counter}: Including stats calculation this run")
                
                try:
                    if use_parallel:
                        results = run_tasks_parallel(include_stats=should_run_stats)
                        
                        # Check for parallel execution issues and fallback if needed
                        if "error" in results:
                            with log_lock:
                                logger.warning("⚠️  Parallel execution failed, falling back to sequential")
                            use_parallel = False
                            results = run_tasks_sequential(include_stats=should_run_stats)
                    else:
                        results = run_tasks_sequential(include_stats=should_run_stats)
                        
                except Exception as e:
                    with log_lock:
                        logger.error(f"❌ Error in scheduled execution: {str(e)}")
                    # Continue running even if one cycle fails
                
        except KeyboardInterrupt:
            with log_lock:
                logger.info("Scheduler interrupted by user")
            break
    
    with log_lock:
        logger.info("✅ Scheduler stopped gracefully")

def main():
    """Main entry point"""
    with log_lock:
        logger.info("🚀 Starting Production Block Matcher with Unified Data Sync (Multithreaded)...")
    run_scheduled()

if __name__ == "__main__":
    main()