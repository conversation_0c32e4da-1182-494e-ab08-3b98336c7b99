#!/usr/bin/env python3
"""
Block Processing Bot - Automated block processor without web server
Handles processing of chargeback blocks from various providers (Ethoca, RDR, etc.)
"""

import asyncio
from dotenv import load_dotenv
from controllers.block import BlockController
from utils.logging import setup_logging

load_dotenv()

# Setup logging
setup_logging()

async def main():
    """Main entry point - delegates to block controller"""
    controller = BlockController()
    await controller.main()

if __name__ == "__main__":
    # Run the controller
    asyncio.run(main())