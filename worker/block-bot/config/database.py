"""
Database connection and session management for PostgreSQL with psycopg2
"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.pool import QueuePool
from sqlalchemy.dialects import postgresql
import os

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

class Database:
    """PostgreSQL database manager"""
    
    def __init__(self, database_url: str = None):
        if database_url is None:
            database_url = os.getenv('DATABASE_URL', 'postgresql://user:password@localhost:5432/quantchargeback')
        
        # Handle Prisma-style schema parameter
        if '?schema=' in database_url:
            # Extract schema from URL
            url_parts = database_url.split('?')
            base_url = url_parts[0]
            params = url_parts[1]
            
            schema_name = None
            other_params = []
            
            for param in params.split('&'):
                if param.startswith('schema='):
                    schema_name = param.split('=')[1]
                else:
                    other_params.append(param)
            
            # Rebuild URL with search_path option
            if schema_name:
                search_path_option = f"-c search_path={schema_name}"
                if other_params:
                    database_url = f"{base_url}?{'&'.join(other_params)}&options={search_path_option.replace(' ', '%20')}"
                else:
                    database_url = f"{base_url}?options={search_path_option.replace(' ', '%20')}"
            else:
                database_url = f"{base_url}?{'&'.join(other_params)}" if other_params else base_url
        
        
        # PostgreSQL optimized engine configuration with psycopg2
        self.engine = create_engine(
            database_url,
            poolclass=QueuePool,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,  # Verify connections before use
            pool_recycle=3600,   # Recycle connections every hour
            echo=False,  # Set to True for SQL debugging
            # psycopg2 specific optimizations
            connect_args={
                "application_name": "block-bot-matcher",  # Identify app in pg_stat_activity
                "connect_timeout": 10,  # Connection timeout
            }
        )
        
        # Create scoped session for thread safety
        self.session_factory = sessionmaker(bind=self.engine)
        self.Session = scoped_session(self.session_factory)
    
    def get_session(self):
        """Get a new database session"""
        return self.Session()

# Create global database instance
db = Database()

