# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/quantchargeback

# Block Matcher Performance Settings (NEW)
BATCH_SIZE=500                          # Number of blocks to process per batch (max 1000)
MAX_UPSERT_BATCH_SIZE=1000             # Maximum records for single upsert operation
MAX_RETRIES=3                          # Number of retry attempts on failure
SLOW_OPERATION_THRESHOLD_SECONDS=30    # Threshold for slow operation warnings

# Memory Management (NEW)
MEMORY_CLEANUP_ENABLED=true            # Enable garbage collection between batches

# Monitoring and Logging (ENHANCED)
LOG_LEVEL=INFO                         # DEBUG, INFO, WARNING, ERROR, CRITICAL
PROGRESS_LOG_ENABLED=true              # Enable batch progress logging
PERFORMANCE_MONITORING_ENABLED=true   # Enable performance monitoring and alerts

# Block Fee Configuration (in cents)
ETHOCA_BLOCK_FEE=2500  # $25.00
RDR_BLOCK_FEE=2500     # $25.00

# Sync Configuration
SYNC_INTERVAL_MINUTES=10

# Shopify Configuration
SHOPIFY_API_VERSION=2024-01

# Stripe Configuration
STRIPE_API_KEY=your_stripe_api_key_here

# Processing Configuration
PROCESS_INTERVAL_MINUTES=5

# Block Matcher Scheduling
SCHEDULE_INTERVAL_MINUTES=15        # How often to run the block matcher (in minutes)

# Retry Configuration (Legacy - now handled by Block Matcher settings)
RETRY_DELAY_MINUTES=5

# OAuth Configuration
OAUTH_BASE_URL=https://your-oauth-server.com
OAUTH_CLIENT_ID=your_oauth_client_id
OAUTH_CLIENT_SECRET=your_oauth_client_secret

# Production Recommendations:
# - BATCH_SIZE=300-500 for high-volume production
# - BATCH_SIZE=100-200 for systems with limited memory
# - BATCH_SIZE=500-1000 for high-performance systems
# - MAX_RETRIES=3-5 for unreliable networks
# - SLOW_OPERATION_THRESHOLD_SECONDS=30-60 for monitoring