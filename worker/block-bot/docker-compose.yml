version: '3.8'

services:
  block-bot:
    build: .
    container_name: block-bot
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL:-redis://redis:6379}
      - AUTO_PROCESS_ENABLED=${AUTO_PROCESS_ENABLED:-true}
      - PROCESS_INTERVAL_MINUTES=${PROCESS_INTERVAL_MINUTES:-5}
      - ETHOCA_BLOCK_FEE=${ETHOCA_BLOCK_FEE:-2500}
      - RDR_BLOCK_FEE=${RDR_BLOCK_FEE:-2500}
    networks:
      - block-network
    depends_on:
      - redis
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

  redis:
    image: redis:7-alpine
    container_name: block-redis
    ports:
      - "6380:6379"
    networks:
      - block-network
    restart: unless-stopped
    volumes:
      - redis-data:/data

networks:
  block-network:
    driver: bridge

volumes:
  redis-data: