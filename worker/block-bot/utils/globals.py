"""
Global constants and configurations for block processing
"""

# Block types
BLOCK_TYPES = {
    "RDR": "RDR",
    "ETHOCA": "ETHOCA"
}

# Block statuses
BLOCK_STATUS = {
    "PENDING": "PENDING",
    "SENT": "SENT", 
    "FAILED": "FAILED",
    "INVALID_DATA": "INVALID_DATA",
    "NO_DESCRIPTOR": "NO_DESCRIPTOR",
    "INVALID_TYPE": "INVALID_TYPE"
}

# Provider types
PROVIDERS = {
    "SHOPIFY": "SHOPIFY",
    "STRIPE": "STRIPE"
}

# Store statuses
STORE_STATUS = {
    "ACTIVE": "ACTIVE",
    "INACTIVE": "INACTIVE",
    "SUSPENDED": "SUSPENDED"
}

# Bill statuses
BILL_STATUS = {
    "PENDING": "PENDING",
    "PAID": "PAID",
    "FAILED": "FAILED",
    "CANCELLED": "CANCELLED"
}