"""
Formatting and parsing utility functions
"""

from typing import Dict

def parse_descriptor(descriptor: str) -> Dict[str, str]:
    """Parse a descriptor string to extract merchant info"""
    parts = descriptor.split("*") if "*" in descriptor else [descriptor]
    return {
        "full": descriptor,
        "merchant": parts[0].strip() if parts else descriptor,
        "suffix": parts[1].strip() if len(parts) > 1 else ""
    }

