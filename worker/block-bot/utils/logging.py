"""
Enhanced logging configuration for block processing server
"""

import logging
import os
import sys
from datetime import datetime
from pathlib import Path
import colorlog

def setup_logging():
    """Setup enhanced logging with file and console output"""
    
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logger_name = "block_bot"
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.DEBUG)
    
    if logger.handlers:
        return logger
    
    log_filename = log_dir / f"block_bot_{datetime.now().strftime('%Y%m%d')}.log"
    
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    console_formatter = colorlog.ColoredFormatter(
        '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s%(reset)s',
        datefmt='%H:%M:%S',
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
    )
    
    file_handler = logging.FileHandler(log_filename, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(file_formatter)
    
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(console_formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def get_logger(name: str = None):
    """Get a logger instance"""
    if name:
        return logging.getLogger(f"block_bot.{name}")
    return logging.getLogger("block_bot")